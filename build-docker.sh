#!/bin/bash

# Build script for notification service with ML support

set -e

echo "🚀 Building Notification Service with ML Support..."

# Build the Java application
echo "📦 Building Java application..."
mvn clean package -DskipTests

# Build Docker image
echo "🐳 Building Docker image..."
docker build -t lookforx/notification-service:latest .

# Tag for different environments
docker tag lookforx/notification-service:latest lookforx/notification-service:$(date +%Y%m%d-%H%M%S)

echo "✅ Build completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Push to registry: docker push lookforx/notification-service:latest"
echo "2. Deploy to K8s: kubectl apply -f k8s/"
echo ""
echo "🔍 Image details:"
docker images | grep lookforx/notification-service
