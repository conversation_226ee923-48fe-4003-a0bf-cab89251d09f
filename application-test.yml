# Test Environment Configuration
spring:
  # Kafka Configuration for Test
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:*************:9092}
    producer:
      retries: 2
      batch-size: 8192
      linger-ms: 0
      buffer-memory: 16777216
      acks: 1
      enable-idempotence: false
      compression-type: none
    consumer:
      group-id: ${spring.application.name}-test-consumer-group
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 50

  # MongoDB Configuration for Test
  data:
    mongodb:
      host: localhost
      port: 27017
      database: lookforx_logs_test
      auto-index-creation: true

# Management and Monitoring for Test
management:
  # Zipkin Configuration for Test
  zipkin:
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
      connect-timeout: 1s
      read-timeout: 5s

  # Tracing Configuration for Test
  tracing:
    enabled: true
    sampling:
      probability: 0.5  # 50% sampling for test
    baggage:
      correlation:
        enabled: true
        fields:
          - user-id
          - session-id
          - request-id
      remote-fields:
        - user-id
        - session-id
        - request-id
        - correlation-id

  # Metrics Configuration for Test
  metrics:
    tags:
      application: ${spring.application.name}
      environment: test
      version: ${spring.application.version}
    distribution:
      percentiles-histogram:
        http.server.requests: true
        http.client.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
        http.client.requests: 0.5, 0.95, 0.99
    export:
      prometheus:
        enabled: true

# LookForX Logging Configuration for Test
lookforx:
  logging:
    kafka:
      enabled: true
      consumer:
        group-id: logging-test-consumer-group
        auto-offset-reset: earliest
        max-poll-records: 50
        concurrency: 1
    storage:
      enabled: true
    consumer:
      enabled: false  # Only enable in dedicated logging service

# Logging Configuration for Test
logging:
  level:
    root: INFO
    com.lookforx: INFO
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.springframework.kafka: WARN
    org.apache.kafka: WARN
    zipkin2: WARN
    io.micrometer.tracing: INFO
    org.mongodb.driver: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    path: ./logs