spring:
  application:
    name: reference-data-microservice
  datasource:
    url: ****************************************************
    username: postgres
    password: L00kForX_?
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    defer-datasource-initialization: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect

  data:
    redis:
      host: *************
      port: 6379
      password: ""       # <PERSON><PERSON><PERSON> şifre yoksa "" ya da hiç yazma
      timeout: 2000ms     # veya "2s" şeklinde açıkça zaman birimi ile
      database: 2
  cache:
    type: redis
  # Jackson configuration for Java 8 time
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  logging:
    level:
      root: INFO
      com.lookforx.referencedataservice: INFO

  kafka:
    bootstrap-servers: *************:9092
    producer:
      # Increase timeouts for cloud Kafka
      request-timeout-ms: 600000      # 10 minutes
      delivery-timeout-ms: 1200000    # 20 minutes
      max-block-ms: 600000            # 10 minutes
      retries: 10                     # Increase retry count
      retry-backoff-ms: 5000          # 5 seconds between retries
      # Batch settings for better performance
      batch-size: 32768               # 32KB batch size
      linger-ms: 100                  # Wait 100ms to batch messages
      buffer-memory: 67108864         # 64MB buffer
      # Compression for better network utilization
      compression-type: gzip
      # Acknowledgment settings
      acks: all                       # Wait for all replicas
      # Serialization
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

management:
  tracing:
    sampling:
      probability: 1.0
    zipkin:
      endpoint: http://*************:9411/api/v2/spans
