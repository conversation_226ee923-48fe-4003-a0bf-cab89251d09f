version: '3.8'

services:
  # Elasticsearch: Logları saklar ve indexler.
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.14.1 # Tüm stack için aynı versiyonu kullanın!
    container_name: elasticsearch
    environment:
      - discovery.type=single-node # Tek node'lu bir cluster olarak çalıştır.
      - ES_JAVA_OPTS=-Xms1g -Xmx1g # VM'inizin belleğine göre ayarlayın (Örn: 1GB).
      - xpack.security.enabled=false # BAŞLANGIÇ İÇİN GÜVENLİK KAPALI. Production için 'true' yapıp parola belirleyin.
    volumes:
      - esdata:/usr/share/elasticsearch/data # Elasticsearch verilerini kalıcı hale getirir.
    ports:
      - "9200:9200"
    networks:
      - elk-net
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9200/_cluster/health?wait_for_status=yellow&timeout=30s"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Logstash: <PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON> ve Elasticsearch'e gönderir.
  logstash:
    image: docker.elastic.co/logstash/logstash:8.14.1
    container_name: logstash
    volumes:
      - ./logstash/pipeline:/usr/share/logstash/pipeline:ro # Logstash konfigürasyonunu içeri bağlar.
    ports:
      - "5044:5044/tcp"
    networks:
      - elk-net
    depends_on:
      elasticsearch:
        condition: service_healthy # Elasticsearch sağlıklı olduğunda başla.

  # Kibana: Logları görselleştirme, arama ve dashboard oluşturma arayüzü.
  kibana:
    image: docker.elastic.co/kibana/kibana:8.14.1
    container_name: kibana
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200 # Docker ağı üzerinden Elasticsearch'e bağlanır.
    ports:
      - "5601:5601"
    networks:
      - elk-net
    depends_on:
      elasticsearch:
        condition: service_healthy # Elasticsearch sağlıklı olduğunda başla.

volumes:
  esdata: # Docker'ın yönettiği isimlendirilmiş volume.

networks:
  elk-net:
    driver: bridge