# Production Environment Configuration
spring:
  # Kafka Configuration for Production
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka-cluster:9092}
    producer:
      retries: 5
      batch-size: 32768
      linger-ms: 5
      buffer-memory: 67108864
      acks: all  # Highest durability for production
      enable-idempotence: true
      compression-type: lz4
      max-in-flight-requests-per-connection: 5
    consumer:
      group-id: ${spring.application.name}-prod-consumer-group
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 1000
      session-timeout-ms: 30000
      heartbeat-interval-ms: 10000

  # MongoDB Configuration for Production
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://mongodb-cluster:27017/lookforx_logs_prod}
      auto-index-creation: false  # Manually manage indexes in production

# Management and Monitoring for Production
management:
  # Zipkin Configuration for Production
  zipkin:
    tracing:
      endpoint: ${ZIPKIN_ENDPOINT:http://zipkin:9411/api/v2/spans}
      connect-timeout: 2s
      read-timeout: 30s

  # Tracing Configuration for Production
  tracing:
    enabled: true
    sampling:
      probability: 0.1  # 10% sampling for production performance
    baggage:
      correlation:
        enabled: true
        fields:
          - user-id
          - session-id
          - request-id
      remote-fields:
        - user-id
        - session-id
        - request-id
        - correlation-id

  # Metrics Configuration for Production
  metrics:
    tags:
      application: ${spring.application.name}
      environment: prod
      version: ${spring.application.version}
      datacenter: ${DATACENTER:default}
      region: ${REGION:default}
    distribution:
      percentiles-histogram:
        http.server.requests: true
        http.client.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
        http.client.requests: 0.5, 0.95, 0.99
    export:
      prometheus:
        enabled: true

# LookForX Logging Configuration for Production
lookforx:
  logging:
    kafka:
      enabled: true
      consumer:
        group-id: logging-prod-consumer-group
        auto-offset-reset: earliest
        max-poll-records: 1000
        concurrency: 5
    storage:
      enabled: true
    consumer:
      enabled: false  # Only enable in dedicated logging service

# Logging Configuration for Production
logging:
  level:
    root: WARN
    com.lookforx: INFO
    org.springframework.web: WARN
    org.springframework.security: WARN
    org.springframework.kafka: WARN
    org.apache.kafka: ERROR
    zipkin2: ERROR
    io.micrometer.tracing: WARN
    org.mongodb.driver: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    path: ${LOG_PATH:/var/log/lookforx}