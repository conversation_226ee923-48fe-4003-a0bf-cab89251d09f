# Logging Service Production Configuration
spring:
  application:
    name: logging-microservice
    version: 1.0.0
  
  # Kafka Configuration for Logging Service Production
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:kafka-cluster:9092}
    producer:
      retries: 5
      batch-size: 32768
      linger-ms: 5
      buffer-memory: 67108864
      acks: all
      enable-idempotence: true
      compression-type: lz4
      max-in-flight-requests-per-connection: 5
    consumer:
      group-id: logging-service-prod-consumer-group
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 2000
      session-timeout-ms: 30000
      heartbeat-interval-ms: 10000
      fetch-min-bytes: 1024
      fetch-max-wait-ms: 500

  # MongoDB Configuration for Logging Service Production
  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://mongodb-cluster:27017/lookforx_logs_prod}
      auto-index-creation: false

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  health:
    mongo:
      enabled: true
    kafka:
      enabled: true

  # Zipkin Configuration
  zipkin:
    tracing:
      endpoint: ${ZIPKIN_ENDPOINT:http://zipkin:9411/api/v2/spans}
      connect-timeout: 2s
      read-timeout: 30s
  
  # Tracing Configuration
  tracing:
    enabled: true
    sampling:
      probability: 0.05  # Very low sampling for logging service in production
    baggage:
      correlation:
        enabled: true
        fields:
          - user-id
          - session-id
          - request-id
      remote-fields:
        - user-id
        - session-id
        - request-id
        - correlation-id

  # Metrics Configuration
  metrics:
    tags:
      application: logging-service
      environment: prod
      version: 1.0.0
      datacenter: ${DATACENTER:default}
      region: ${REGION:default}
    export:
      prometheus:
        enabled: true

# LookForX Logging Configuration - CONSUMER ENABLED
lookforx:
  logging:
    kafka:
      enabled: true
      consumer:
        group-id: logging-service-consumer-group
        auto-offset-reset: earliest
        max-poll-records: 2000
        concurrency: 10
    storage:
      enabled: true
    consumer:
      enabled: true  # ENABLE CONSUMER FOR LOGGING SERVICE

# Logging Configuration for Logging Service
logging:
  level:
    root: WARN
    com.lookforx: INFO
    com.lookforx.common.logging: INFO
    org.springframework.kafka: WARN
    org.apache.kafka: ERROR
    zipkin2: ERROR
    io.micrometer.tracing: WARN
    org.mongodb.driver: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    path: ${LOG_PATH:/var/log/lookforx}

# Server Configuration
server:
  port: ${SERVER_PORT:8090}
  servlet:
    context-path: /logging-service

# Eureka Client Configuration
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_SERVER_URL:http://eureka-server:8761/eureka/}
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90
    hostname: ${HOSTNAME:logging-service}
