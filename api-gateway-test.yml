spring:
  application:
    name: api-gateway

springdoc:
  swagger-ui:
    path: /swagger-ui.html
    urls:
      - name: User Service
        url: /user-service/v3/api-docs
      - name: Request Service
        url: /request-service/v3/api-docs
      - name: Search Service
        url: /search-service/v3/api-docs
      - name: Notification Service
        url: /notification-service/v3/api-docs
      - name: Category Service
        url: /category-service/v3/api-docs
      - name: Bid Service
        url: /bid-service/v3/api-docs
      - name: Auth Service
        url: /auth-service/v3/api-docs
      - name: Membership Service
        url: /membership-service/v3/api-docs
      - name: Messaging Service
        url: /messaging-service/v3/api-docs
      - name: Media Service
        url: /media-service/v3/api-docs
      - name: Exception Service
        url: /exception-service/v3/api-docs
      - name: Question Form Service
        url: /question-form-service/v3/api-docs
      - name: Campaign Service
        url: /campaign-service/v3/api-docs
      - name: Reference Data Service
        url: /reference-data-service/v3/api-docs
      - name: Payment Service
        url: /payment-service/v3/api-docs
      - name: Customer Support Service
        url: /customer-support-service/v3/api-docs
      - name: Logging Service
        url: /logging-microservice/v3/api-docs