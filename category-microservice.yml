spring:
  application:
    name: category-microservice
  datasource:
    url: **********************************************
    username: postgres
    password: L00kForX_?
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  logging:
    level:
      org.hibernate.SQL: DEBUG
      org.hibernate.type.descriptor.sql.BasicBinder: TRACE
      springframework.web : DEBUG
      org.springframework.data.elasticsearch.client.elc: DEBUG
      co.elastic.clients.transport: DEBUG
  cache:
    cache-names: categories  # Cache ismi (burada categories)
    redis:
      host: ***********            # Docker Compose servis adı
      port: 6379
      password: ""           # Redis şifresi, eğer var ise
      timeout: 2000


