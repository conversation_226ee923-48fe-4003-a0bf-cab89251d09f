# ----------------------------
# Java ve Maven
# ----------------------------
target/
*.log
*.class
*.jar
*.war
*.ear

# ----------------------------
# Spring Boot
# ----------------------------
*.db
*.h2.db
*.mv.db
*.sqlite
*.pid
*.tmp
hs_err_pid*

# ----------------------------
# IntelliJ IDEA
# ----------------------------
.idea/
*.iws
*.iml
*.ipr
out/

# ----------------------------
# Eclipse
# ----------------------------
.project
.classpath
.settings/
bin/

# ----------------------------
# OS / Sistem
# ----------------------------
.DS_Store
Thumbs.db

# ----------------------------
# Logs
# ----------------------------
logs/
*.log
spring.log

# ----------------------------
# Docker
# ----------------------------
*.bak
*.swp
docker-compose.override.yml
.env

# ----------------------------
# Test ve coverage dosyaları
# ----------------------------
jacoco.exec
coverage/
test-output/
surefire-reports/
failsafe-reports/

# ----------------------------
# VS Code (ekstra olarak kullanıyorsan)
# ----------------------------
.vscode/

# ----------------------------
# Redis veya Kafka local dump'ları (geliştirme ortamı)
# ----------------------------
dump.rdb
