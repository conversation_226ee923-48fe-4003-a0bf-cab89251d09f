# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
replay_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# Mac
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Spring Boot
*.pid
*.pid.lock

# Application specific
application-local.yml
application-local.properties
application-dev.yml
application-dev.properties
application-prod.yml
application-prod.properties

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log.*

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
.dockerignore
docker-compose.override.yml

# Coverage reports
coverage/
*.lcov

# Node modules (if any frontend tools)
node_modules/

# Cache
.cache/
*.cache

# Test results
test-results/
coverage-reports/

# JaCoCo
jacoco.exec
jacoco-it.exec

# SonarQube
.sonar/
.scannerwork/

# Backup files
*.bak
*.backup

# IDE specific
*.code-workspace
.history/

# OS generated files
.DS_Store?
._*
.Spotlight-V100
.Trashes
Icon?
ehthumbs.db
Thumbs.db

# Customer Support Service specific
customer-support-service.log
ticket-attachments/
temp-uploads/
