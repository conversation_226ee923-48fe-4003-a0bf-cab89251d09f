# Spam Detection Service Integration

This document describes how to integrate and use the spam detection service in your microservices.

## Overview

The spam detection service provides ML-powered content classification to detect spam, adult content, and drug-related content across multiple languages. The integration is provided through a Feign client in the common module.

## Components

### 1. Feign Client
- **SpamDetectionClient**: Main interface for calling the spam detection service
- **SpamDetectionClientConfig**: Configuration with appropriate timeouts for ML processing
- **SpamDetectionErrorDecoder**: Custom error handling for service failures

### 2. Service Layer
- **SpamDetectionClientService**: High-level service with error handling and logging
- Provides convenience methods for common use cases
- Includes health checks and metrics

### 3. DTOs
- **SpamDetectionRequest**: Single text classification request
- **SpamDetectionResponse**: Classification result with confidence score
- **BulkSpamDetectionRequest**: Multiple texts classification request
- **BulkSpamDetectionResponse**: Bulk classification results

## Configuration

Add the following configuration to your microservice's `application.yml`:

```yaml
lookforx:
  spam-detection:
    enabled: true
    url: http://spam-detection-service:8765
```

For development/local testing:
```yaml
lookforx:
  spam-detection:
    enabled: true
    url: http://localhost:8765
```

## Usage Examples

### 1. Basic Text Classification

```java
@Service
@RequiredArgsConstructor
public class ContentModerationService {
    
    private final SpamDetectionClientService spamDetectionService;
    
    public boolean isContentSafe(String content, LanguageCode language) {
        try {
            // Check if service is healthy first
            if (!spamDetectionService.isServiceHealthy()) {
                log.warn("Spam detection service is not healthy, allowing content");
                return true; // Fail open for availability
            }
            
            // Classify the content
            SpamDetectionResponse response = spamDetectionService.classifyText(content, language);
            
            // Check if it's spam (using default 0.7 threshold)
            return !Boolean.TRUE.equals(response.getIsSpam());
            
        } catch (Exception e) {
            log.error("Failed to check content safety: {}", e.getMessage());
            return true; // Fail open
        }
    }
}
```

### 2. Custom Threshold Classification

```java
public boolean isContentSafeStrict(String content, LanguageCode language) {
    // Use lower threshold (0.5) for stricter filtering
    return !spamDetectionService.isSpam(content, language, 0.5);
}
```

### 3. Bulk Content Processing

```java
public List<String> filterSpamContent(List<String> contents, LanguageCode language) {
    try {
        BulkSpamDetectionResponse response = spamDetectionService.classifyTexts(contents, language);
        
        return response.getResults().stream()
            .filter(result -> !Boolean.TRUE.equals(result.getIsSpam()))
            .map(SpamDetectionResponse::getText)
            .collect(Collectors.toList());
            
    } catch (Exception e) {
        log.error("Failed to filter spam content: {}", e.getMessage());
        return contents; // Return all content if service fails
    }
}
```

### 4. Health Check Integration

```java
@Component
public class SpamDetectionHealthIndicator implements HealthIndicator {
    
    private final SpamDetectionClientService spamDetectionService;
    
    @Override
    public Health health() {
        try {
            boolean healthy = spamDetectionService.isServiceHealthy();
            
            if (healthy) {
                Map<String, Object> metrics = spamDetectionService.getServiceMetrics();
                return Health.up()
                    .withDetails(metrics)
                    .build();
            } else {
                return Health.down()
                    .withDetail("reason", "Spam detection service is not healthy")
                    .build();
            }
        } catch (Exception e) {
            return Health.down()
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

## Error Handling

The service includes comprehensive error handling:

- **Service Unavailable (503)**: ML model is loading or service is down
- **Timeout (408)**: Request took too long (>60 seconds)
- **Bad Request (400)**: Invalid input parameters
- **Internal Error (500)**: Unexpected service error

All methods include fallback behavior to ensure your application remains available even if spam detection fails.

## Performance Considerations

- **Single Text**: ~1-4 seconds processing time
- **Bulk Processing**: More efficient for multiple texts
- **Caching**: Consider caching results for frequently checked content
- **Async Processing**: For non-blocking operations, consider async patterns

## Language Support

The service supports 50+ languages including:
- EN (English), TR (Turkish), DE (German), FR (French)
- ES (Spanish), IT (Italian), PT (Portuguese), RU (Russian)
- AR (Arabic), ZH (Chinese), JA (Japanese), KO (Korean)
- And many more...

## Monitoring

Use the metrics endpoint to monitor service performance:

```java
Map<String, Object> metrics = spamDetectionService.getServiceMetrics();
log.info("Spam detection metrics: {}", metrics);
```

## Best Practices

1. **Always check service health** before processing critical content
2. **Use appropriate thresholds** based on your use case
3. **Implement fallback behavior** for service failures
4. **Cache results** for performance optimization
5. **Use bulk processing** for multiple texts
6. **Monitor service metrics** for performance insights
7. **Fail open** rather than blocking content when service is unavailable
