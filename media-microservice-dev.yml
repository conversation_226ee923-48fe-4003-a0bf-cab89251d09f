spring:
  application:
    name: media-microservice
  logging:
    level:
      root: INFO
      com.lookforx.mediaservice: INFO
  data:
    redis:
      host: *************
      port: 6379
      password: ""       # <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> yo<PERSON>a "" ya da hiç yazma
      timeout: 2000ms     # veya "2s" ş<PERSON><PERSON><PERSON> <PERSON><PERSON> zaman birimi ile
  cache:
    type: redis

  kafka:
    bootstrap-servers: *************:9092
    producer:
      # Increase timeouts for cloud Kafka
      request-timeout-ms: 600000      # 10 minutes
      delivery-timeout-ms: 1200000    # 20 minutes
      max-block-ms: 600000            # 10 minutes
      retries: 10                     # Increase retry count
      retry-backoff-ms: 5000          # 5 seconds between retries
      # Batch settings for better performance
      batch-size: 32768               # 32KB batch size
      linger-ms: 100                  # Wait 100ms to batch messages
      buffer-memory: 67108864         # 64MB buffer
      # Compression for better network utilization
      compression-type: gzip
      # Acknowledgment settings
      acks: all                       # Wait for all replicas
      # Serialization
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

cloudflare:
  r2:
    endpoint: https://a223a67125eebd58fb60377d532a1a05.r2.cloudflarestorage.com
    accessKey: 7683a7e72535703436a448f3ec33b2fb
    secretKey: f15a4167f177d882346223f6faa0ec592f299a61b59856f1955e3f2547821d7c
    bucket: lookforx