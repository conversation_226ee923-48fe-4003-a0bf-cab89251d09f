name: Deploy API Gateway to GKE

on:
  workflow_run:
    workflows:
      - "Deploy Config Server to GKE"
    types:
      - completed
  push:
    branches:
      - dev

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          distribution: temurin
          java-version: '21'

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v1
        with:
          credentials_json: ${{ secrets.GCP_CREDENTIALS }}

      - name: Setup GKE credentials
        uses: google-github-actions/get-gke-credentials@v1
        with:
          cluster_name: lookforx-k8s-cluster
          location: europe-central2

      - name: Build and Push Docker image with Jib (Maven)
        run: |
          mvn compile jib:build \
            -Dimage=europe-central2-docker.pkg.dev/lookforx-test/lookforx-repo/api-gateway:latest

      - name: Deploy to Kubernetes
        run: |
          kubectl apply -f k8s/deployment.yaml
          kubectl apply -f k8s/service.yaml
