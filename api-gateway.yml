spring:
  application:
    name: api-gateway
  cloud:
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        - id: user-service
          uri: lb://USER-MICROSERVICE
          predicates:
            - Path=/user-service/**
          filters:
            - StripPrefix=1
        - id: request-service
          uri: lb://REQUEST-MICROSERVICE
          predicates:
            - Path=/request-service/**
          filters:
            - StripPrefix=1
        - id: search-service
          uri: lb://SEARCH-MICROSERVICE
          predicates:
            - Path=/search-service/**
          filters:
            - StripPrefix=1
        - id: notification-service
          uri: lb://NOTIFICATION-MICROSERVICE
          predicates:
            - Path=/notification-service/**
          filters:
            - StripPrefix=1
        - id: category-service
          uri: lb://CATEGORY-MICROSERVICE
          predicates:
            - Path=/category-service/**
          filters:
            - StripPrefix=1
        - id: bid-service
          uri: lb://BID-MICROSERVICE
          predicates:
            - Path=/bid-service/**
          filters:
            - StripPrefix=1
        - id: auth-service
          uri: lb://AUTH-MICROSERVICE
          predicates:
            - Path=/auth-service/**
          filters:
            - StripPrefix=1
        - id: membership-service
          uri: lb://MEMBERSHIP-MICROSERVICE
          predicates:
            - Path=/membership-service/**
          filters:
            - StripPrefix=1
        - id: messaging-service
          uri: lb://MESSAGING-MICROSERVICE
          predicates:
            - Path=/messaging-service/**
          filters:
            - StripPrefix=1
        - id: media-service
          uri: lb://MEDIA-MICROSERVICE
          predicates:
            - Path=/media-service/**
          filters:
            - StripPrefix=1
        - id: question-form-service
          uri: lb://QUESTION-FORM-MICROSERVICE
          predicates:
            - Path=/question-form-service/**
          filters:
            - StripPrefix=1
        - id: exception-service
          uri: lb://EXCEPTION-MICROSERVICE
          predicates:
            - Path=/exception-service/**
          filters:
            - StripPrefix=1
        - id: campaign-service
          uri: lb://CAMPAIGN-MICROSERVICE
          predicates:
            - Path=/campaign-service/**
          filters:
            - StripPrefix=1
        - id: reference-data-service
          uri: lb://REFERENCE-DATA-MICROSERVICE
          predicates:
            - Path=/reference-data-service/**
          filters:
            - StripPrefix=1
        - id: payment-service
          uri: lb://PAYMENT-MICROSERVICE
          predicates:
            - Path=/payment-service/**
        - id: customer-support-service
          uri: lb://CUSTOMER-SUPPORT-MICROSERVICE
          predicates:
            - Path=/customer-support-service/**
          filters:
            - StripPrefix=1
        - id: location-service
          uri: lb://LOCATION-MICROSERVICE
          predicates:
            - Path=/location-service/**
          filters:
            - StripPrefix=1
      default-filters:
        - DedupeResponseHeader=Access-Control-Allow-Origin Access-Control-Allow-Credentials, RETAIN_FIRST
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOrigins:
              - "http://localhost:3000"
              - "http://************:3000"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true
            maxAge: 3600

logging:
  level:
    org.springframework.cloud.gateway: DEBUG
    reactor.netty: DEBUG
    org.springframework.web: DEBUG