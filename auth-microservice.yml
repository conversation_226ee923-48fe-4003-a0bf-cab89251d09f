spring:
  application:
    name: auth-microservice
    version: 1.0.0
  datasource:
    url: jdbc:postgresql://************:5432/authdb
    username: postgres
    password: L00kForX_?
    driver-class-name: org.postgresql.Driver
  security:
    oauth2:
      client:
        registration:
          google:
            client-id: 1058345875329-0190lki4t4m9vghs9hafjpdep7b7a48i.apps.googleusercontent.com
            client-secret: 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
            redirect-uri: http://localhost:8080/auth-service/api/v1/oauth2/callback/google
            scope:
              - email
              - profile

app:
  oauth2:
    redirectUri: http://localhost:8080/auth-service/api/v1/oauth2/callback/google

  jpa:
    hibernate:
      ddl-auto: update
    database-platform: org.hibernate.dialect.PostgreSQLDialect
jwt:
  secret: 404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
  access-token:
    expiration: 86400000 # 1 day in milliseconds
  refresh-token:
    expiration: 604800000 # 7 days in milliseconds

