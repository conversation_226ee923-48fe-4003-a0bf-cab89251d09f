spring:
  application:
    name: request-microservice

  data:
    mongodb:
      uri: mongodb+srv://lookforx:<EMAIL>/requestdb-prod?retryWrites=true&w=majority&appName=lookforx
      auto-index-creation: false
    redis:
      host: *************
      port: 6379
      database: 7
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 16
          max-idle: 8
          min-idle: 2

  cache:
    type: redis
    redis:
      time-to-live: 7200000

  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      retries: 5
      batch-size: 32768
      linger-ms: 5
      buffer-memory: 67108864
      acks: all
      enable-idempotence: true

# Logging Configuration
logging:
  level:
    root: WARN
    com.lookforx.requestservice: INFO
    org.springframework.data.mongodb: WARN
    org.springframework.cache: WARN
    org.springframework.kafka: WARN