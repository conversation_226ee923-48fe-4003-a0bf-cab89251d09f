# Development Environment Configuration
spring:
  # Kafka Configuration for Development
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:*************:9092}
    producer:
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432
      acks: 1  # Faster for development
      enable-idempotence: true
      compression-type: snappy
    consumer:
      group-id: ${spring.application.name}-dev-consumer-group
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 100  # Smaller batches for development

# Management and Monitoring for Development
management:
  # Zipkin Configuration for Development
  zipkin:
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
      connect-timeout: 1s
      read-timeout: 10s

  # Tracing Configuration for Development
  tracing:
    enabled: true
    sampling:
      probability: 1.0  # 100% sampling for development
    baggage:
      correlation:
        enabled: true
        fields:
          - user-id
          - session-id
          - request-id
      remote-fields:
        - user-id
        - session-id
        - request-id
        - correlation-id

  # Metrics Configuration for Development
  metrics:
    tags:
      application: ${spring.application.name}
      environment: dev
      version: ${spring.application.version}
    distribution:
      percentiles-histogram:
        http.server.requests: true
        http.client.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
        http.client.requests: 0.5, 0.95, 0.99
    export:
      prometheus:
        enabled: true

# LookForX Logging Configuration for Development
lookforx:
  logging:
    kafka:
      enabled: true
      consumer:
        group-id: logging-dev-consumer-group
        auto-offset-reset: earliest
        max-poll-records: 100
        concurrency: 2
    storage:
      enabled: true
    consumer:
      enabled: false  # Only enable in dedicated logging service

# Logging Configuration for Development
logging:
  level:
    root: INFO
    com.lookforx: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
    org.springframework.kafka: INFO
    org.apache.kafka: WARN
    zipkin2: WARN
    io.micrometer.tracing: DEBUG
    org.mongodb.driver: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    path: ./logs