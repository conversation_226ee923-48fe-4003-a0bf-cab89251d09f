spring:
  application:
    name: category-microservice
  datasource:
    url: ***********************************************
    username: postgres
    password: L00kForX_?
    driver-class-name: org.postgresql.Driver
    hikari:
      # 1) Make sure <PERSON><PERSON> retires connections *before* the DB server closes them.
      #    If your DB server’s wait_timeout is e.g. 10m, set this a bit lower:
      max-lifetime: 600000        # 10 minutes in ms

      # 2) How long a connection can sit idle in the pool before eligible for eviction
      idle-timeout: 300000        # 5 minutes

      # 3) How long to wait for a connection from the pool before timing out
      connection-timeout: 30000   # 30 seconds

      # 4) (Optional) Force <PERSON><PERSON> to run a lightweight test query before giving you a connection
      #    This guarantees the connection is alive, avoiding that “closed” edge case
      connection-test-query: SELECT 1

      # 5) (Optional) Keep idle connections alive by pinging periodically
      keepalive-time: 300000      # 5 minutes

  # JPA and Hibernate Configuration
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        # Performance optimizations
        jdbc:
          batch_size: 25
          fetch_size: 50
        "[order_inserts]": true
        "[order_updates]": true
        "[batch_versioned_data]": true
        # Query optimizations
        query:
          "[plan_cache_max_size]": 2048
          "[plan_parameter_metadata_max_size]": 128
        # Statistics for monitoring
        "[generate_statistics]": true
        session:
          events:
            log:
              "[LOG_QUERIES_SLOWER_THAN_MS]": 1000

    database-platform: org.hibernate.dialect.PostgreSQLDialect

  kafka:
    bootstrap-servers: *************:9092
    producer:
      # Increase timeouts for cloud Kafka
      request-timeout-ms: 600000      # 10 minutes
      delivery-timeout-ms: 1200000    # 20 minutes
      max-block-ms: 600000            # 10 minutes
      retries: 10                     # Increase retry count
      retry-backoff-ms: 5000          # 5 seconds between retries
      # Batch settings for better performance
      batch-size: 32768               # 32KB batch size
      linger-ms: 100                  # Wait 100ms to batch messages
      buffer-memory: 67108864         # 64MB buffer
      # Compression for better network utilization
      compression-type: gzip
      # Acknowledgment settings
      acks: all                       # Wait for all replicas
      # Serialization
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

  # Jackson configuration for Java 8 time
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  logging:
    level:
      root: INFO
      com.lookforx.categoryservice: INFO
  data:
    redis:
      host: *************
      port: 6379
      password: ""       # Eğer şifre yoksa "" ya da hiç yazma
      timeout: 2000ms     # veya "2s" şeklinde açıkça zaman birimi ile
      database: 0
  cache:
    type: redis

  # Feign Client Configuration for resilience
  cloud:
    openfeign:
      client:
        config:
          search-microservice:
            connect-timeout: 10000    # 10 seconds
            read-timeout: 60000       # 60 seconds
            logger-level: basic
          default:
            connect-timeout: 5000     # 5 seconds
            read-timeout: 30000       # 30 seconds
            logger-level: basic

management:
  tracing:
    sampling:
      probability: 1.0
    zipkin:
      endpoint: http://*************:9411/api/v2/spans
  endpoints:
    web:
      exposure:
        include: health,info,metrics,circuitbreakers,retries,timelimiters
  endpoint:
    health:
      show-details: always
  health:
    circuitbreakers:
      enabled: true
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

