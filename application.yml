spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: lookforx # Lütfen servis adınızı buraya yazın
    version: 1.0.0
  jpa:
    show-sql: true
  devtools:
    restart:
      enabled: true
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  # Redis ve Cache sağlık kontrolleri için ana Redis yapılandırmasının kullanılması önerilir.
  # Örnek:
  # data:
  #   redis:
  #     host: ***********
  #     port: 6379

# <PERSON><PERSON>netim (Management) ve Aktüatör Ayarları
management:
  endpoints:
    web:
      exposure:
        # Geliştirme ortamı için '*' kabul edilebilir, ancak üretimde (production) güvenliği artırmak için
        # sadece ihtiyaç duyulan endpoint'leri (örn: health, info, prometheus) açın.
        include: "*"
  endpoint:
    health:
      show-details: always
  health:
    diskspace:
      enabled: true
    redis:
      enabled: true # Redis sağlık kontrolünü bu ş<PERSON>ilde et<PERSON>leştirin

  # Micrometer Tracing (Spring Boot 3+ için yeni standart)
  tracing:
    enabled: true
    sampling:
      probability: 1.0  # Üretimde performansa göre 0.1 gibi bir değere düşürülebilir.
    baggage:
      correlation:
        enabled: true
        fields:
          - user-id
          - session-id
          - request-id
      remote-fields:
        - user-id
        - session-id
        - request-id
        - correlation-id

  # Zipkin Entegrasyonu (Micrometer ile)
  zipkin:
    tracing:
      endpoint: http://*************:9411/api/v2/spans
      connect-timeout: 1s
      read-timeout: 10s

  # Micrometer Metrics ve Prometheus Ayarları
  metrics:
    tags:
      application: ${spring.application.name}
      environment: ${spring.profiles.active}
      version: ${spring.application.version}
    distribution:
      percentiles-histogram:
        http.server.requests: true
        http.client.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
        http.client.requests: 0.5, 0.95, 0.99
    export:
      prometheus:
        enabled: true

  # Tracing'den belirli path'leri hariç tutmak için yeni standart
  observations:
    http:
      server:
        requests:
          skip-pattern: "/actuator.*|/health.*|/info.*|/metrics.*|/prometheus.*"

# Kafka Configuration
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:*************:9092,localhost:9092}
    producer:
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432
      acks: all
      enable-idempotence: true
      compression-type: snappy
      properties:
        metadata.max.age.ms: 300000
        connections.max.idle.ms: 540000
        request.timeout.ms: 300000
        delivery.timeout.ms: 600000
    consumer:
      group-id: ${spring.application.name}-consumer-group
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 500
    admin:
      auto-create: true
      properties:
        auto.create.topics.enable: true

# LookForX Logging Configuration
lookforx:
  logging:
    kafka:
      enabled: true
      consumer:
        group-id: logging-consumer-group
        auto-offset-reset: earliest
        max-poll-records: 500
        concurrency: 3
    storage:
      enabled: true
    consumer:
      enabled: false  # Only enable in dedicated logging service

# Logging Configuration
logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  level:
    root: INFO
    com.lookforx: INFO
    org.springframework.kafka: WARN
    org.apache.kafka: WARN
    zipkin2: WARN
    io.micrometer.tracing: INFO
  file:
    path: ./logs