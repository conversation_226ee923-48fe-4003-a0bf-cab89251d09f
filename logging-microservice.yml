spring:
  application:
    name: logging-microservice
    version: 1.0.0

# Kafka Configuration
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:34.118.17.104:9092}
    producer:
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432
      acks: all
      enable-idempotence: true
      compression-type: snappy
    consumer:
      group-id: logging-microservice-consumer-group
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 500

# LookForX Logging Configuration - CONSUMER ENABLED
lookforx:
  logging:
    kafka:
      enabled: true
      consumer:
        group-id: logging-microservice-consumer-group
        auto-offset-reset: earliest
        max-poll-records: 1000
        concurrency: 5
    storage:
      enabled: true
    consumer:
      enabled: true  # ENABLE CONSUMER FOR LOGGING SERVICE

# Server Configuration
server:
  port: 8090
  servlet:
    context-path: /logging-microservice

# Eureka Client Configuration
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90