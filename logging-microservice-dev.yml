# Logging Service Development Configuration
spring:
  application:
    name: logging-microservice
    version: 1.0.0

  # MongoDB Configuration for Logging Service
  data:
    mongodb:
      uri: mongodb+srv://lookforx:<EMAIL>/logdb?retryWrites=true&w=majority&appName=lookforx
      auto-index-creation: true

  # Kafka Configuration for Logging Service
  kafka:
    bootstrap-servers: 34.118.17.104:9092
    producer:
      # Increase timeouts for cloud Kafka
      request-timeout-ms: 600000      # 10 minutes
      delivery-timeout-ms: 1200000    # 20 minutes
      max-block-ms: 600000            # 10 minutes
      retries: 10                     # Increase retry count
      retry-backoff-ms: 5000          # 5 seconds between retries
      # Batch settings for better performance
      batch-size: 32768               # 32KB batch size
      linger-ms: 100                  # Wait 100ms to batch messages
      buffer-memory: 67108864         # 64MB buffer
      # Compression for better network utilization
      compression-type: gzip
      # Acknowledgment settings
      acks: 1                         # Wait for leader acknowledgment
      # Serialization
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      # Consumer timeout settings
      session-timeout-ms: 300000      # 5 minutes
      heartbeat-interval-ms: 30000    # 30 seconds
      request-timeout-ms: 600000      # 10 minutes
      # Deserialization
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      # Consumer group settings
      group-id: logging-service-group
      auto-offset-reset: earliest
      enable-auto-commit: true
      auto-commit-interval-ms: 5000
    # Admin client settings for topic management
    admin:
      request-timeout-ms: 600000      # 10 minutes
      retries: 10

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  health:
    mongo:
      enabled: true
    kafka:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true

  # Zipkin Configuration
  zipkin:
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
      connect-timeout: 1s
      read-timeout: 10s
  
  # Tracing Configuration
  tracing:
    enabled: true
    sampling:
      probability: 0.1  # Lower sampling for logging service
    baggage:
      correlation:
        enabled: true
        fields:
          - user-id
          - session-id
          - request-id
      remote-fields:
        - user-id
        - session-id
        - request-id
        - correlation-id

  # Metrics Configuration
  metrics:
    tags:
      application: logging-service
      environment: dev
      version: 1.0.0
    export:
      prometheus:
        enabled: true

# LookForX Logging Configuration - CONSUMER ENABLED
lookforx:
  logging:
    kafka:
      enabled: true
      appenders:
        enabled: false  # Disable Kafka appenders for logging service
      consumer:
        group-id: logging-service-consumer-group
        auto-offset-reset: earliest
        max-poll-records: 1000
        concurrency: 5
    storage:
      enabled: true
    consumer:
      enabled: true  # ENABLE CONSUMER FOR LOGGING SERVICE

# Logging Configuration for Logging Service
logging:
  level:
    root: INFO
    com.lookforx: DEBUG
    com.lookforx.common.logging: DEBUG
    org.springframework.kafka: INFO
    org.apache.kafka: WARN
    zipkin2: WARN
    io.micrometer.tracing: INFO
    org.mongodb.driver: INFO
    org.springframework.cloud.config: DEBUG
    org.springframework.boot.context.config: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    path: ./logs

# Server Configuration
server:
  port: 8090
  servlet:
    context-path: /logging-service

# Eureka Client Configuration
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90
