spring:
  application:
    name: question-form-microservice

  datasource:
    url: **************************************************
    username: postgres
    password: L00kForX_?
    driver-class-name: org.postgresql.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect

  data:
    mongodb:
      uri: mongodb+srv://lookforx:<EMAIL>/questionformdb?retryWrites=true&w=majority&appName=lookforx
      auto-index-creation: true

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
