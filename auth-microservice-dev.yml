spring:
  application:
    name: auth-microservice
    version: 1.0.0
  data:
    mongodb:
      uri: mongodb+srv://lookforx:<EMAIL>/authdb?retryWrites=true&w=majority&appName=lookforx
      auto-index-creation: true
  kafka:
    bootstrap-servers: *************:9092
    producer:
      # Increase timeouts for cloud Kafka
      request-timeout-ms: 600000      # 10 minutes
      delivery-timeout-ms: 1200000    # 20 minutes
      max-block-ms: 600000            # 10 minutes
      retries: 10                     # Increase retry count
      retry-backoff-ms: 5000          # 5 seconds between retries
      # Batch settings for better performance
      batch-size: 32768               # 32KB batch size
      linger-ms: 100                  # Wait 100ms to batch messages
      buffer-memory: 67108864         # 64MB buffer
      # Compression for better network utilization
      compression-type: gzip
      # Acknowledgment settings
      acks: all                       # Wait for all replicas
      # Serialization
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        spring.json.type.mapping: >
          baseEvent:com.lookforx.common.events.BaseEvent,
          userWelcomeEvent:com.lookforx.common.events.UserWelcomeEvent
    # Admin client settings for topic management
    admin:
      request-timeout-ms: 600000      # 10 minutes
      retries: 10
  # ─── LOGGING CONFIG ─────────────────────────────────────────────────────────
  logging:
    level:
      root: INFO
      com.lookforx.auth: INFO

  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ************-3p4ujlapfq1skd7et428rfehqo1imuof.apps.googleusercontent.com
            client-secret: 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
            redirect-uri: http://localhost:8080/auth-service/api/v1/oauth2/callback/google
            scope:
              - email
              - profile
            authorization-grant-type: authorization_code
            client-name: Google
        provider:
          google:
            authorization-uri: https://accounts.google.com/o/oauth2/auth
            token-uri: https://oauth2.googleapis.com/token
            user-info-uri: https://www.googleapis.com/oauth2/v3/userinfo
            user-name-attribute: sub

app:
  oauth2:
    # Primary redirect URI for OAuth2 callback
    redirectUri: http://localhost:8080/auth-service/api/v1/oauth2/callback/google
    # Frontend base URL for OAuth redirects
    frontendUrl: http://localhost:3000
    # Frontend redirect URI after successful authentication
    frontendRedirectUri: http://localhost:3000/auth/success
    # Frontend redirect URI after failed authentication
    frontendErrorUri: http://localhost:3000/auth/error


management:
  tracing:
    sampling:
      probability: 1.0
    zipkin:
      endpoint: http://*************:9411/api/v2/spans
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

jwt:
  secret: 404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
  access-token:
    expiration: ******** # 1 day in milliseconds
  refresh-token:
    expiration: 604800000 # 7 days in milliseconds