# Logging Service Development Configuration
spring:
  application:
    name: logging-microservice
    version: 1.0.0
  
  # Kafka Configuration for Logging Service
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:34.118.17.104:9092}
    producer:
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432
      acks: 1
      enable-idempotence: true
      compression-type: snappy
    consumer:
      group-id: logging-service-dev-consumer-group
      auto-offset-reset: earliest
      enable-auto-commit: false
      max-poll-records: 500
      session-timeout-ms: 30000
      heartbeat-interval-ms: 10000

  # MongoDB Configuration for Logging Service
  data:
    mongodb:
      uri: mongodb+srv://lookforx:<EMAIL>/logdb?retryWrites=true&w=majority&appName=lookforx
      auto-index-creation: true

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  health:
    mongo:
      enabled: true
    kafka:
      enabled: true

  # Zipkin Configuration
  zipkin:
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
      connect-timeout: 1s
      read-timeout: 10s
  
  # Tracing Configuration
  tracing:
    enabled: true
    sampling:
      probability: 0.1  # Lower sampling for logging service
    baggage:
      correlation:
        enabled: true
        fields:
          - user-id
          - session-id
          - request-id
      remote-fields:
        - user-id
        - session-id
        - request-id
        - correlation-id

  # Metrics Configuration
  metrics:
    tags:
      application: logging-service
      environment: dev
      version: 1.0.0
    export:
      prometheus:
        enabled: true

# LookForX Logging Configuration - CONSUMER ENABLED
lookforx:
  logging:
    kafka:
      enabled: true
      consumer:
        group-id: logging-service-consumer-group
        auto-offset-reset: earliest
        max-poll-records: 1000
        concurrency: 5
    storage:
      enabled: true
    consumer:
      enabled: true  # ENABLE CONSUMER FOR LOGGING SERVICE

# Logging Configuration for Logging Service
logging:
  level:
    root: INFO
    com.lookforx: DEBUG
    com.lookforx.common.logging: DEBUG
    org.springframework.kafka: INFO
    org.apache.kafka: WARN
    zipkin2: WARN
    io.micrometer.tracing: INFO
    org.mongodb.driver: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    path: ./logs

# Server Configuration
server:
  port: 8090
  servlet:
    context-path: /logging-service

# Eureka Client Configuration
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90
