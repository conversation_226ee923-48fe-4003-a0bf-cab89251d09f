package com.lookforx.common.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Configuration class to customize CORS (Cross-Origin Resource Sharing) settings
 * for Spring MVC controllers.
 * <p>
 * This configuration enables cross-origin requests from specific frontend origins
 * and applies CORS rules globally to all controller endpoints.
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * Configures global CORS mappings for the application.
     * <p>
     * Allows requests from specified frontend origins with commonly used HTTP methods,
     * enables credentials, and accepts all headers.
     *
     * @param registry the {@link CorsRegistry} used to define allowed origins, methods, and headers
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("http://localhost:3000", "http://************:3000")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .allowedHeaders("*")
                .allowCredentials(true)
                .maxAge(3600);
    }

}

