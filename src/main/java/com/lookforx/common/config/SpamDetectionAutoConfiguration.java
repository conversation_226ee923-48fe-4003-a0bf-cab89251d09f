package com.lookforx.common.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

/**
 * Auto-configuration for Spam Detection Service integration
 */
@Configuration
@ConditionalOnProperty(
    name = "lookforx.spam-detection.enabled", 
    havingValue = "true", 
    matchIfMissing = true
)
@EnableFeignClients(basePackages = "com.lookforx.common.client")
@ComponentScan(basePackages = {
    "com.lookforx.common.client",
    "com.lookforx.common.service"
})
@Lazy
public class SpamDetectionAutoConfiguration {
    // Auto-configuration for spam detection components
}
