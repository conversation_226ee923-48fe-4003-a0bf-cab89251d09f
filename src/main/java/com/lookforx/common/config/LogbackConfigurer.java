package com.lookforx.common.config;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import com.lookforx.common.kafka.KafkaLogAppender;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * Configures Logback appenders with Spring properties after application context is ready
 * Specific implementation for Logging Service
 */
@Slf4j
@Component
public class LogbackConfigurer {
    
    private final Environment environment;
    private volatile boolean configured = false;
    
    public LogbackConfigurer(Environment environment) {
        this.environment = environment;
        System.out.println("=== Logging Service LogbackConfigurer bean created ===");
        System.err.println("=== Logging Service LogbackConfigurer bean created ===");
        log.info("=== Logging Service LogbackConfigurer bean created ===");

        // Immediate configuration attempt
        System.out.println("=== Attempting immediate configuration ===");
        try {
            configureLogbackAppenders("Constructor");
        } catch (Exception e) {
            System.err.println("Constructor configuration failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @PostConstruct
    public void init() {
        System.out.println("=== Logging Service LogbackConfigurer @PostConstruct called ===");
        log.info("=== Logging Service LogbackConfigurer @PostConstruct called ===");
        
        // Schedule configuration after a delay to ensure config server properties are loaded
        new Thread(() -> {
            try {
                Thread.sleep(5000); // 5 second delay
                System.out.println("=== Logging Service LogbackConfigurer delayed configuration starting ===");
                configureLogbackAppenders("PostConstruct-Delayed");
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }
    
    /**
     * Listen for ApplicationReadyEvent - this ensures all beans are initialized
     * and config server properties should be loaded
     */
    @EventListener
    @Order(1000) // Run after other listeners
    public void onApplicationReady(ApplicationReadyEvent event) {
        System.out.println("=== Logging Service LogbackConfigurer: ApplicationReadyEvent triggered ===");
        log.info("=== Logging Service LogbackConfigurer: ApplicationReadyEvent triggered ===");
        configureLogbackAppenders("ApplicationReadyEvent");
    }
    
    /**
     * Listen for ContextRefreshedEvent as a fallback
     */
    @EventListener
    @Order(2000) // Run after ApplicationReadyEvent
    public void onContextRefreshed(ContextRefreshedEvent event) {
        if (!configured) {
            configureLogbackAppenders("ContextRefreshedEvent");
        }
    }
    
    /**
     * Listen for environment changes (config server refresh)
     */
    @EventListener
    public void onEnvironmentChange(EnvironmentChangeEvent event) {
        if (event.getKeys().stream().anyMatch(key -> key.contains("kafka") || key.contains("bootstrap"))) {
            log.info("Environment change detected for Kafka properties, reconfiguring...");
            configured = false; // Allow reconfiguration
            configureLogbackAppenders("EnvironmentChangeEvent");
        }
    }
    
    private void configureLogbackAppenders(String triggerEvent) {
        System.out.println("=== LogbackConfigurer.configureLogbackAppenders called with: " + triggerEvent + " ===");

        if (configured) {
            System.out.println("=== LogbackConfigurer already configured, skipping ===");
            log.debug("Logback already configured, skipping...");
            return;
        }

        // Check if Kafka appenders are enabled for this service
        Boolean kafkaAppendersEnabled = environment.getProperty("lookforx.logging.kafka.appenders.enabled", Boolean.class, true);
        System.out.println("=== Kafka appenders enabled check: " + kafkaAppendersEnabled + " ===");
        if (!kafkaAppendersEnabled) {
            System.out.println("=== Kafka appenders disabled for this service, skipping configuration ===");
            log.info("Kafka appenders disabled for this service via lookforx.logging.kafka.appenders.enabled=false");
            configured = true; // Mark as configured to prevent retries
            return;
        }

        System.out.println("=== Starting Logback Kafka appenders configuration ===");
        log.info("Configuring Logback Kafka appenders triggered by: {}", triggerEvent);
        
        // Check if config server properties are loaded
        if (!isConfigServerPropertiesLoaded()) {
            log.warn("Config server properties may not be fully loaded yet, using available properties");
        }
        
        // Wait for config server properties to be fully loaded
        if ("ApplicationReadyEvent".equals(triggerEvent)) {
            try {
                Thread.sleep(2000); // Longer delay for initial configuration
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return;
            }
        }
        
        try {
            LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();
            
            // Get Spring properties with debug logging
            String serviceName = environment.getProperty("spring.application.name", "unknown-service");
            String serviceVersion = environment.getProperty("spring.application.version", "1.0.0");
            String environmentName = environment.getProperty("spring.profiles.active", "dev");
            
            // Try multiple property sources for Kafka bootstrap servers
            String bootstrapServers = null;
            String[] kafkaPropertyKeys = {
                "spring.kafka.bootstrap-servers",
                "KAFKA_BOOTSTRAP_SERVERS",
                "kafka.bootstrap-servers"
            };
            
            for (String key : kafkaPropertyKeys) {
                bootstrapServers = environment.getProperty(key);
                if (bootstrapServers != null && !bootstrapServers.trim().isEmpty()) {
                    log.info("Found Kafka bootstrap servers from property '{}': {}", key, bootstrapServers);
                    System.out.println("Found Kafka bootstrap servers from property '" + key + "': " + bootstrapServers);
                    break;
                }
            }
            
            // Fallback to default if not found
            if (bootstrapServers == null || bootstrapServers.trim().isEmpty()) {
                bootstrapServers = "localhost:9092";
                log.warn("No Kafka bootstrap servers found in config, using default: {}", bootstrapServers);
                System.out.println("No Kafka bootstrap servers found in config, using default: " + bootstrapServers);
            }
            
            String logPath = environment.getProperty("logging.file.path", "./logs");
            
            // Debug: Check specific property sources
            System.out.println("=== Property sources debug ===");
            System.out.println("spring.kafka.bootstrap-servers: " + environment.getProperty("spring.kafka.bootstrap-servers"));
            System.out.println("KAFKA_BOOTSTRAP_SERVERS: " + environment.getProperty("KAFKA_BOOTSTRAP_SERVERS"));
            System.out.println("kafka.bootstrap-servers: " + environment.getProperty("kafka.bootstrap-servers"));
            
            // Check if config server properties are loaded
            String configServerUri = environment.getProperty("spring.cloud.config.uri");
            String configServerEnabled = environment.getProperty("spring.cloud.config.enabled");
            System.out.println("Config server URI: " + configServerUri + ", enabled: " + configServerEnabled);
            
            log.info("Configuring Kafka appenders with:");
            log.info("  Service Name: {}", serviceName);
            log.info("  Service Version: {}", serviceVersion);
            log.info("  Environment: {}", environmentName);
            log.info("  Kafka Bootstrap Servers: {}", bootstrapServers);
            log.info("  Log Path: {}", logPath);
            
            // Set context properties for logback-spring.xml
            context.putProperty("SERVICE_NAME", serviceName);
            context.putProperty("SERVICE_VERSION", serviceVersion);
            context.putProperty("ENVIRONMENT", environmentName);
            context.putProperty("KAFKA_BOOTSTRAP_SERVERS", bootstrapServers);
            context.putProperty("LOG_PATH", logPath);

            // Reconfigure existing appenders to use new properties
            reconfigureExistingAppenders(context, serviceName, serviceVersion, environmentName);
            
            // Create and configure Kafka appenders programmatically
            createKafkaAppender(context, "KAFKA_LOGS", "application-logs", bootstrapServers, serviceName, serviceVersion, environmentName, false);
            createKafkaAppender(context, "KAFKA_ERRORS", "error-logs", bootstrapServers, serviceName, serviceVersion, environmentName, true);
            
            log.info("Logback Kafka appenders configured successfully!");
            System.out.println("=== Logback Kafka appenders configured successfully! ===");
            configured = true; // Mark as configured
            
        } catch (Exception e) {
            log.error("Failed to configure Logback Kafka appenders", e);
            System.err.println("Failed to configure Logback Kafka appenders: " + e.getMessage());
            e.printStackTrace();
            // Don't mark as configured on failure, allow retry
        }
    }
    
    /**
     * Check if config server properties are loaded by looking for specific indicators
     */
    private boolean isConfigServerPropertiesLoaded() {
        // Check if we have any config server specific properties
        String configUri = environment.getProperty("spring.cloud.config.uri");
        String configEnabled = environment.getProperty("spring.cloud.config.enabled");
        
        System.out.println("=== Config Server Check ===");
        System.out.println("Config URI: " + configUri);
        System.out.println("Config Enabled: " + configEnabled);
        
        // If config server is disabled, consider properties as loaded
        if ("false".equals(configEnabled)) {
            System.out.println("Config server is disabled, considering properties as loaded");
            log.debug("Config server is disabled, considering properties as loaded");
            return true;
        }
        
        // Check for application name to see if we're getting config from server
        String appName = environment.getProperty("spring.application.name");
        System.out.println("Application name: " + appName);
        
        // If config server is enabled, check if we have remote properties
        if (configUri != null || appName != null) {
            // Look for properties that typically come from config server
            String kafkaServers = environment.getProperty("spring.kafka.bootstrap-servers");
            System.out.println("Kafka servers from environment: " + kafkaServers);
            
            // Also check environment variables
            String kafkaEnvVar = System.getenv("KAFKA_BOOTSTRAP_SERVERS");
            System.out.println("KAFKA_BOOTSTRAP_SERVERS env var: " + kafkaEnvVar);
            
            if (kafkaServers != null && !kafkaServers.equals("localhost:9092")) {
                System.out.println("Found non-default Kafka servers, config server properties likely loaded");
                log.debug("Found non-default Kafka servers, config server properties likely loaded");
                return true;
            }
            
            if (kafkaEnvVar != null && !kafkaEnvVar.equals("localhost:9092")) {
                System.out.println("Found non-default Kafka servers from env var");
                return true;
            }
        }
        
        System.out.println("Config server properties may not be fully loaded yet");
        log.debug("Config server properties may not be fully loaded yet");
        return false;
    }
    
    private void createKafkaAppender(LoggerContext context, String appenderName, String topicName, 
                                    String bootstrapServers, String serviceName, String serviceVersion, String environment, boolean errorOnly) {
        try {
            log.info("Creating {} appender with topic: {}", appenderName, topicName);
            System.out.println("Creating " + appenderName + " appender with topic: " + topicName);
            
            // Create Kafka appender
            KafkaLogAppender kafkaAppender = new KafkaLogAppender();
            kafkaAppender.setContext(context);
            kafkaAppender.setName(appenderName);
            
            // Configure properties
            kafkaAppender.setBootstrapServers(bootstrapServers);
            kafkaAppender.setTopic(topicName);
            kafkaAppender.setServiceName(serviceName);
            kafkaAppender.setVersion(serviceVersion);
            kafkaAppender.setEnvironment(environment);
            
            // Create and set encoder
            net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder encoder = 
                new net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder();
            encoder.setContext(context);
            encoder.start();
            kafkaAppender.setEncoder(encoder);
            
            // Add error filter if needed
            if (errorOnly) {
                ch.qos.logback.classic.filter.ThresholdFilter filter = new ch.qos.logback.classic.filter.ThresholdFilter();
                filter.setLevel("ERROR");
                filter.setContext(context);
                filter.start();
                kafkaAppender.addFilter(filter);
            }
            
            // Start the appender
            kafkaAppender.start();
            
            // Create async wrapper
            ch.qos.logback.classic.AsyncAppender asyncAppender = new ch.qos.logback.classic.AsyncAppender();
            asyncAppender.setContext(context);
            asyncAppender.setName("ASYNC_" + appenderName);
            asyncAppender.setQueueSize(errorOnly ? 500 : 1000);
            asyncAppender.setDiscardingThreshold(0);
            asyncAppender.setIncludeCallerData(false);
            asyncAppender.setMaxFlushTime(5000);
            asyncAppender.setNeverBlock(true);
            asyncAppender.addAppender(kafkaAppender);
            asyncAppender.start();
            
            // Add to root logger
            context.getLogger("ROOT").addAppender(asyncAppender);
            
            log.info("Successfully created and configured {} appender", appenderName);
            System.out.println("Successfully created and configured " + appenderName + " appender");
            
        } catch (Exception e) {
            log.error("Failed to create appender: {}", appenderName, e);
            System.err.println("Failed to create appender: " + appenderName + " - " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Reconfigure existing appenders (CONSOLE, FILE) to use updated context properties
     */
    private void reconfigureExistingAppenders(LoggerContext context, String serviceName, String serviceVersion, String environment) {
        try {
            System.out.println("=== Reconfiguring existing appenders ===");
            log.info("Reconfiguring existing appenders with updated properties");

            // Get root logger
            ch.qos.logback.classic.Logger rootLogger = context.getLogger("ROOT");

            // Iterate through all appenders
            for (java.util.Iterator<Appender<ILoggingEvent>> it = rootLogger.iteratorForAppenders(); it.hasNext();) {
                Appender<ILoggingEvent> appender = it.next();
                String appenderName = appender.getName();

                System.out.println("Found appender: " + appenderName + " of type: " + appender.getClass().getSimpleName());

                if ("CONSOLE".equals(appenderName) || "FILE".equals(appenderName)) {
                    System.out.println("Reconfiguring appender: " + appenderName);
                    reconfigureAppenderEncoder(appender, serviceName, serviceVersion, environment);
                }
            }

            System.out.println("=== Existing appenders reconfigured ===");
            log.info("Successfully reconfigured existing appenders");

        } catch (Exception e) {
            System.err.println("Failed to reconfigure existing appenders: " + e.getMessage());
            log.error("Failed to reconfigure existing appenders", e);
            e.printStackTrace();
        }
    }

    /**
     * Reconfigure an appender's encoder to use updated service properties
     */
    private void reconfigureAppenderEncoder(Appender<ILoggingEvent> appender, String serviceName, String serviceVersion, String environment) {
        try {
            // Stop the appender
            appender.stop();

            // If it's a ConsoleAppender or RollingFileAppender with LoggingEventCompositeJsonEncoder
            if (appender instanceof ch.qos.logback.core.ConsoleAppender ||
                appender instanceof ch.qos.logback.core.rolling.RollingFileAppender) {

                // Get the encoder
                Object encoder = null;
                if (appender instanceof ch.qos.logback.core.ConsoleAppender) {
                    encoder = ((ch.qos.logback.core.ConsoleAppender<ILoggingEvent>) appender).getEncoder();
                } else if (appender instanceof ch.qos.logback.core.rolling.RollingFileAppender) {
                    encoder = ((ch.qos.logback.core.rolling.RollingFileAppender<ILoggingEvent>) appender).getEncoder();
                }

                if (encoder instanceof net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder) {
                    System.out.println("Updating LoggingEventCompositeJsonEncoder for " + appender.getName());

                    // Create new encoder with updated properties
                    net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder newEncoder =
                        new net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder();
                    newEncoder.setContext(appender.getContext());

                    // Set providers with updated service info
                    net.logstash.logback.composite.loggingevent.LoggingEventJsonProviders providers =
                        new net.logstash.logback.composite.loggingevent.LoggingEventJsonProviders();

                    // Add timestamp
                    net.logstash.logback.composite.loggingevent.LoggingEventFormattedTimestampJsonProvider timestampProvider =
                        new net.logstash.logback.composite.loggingevent.LoggingEventFormattedTimestampJsonProvider();
                    providers.addTimestamp(timestampProvider);

                    // Add version
                    net.logstash.logback.composite.LogstashVersionJsonProvider versionProvider =
                        new net.logstash.logback.composite.LogstashVersionJsonProvider();
                    providers.addVersion(versionProvider);

                    // Add log level
                    net.logstash.logback.composite.loggingevent.LogLevelJsonProvider logLevelProvider =
                        new net.logstash.logback.composite.loggingevent.LogLevelJsonProvider();
                    providers.addLogLevel(logLevelProvider);

                    // Add message
                    net.logstash.logback.composite.loggingevent.MessageJsonProvider messageProvider =
                        new net.logstash.logback.composite.loggingevent.MessageJsonProvider();
                    providers.addMessage(messageProvider);

                    // Add MDC
                    net.logstash.logback.composite.loggingevent.MdcJsonProvider mdcProvider =
                        new net.logstash.logback.composite.loggingevent.MdcJsonProvider();
                    providers.addMdc(mdcProvider);

                    // Add arguments
                    net.logstash.logback.composite.loggingevent.ArgumentsJsonProvider argumentsProvider =
                        new net.logstash.logback.composite.loggingevent.ArgumentsJsonProvider();
                    providers.addArguments(argumentsProvider);

                    // Add stack trace
                    net.logstash.logback.composite.loggingevent.StackTraceJsonProvider stackTraceProvider =
                        new net.logstash.logback.composite.loggingevent.StackTraceJsonProvider();
                    providers.addStackTrace(stackTraceProvider);

                    // Add pattern with updated service info
                    net.logstash.logback.composite.loggingevent.LoggingEventPatternJsonProvider patternProvider =
                        new net.logstash.logback.composite.loggingevent.LoggingEventPatternJsonProvider();
                    String pattern = String.format("{\n" +
                        "\"service\": \"%s\",\n" +
                        "\"version\": \"%s\",\n" +
                        "\"environment\": \"%s\",\n" +
                        "\"host\": \"${HOSTNAME:-unknown}\",\n" +
                        "\"thread\": \"%%thread\",\n" +
                        "\"logger\": \"%%logger{36}\"\n" +
                        "}", serviceName, serviceVersion, environment);
                    patternProvider.setPattern(pattern);
                    providers.addPattern(patternProvider);

                    newEncoder.setProviders(providers);
                    newEncoder.start();

                    // Set the new encoder
                    if (appender instanceof ch.qos.logback.core.ConsoleAppender) {
                        ((ch.qos.logback.core.ConsoleAppender<ILoggingEvent>) appender).setEncoder(newEncoder);
                    } else if (appender instanceof ch.qos.logback.core.rolling.RollingFileAppender) {
                        ((ch.qos.logback.core.rolling.RollingFileAppender<ILoggingEvent>) appender).setEncoder(newEncoder);
                    }

                    System.out.println("Successfully updated encoder for " + appender.getName());
                }
            }

            // Restart the appender
            appender.start();

        } catch (Exception e) {
            System.err.println("Failed to reconfigure encoder for appender " + appender.getName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
}
