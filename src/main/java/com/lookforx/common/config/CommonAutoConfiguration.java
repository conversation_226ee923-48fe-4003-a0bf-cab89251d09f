package com.lookforx.common.config;

import com.lookforx.common.handlers.CommonGlobalExceptionHandler;
import com.lookforx.common.kafka.CommonKafkaConfig;
import com.lookforx.common.tracing.config.TracingConfig;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

import javax.sql.DataSource;

/**
 * Auto-configuration for LookForX common components.
 */
@AutoConfiguration
@ComponentScan(basePackages = {
    "com.lookforx.common",
    "com.lookforx.common.logging",
    "com.lookforx.common.logging.config",
    "com.lookforx.common.tracing"
})
@Import({
    SpamDetectionAutoConfiguration.class,
    TracingAutoConfiguration.class,
    CommonKafkaConfig.class
})
@Lazy
public class CommonAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public CommonGlobalExceptionHandler globalExceptionHandler() {
        return new CommonGlobalExceptionHandler();
    }

    /**
     * Configure Feign clients to be lazy by default
     */
    @Bean
    @ConditionalOnMissingBean
    public feign.Feign.Builder feignBuilder() {
        return feign.Feign.builder();
    }

    /**
     * JPA configuration that is only enabled when DataSource is available and JPA is enabled.
     */
    @Configuration
    @ConditionalOnClass(DataSource.class)
    @ConditionalOnProperty(name = "lookforx.jpa.auditing.enabled", havingValue = "true", matchIfMissing = false)
    @EnableJpaAuditing
    static class JpaConfiguration {
    }
}
