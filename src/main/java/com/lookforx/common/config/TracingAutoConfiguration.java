package com.lookforx.common.config;

import com.lookforx.common.tracing.config.TracingConfig;
import com.lookforx.common.tracing.interceptor.TracingInterceptor;
import io.micrometer.tracing.Tracer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Auto-configuration for LookForX tracing components
 */
@Configuration
@ConditionalOnProperty(name = "management.tracing.enabled", havingValue = "true", matchIfMissing = true)
@ComponentScan(basePackages = {
    "com.lookforx.common.tracing.aspect",
    "com.lookforx.common.tracing.interceptor",
    "com.lookforx.common.tracing.publisher"
})
@Import(TracingConfig.class)
public class TracingAutoConfiguration {

    /**
     * Configuration for distributed tracing
     */
    @Configuration
    @ConditionalOnClass(Tracer.class)
    static class DistributedTracingConfiguration {
        // Tracing beans are auto-configured through component scan and import
    }

    /**
     * Web MVC configuration for tracing interceptor
     */
    @Configuration
    @ConditionalOnClass(name = "org.springframework.web.servlet.DispatcherServlet")
    static class TracingWebMvcConfiguration implements WebMvcConfigurer {

        private final TracingInterceptor tracingInterceptor;

        public TracingWebMvcConfiguration(TracingInterceptor tracingInterceptor) {
            this.tracingInterceptor = tracingInterceptor;
        }

        @Override
        public void addInterceptors(InterceptorRegistry registry) {
            registry.addInterceptor(tracingInterceptor)
                    .addPathPatterns("/**")
                    .excludePathPatterns(
                        "/actuator/**",
                        "/health/**",
                        "/info/**",
                        "/metrics/**",
                        "/prometheus/**",
                        "/swagger-ui/**",
                        "/v3/api-docs/**"
                    );
        }
    }
}
