package com.lookforx.common.annotations;

import com.lookforx.common.annotations.validators.LanguageCodeValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * Validation annotation for LanguageCode enum values.
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = LanguageCodeValidator.class)
@Documented
public @interface ValidLanguageCode {
    
    String message() default "Invalid language code";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}
