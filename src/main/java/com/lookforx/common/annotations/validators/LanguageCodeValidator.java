package com.lookforx.common.annotations.validators;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.common.annotations.ValidLanguageCode;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

/**
 * Validator for LanguageCode enum values.
 */
public class LanguageCodeValidator implements ConstraintValidator<ValidLanguageCode, String> {

    @Override
    public void initialize(ValidLanguageCode constraintAnnotation) {
        // No initialization needed
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // Let @NotNull handle null validation
        }

        try {
            LanguageCode.fromCode(value);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
