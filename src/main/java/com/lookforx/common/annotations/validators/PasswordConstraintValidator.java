package com.lookforx.common.annotations.validators;

import com.lookforx.common.annotations.PasswordValidation;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.regex.Pattern;

public class PasswordConstraintValidator
        implements ConstraintValidator<PasswordValidation, String> {

    private static final Pattern PASSWORD_PATTERN = Pattern.compile("""
        (?x)                    # enable whitespace/comments
        ^                       # start of input
        (?=.{8,}$)              # at least 8 chars
        (?=.*[A-Z])             # at least one uppercase
        (?=.*\\d)               # at least one digit
        (?=.*\\p{Punct})        # at least one punctuation char
        .*                      # the rest
        $                       # end of input
        """);

    @Override
    public void initialize(PasswordValidation constraintAnnotation) {
        // no-op
    }

    @Override
    public boolean isValid(String password, ConstraintValidatorContext context) {
        return password != null && PASSWORD_PATTERN.matcher(password).matches();
    }

}