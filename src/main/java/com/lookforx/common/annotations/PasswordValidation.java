package com.lookforx.common.annotations;

import com.lookforx.common.annotations.validators.PasswordConstraintValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import java.lang.annotation.*;

import static java.lang.annotation.ElementType.*;

@Target({ FIELD, PARAMETER })
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = PasswordConstraintValidator.class)
public @interface PasswordValidation {
    String message() default
            "Password must be at least 8 characters long, contain an uppercase letter, a number and a special character";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
