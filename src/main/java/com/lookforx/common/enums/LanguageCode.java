package com.lookforx.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Supported language codes for the LookForX platform.
 * Based on ISO 639-1 standard.
 */
public enum LanguageCode {
    EN("EN", "English", "English", "İngilizce"),
    TR("TR", "Türkçe", "Turkish", "Türkçe"),
    DE("DE", "Deutsch", "German", "Almanca"),
    FR("FR", "Français", "French", "Fransızca"),
    ES("ES", "Español", "Spanish", "İspanyolca"),
    IT("IT", "Italiano", "Italian", "İtalyanca"),
    PT("PT", "Português", "Portuguese", "Portekizce"),
    RU("RU", "Русский", "Russian", "Rusça"),
    AR("AR", "العربية", "Arabic", "Arap<PERSON>"),
    ZH("ZH", "中文", "Chinese", "Çince"),
    JA("JA", "日本語", "Japanese", "Japonca"),
    KO("KO", "한국어", "Korean", "Korece"),
    HI("HI", "हिन्दी", "Hindi", "Hintçe"),
    BN("BN", "বাংলা", "Bengali", "Bengalce"),
    UR("UR", "اردو", "Urdu", "Urduca"),
    FA("FA", "فارسی", "Persian", "Farsça"),
    TH("TH", "ไทย", "Thai", "Tayca"),
    VI("VI", "Tiếng Việt", "Vietnamese", "Vietnamca"),
    ID("ID", "Bahasa Indonesia", "Indonesian", "Endonezce"),
    MS("MS", "Bahasa Melayu", "Malay", "Malayca"),
    NL("NL", "Nederlands", "Dutch", "Hollandaca"),
    SV("SV", "Svenska", "Swedish", "İsveççe"),
    NO("NO", "Norsk", "Norwegian", "Norveççe"),
    DA("DA", "Dansk", "Danish", "Danca"),
    FI("FI", "Suomi", "Finnish", "Fince"),
    PL("PL", "Polski", "Polish", "Lehçe"),
    CS("CS", "Čeština", "Czech", "Çekçe"),
    SK("SK", "Slovenčina", "Slovak", "Slovakça"),
    HU("HU", "Magyar", "Hungarian", "Macarca"),
    RO("RO", "Română", "Romanian", "Rumence"),
    BG("BG", "Български", "Bulgarian", "Bulgarca"),
    HR("HR", "Hrvatski", "Croatian", "Hırvatça"),
    SR("SR", "Српски", "Serbian", "Sırpça"),
    SL("SL", "Slovenščina", "Slovenian", "Slovence"),
    ET("ET", "Eesti", "Estonian", "Estonca"),
    LV("LV", "Latviešu", "Latvian", "Letonca"),
    LT("LT", "Lietuvių", "Lithuanian", "Litvanca"),
    EL("EL", "Ελληνικά", "Greek", "Yunanca"),
    HE("HE", "עברית", "Hebrew", "İbranice"),
    UK("UK", "Українська", "Ukrainian", "Ukraynaca"),
    BE("BE", "Беларуская", "Belarusian", "Belarusça"),
    KY("KY", "Кыргызча", "Kyrgyz", "Kırgızca"),
    UZ("UZ", "O'zbek", "Uzbek", "Özbekçe"),
    KM("KM", "ខ្មែរ", "Khmer", "Kmerce"),
    MY("MY", "မြန်မာ", "Myanmar", "Myanmarca"),
    TG("TG", "Тоҷикӣ", "Tajik", "Tacikçe"),
    AZ("AZ", "Azərbaycan", "Azerbaijani", "Azerice"),
    HY("HY", "Հայերեն", "Armenian", "Ermenice"),
    GA("GA", "Gaeilge", "Irish", "İrlandaca"),
    CY("CY", "Cymraeg", "Welsh", "Galce"),
    IS("IS", "Íslenska", "Icelandic", "İzlandaca"),
    MK("MK", "Македонски", "Macedonian", "Makedonca"),
    BS("BS", "Bosanski", "Bosnian", "Boşnakça"),
    SQ("SQ", "Shqip", "Albanian", "Arnavutça"),
    MN("MN", "Монгол", "Mongolian", "Moğolca"),
    NE("NE", "नेपाली", "Nepali", "Nepalce"),
    PA("PA", "ਪੰਜਾਬੀ", "Punjabi", "Pencapça"),
    GL("GL", "Galego", "Galician", "Galiçyaca"),
    LA("LA", "Latina", "Latin", "Latince");

    private final String code;
    private final String nativeName;
    private final String englishName;
    private final String turkishName;

    LanguageCode(String code, String nativeName, String englishName, String turkishName) {
        this.code = code;
        this.nativeName = nativeName;
        this.englishName = englishName;
        this.turkishName = turkishName;
    }

    @JsonValue
    public String getCode() {
        return code;
    }

    public String getNativeName() {
        return nativeName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public String getTurkishName() {
        return turkishName;
    }

    @JsonCreator
    public static LanguageCode fromCode(String code) {
        for (LanguageCode lang : values()) {
            if (lang.code.equalsIgnoreCase(code)) {
                return lang;
            }
        }
        throw new IllegalArgumentException("Unknown language code: " + code);
    }

    @Override
    public String toString() {
        return code;
    }
}
