package com.lookforx.common.util;

import java.text.Normalizer;
import java.util.Arrays;
import java.util.Collection;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * String utility methods for LookForX microservices.
 */
public final class StringUtils {

    private static final Pattern DIACRITICS_PATTERN = Pattern.compile("\\p{InCombiningDiacriticalMarks}+");

    private StringUtils() {
        // Utility class
    }

    /**
     * Checks if a string is null or empty.
     */
    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }

    /**
     * Checks if a string is not null and not empty.
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    /**
     * Checks if a string is null, empty, or contains only whitespace.
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * Checks if a string is not null, not empty, and contains non-whitespace characters.
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    /**
     * Returns the string if not null, otherwise returns the default value.
     */
    public static String defaultIfNull(String str, String defaultValue) {
        return str != null ? str : defaultValue;
    }

    /**
     * Returns the string if not blank, otherwise returns the default value.
     */
    public static String defaultIfBlank(String str, String defaultValue) {
        return isNotBlank(str) ? str : defaultValue;
    }

    /**
     * Capitalizes the first letter of a string.
     */
    public static String capitalize(String str) {
        if (isEmpty(str)) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1).toLowerCase();
    }

    /**
     * Converts a string to camelCase.
     */
    public static String toCamelCase(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        return Arrays.stream(str.split("[\\s_-]+"))
                .filter(StringUtils::isNotEmpty)
                .map(word -> word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase())
                .collect(Collectors.joining());
    }

    /**
     * Converts a string to snake_case.
     */
    public static String toSnakeCase(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        return str.replaceAll("([a-z])([A-Z])", "$1_$2")
                .replaceAll("[\\s-]+", "_")
                .toLowerCase();
    }

    /**
     * Removes diacritics (accents) from a string.
     */
    public static String removeDiacritics(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        String normalized = Normalizer.normalize(str, Normalizer.Form.NFD);
        return DIACRITICS_PATTERN.matcher(normalized).replaceAll("");
    }

    /**
     * Truncates a string to the specified length.
     */
    public static String truncate(String str, int maxLength) {
        if (isEmpty(str) || str.length() <= maxLength) {
            return str;
        }
        return str.substring(0, maxLength);
    }

    /**
     * Truncates a string to the specified length and adds ellipsis.
     */
    public static String truncateWithEllipsis(String str, int maxLength) {
        if (isEmpty(str) || str.length() <= maxLength) {
            return str;
        }
        if (maxLength <= 3) {
            return truncate(str, maxLength);
        }
        return str.substring(0, maxLength - 3) + "...";
    }

    /**
     * Joins a collection of strings with the specified delimiter.
     */
    public static String join(Collection<String> strings, String delimiter) {
        if (strings == null || strings.isEmpty()) {
            return "";
        }
        return String.join(delimiter, strings);
    }

    /**
     * Masks a string by replacing characters with asterisks, keeping only the first and last characters.
     */
    public static String mask(String str) {
        if (isEmpty(str) || str.length() <= 2) {
            return "*".repeat(str != null ? str.length() : 0);
        }
        
        String masked = "*".repeat(str.length() - 2);
        return str.charAt(0) + masked + str.charAt(str.length() - 1);
    }

    /**
     * Generates a slug from a string (URL-friendly version).
     */
    public static String toSlug(String str) {
        if (isEmpty(str)) {
            return str;
        }
        
        return removeDiacritics(str)
                .toLowerCase()
                .replaceAll("[^a-z0-9\\s-]", "")
                .replaceAll("[\\s-]+", "-")
                .replaceAll("^-+|-+$", "");
    }
}
