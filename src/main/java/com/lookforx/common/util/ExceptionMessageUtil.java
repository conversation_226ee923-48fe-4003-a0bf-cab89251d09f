package com.lookforx.common.util;

import com.lookforx.common.dto.ExceptionMessageResponse;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.common.service.ExceptionMessageService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;

import java.util.Arrays;
import java.util.IllegalFormatException;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * Utility class for handling exception messages in a convenient way.
 * This class provides static-like access to ExceptionMessageService functionality.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ExceptionMessageUtil {

    private final ExceptionMessageService exceptionMessageService;
    private static ExceptionMessageUtil instance;

    /**
     * Initialize the static instance for utility access.
     */
    @PostConstruct
    public void init() {
        instance = this;
    }

    /**
     * Get localized exception message.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The language code
     * @return Localized exception message
     */
    public static String getMessage(String exceptionCode, String languageCode) {
        if (instance != null) {
            return instance.exceptionMessageService.getExceptionMessageText(exceptionCode, languageCode);
        }
        return getFallbackMessage(exceptionCode, languageCode);
    }

    /**
     * Get localized exception message using LanguageCode enum.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The LanguageCode enum
     * @return Localized exception message
     */
    public static String getMessage(String exceptionCode, LanguageCode languageCode) {
        return getMessage(exceptionCode, languageCode.getCode());
    }

    /**
     * Get exception message in English (default).
     * 
     * @param exceptionCode The exception code
     * @return English exception message
     */
    public static String getMessage(String exceptionCode) {
        return getMessage(exceptionCode, LanguageCode.EN);
    }

    /**
     * Get full exception message response.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The language code
     * @return ExceptionMessageResponse object
     */
    public static ExceptionMessageResponse getMessageResponse(String exceptionCode, String languageCode) {
        if (instance != null) {
            return instance.exceptionMessageService.getExceptionMessage(exceptionCode, languageCode);
        }
        return createFallbackResponse(exceptionCode, languageCode);
    }

    /**
     * Get full exception message response using LanguageCode enum.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The LanguageCode enum
     * @return ExceptionMessageResponse object
     */
    public static ExceptionMessageResponse getMessageResponse(String exceptionCode, LanguageCode languageCode) {
        return getMessageResponse(exceptionCode, languageCode.getCode());
    }

    /**
     * Check if exception message exists.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The language code
     * @return true if message exists
     */
    public static boolean hasMessage(String exceptionCode, String languageCode) {
        if (instance != null) {
            return instance.exceptionMessageService.hasExceptionMessage(exceptionCode, languageCode);
        }
        return false;
    }

    /**
     * Check if exception message exists using LanguageCode enum.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The LanguageCode enum
     * @return true if message exists
     */
    public static boolean hasMessage(String exceptionCode, LanguageCode languageCode) {
        return hasMessage(exceptionCode, languageCode.getCode());
    }

    /**
     * Create fallback message when service is not available.
     *
     * @param exceptionCode The exception code
     * @param languageCode The language code
     * @return Fallback message
     */
    private static String getFallbackMessage(String exceptionCode, String languageCode) {
        // Provide basic fallback messages in Turkish and English
        if ("tr".equalsIgnoreCase(languageCode)) {
            return switch (exceptionCode.toLowerCase()) {
                case "resource_not_found" -> "Kaynak bulunamadı";
                case "validation_error" -> "Doğrulama hatası";
                case "access_denied" -> "Erişim reddedildi";
                case "internal_server_error" -> "Sunucu hatası";
                case "bad_request" -> "Geçersiz istek";
                case "unauthorized" -> "Yetkisiz erişim";
                case "forbidden" -> "Yasak erişim";
                case "conflict" -> "Çakışma hatası";
                default -> "Bir hata oluştu";
            };
        } else {
            return switch (exceptionCode.toLowerCase()) {
                case "resource_not_found" -> "Resource not found";
                case "validation_error" -> "Validation error";
                case "access_denied" -> "Access denied";
                case "internal_server_error" -> "Internal server error";
                case "bad_request" -> "Bad request";
                case "unauthorized" -> "Unauthorized";
                case "forbidden" -> "Forbidden";
                case "conflict" -> "Conflict error";
                default -> "An error occurred";
            };
        }
    }

    /**
     * Create fallback response when service is not available.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The language code
     * @return Fallback ExceptionMessageResponse
     */
    private static ExceptionMessageResponse createFallbackResponse(String exceptionCode, String languageCode) {
        return ExceptionMessageResponse.builder()
                .exceptionCode(exceptionCode)
                .languageCode(languageCode)
                .message(getFallbackMessage(exceptionCode, languageCode))
                .build();
    }

    public static String resolveLanguage(HttpServletRequest request) {
        return Optional.ofNullable(request.getHeader("Accept-Language"))
                .map(h -> h.split(",")[0])
                .map(String::toUpperCase)
                .orElse("EN");
    }

    public static String fetchAndFormat(String exceptionCode, String lang, Object... args) {
        String fallback = String.format("No template for '%s'", exceptionCode);

        String template = Optional.of(exceptionCode)
                .flatMap(code -> {
                    try {
                        ExceptionMessageResponse resp =
                                getMessageResponse(code, lang);
                        return Optional.ofNullable(resp).map(ExceptionMessageResponse::getMessage);
                    } catch (Exception e) {
                        log.warn("Failed to fetch template {} / {}: {}", code, lang, e.getMessage());
                        return Optional.empty();
                    }
                })
                .orElse(fallback);

        return Stream.of(template)
                .map(t -> {
                    if (t.contains("%")) {
                        try {
                            return String.format(t, args);
                        } catch (IllegalFormatException ife) {
                            log.error("Bad format '{}', args={}", t, Arrays.toString(args), ife);
                            return t;
                        }
                    }
                    return args.length > 0 ? t + " " + args[0] : t;
                })
                .findFirst().get();
    }

}
