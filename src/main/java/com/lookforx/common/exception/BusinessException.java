package com.lookforx.common.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception thrown for business logic violations.
 */
public class BusinessException extends BaseException {
    
    public BusinessException(String errorCode, String message) {
        super(errorCode, message, HttpStatus.BAD_REQUEST);
    }

    public BusinessException(String errorCode, String message, Object... args) {
        super(errorCode, message, HttpStatus.BAD_REQUEST, args);
    }

    public BusinessException(String errorCode, String message, Throwable cause) {
        super(errorCode, message, cause, HttpStatus.BAD_REQUEST);
    }
}
