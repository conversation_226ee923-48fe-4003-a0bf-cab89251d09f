package com.lookforx.common.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception thrown when a requested resource is not found.
 */
public class ResourceNotFoundException extends BaseException {

    private static final String ERROR_CODE = "RESOURCE_NOT_FOUND";

    /**
     * @param resourceType     e.g. “User”, “Category”
     * @param identifierName   e.g. “id”, “email”
     * @param identifierValue  the value that wasn’t found
     */
    public ResourceNotFoundException(String resourceType,
                                     String identifierName,
                                     Object identifierValue) {
        super(
                ERROR_CODE,
                String.format("%s not found with %s: %s",
                        resourceType, identifierName, identifierValue),
                HttpStatus.NOT_FOUND,
                resourceType, identifierName, identifierValue
        );
    }

    /**
     * Shortcut for the common case of “not found by id”.
     */
    public ResourceNotFoundException(String resourceType, Object id) {
        this(resourceType, "id", id);
    }

    /**
     * If you want to supply a completely custom message.
     */
    public ResourceNotFoundException(String message) {
        super(ERROR_CODE, message, HttpStatus.NOT_FOUND);
    }

}