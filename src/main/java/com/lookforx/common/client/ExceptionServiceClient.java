package com.lookforx.common.client;

import com.lookforx.common.config.FeignConfig;
import com.lookforx.common.dto.ExceptionMessageResponse;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(
        name = "exception-microservice",
        configuration = FeignConfig.class
)
public interface ExceptionServiceClient {

    @GetMapping("/exception-message")
    @CircuitBreaker(name = "exceptionService", fallbackMethod = "fallbackGetExceptionMessage")
    ExceptionMessageResponse getExceptionMessage(
            @RequestParam("exceptionCode")   String exceptionCode,
            @RequestParam("languageCode")    String languageCode
    );

    default ExceptionMessageResponse fallbackGetExceptionMessage(String exceptionCode, String languageCode, Throwable throwable) {
        String fallbackMessage = determineFallbackMessage(exceptionCode, languageCode, throwable);

        return ExceptionMessageResponse.builder()
                .exceptionCode(exceptionCode)
                .languageCode(languageCode)
                .message(fallbackMessage)
                .build();
    }

    /**
     * Determine appropriate fallback message based on language and exception code.
     */
    default String determineFallbackMessage(String exceptionCode, String languageCode, Throwable throwable) {
        // Provide basic fallback messages in Turkish and English
        if ("tr".equalsIgnoreCase(languageCode)) {
            return switch (exceptionCode.toLowerCase()) {
                case "resource_not_found" -> "Kaynak bulunamadı";
                case "validation_error" -> "Doğrulama hatası";
                case "access_denied" -> "Erişim reddedildi";
                case "internal_server_error" -> "Sunucu hatası";
                case "bad_request" -> "Geçersiz istek";
                case "unauthorized" -> "Yetkisiz erişim";
                case "forbidden" -> "Yasak erişim";
                case "conflict" -> "Çakışma hatası";
                default -> throwable.getMessage() != null ? throwable.getMessage() : "Bir hata oluştu";
            };
        } else {
            return switch (exceptionCode.toLowerCase()) {
                case "resource_not_found" -> "Resource not found";
                case "validation_error" -> "Validation error";
                case "access_denied" -> "Access denied";
                case "internal_server_error" -> "Internal server error";
                case "bad_request" -> "Bad request";
                case "unauthorized" -> "Unauthorized";
                case "forbidden" -> "Forbidden";
                case "conflict" -> "Conflict error";
                default -> throwable.getMessage() != null ? throwable.getMessage() : "An error occurred";
                
            };
        }
    }

}