package com.lookforx.common.client;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import feign.codec.ErrorDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.util.concurrent.TimeUnit;

/**
 * Configuration for Spam Detection Feign Client
 */
@Configuration
@Lazy
public class SpamDetectionClientConfig {

    /**
     * Configure request options with appropriate timeouts for ML processing
     */
    @Bean
    public Request.Options requestOptions() {
        return new Request.Options(
            30, TimeUnit.SECONDS,  // Connect timeout - 30 seconds
            60, TimeUnit.SECONDS,  // Read timeout - 60 seconds for ML processing
            true                   // Follow redirects
        );
    }

    /**
     * Configure retry policy
     */
    @Bean
    public Retryer retryer() {
        return new Retryer.Default(
            1000,  // Initial interval - 1 second
            3000,  // Max interval - 3 seconds
            3      // Max attempts
        );
    }

    /**
     * Configure logging level
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.BASIC;
    }

    /**
     * Custom error decoder for better error handling
     */
    @Bean
    public ErrorDecoder errorDecoder() {
        return new SpamDetectionErrorDecoder();
    }
}
