package com.lookforx.common.client;

import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

/**
 * Custom error decoder for Spam Detection Service
 */
@Slf4j
public class SpamDetectionErrorDecoder implements ErrorDecoder {

    private final ErrorDecoder defaultErrorDecoder = new Default();

    @Override
    public Exception decode(String methodKey, Response response) {
        HttpStatus status = HttpStatus.valueOf(response.status());

        switch (status) {
            case SERVICE_UNAVAILABLE -> {
                log.warn("Spam Detection Service is unavailable (503) - Model may be loading");
                return new ResponseStatusException(
                        HttpStatus.SERVICE_UNAVAILABLE,
                        "Spam Detection Service is temporarily unavailable. Model may be loading."
                );
            }
            case INTERNAL_SERVER_ERROR -> {
                log.error("Spam Detection Service internal error (500) for method: {}", methodKey);
                return new ResponseStatusException(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "Spam Detection Service encountered an internal error"
                );
            }
            case REQUEST_TIMEOUT -> {
                log.warn("Spam Detection Service timeout (408) for method: {}", methodKey);
                return new ResponseStatusException(
                        HttpStatus.REQUEST_TIMEOUT,
                        "Spam Detection Service request timed out"
                );
            }
            case BAD_REQUEST -> {
                log.warn("Bad request to Spam Detection Service (400) for method: {}", methodKey);
                return new ResponseStatusException(
                        HttpStatus.BAD_REQUEST,
                        "Invalid request to Spam Detection Service"
                );
            }
            default -> {
                log.error("Unexpected error from Spam Detection Service: {} for method: {}",
                        status, methodKey);
                return defaultErrorDecoder.decode(methodKey, response);
            }
        }
    }
}
