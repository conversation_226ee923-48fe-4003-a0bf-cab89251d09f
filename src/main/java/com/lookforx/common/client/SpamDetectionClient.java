package com.lookforx.common.client;

import com.lookforx.common.dto.BulkSpamDetectionRequest;
import com.lookforx.common.dto.BulkSpamDetectionResponse;
import com.lookforx.common.dto.SpamDetectionRequest;
import com.lookforx.common.dto.SpamDetectionResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.validation.Valid;
import java.util.Map;

/**
 * Feign client for Spam Detection Service
 */
@Lazy
@FeignClient(
    name = "spam-detection-service",
    configuration = SpamDetectionClientConfig.class
)
public interface SpamDetectionClient {

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    Map<String, Object> health();

    /**
     * Classify a single text for spam detection
     */
    @PostMapping("/classify")
    SpamDetectionResponse classify(@Valid @RequestBody SpamDetectionRequest request);

    /**
     * Classify multiple texts for spam detection in bulk
     */
    @PostMapping("/classify/bulk")
    BulkSpamDetectionResponse classifyBulk(@Valid @RequestBody BulkSpamDetectionRequest request);

    /**
     * Get service metrics
     */
    @GetMapping("/metrics")
    Map<String, Object> metrics();
}
