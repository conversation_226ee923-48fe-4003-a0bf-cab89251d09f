package com.lookforx.common.tracing.aspect;

import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * Aspect for automatic tracing of service methods
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class TracingAspect {
    
    private final Tracer tracer;
    
    @Around("@annotation(com.lookforx.common.tracing.annotation.Traced) || " +
            "execution(* com.lookforx.*.service.*.*(..)) || " +
            "execution(* com.lookforx.*.controller.*.*(..)) || " +
            "execution(* com.lookforx.*.repository.*.*(..))")
    public Object traceMethod(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = method.getName();
        
        // Create span name
        String spanName = className + "." + methodName;
        
        Span span = tracer.nextSpan()
                .name(spanName)
                .tag("class", className)
                .tag("method", methodName)
                .tag("component", getComponentType(className));

        span.start();
        try (Tracer.SpanInScope ws = tracer.withSpan(span)) {
            // Add method parameters to span if they are simple types
            Object[] args = joinPoint.getArgs();
            if (args != null && args.length > 0) {
                for (int i = 0; i < Math.min(args.length, 5); i++) { // Limit to first 5 args
                    Object arg = args[i];
                    if (arg != null && isSimpleType(arg.getClass())) {
                        span.tag("arg." + i, arg.toString());
                    }
                }
            }
            
            // Update MDC with current span info
            MDC.put("className", className);
            MDC.put("methodName", methodName);
            
            long startTime = System.currentTimeMillis();
            
            try {
                Object result = joinPoint.proceed();
                
                long duration = System.currentTimeMillis() - startTime;
                span.tag("duration.ms", String.valueOf(duration));
                MDC.put("duration", String.valueOf(duration));
                
                log.debug("Method {} executed successfully in {}ms", spanName, duration);
                
                return result;
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                
                span.tag("error", "true")
                    .tag("error.message", e.getMessage())
                    .tag("error.type", e.getClass().getSimpleName())
                    .tag("duration.ms", String.valueOf(duration));
                
                log.error("Method {} failed after {}ms with error: {}", spanName, duration, e.getMessage());
                
                throw e;
            }
            
        } finally {
            span.end();
            // Clean up MDC entries added by this aspect
            MDC.remove("className");
            MDC.remove("methodName");
            MDC.remove("duration");
        }
    }
    
    private String getComponentType(String className) {
        if (className.endsWith("Controller")) {
            return "controller";
        } else if (className.endsWith("Service")) {
            return "service";
        } else if (className.endsWith("Repository")) {
            return "repository";
        } else if (className.endsWith("Client")) {
            return "client";
        } else {
            return "component";
        }
    }
    
    private boolean isSimpleType(Class<?> type) {
        return type.isPrimitive() ||
               type == String.class ||
               type == Integer.class ||
               type == Long.class ||
               type == Double.class ||
               type == Float.class ||
               type == Boolean.class ||
               type == Short.class ||
               type == Byte.class ||
               type == Character.class ||
               Number.class.isAssignableFrom(type);
    }
}
