package com.lookforx.common.tracing.publisher;

import com.lookforx.common.events.TraceEvent;
import com.lookforx.common.kafka.LoggingKafkaTopics;
import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Publisher for trace events to Kafka
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TraceEventPublisher {
    
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final Tracer tracer;
    
    @Value("${spring.application.name:unknown-service}")
    private String serviceName;
    
    @Value("${spring.application.version:1.0.0}")
    private String serviceVersion;
    
    @Value("${spring.profiles.active:dev}")
    private String environment;
    
    @Value("${server.address:localhost}")
    private String host;
    
    public void publishSpanStart(Span span, String operationName, String spanKind) {
        try {
            TraceEvent traceEvent = createTraceEvent(span, operationName, spanKind, "STARTED");
            traceEvent.setStartTime(System.currentTimeMillis());
            
            kafkaTemplate.send(LoggingKafkaTopics.APPLICATION_TRACES, traceEvent.getEventId(), traceEvent);
            
            log.debug("Published trace start event for span: {}", span.context().spanId());
            
        } catch (Exception e) {
            log.error("Failed to publish trace start event", e);
        }
    }
    
    public void publishSpanEnd(Span span, String operationName, String spanKind, 
                              Long duration, String status, String errorMessage) {
        try {
            TraceEvent traceEvent = createTraceEvent(span, operationName, spanKind, status);
            traceEvent.setEndTime(System.currentTimeMillis());
            traceEvent.setDuration(duration);
            traceEvent.setErrorMessage(errorMessage);
            
            if ("ERROR".equals(status)) {
                traceEvent.setErrorType("APPLICATION_ERROR");
            }
            
            kafkaTemplate.send(LoggingKafkaTopics.APPLICATION_TRACES, traceEvent.getEventId(), traceEvent);
            
            log.debug("Published trace end event for span: {} with status: {}", 
                     span.context().spanId(), status);
            
        } catch (Exception e) {
            log.error("Failed to publish trace end event", e);
        }
    }
    
    public void publishCustomTraceEvent(String operationName, Map<String, String> tags, 
                                       Map<String, Object> logs) {
        try {
            Span currentSpan = tracer.currentSpan();
            if (currentSpan == null) {
                log.warn("No current span available for custom trace event");
                return;
            }
            
            TraceEvent traceEvent = createTraceEvent(currentSpan, operationName, "INTERNAL", "OK");
            traceEvent.setTags(tags);
            traceEvent.setLogs(logs);
            
            kafkaTemplate.send(LoggingKafkaTopics.APPLICATION_TRACES, traceEvent.getEventId(), traceEvent);
            
            log.debug("Published custom trace event: {}", operationName);
            
        } catch (Exception e) {
            log.error("Failed to publish custom trace event", e);
        }
    }
    
    private TraceEvent createTraceEvent(Span span, String operationName, String spanKind, String status) {
        Map<String, String> mdcMap = MDC.getCopyOfContextMap();
        if (mdcMap == null) {
            mdcMap = new HashMap<>();
        }
        
        return TraceEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .serviceName(serviceName)
                .environment(environment)
                .version(serviceVersion)
                .traceId(span.context().traceId())
                .spanId(span.context().spanId())
                .parentSpanId(span.context().parentId())
                .operationName(operationName)
                .spanKind(spanKind)
                .status(status)
                .userId(mdcMap.get("userId"))
                .sessionId(mdcMap.get("sessionId"))
                .requestId(mdcMap.get("requestId"))
                .correlationId(mdcMap.get("correlationId"))
                .host(host)
                .ip(mdcMap.get("clientIp"))
                .userAgent(mdcMap.get("userAgent"))
                .requestUri(mdcMap.get("requestUri"))
                .httpMethod(mdcMap.get("httpMethod"))
                .httpStatus(mdcMap.get("httpStatus") != null ? 
                           Integer.valueOf(mdcMap.get("httpStatus")) : null)
                .timestamp(LocalDateTime.now())
                .build();
    }
}
