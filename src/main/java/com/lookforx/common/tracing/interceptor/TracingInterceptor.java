package com.lookforx.common.tracing.interceptor;

import io.micrometer.tracing.Span;
import io.micrometer.tracing.Tracer;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.UUID;

/**
 * HTTP interceptor for adding tracing information to requests
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TracingInterceptor implements HandlerInterceptor {
    
    private final Tracer tracer;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // Generate or extract correlation ID
        String correlationId = request.getHeader("X-Correlation-ID");
        if (correlationId == null || correlationId.isEmpty()) {
            correlationId = UUID.randomUUID().toString();
        }
        
        // Generate request ID
        String requestId = UUID.randomUUID().toString();
        
        // Get current span information
        Span currentSpan = tracer.nextSpan().name(request.getMethod() + " " + request.getRequestURI());
        currentSpan.start();
        String traceId = currentSpan.context().traceId();
        String spanId = currentSpan.context().spanId();
        
        // Add to MDC for logging
        MDC.put("traceId", traceId);
        MDC.put("spanId", spanId);
        MDC.put("correlationId", correlationId);
        MDC.put("requestId", requestId);
        MDC.put("userId", extractUserId(request));
        MDC.put("sessionId", extractSessionId(request));
        MDC.put("clientIp", getClientIpAddress(request));
        MDC.put("userAgent", request.getHeader("User-Agent"));
        MDC.put("requestUri", request.getRequestURI());
        MDC.put("httpMethod", request.getMethod());
        
        // Add to response headers
        response.setHeader("X-Trace-ID", traceId);
        response.setHeader("X-Span-ID", spanId);
        response.setHeader("X-Correlation-ID", correlationId);
        response.setHeader("X-Request-ID", requestId);
        
        // Add tags to span
        currentSpan.tag("http.method", request.getMethod())
                  .tag("http.url", request.getRequestURL().toString())
                  .tag("http.user_agent", request.getHeader("User-Agent"))
                  .tag("http.remote_addr", getClientIpAddress(request))
                  .tag("service.name", System.getProperty("spring.application.name", "unknown"));
        
        // Store span in request attributes
        request.setAttribute("currentSpan", currentSpan);
        
        log.debug("Started tracing for request: {} {} with traceId: {}, spanId: {}", 
                 request.getMethod(), request.getRequestURI(), traceId, spanId);
        
        return true;
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, 
                               Object handler, Exception ex) {
        try {
            // Get span from request attributes
            Span currentSpan = (Span) request.getAttribute("currentSpan");
            if (currentSpan != null) {
                // Add response information to span
                currentSpan.tag("http.status_code", String.valueOf(response.getStatus()));
                
                if (ex != null) {
                    currentSpan.tag("error", "true")
                              .tag("error.message", ex.getMessage())
                              .tag("error.type", ex.getClass().getSimpleName());
                }
                
                // End span
                currentSpan.end();
            }
            
            // Add HTTP status to MDC
            MDC.put("httpStatus", String.valueOf(response.getStatus()));
            
            log.debug("Completed tracing for request: {} {} with status: {}", 
                     request.getMethod(), request.getRequestURI(), response.getStatus());
            
        } finally {
            // Clear MDC
            MDC.clear();
        }
    }
    
    private String extractUserId(HttpServletRequest request) {
        // Try to extract user ID from JWT token or session
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            // Extract user ID from JWT token (implementation depends on your JWT structure)
            // This is a placeholder - implement according to your JWT structure
            return "extracted-user-id";
        }
        return null;
    }
    
    private String extractSessionId(HttpServletRequest request) {
        // Extract session ID from request
        if (request.getSession(false) != null) {
            return request.getSession().getId();
        }
        return null;
    }
    
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
