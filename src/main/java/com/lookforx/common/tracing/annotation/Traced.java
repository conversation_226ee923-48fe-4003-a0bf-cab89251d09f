package com.lookforx.common.tracing.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to mark methods for automatic tracing
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface Traced {
    
    /**
     * Custom span name. If not provided, class.method will be used.
     */
    String value() default "";
    
    /**
     * Whether to include method parameters in the span tags
     */
    boolean includeParameters() default false;
    
    /**
     * Whether to include the return value in the span tags
     */
    boolean includeReturnValue() default false;
}
