package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * Event fired when a new campaign is created
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class CampaignCreatedEvent extends BaseEvent {
    
    private Long campaignId;
    private String campaignName;
    private String campaignType;
    private BigDecimal budget;
    private String currency;
    private String startDate;
    private String endDate;
    private String targetAudience;
    private String creatorEmail;
    private String creatorName;
    
    @Override
    public String getEventType() {
        return "CAMPAIGN_CREATED";
    }
}
