package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * Event fired when user membership is upgraded
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class MembershipUpgradedEvent extends BaseEvent {
    
    private String membershipId;
    private String oldMembershipType;
    private String newMembershipType;
    private BigDecimal upgradeFee;
    private String currency;
    private String validUntil;
    private String userEmail;
    private String userName;
    private String benefits;
    
    @Override
    public String getEventType() {
        return "MEMBERSHIP_UPGRADED";
    }
}
