package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Event fired when a user logs in
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class UserLoginEvent extends BaseEvent {
    
    private String email;
    private String ipAddress;
    private String userAgent;
    private String deviceType;
    private boolean successful;
    private String failureReason;
    
    @Override
    public String getEventType() {
        return "USER_LOGIN";
    }
}
