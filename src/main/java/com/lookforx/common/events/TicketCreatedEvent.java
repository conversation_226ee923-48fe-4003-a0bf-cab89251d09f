package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

/**
 * Event fired when a new support ticket is created
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class TicketCreatedEvent extends BaseEvent {
    
    private UUID ticketId;
    private String ticketType; // REQUEST, COMPLAINT, SUGGESTION
    private String relatedEntityId; // requestId for REQUEST tickets, targetId for COMPLAINT tickets
    private String title;
    private String description;
    private String priority;
    private String targetType; // For complaints: USER, REQUEST, BID
    private UUID targetId; // ID of the target being complained about
    
    @Override
    public String getEventType() {
        return "TICKET_CREATED";
    }
}
