package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * Event fired when a new request is created
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class RequestCreatedEvent extends BaseEvent {
    
    private Long requestId;
    private String title;
    private String description;
    private Long categoryId;
    private String categoryName;
    private BigDecimal budget;
    private String currency;
    private String deadline;
    private String requesterEmail;
    private String requesterName;
    
    @Override
    public String getEventType() {
        return "REQUEST_CREATED";
    }
}
