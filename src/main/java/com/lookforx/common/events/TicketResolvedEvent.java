package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

/**
 * Event fired when a support ticket is resolved
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class TicketResolvedEvent extends BaseEvent {
    
    private UUID ticketId;
    private String ticketType;
    private String title;
    private String resolution;
    private UUID resolvedBy;
    private String resolutionNotes;
    
    @Override
    public String getEventType() {
        return "TICKET_RESOLVED";
    }
}
