package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

/**
 * Event fired when user feedback is submitted
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class FeedbackSubmittedEvent extends BaseEvent {
    
    private UUID feedbackId;
    private String category; // ISSUE, SUGGESTION
    private String title;
    private String message;
    private String imageUrl;
    private Boolean isAnonymous;
    private String userEmail;
    
    @Override
    public String getEventType() {
        return "FEEDBACK_SUBMITTED";
    }
}
