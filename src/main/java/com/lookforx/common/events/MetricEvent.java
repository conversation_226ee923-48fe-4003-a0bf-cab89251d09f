package com.lookforx.common.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Metric event for application metrics and performance monitoring
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MetricEvent extends BaseEvent {
    
    private String serviceName;
    private String environment;
    private String version;
    private String metricName;
    private String metricType; // COUNTER, GAUGE, TIMER, HISTOGRAM
    private Double value;
    private String unit;
    private Map<String, String> tags;
    private String traceId;
    private String spanId;
    private String userId;
    private String sessionId;
    private String requestId;
    private String correlationId;
    private String host;
    private String ip;
    private Map<String, Object> customFields;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime timestamp;
    
    @Override
    public String getEventType() {
        return "METRIC_EVENT";
    }
}
