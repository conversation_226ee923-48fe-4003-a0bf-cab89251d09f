package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Event fired when a new user registers
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class UserRegisteredEvent extends BaseEvent {
    
    private String email;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String verificationToken;
    private String preferredLanguage;
    
    @Override
    public String getEventType() {
        return "USER_REGISTERED";
    }
}
