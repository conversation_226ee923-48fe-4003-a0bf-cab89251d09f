package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Event fired when a new user signs up and needs a welcome notification
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class UserWelcomeEvent extends BaseEvent {
    
    private String userEmail;
    private String userName;
    private String registrationMethod; // "EMAIL" or "GOOGLE"
    private String preferredLanguage;
    private boolean isEmailVerified;
    
    @Override
    public String getEventType() {
        return "USER_WELCOME";
    }
}
