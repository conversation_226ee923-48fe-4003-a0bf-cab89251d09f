package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Event fired when request status changes
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class RequestStatusChangedEvent extends BaseEvent {
    
    private Long requestId;
    private String requestTitle;
    private String oldStatus;
    private String newStatus;
    private String changedBy;
    private String reason;
    private Long assignedProviderId;
    private String assignedProviderEmail;
    private String requesterEmail;
    
    @Override
    public String getEventType() {
        return "REQUEST_STATUS_CHANGED";
    }
}
