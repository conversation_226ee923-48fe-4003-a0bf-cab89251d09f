package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

/**
 * Event fired when a comment is added to a support ticket
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class TicketCommentAddedEvent extends BaseEvent {
    
    private UUID ticketId;
    private String ticketType;
    private String title;
    private String comment;
    private UUID commentBy;
    private String commentByName;
    private boolean isInternal;
    
    @Override
    public String getEventType() {
        return "TICKET_COMMENT_ADDED";
    }
}
