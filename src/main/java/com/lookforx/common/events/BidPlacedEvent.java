package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * Event fired when a bid is placed on a request
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class BidPlacedEvent extends BaseEvent {
    
    private Long bidId;
    private Long requestId;
    private String requestTitle;
    private BigDecimal bidAmount;
    private String currency;
    private String proposedDeadline;
    private String bidderEmail;
    private String bidderName;
    private String requesterEmail;
    private String message;
    
    @Override
    public String getEventType() {
        return "BID_PLACED";
    }
}
