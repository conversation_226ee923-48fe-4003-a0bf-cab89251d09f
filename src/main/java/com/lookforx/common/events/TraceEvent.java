package com.lookforx.common.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Trace event for distributed tracing
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TraceEvent extends BaseEvent {
    
    private String serviceName;
    private String environment;
    private String version;
    private String traceId;
    private String spanId;
    private String parentSpanId;
    private String operationName;
    private String spanKind; // CLIENT, SERVER, PRODUCER, CONSUMER, INTERNAL
    private String status; // OK, ERROR, TIMEOUT
    private Long startTime;
    private Long endTime;
    private Long duration;
    private String userId;
    private String sessionId;
    private String requestId;
    private String correlationId;
    private String host;
    private String ip;
    private String userAgent;
    private String requestUri;
    private String httpMethod;
    private Integer httpStatus;
    private String errorMessage;
    private String errorType;
    private Map<String, String> tags;
    private Map<String, Object> logs;
    private Map<String, Object> customFields;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime timestamp;
    
    @Override
    public String getEventType() {
        return "TRACE_EVENT";
    }
}
