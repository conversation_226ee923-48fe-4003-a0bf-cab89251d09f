package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * Event fired when a form is submitted
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class FormSubmittedEvent extends BaseEvent {
    
    private String formSubmissionId;
    private Long formTemplateId;
    private String formTemplateName;
    private Long categoryId;
    private String requestId;
    private String submitterEmail;
    private String submitterName;
    private Integer responseCount;
    
    @Override
    public String getEventType() {
        return "FORM_SUBMITTED";
    }
}
