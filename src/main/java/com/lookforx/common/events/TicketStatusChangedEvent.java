package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

/**
 * Event fired when a support ticket status changes
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class TicketStatusChangedEvent extends BaseEvent {
    
    private UUID ticketId;
    private String ticketType;
    private String oldStatus;
    private String newStatus;
    private String title;
    private String resolutionNotes;
    private UUID assignedTo;
    private UUID changedBy;
    
    @Override
    public String getEventType() {
        return "TICKET_STATUS_CHANGED";
    }
}
