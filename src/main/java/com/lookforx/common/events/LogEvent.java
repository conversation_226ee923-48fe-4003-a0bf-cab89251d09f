package com.lookforx.common.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;


import java.time.LocalDateTime;
import java.util.Map;

/**
 * Log event for Kafka-based logging system
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LogEvent extends BaseEvent {
    
    private String logServiceName;
    private String logEnvironment;
    private String logVersion;
    private String level;
    private String logger;
    private String message;
    private String thread;
    private String traceId;
    private String spanId;
    private String sessionId;
    private String requestId;
    private String className;
    private String methodName;
    private Integer lineNumber;
    private String exception;
    private String stackTrace;
    private String host;
    private String ip;
    private String service;
    private String userAgent;
    private String requestUri;
    private String httpMethod;
    private Integer httpStatus;
    private Long duration;
    private Map<String, Object> mdc;
    private Map<String, Object> customFields;
    

    
    @Override
    public String getEventType() {
        return "LOG_EVENT";
    }
}
