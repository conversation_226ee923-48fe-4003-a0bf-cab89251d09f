package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

/**
 * Event fired when a support ticket is assigned to an admin
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class TicketAssignedEvent extends BaseEvent {
    
    private UUID ticketId;
    private String ticketType;
    private String title;
    private String priority;
    private UUID assignedTo;
    private UUID assignedBy;
    private String assignmentNotes;
    
    @Override
    public String getEventType() {
        return "TICKET_ASSIGNED";
    }
}
