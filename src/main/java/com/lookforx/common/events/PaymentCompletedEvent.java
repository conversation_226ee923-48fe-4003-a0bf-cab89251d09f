package com.lookforx.common.events;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 * Event fired when a payment is completed
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class PaymentCompletedEvent extends BaseEvent {
    
    private String paymentId;
    private Long requestId;
    private String requestTitle;
    private BigDecimal amount;
    private String currency;
    private String paymentMethod;
    private String payerEmail;
    private String payerName;
    private String payeeEmail;
    private String payeeName;
    private String transactionId;
    
    @Override
    public String getEventType() {
        return "PAYMENT_COMPLETED";
    }
}
