package com.lookforx.common.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * Base event class for all domain events in the system
 */
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "eventType"
)
@JsonSubTypes({
    @JsonSubTypes.Type(value = UserRegisteredEvent.class, name = "USER_REGISTERED"),
    @JsonSubTypes.Type(value = UserWelcomeEvent.class, name = "USER_WELCOME"),
    @JsonSubTypes.Type(value = UserLoginEvent.class, name = "USER_LOGIN"),
    @JsonSubTypes.Type(value = FormSubmittedEvent.class, name = "FORM_SUBMITTED"),
    @JsonSubTypes.Type(value = RequestCreatedEvent.class, name = "REQUEST_CREATED"),
    @JsonSubTypes.Type(value = RequestStatusChangedEvent.class, name = "REQUEST_STATUS_CHANGED"),
    @JsonSubTypes.Type(value = BidPlacedEvent.class, name = "BID_PLACED"),
    @JsonSubTypes.Type(value = PaymentCompletedEvent.class, name = "PAYMENT_COMPLETED"),
    @JsonSubTypes.Type(value = CampaignCreatedEvent.class, name = "CAMPAIGN_CREATED"),
    @JsonSubTypes.Type(value = MembershipUpgradedEvent.class, name = "MEMBERSHIP_UPGRADED"),
    @JsonSubTypes.Type(value = TicketCreatedEvent.class, name = "TICKET_CREATED"),
    @JsonSubTypes.Type(value = TicketStatusChangedEvent.class, name = "TICKET_STATUS_CHANGED"),
    @JsonSubTypes.Type(value = TicketAssignedEvent.class, name = "TICKET_ASSIGNED"),
    @JsonSubTypes.Type(value = TicketResolvedEvent.class, name = "TICKET_RESOLVED"),
    @JsonSubTypes.Type(value = TicketCommentAddedEvent.class, name = "TICKET_COMMENT_ADDED"),
    @JsonSubTypes.Type(value = FeedbackSubmittedEvent.class, name = "FEEDBACK_SUBMITTED")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public abstract class BaseEvent {
    
    @Builder.Default
    private String eventId = UUID.randomUUID().toString();

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();

    private String serviceName;
    @Builder.Default
    private String version = "1.0";
    private String userId;
    private String correlationId;
    
    // Additional metadata
    private Map<String, Object> metadata;
    
    public abstract String getEventType();
}
