package com.lookforx.common.kafka;

import com.lookforx.common.events.BaseEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * Service for publishing events to Kafka topics
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Lazy
public class EventPublisher {
    
    private final KafkaTemplate<String, BaseEvent> kafkaTemplate;
    
    /**
     * Publish an event to the specified topic
     */
    public void publishEvent(String topic, BaseEvent event) {
        try {
            CompletableFuture<SendResult<String, BaseEvent>> future = 
                kafkaTemplate.send(topic, event.getEventId(), event);
            
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    log.info("Event published successfully: eventId={}, topic={}, eventType={}", 
                        event.getEventId(), topic, event.getEventType());
                } else {
                    log.error("Failed to publish event: eventId={}, topic={}, eventType={}", 
                        event.getEventId(), topic, event.getEventType(), ex);
                }
            });
        } catch (Exception e) {
            log.error("Error publishing event: eventId={}, topic={}, eventType={}", 
                event.getEventId(), topic, event.getEventType(), e);
        }
    }
    
    /**
     * Publish user event
     */
    public void publishUserEvent(BaseEvent event) {
        publishEvent(KafkaTopics.USER_EVENTS, event);
    }
    
    /**
     * Publish form event
     */
    public void publishFormEvent(BaseEvent event) {
        publishEvent(KafkaTopics.FORM_EVENTS, event);
    }
    
    /**
     * Publish request event
     */
    public void publishRequestEvent(BaseEvent event) {
        publishEvent(KafkaTopics.REQUEST_EVENTS, event);
    }
    
    /**
     * Publish bid event
     */
    public void publishBidEvent(BaseEvent event) {
        publishEvent(KafkaTopics.BID_EVENTS, event);
    }
    
    /**
     * Publish payment event
     */
    public void publishPaymentEvent(BaseEvent event) {
        publishEvent(KafkaTopics.PAYMENT_EVENTS, event);
    }
    
    /**
     * Publish campaign event
     */
    public void publishCampaignEvent(BaseEvent event) {
        publishEvent(KafkaTopics.CAMPAIGN_EVENTS, event);
    }
    
    /**
     * Publish membership event
     */
    public void publishMembershipEvent(BaseEvent event) {
        publishEvent(KafkaTopics.MEMBERSHIP_EVENTS, event);
    }

    /**
     * Publish support ticket event
     */
    public void publishSupportTicketEvent(BaseEvent event) {
        publishEvent(KafkaTopics.SUPPORT_TICKET_EVENTS, event);
    }

    /**
     * Publish support feedback event
     */
    public void publishSupportFeedbackEvent(BaseEvent event) {
        publishEvent(KafkaTopics.SUPPORT_FEEDBACK_EVENTS, event);
    }
}
