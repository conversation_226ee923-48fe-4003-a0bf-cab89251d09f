package com.lookforx.common.kafka;

import com.lookforx.common.events.BaseEvent;
import com.lookforx.common.events.LogEvent;
import com.lookforx.common.events.MetricEvent;
import com.lookforx.common.events.TraceEvent;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerializer;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * Unified Kafka configuration for all LookForX services
 * Handles both general events and logging events
 */
@Configuration
@Slf4j
public class CommonKafkaConfig {
    
    @Value("${spring.kafka.bootstrap-servers:localhost:9092}")
    private String bootstrapServers;
    
    @Value("${spring.kafka.producer.retries:3}")
    private Integer retries;
    
    @Value("${spring.kafka.producer.batch-size:16384}")
    private Integer batchSize;
    
    @Value("${spring.kafka.producer.linger-ms:1}")
    private Integer lingerMs;
    
    @Value("${spring.kafka.producer.buffer-memory:33554432}")
    private Long bufferMemory;
    
    @Value("${spring.kafka.producer.acks:all}")
    private String acks;
    
    @Value("${spring.kafka.producer.compression-type:snappy}")
    private String compressionType;
    
    @Value("${spring.kafka.consumer.group-id:${spring.application.name:lookforx}-consumer-group}")
    private String defaultConsumerGroupId;
    
    @Value("${spring.kafka.consumer.auto-offset-reset:earliest}")
    private String autoOffsetReset;
    
    @Value("${spring.kafka.consumer.max-poll-records:500}")
    private Integer maxPollRecords;
    
    @Value("${spring.kafka.consumer.concurrency:3}")
    private Integer concurrency;
    
    // ========== PRODUCER CONFIGURATIONS ==========
    
    /**
     * Primary producer factory for general events (BaseEvent)
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(name = "producerFactory")
    public ProducerFactory<String, BaseEvent> producerFactory() {
        return new DefaultKafkaProducerFactory<>(getProducerProps());
    }
    
    /**
     * Primary Kafka template for general events
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(name = "kafkaTemplate")
    public KafkaTemplate<String, BaseEvent> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }
    
    /**
     * Producer factory for logging events (Object type for flexibility)
     */
    @Bean("loggingProducerFactory")
    @ConditionalOnProperty(name = "lookforx.logging.kafka.enabled", havingValue = "true", matchIfMissing = true)
    public ProducerFactory<String, Object> loggingProducerFactory() {
        return new DefaultKafkaProducerFactory<>(getProducerProps());
    }
    
    /**
     * Kafka template for logging events
     */
    @Bean("loggingKafkaTemplate")
    @ConditionalOnProperty(name = "lookforx.logging.kafka.enabled", havingValue = "true", matchIfMissing = true)
    public KafkaTemplate<String, Object> loggingKafkaTemplate() {
        return new KafkaTemplate<>(loggingProducerFactory());
    }
    
    // ========== CONSUMER CONFIGURATIONS ==========

    /**
     * Consumer factory for String messages (for raw log processing)
     */
    @Bean("stringConsumerFactory")
    @ConditionalOnProperty(name = "lookforx.logging.consumer.enabled", havingValue = "true", matchIfMissing = false)
    public ConsumerFactory<String, String> stringConsumerFactory() {
        Map<String, Object> props = getStringConsumerProps();
        props.put(ConsumerConfig.GROUP_ID_CONFIG, getLoggingConsumerGroupId() + "-strings");
        return new DefaultKafkaConsumerFactory<>(props);
    }

    /**
     * Listener container factory for String messages
     */
    @Bean("stringListenerContainerFactory")
    @ConditionalOnProperty(name = "lookforx.logging.consumer.enabled", havingValue = "true", matchIfMissing = false)
    public ConcurrentKafkaListenerContainerFactory<String, String> stringListenerContainerFactory() {
        log.info("=== Creating StringListenerContainerFactory ===");
        ConcurrentKafkaListenerContainerFactory<String, String> factory =
            new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(stringConsumerFactory());
        factory.setConcurrency(concurrency);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.BATCH);
        factory.getContainerProperties().setPollTimeout(3000);

        // Enable batch listener for better performance
        factory.setBatchListener(true);

        log.info("StringListenerContainerFactory configured with concurrency: {} (batch ack, batch enabled)", concurrency);
        return factory;
    }

    /**
     * Consumer factory for Log Events
     */
    @Bean("logEventConsumerFactory")
    @ConditionalOnProperty(name = "lookforx.logging.consumer.enabled", havingValue = "true", matchIfMissing = false)
    public ConsumerFactory<String, LogEvent> logEventConsumerFactory() {
        Map<String, Object> props = getConsumerProps();
        props.put(ConsumerConfig.GROUP_ID_CONFIG, getLoggingConsumerGroupId() + "-logs");
        props.put(JsonDeserializer.VALUE_DEFAULT_TYPE, LogEvent.class.getName());
        return new DefaultKafkaConsumerFactory<>(props);
    }
    
    /**
     * Listener container factory for Log Events
     */
    @Bean("logEventListenerContainerFactory")
    @ConditionalOnProperty(name = "lookforx.logging.consumer.enabled", havingValue = "true", matchIfMissing = false)
    public ConcurrentKafkaListenerContainerFactory<String, LogEvent> logEventListenerContainerFactory() {
        log.info("=== Creating LogEventListenerContainerFactory ===");
        ConcurrentKafkaListenerContainerFactory<String, LogEvent> factory =
            new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(logEventConsumerFactory());
        factory.setConcurrency(concurrency);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.BATCH);
        factory.getContainerProperties().setPollTimeout(3000);

        // Enable batch listener for better performance
        factory.setBatchListener(true);

        log.info("LogEventListenerContainerFactory configured with concurrency: {} (batch enabled)", concurrency);
        return factory;
    }
    
    /**
     * Consumer factory for Metric Events
     */
    @Bean("metricEventConsumerFactory")
    @ConditionalOnProperty(name = "lookforx.logging.consumer.enabled", havingValue = "true", matchIfMissing = false)
    public ConsumerFactory<String, MetricEvent> metricEventConsumerFactory() {
        Map<String, Object> props = getConsumerProps();
        props.put(ConsumerConfig.GROUP_ID_CONFIG, getLoggingConsumerGroupId() + "-metrics");
        props.put(JsonDeserializer.VALUE_DEFAULT_TYPE, MetricEvent.class.getName());
        return new DefaultKafkaConsumerFactory<>(props);
    }
    
    /**
     * Listener container factory for Metric Events
     */
    @Bean("metricEventListenerContainerFactory")
    @ConditionalOnProperty(name = "lookforx.logging.consumer.enabled", havingValue = "true", matchIfMissing = false)
    public ConcurrentKafkaListenerContainerFactory<String, MetricEvent> metricEventListenerContainerFactory() {
        log.info("=== Creating MetricEventListenerContainerFactory ===");
        ConcurrentKafkaListenerContainerFactory<String, MetricEvent> factory =
            new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(metricEventConsumerFactory());
        factory.setConcurrency(concurrency);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.BATCH);
        factory.getContainerProperties().setPollTimeout(3000);
        log.info("MetricEventListenerContainerFactory configured with concurrency: {}", concurrency);
        return factory;
    }
    
    /**
     * Consumer factory for Trace Events
     */
    @Bean("traceEventConsumerFactory")
    @ConditionalOnProperty(name = "lookforx.logging.consumer.enabled", havingValue = "true", matchIfMissing = false)
    public ConsumerFactory<String, TraceEvent> traceEventConsumerFactory() {
        Map<String, Object> props = getConsumerProps();
        props.put(ConsumerConfig.GROUP_ID_CONFIG, getLoggingConsumerGroupId() + "-traces");
        props.put(JsonDeserializer.VALUE_DEFAULT_TYPE, TraceEvent.class.getName());
        return new DefaultKafkaConsumerFactory<>(props);
    }
    
    /**
     * Listener container factory for Trace Events
     */
    @Bean("traceEventListenerContainerFactory")
    @ConditionalOnProperty(name = "lookforx.logging.consumer.enabled", havingValue = "true", matchIfMissing = false)
    public ConcurrentKafkaListenerContainerFactory<String, TraceEvent> traceEventListenerContainerFactory() {
        log.info("=== Creating TraceEventListenerContainerFactory ===");
        ConcurrentKafkaListenerContainerFactory<String, TraceEvent> factory =
            new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(traceEventConsumerFactory());
        factory.setConcurrency(concurrency);
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.BATCH);
        factory.getContainerProperties().setPollTimeout(3000);
        log.info("TraceEventListenerContainerFactory configured with concurrency: {}", concurrency);
        return factory;
    }
    
    // ========== HELPER METHODS ==========
    
    /**
     * Common producer properties
     */
    private Map<String, Object> getProducerProps() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, JsonSerializer.class);
        configProps.put(ProducerConfig.RETRIES_CONFIG, retries);
        configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, batchSize);
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, lingerMs);
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, bufferMemory);
        configProps.put(ProducerConfig.ACKS_CONFIG, acks);
        configProps.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, compressionType);
        configProps.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 5);
        configProps.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);

        // Increase timeout values for better reliability
        configProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 300000); // 5 minutes
        configProps.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, 600000); // 10 minutes
        configProps.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 300000); // 5 minutes

        return configProps;
    }
    
    /**
     * Common consumer properties
     */
    private Map<String, Object> getConsumerProps() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, autoOffsetReset);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, JsonDeserializer.class);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollRecords);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        props.put(JsonDeserializer.TRUSTED_PACKAGES, "com.lookforx.common.events,com.lookforx.common.logging.events");
        return props;
    }

    /**
     * String consumer properties (for raw log processing)
     */
    private Map<String, Object> getStringConsumerProps() {
        Map<String, Object> props = new HashMap<>();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, autoOffsetReset);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, maxPollRecords);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        return props;
    }
    
    /**
     * Get logging consumer group ID
     */
    private String getLoggingConsumerGroupId() {
        return System.getProperty("lookforx.logging.kafka.consumer.group-id", "logging-consumer-group");
    }
}
