package com.lookforx.common.kafka;

import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.NewTopic;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;
import org.springframework.kafka.core.KafkaAdmin;

import java.util.HashMap;
import java.util.Map;

/**
 * Kafka Topic Configuration
 * Automatically creates required topics if they don't exist
 */
@Configuration
public class KafkaTopicConfig {

    private static final Logger log = LoggerFactory.getLogger(KafkaTopicConfig.class);

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Bean
    public KafkaAdmin kafkaAdmin() {
        Map<String, Object> configs = new HashMap<>();
        configs.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configs.put(AdminClientConfig.REQUEST_TIMEOUT_MS_CONFIG, 30000);
        configs.put(AdminClientConfig.DEFAULT_API_TIMEOUT_MS_CONFIG, 60000);

        log.info("=== KafkaTopicConfig: Creating KafkaAdmin with bootstrap servers: {} ===", bootstrapServers);
        return new KafkaAdmin(configs);
    }

    @Bean
    public NewTopic applicationLogsTopic() {
        log.info("=== KafkaTopicConfig: Creating application-logs topic ===");
        return TopicBuilder.name(LoggingKafkaTopics.APPLICATION_LOGS)
                .partitions(3)
                .replicas(1)
                .compact()
                .build();
    }

    @Bean
    public NewTopic errorLogsTopic() {
        log.info("Creating error-logs topic");
        return TopicBuilder.name(LoggingKafkaTopics.ERROR_LOGS)
                .partitions(3)
                .replicas(1)
                .compact()
                .build();
    }

    @Bean
    public NewTopic applicationMetricsTopic() {
        log.info("Creating application-metrics topic");
        return TopicBuilder.name(LoggingKafkaTopics.APPLICATION_METRICS)
                .partitions(3)
                .replicas(1)
                .compact()
                .build();
    }

    @Bean
    public NewTopic applicationTracesTopic() {
        log.info("Creating application-traces topic");
        return TopicBuilder.name(LoggingKafkaTopics.APPLICATION_TRACES)
                .partitions(3)
                .replicas(1)
                .compact()
                .build();
    }
}
