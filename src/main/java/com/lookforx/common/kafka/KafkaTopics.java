package com.lookforx.common.kafka;

/**
 * Kafka topic constants for all microservices
 */
public final class KafkaTopics {
    
    // User events
    public static final String USER_EVENTS = "user-events";
    
    // Form events
    public static final String FORM_EVENTS = "form-events";
    
    // Request events
    public static final String REQUEST_EVENTS = "request-events";
    
    // Bid events
    public static final String BID_EVENTS = "bid-events";
    
    // Payment events
    public static final String PAYMENT_EVENTS = "payment-events";
    
    // Campaign events
    public static final String CAMPAIGN_EVENTS = "campaign-events";
    
    // Membership events
    public static final String MEMBERSHIP_EVENTS = "membership-events";

    // Support events
    public static final String SUPPORT_TICKET_EVENTS = "support.ticket.events";
    public static final String SUPPORT_FEEDBACK_EVENTS = "support.feedback.events";

    // Notification events (for internal notification service use)
    public static final String NOTIFICATION_EVENTS = "notification-events";
    
    // Dead letter queue for failed events
    public static final String DEAD_LETTER_QUEUE = "dead-letter-queue";
    
    private KafkaTopics() {
        // Utility class
    }
}
