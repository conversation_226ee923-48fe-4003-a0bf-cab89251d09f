package com.lookforx.common.kafka;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.AppenderBase;
import ch.qos.logback.core.encoder.Encoder;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.lookforx.common.events.LogEvent;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.net.InetAddress;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Custom Kafka appender for Logback to send logs to Kafka using LogEventPublisher
 */
public class KafkaLogAppender extends AppenderBase<ILoggingEvent> implements ApplicationContextAware {

    private String bootstrapServers = "localhost:9092";
    private String topic = "application-logs";
    private String serviceName;
    private String environment;
    private String version;
    private KafkaProducer<String, String> producer;
    private ObjectMapper objectMapper;
    private String hostName;
    private Encoder<ILoggingEvent> encoder;
    private ApplicationContext applicationContext;
    private LogEventPublisher logEventPublisher;
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
        try {
            // Try to get LogEventPublisher from Spring context
            this.logEventPublisher = applicationContext.getBean(LogEventPublisher.class);
        } catch (Exception e) {
            // LogEventPublisher not available, will use direct Kafka producer
            System.out.println("LogEventPublisher not available, using direct Kafka producer");
        }
    }

    @Override
    public void start() {
        if (this.encoder == null) {
            addError("No encoder set for the appender named [" + name + "].");
            return;
        }

        try {
            // Validate required properties
            if (bootstrapServers == null || bootstrapServers.trim().isEmpty()) {
                addError("Bootstrap servers not configured for KafkaLogAppender");
                return;
            }

            if (topic == null || topic.trim().isEmpty()) {
                addError("Topic not configured for KafkaLogAppender");
                return;
            }

            // Initialize ObjectMapper
            objectMapper = new ObjectMapper();
            objectMapper.registerModule(new JavaTimeModule());

            // Get hostname
            try {
                hostName = InetAddress.getLocalHost().getHostName();
            } catch (Exception e) {
                hostName = "unknown";
            }

            // Initialize Kafka producer
            Properties props = new Properties();
            props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
            props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
            props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
            props.put(ProducerConfig.ACKS_CONFIG, "all");
            props.put(ProducerConfig.RETRIES_CONFIG, 3);
            props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
            props.put(ProducerConfig.LINGER_MS_CONFIG, 1);
            props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);
            props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "snappy");
            props.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 5);
            props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);

            // Additional settings for reliability
            props.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 30000);
            props.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, 120000);

            producer = new KafkaProducer<>(props);

            super.start();
        } catch (Exception e) {
            addError("Failed to start KafkaLogAppender: " + e.getMessage(), e);
        }
    }
    
    @Override
    public void stop() {
        super.stop();
        if (producer != null) {
            try {
                // Graceful shutdown with timeout
                producer.close(java.time.Duration.ofSeconds(5));
            } catch (Exception e) {
                // Ignore exceptions during shutdown
                System.err.println("Error closing Kafka producer: " + e.getMessage());
            } finally {
                producer = null;
            }
        }
    }
    
    @Override
    protected void append(ILoggingEvent event) {
        try {
            // Try to use LogEventPublisher if available
            if (logEventPublisher != null) {
                LogEvent logEvent = createLogEvent(event);
                logEventPublisher.publishLogEvent(logEvent);
                return;
            }

            // Fallback to direct Kafka producer
            if (producer == null) {
                return; // Silently ignore if producer is not initialized
            }

            LogEvent logEvent = createLogEvent(event);
            String logJson = objectMapper.writeValueAsString(logEvent);

            ProducerRecord<String, String> record = new ProducerRecord<>(
                topic,
                logEvent.getEventId(),
                logJson
            );

            // Send asynchronously with timeout
            producer.send(record, (metadata, exception) -> {
                if (exception != null) {
                    // Don't use addError here to avoid infinite loop
                    System.err.println("Failed to send log to Kafka: " + exception.getMessage());
                }
            });

        } catch (Exception e) {
            // Don't use addError here to avoid infinite loop
            System.err.println("Failed to append log event: " + e.getMessage());
        }
    }
    
    private LogEvent createLogEvent(ILoggingEvent event) {
        Map<String, String> mdcMap = event.getMDCPropertyMap();
        
        LogEvent logEvent = LogEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .logServiceName(serviceName != null ? serviceName : mdcMap.get("service.name"))
                .logEnvironment(environment != null ? environment : mdcMap.get("environment"))
                .logVersion(version != null ? version : mdcMap.get("service.version"))
                .level(event.getLevel().toString())
                .logger(event.getLoggerName())
                .message(event.getFormattedMessage())
                .thread(event.getThreadName())
                .traceId(mdcMap.get("traceId"))
                .spanId(mdcMap.get("spanId"))
                .userId(mdcMap.get("userId"))
                .sessionId(mdcMap.get("sessionId"))
                .requestId(mdcMap.get("requestId"))
                .correlationId(mdcMap.get("correlationId"))
                .className(event.getLoggerName())
                .exception(event.getThrowableProxy() != null ? event.getThrowableProxy().getClassName() : null)
                .stackTrace(event.getThrowableProxy() != null ? event.getThrowableProxy().getMessage() : null)
                .host(hostName)
                .ip(mdcMap.get("clientIp"))
                .userAgent(mdcMap.get("userAgent"))
                .requestUri(mdcMap.get("requestUri"))
                .httpMethod(mdcMap.get("httpMethod"))
                .httpStatus(mdcMap.get("httpStatus") != null ? Integer.valueOf(mdcMap.get("httpStatus")) : null)
                .duration(mdcMap.get("duration") != null ? Long.valueOf(mdcMap.get("duration")) : null)
                .mdc(new HashMap<>(mdcMap))
                .timestamp(LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(event.getTimeStamp()),
                    ZoneId.systemDefault()))
                .build();

        return logEvent;
    }
    
    // Getters and setters for configuration
    public void setBootstrapServers(String bootstrapServers) {
        this.bootstrapServers = bootstrapServers;
    }
    
    public void setTopic(String topic) {
        this.topic = topic;
    }
    
    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }
    
    public void setEnvironment(String environment) {
        this.environment = environment;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
    
    public void setEncoder(Encoder<ILoggingEvent> encoder) {
        this.encoder = encoder;
    }
}
