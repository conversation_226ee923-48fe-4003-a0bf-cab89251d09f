package com.lookforx.common.kafka;

/**
 * Kafka topics for logging system
 */
public final class LoggingKafkaTopics {
    
    // Application logs topic
    public static final String APPLICATION_LOGS = "application-logs";
    
    // Metrics topic
    public static final String APPLICATION_METRICS = "application-metrics";
    
    // Tracing topic
    public static final String APPLICATION_TRACES = "application-traces";
    
    // Audit logs topic
    public static final String AUDIT_LOGS = "audit-logs";
    
    // Error logs topic (for critical errors)
    public static final String ERROR_LOGS = "error-logs";
    
    // Performance logs topic
    public static final String PERFORMANCE_LOGS = "performance-logs";
    
    // Security logs topic
    public static final String SECURITY_LOGS = "security-logs";
    
    // Dead letter queue for failed log processing
    public static final String LOGGING_DLQ = "logging-dlq";
    
    private LoggingKafkaTopics() {
        // Utility class
    }
}
