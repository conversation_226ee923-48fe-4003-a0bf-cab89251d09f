package com.lookforx.common.kafka;

import com.lookforx.common.events.BaseEvent;
import com.lookforx.common.events.LogEvent;
import com.lookforx.common.events.MetricEvent;
import com.lookforx.common.events.TraceEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * Service for publishing log events to Kafka topics
 * Specialized for logging system events
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Lazy
public class LogEventPublisher {
    
    private final KafkaTemplate<String, BaseEvent> kafkaTemplate;
    
    /**
     * Publish an event to the specified topic
     */
    public void publishEvent(String topic, BaseEvent event) {
        try {
            CompletableFuture<SendResult<String, BaseEvent>> future = 
                kafkaTemplate.send(topic, event.getEventId(), event);
            
            future.whenComplete((result, ex) -> {
                if (ex == null) {
                    log.debug("Log event published successfully: eventId={}, topic={}, eventType={}", 
                        event.getEventId(), topic, event.getEventType());
                } else {
                    log.error("Failed to publish log event: eventId={}, topic={}, eventType={}", 
                        event.getEventId(), topic, event.getEventType(), ex);
                }
            });
        } catch (Exception e) {
            log.error("Error publishing log event: eventId={}, topic={}, eventType={}", 
                event.getEventId(), topic, event.getEventType(), e);
        }
    }
    
    /**
     * Publish application log event
     */
    public void publishApplicationLogEvent(LogEvent event) {
        publishEvent(LoggingKafkaTopics.APPLICATION_LOGS, event);
    }
    
    /**
     * Publish error log event
     */
    public void publishErrorLogEvent(LogEvent event) {
        publishEvent(LoggingKafkaTopics.ERROR_LOGS, event);
    }
    
    /**
     * Publish audit log event
     */
    public void publishAuditLogEvent(LogEvent event) {
        publishEvent(LoggingKafkaTopics.AUDIT_LOGS, event);
    }
    
    /**
     * Publish performance log event
     */
    public void publishPerformanceLogEvent(LogEvent event) {
        publishEvent(LoggingKafkaTopics.PERFORMANCE_LOGS, event);
    }
    
    /**
     * Publish security log event
     */
    public void publishSecurityLogEvent(LogEvent event) {
        publishEvent(LoggingKafkaTopics.SECURITY_LOGS, event);
    }
    
    /**
     * Publish metric event
     */
    public void publishMetricEvent(MetricEvent event) {
        publishEvent(LoggingKafkaTopics.APPLICATION_METRICS, event);
    }
    
    /**
     * Publish trace event
     */
    public void publishTraceEvent(TraceEvent event) {
        publishEvent(LoggingKafkaTopics.APPLICATION_TRACES, event);
    }
    
    /**
     * Publish generic log event - automatically determines topic based on log level
     */
    public void publishLogEvent(LogEvent event) {
        String level = event.getLevel();
        if (level != null) {
            switch (level.toUpperCase()) {
                case "ERROR":
                case "FATAL":
                    publishErrorLogEvent(event);
                    break;
                case "AUDIT":
                    publishAuditLogEvent(event);
                    break;
                case "PERFORMANCE":
                case "PERF":
                    publishPerformanceLogEvent(event);
                    break;
                case "SECURITY":
                case "SEC":
                    publishSecurityLogEvent(event);
                    break;
                default:
                    publishApplicationLogEvent(event);
                    break;
            }
        } else {
            publishApplicationLogEvent(event);
        }
    }
}
