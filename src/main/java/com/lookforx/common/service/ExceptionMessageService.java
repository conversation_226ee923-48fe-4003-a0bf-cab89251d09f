package com.lookforx.common.service;

import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.common.dto.ExceptionMessageResponse;
import com.lookforx.common.enums.LanguageCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service for handling exception messages with internationalization support.
 * This service acts as a wrapper around ExceptionServiceClient to provide
 * caching and additional functionality for exception message retrieval.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExceptionMessageService {

    private final ExceptionServiceClient exceptionServiceClient;

    /**
     * Get exception message by exception code and language code.
     * Note: Caching is handled by the exception service itself.
     *
     * @param exceptionCode The exception code to look up
     * @param languageCode The language code for the message
     * @return ExceptionMessageResponse containing the localized message
     */
    public ExceptionMessageResponse getExceptionMessage(String exceptionCode, String languageCode) {
        log.debug("Fetching exception message for code: {} in language: {}", exceptionCode, languageCode);
        
        try {
            ExceptionMessageResponse response = exceptionServiceClient.getExceptionMessage(exceptionCode, languageCode);
            log.debug("Successfully retrieved exception message for code: {}", exceptionCode);
            return response;
        } catch (Exception e) {
            log.error("Error fetching exception message for code: {} in language: {}", exceptionCode, languageCode, e);
            // Return fallback message
            return createFallbackMessage(exceptionCode, languageCode, e.getMessage());
        }
    }

    /**
     * Get exception message using LanguageCode enum.
     * 
     * @param exceptionCode The exception code to look up
     * @param languageCode The LanguageCode enum value
     * @return ExceptionMessageResponse containing the localized message
     */
    public ExceptionMessageResponse getExceptionMessage(String exceptionCode, LanguageCode languageCode) {
        return getExceptionMessage(exceptionCode, languageCode.getCode());
    }

    /**
     * Get exception message with default English language.
     * 
     * @param exceptionCode The exception code to look up
     * @return ExceptionMessageResponse containing the English message
     */
    public ExceptionMessageResponse getExceptionMessage(String exceptionCode) {
        return getExceptionMessage(exceptionCode, LanguageCode.EN.getCode());
    }

    /**
     * Get only the message text for the given exception code and language.
     * 
     * @param exceptionCode The exception code to look up
     * @param languageCode The language code for the message
     * @return The localized message text
     */
    public String getExceptionMessageText(String exceptionCode, String languageCode) {
        ExceptionMessageResponse response = getExceptionMessage(exceptionCode, languageCode);
        return response != null ? response.getMessage() : "Unknown error occurred";
    }

    /**
     * Get only the message text using LanguageCode enum.
     * 
     * @param exceptionCode The exception code to look up
     * @param languageCode The LanguageCode enum value
     * @return The localized message text
     */
    public String getExceptionMessageText(String exceptionCode, LanguageCode languageCode) {
        return getExceptionMessageText(exceptionCode, languageCode.getCode());
    }

    /**
     * Get only the message text with default English language.
     * 
     * @param exceptionCode The exception code to look up
     * @return The English message text
     */
    public String getExceptionMessageText(String exceptionCode) {
        return getExceptionMessageText(exceptionCode, LanguageCode.EN.getCode());
    }

    /**
     * Check if an exception message exists for the given code and language.
     * 
     * @param exceptionCode The exception code to check
     * @param languageCode The language code
     * @return true if message exists, false otherwise
     */
    public boolean hasExceptionMessage(String exceptionCode, String languageCode) {
        try {
            ExceptionMessageResponse response = getExceptionMessage(exceptionCode, languageCode);
            return response != null && response.getMessage() != null && !response.getMessage().isEmpty();
        } catch (Exception e) {
            log.warn("Error checking exception message existence for code: {}", exceptionCode, e);
            return false;
        }
    }

    /**
     * Check if an exception message exists using LanguageCode enum.
     * 
     * @param exceptionCode The exception code to check
     * @param languageCode The LanguageCode enum value
     * @return true if message exists, false otherwise
     */
    public boolean hasExceptionMessage(String exceptionCode, LanguageCode languageCode) {
        return hasExceptionMessage(exceptionCode, languageCode.getCode());
    }

    /**
     * Create a fallback exception message when the service is unavailable.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The language code
     * @param errorMessage The error message from the failed call
     * @return A fallback ExceptionMessageResponse
     */
    private ExceptionMessageResponse createFallbackMessage(String exceptionCode, String languageCode, String errorMessage) {
        String fallbackMessage = determineFallbackMessage(exceptionCode, languageCode);
        
        return ExceptionMessageResponse.builder()
                .exceptionCode(exceptionCode)
                .languageCode(languageCode)
                .message(fallbackMessage)
                .build();
    }

    /**
     * Determine appropriate fallback message based on language and exception code.
     *
     * @param exceptionCode The exception code
     * @param languageCode The language code
     * @return Appropriate fallback message
     */
    private String determineFallbackMessage(String exceptionCode, String languageCode) {
        // Provide basic fallback messages in Turkish and English
        if ("tr".equalsIgnoreCase(languageCode)) {
            return switch (exceptionCode.toLowerCase()) {
                case "resource_not_found" -> "Kaynak bulunamadı";
                case "validation_error" -> "Doğrulama hatası";
                case "access_denied" -> "Erişim reddedildi";
                case "internal_server_error" -> "Sunucu hatası";
                case "bad_request" -> "Geçersiz istek";
                case "unauthorized" -> "Yetkisiz erişim";
                case "forbidden" -> "Yasak erişim";
                case "conflict" -> "Çakışma hatası";
                default -> "Bir hata oluştu";
            };
        } else {
            return switch (exceptionCode.toLowerCase()) {
                case "resource_not_found" -> "Resource not found";
                case "validation_error" -> "Validation error";
                case "access_denied" -> "Access denied";
                case "internal_server_error" -> "Internal server error";
                case "bad_request" -> "Bad request";
                case "unauthorized" -> "Unauthorized";
                case "forbidden" -> "Forbidden";
                case "conflict" -> "Conflict error";
                default -> "An error occurred";
            };
        }
    }
}
