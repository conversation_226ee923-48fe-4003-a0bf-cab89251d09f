package com.lookforx.common.service;

import com.lookforx.common.dto.ExceptionMessageResponse;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.common.exception.ResourceNotFoundException;
import com.lookforx.common.exception.ValidationException;
import com.lookforx.common.util.ExceptionMessageUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Base business service class that provides common functionality for all business services.
 * This class includes exception handling with internationalized messages.
 */
@RequiredArgsConstructor
@Slf4j
public abstract class BaseBusinessService {

    @Autowired(required = false)
    private ExceptionMessageService exceptionMessageService;

    /**
     * Throw ResourceNotFoundException with localized message.
     * 
     * @param resourceName The name of the resource
     * @param resourceId The ID of the resource
     * @param languageCode The language code for the error message
     */
    protected void throwResourceNotFound(String resourceName, Object resourceId, String languageCode) {
        String exceptionCode = "RESOURCE_NOT_FOUND";
        String message = getLocalizedMessage(exceptionCode, languageCode, 
                String.format("%s with id %s not found", resourceName, resourceId));
        
        log.warn("Resource not found: {} with id: {}", resourceName, resourceId);
        throw new ResourceNotFoundException(message);
    }

    /**
     * Throw ResourceNotFoundException with localized message using LanguageCode enum.
     * 
     * @param resourceName The name of the resource
     * @param resourceId The ID of the resource
     * @param languageCode The LanguageCode enum
     */
    protected void throwResourceNotFound(String resourceName, Object resourceId, LanguageCode languageCode) {
        throwResourceNotFound(resourceName, resourceId, languageCode.getCode());
    }

    /**
     * Throw ResourceNotFoundException with English message (default).
     * 
     * @param resourceName The name of the resource
     * @param resourceId The ID of the resource
     */
    protected void throwResourceNotFound(String resourceName, Object resourceId) {
        throwResourceNotFound(resourceName, resourceId, LanguageCode.EN);
    }

    /**
     * Throw ValidationException with localized message.
     * 
     * @param fieldName The name of the field that failed validation
     * @param languageCode The language code for the error message
     */
    protected void throwValidationError(String fieldName, String languageCode) {
        String exceptionCode = "VALIDATION_ERROR";
        String message = getLocalizedMessage(exceptionCode, languageCode, 
                String.format("Validation failed for field: %s", fieldName));
        
        log.warn("Validation error for field: {}", fieldName);
        throw new ValidationException(message);
    }

    /**
     * Throw ValidationException with localized message using LanguageCode enum.
     * 
     * @param fieldName The name of the field that failed validation
     * @param languageCode The LanguageCode enum
     */
    protected void throwValidationError(String fieldName, LanguageCode languageCode) {
        throwValidationError(fieldName, languageCode.getCode());
    }

    /**
     * Throw ValidationException with English message (default).
     * 
     * @param fieldName The name of the field that failed validation
     */
    protected void throwValidationError(String fieldName) {
        throwValidationError(fieldName, LanguageCode.EN);
    }

    /**
     * Get localized message for an exception code.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The language code
     * @param fallbackMessage The fallback message if service is unavailable
     * @return Localized message
     */
    protected String getLocalizedMessage(String exceptionCode, String languageCode, String fallbackMessage) {
        try {
            if (exceptionMessageService != null) {
                return exceptionMessageService.getExceptionMessageText(exceptionCode, languageCode);
            } else {
                // Fallback to utility class
                return ExceptionMessageUtil.getMessage(exceptionCode, languageCode);
            }
        } catch (Exception e) {
            log.warn("Failed to get localized message for code: {}, using fallback", exceptionCode, e);
            return fallbackMessage;
        }
    }

    /**
     * Get localized message for an exception code using LanguageCode enum.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The LanguageCode enum
     * @param fallbackMessage The fallback message if service is unavailable
     * @return Localized message
     */
    protected String getLocalizedMessage(String exceptionCode, LanguageCode languageCode, String fallbackMessage) {
        return getLocalizedMessage(exceptionCode, languageCode.getCode(), fallbackMessage);
    }

    /**
     * Get localized message response for an exception code.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The language code
     * @return ExceptionMessageResponse
     */
    protected ExceptionMessageResponse getLocalizedMessageResponse(String exceptionCode, String languageCode) {
        try {
            if (exceptionMessageService != null) {
                return exceptionMessageService.getExceptionMessage(exceptionCode, languageCode);
            } else {
                // Fallback to utility class
                return ExceptionMessageUtil.getMessageResponse(exceptionCode, languageCode);
            }
        } catch (Exception e) {
            log.warn("Failed to get localized message response for code: {}", exceptionCode, e);
            return ExceptionMessageResponse.builder()
                    .exceptionCode(exceptionCode)
                    .languageCode(languageCode)
                    .message("An error occurred")
                    .build();
        }
    }

    /**
     * Get localized message response using LanguageCode enum.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The LanguageCode enum
     * @return ExceptionMessageResponse
     */
    protected ExceptionMessageResponse getLocalizedMessageResponse(String exceptionCode, LanguageCode languageCode) {
        return getLocalizedMessageResponse(exceptionCode, languageCode.getCode());
    }

    /**
     * Check if a localized message exists for the given exception code.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The language code
     * @return true if message exists
     */
    protected boolean hasLocalizedMessage(String exceptionCode, String languageCode) {
        try {
            if (exceptionMessageService != null) {
                return exceptionMessageService.hasExceptionMessage(exceptionCode, languageCode);
            } else {
                return ExceptionMessageUtil.hasMessage(exceptionCode, languageCode);
            }
        } catch (Exception e) {
            log.warn("Failed to check message existence for code: {}", exceptionCode, e);
            return false;
        }
    }

    /**
     * Check if a localized message exists using LanguageCode enum.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The LanguageCode enum
     * @return true if message exists
     */
    protected boolean hasLocalizedMessage(String exceptionCode, LanguageCode languageCode) {
        return hasLocalizedMessage(exceptionCode, languageCode.getCode());
    }
}
