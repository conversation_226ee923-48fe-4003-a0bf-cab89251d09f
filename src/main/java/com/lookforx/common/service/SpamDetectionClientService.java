package com.lookforx.common.service;

import com.lookforx.common.client.SpamDetectionClient;
import com.lookforx.common.dto.BulkSpamDetectionRequest;
import com.lookforx.common.dto.BulkSpamDetectionResponse;
import com.lookforx.common.dto.SpamDetectionRequest;
import com.lookforx.common.dto.SpamDetectionResponse;
import com.lookforx.common.enums.LanguageCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Map;

/**
 * Service for spam detection operations
 * Provides high-level methods for spam detection with error handling and logging
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Lazy
@ConditionalOnProperty(
    name = "lookforx.spam-detection.enabled",
    havingValue = "true",
    matchIfMissing = true
)
public class SpamDetectionClientService {

    private final SpamDetectionClient spamDetectionClient;

    /**
     * Check if spam detection service is healthy and ready
     */
    public boolean isServiceHealthy() {
        try {
            Map<String, Object> health = spamDetectionClient.health();
            Boolean modelLoaded = (Boolean) health.get("model_loaded");
            String status = (String) health.get("status");
            
            return "healthy".equals(status) && Boolean.TRUE.equals(modelLoaded);
        } catch (Exception e) {
            log.warn("Spam detection service health check failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Classify a single text for spam detection
     */
    public SpamDetectionResponse classifyText(String text, LanguageCode languageCode) {
        return classifyText(text, languageCode, 0.7);
    }

    /**
     * Classify a single text for spam detection with custom threshold
     */
    public SpamDetectionResponse classifyText(String text, LanguageCode languageCode, double threshold) {
        try {
            log.debug("Classifying text for spam detection: language={}, threshold={}", 
                     languageCode.getCode(), threshold);
            
            SpamDetectionRequest request = SpamDetectionRequest.builder()
                .text(text)
                .languageCode(languageCode.getCode())
                .threshold(threshold)
                .build();
            
            SpamDetectionResponse response = spamDetectionClient.classify(request);
            
            log.debug("Spam detection result: label={}, score={}, isSpam={}", 
                     response.getLabel(), response.getScore(), response.getIsSpam());
            
            return response;
            
        } catch (ResponseStatusException e) {
            log.error("Spam detection failed for text classification: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during spam detection: {}", e.getMessage(), e);
            throw new RuntimeException("Spam detection service error", e);
        }
    }

    /**
     * Classify multiple texts for spam detection in bulk
     */
    public BulkSpamDetectionResponse classifyTexts(List<String> texts, LanguageCode languageCode) {
        return classifyTexts(texts, languageCode, 0.7);
    }

    /**
     * Classify multiple texts for spam detection in bulk with custom threshold
     */
    public BulkSpamDetectionResponse classifyTexts(List<String> texts, LanguageCode languageCode, double threshold) {
        try {
            log.debug("Bulk classifying {} texts for spam detection: language={}, threshold={}", 
                     texts.size(), languageCode.getCode(), threshold);
            
            BulkSpamDetectionRequest request = BulkSpamDetectionRequest.builder()
                .texts(texts)
                .languageCode(languageCode.getCode())
                .threshold(threshold)
                .build();
            
            BulkSpamDetectionResponse response = spamDetectionClient.classifyBulk(request);
            
            long spamCount = response.getResults().stream()
                .mapToLong(r -> Boolean.TRUE.equals(r.getIsSpam()) ? 1 : 0)
                .sum();
            
            log.debug("Bulk spam detection completed: {} out of {} texts classified as spam", 
                     spamCount, texts.size());
            
            return response;
            
        } catch (ResponseStatusException e) {
            log.error("Bulk spam detection failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during bulk spam detection: {}", e.getMessage(), e);
            throw new RuntimeException("Spam detection service error", e);
        }
    }

    /**
     * Check if a text is spam (convenience method)
     */
    public boolean isSpam(String text, LanguageCode languageCode) {
        return isSpam(text, languageCode, 0.7);
    }

    /**
     * Check if a text is spam with custom threshold (convenience method)
     */
    public boolean isSpam(String text, LanguageCode languageCode, double threshold) {
        try {
            SpamDetectionResponse response = classifyText(text, languageCode, threshold);
            return Boolean.TRUE.equals(response.getIsSpam());
        } catch (Exception e) {
            log.warn("Failed to check spam status for text, defaulting to false: {}", e.getMessage());
            return false; // Default to not spam if service fails
        }
    }

    /**
     * Get service metrics
     */
    public Map<String, Object> getServiceMetrics() {
        try {
            return spamDetectionClient.metrics();
        } catch (Exception e) {
            log.warn("Failed to get spam detection service metrics: {}", e.getMessage());
            return Map.of("error", "Unable to fetch metrics");
        }
    }
}
