package com.lookforx.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response DTO for spam detection classification
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpamDetectionResponse {

    private String text;
    private String label;
    private Double score;

    @JsonProperty("is_spam")
    private Boolean isSpam;

    @JsonProperty("processing_time_ms")
    private Integer processingTimeMs;
}
