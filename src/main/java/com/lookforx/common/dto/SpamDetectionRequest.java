package com.lookforx.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * Request DTO for spam detection classification
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SpamDetectionRequest {

    @NotBlank(message = "Text cannot be blank")
    private String text;

    @Pattern(
        regexp = "^(EN|TR|DE|FR|ES|IT|PT|RU|AR|ZH|JA|KO|HI|BN|UR|FA|TH|VI|ID|MS|NL|SV|NO|DA|FI|PL|CS|SK|HU|RO|BG|HR|SR|SL|ET|LV|LT|EL|HE|UK|BE|KY|UZ|KM|MY|TG|AZ|HY|GA|CY|IS|MK|BS|SQ|MN|NE|PA|GL|LA)$",
        message = "Invalid language code"
    )
    @Builder.Default
    private String languageCode = "EN";

    @DecimalMin(value = "0.0", message = "Threshold must be between 0.0 and 1.0")
    @DecimalMax(value = "1.0", message = "Threshold must be between 0.0 and 1.0")
    @Builder.Default
    private Double threshold = 0.7;
}
