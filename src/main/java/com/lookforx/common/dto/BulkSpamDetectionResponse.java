package com.lookforx.common.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Response DTO for bulk spam detection classification
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkSpamDetectionResponse {

    private List<SpamDetectionResponse> results;

    @JsonProperty("total_processing_time_ms")
    private Integer totalProcessingTimeMs;
}
