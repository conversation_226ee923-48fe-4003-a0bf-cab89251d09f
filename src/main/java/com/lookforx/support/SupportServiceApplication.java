package com.lookforx.support;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.kafka.annotation.EnableKafka;

@SpringBootApplication(
    exclude = {
        org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class,
        org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration.class
    }
)
@ComponentScan(basePackages = {
    "com.lookforx.support",
    "com.lookforx.customersupportservice",
    "com.lookforx.customersupportservice",
    "com.lookforx.common"
})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.lookforx.common.client", "com.lookforx.customersupportservice.client"})
@EnableMongoAuditing
@EnableMongoRepositories(basePackages = "com.lookforx.customersupportservice.repository")
@EnableCaching
@EnableKafka
public class SupportServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(SupportServiceApplication.class, args);
    }

}
