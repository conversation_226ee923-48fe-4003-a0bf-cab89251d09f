package com.lookforx.questionformservice.domain;

import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * FormResponse is now an embedded document within FormSubmission
 * No longer a separate collection in MongoDB
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FormResponse {

    @Field("form_element_id")
    private Long formElementId;

    @Field("value")
    private String value;

    @CreatedDate
    @Field("created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Field("updated_at")
    private LocalDateTime updatedAt;
}