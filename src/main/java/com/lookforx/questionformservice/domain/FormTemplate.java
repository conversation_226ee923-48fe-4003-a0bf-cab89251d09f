package com.lookforx.questionformservice.domain;

import com.lookforx.common.entity.BaseEntity;
import com.lookforx.common.enums.LanguageCode;
import jakarta.persistence.*;
import lombok.*;

import java.util.*;

@Entity
@Table(name = "form_templates")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormTemplate extends BaseEntity {
    
    @Column(nullable = false)
    private Long categoryId;
    
    // Çoklu dil desteği için isim çev<PERSON>leri
    @ElementCollection
    @CollectionTable(
            name = "form_template_name_translations",
            joinColumns = @JoinColumn(name = "form_template_id"),
            indexes = {
                    @Index(name = "idx_form_template_name_language", columnList = "language_code")
            }
    )
    @MapKeyColumn(name = "language_code")
    @MapKeyEnumerated(EnumType.STRING)
    @Column(name = "name", nullable = false)
    @Builder.Default
    private Map<LanguageCode, String> nameTranslations = new HashMap<>();
    
    // Çoklu dil desteği için açıklama çevirileri
    @ElementCollection
    @CollectionTable(
            name = "form_template_description_translations",
            joinColumns = @JoinColumn(name = "form_template_id"),
            indexes = {
                    @Index(name = "idx_form_template_desc_language", columnList = "language_code")
            }
    )
    @MapKeyColumn(name = "language_code")
    @MapKeyEnumerated(EnumType.STRING)
    @Column(name = "description")
    @Builder.Default
    private Map<LanguageCode, String> descriptionTranslations = new HashMap<>();
    
    @OneToMany(mappedBy = "formTemplate", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("displayOrder ASC")
    @Builder.Default
    private List<FormElement> elements = new ArrayList<>();
    
    @Column(nullable = false)
    private boolean active = true;
    
    // Helper method to add element
    public void addElement(FormElement element) {
        elements.add(element);
        element.setFormTemplate(this);
    }
    
    // Helper method to remove element
    public void removeElement(FormElement element) {
        elements.remove(element);
        element.setFormTemplate(null);
    }
    
    // Helper method to get name in specific language with fallback
    public String getName(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String name = nameTranslations.get(languageCode);
        if (name == null || name.isEmpty()) {
            name = nameTranslations.get(fallbackLanguageCode);
        }
        return name;
    }
    
    // Helper method to get description in specific language with fallback
    public String getDescription(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String description = descriptionTranslations.get(languageCode);
        if (description == null || description.isEmpty()) {
            description = descriptionTranslations.get(fallbackLanguageCode);
        }
        return description;
    }
}
