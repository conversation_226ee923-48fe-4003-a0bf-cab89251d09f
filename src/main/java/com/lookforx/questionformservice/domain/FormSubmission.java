package com.lookforx.questionformservice.domain;

import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Document(collection = "form_submissions")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FormSubmission {

    @Id
    private String id;

    @Field("form_template_id")
    @Indexed
    private Long formTemplateId;

    @Field("category_id")
    @Indexed
    private Long categoryId;

    @Field("user_id")
    @Indexed
    private Long userId;

    @Field("request_id")
    @Indexed(unique = true)
    private String requestId;

    @Field("submitted_by")
    private String submittedBy;

    @Field("responses")
    @Builder.Default
    private List<FormResponse> responses = new ArrayList<>();

    @CreatedDate
    @Field("created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Field("updated_at")
    private LocalDateTime updatedAt;
}