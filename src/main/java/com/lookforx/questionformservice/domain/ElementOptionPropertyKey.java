package com.lookforx.questionformservice.domain;

import com.lookforx.common.enums.LanguageCode;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ElementOptionPropertyKey implements Serializable {
    
    @Column(name = "property_key", nullable = false)
    private String key;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "language_code", nullable = false)
    private LanguageCode languageCode;
    
    // Factory method to create a new key
    public static ElementOptionPropertyKey of(String key, LanguageCode languageCode) {
        return new ElementOptionPropertyKey(key, languageCode);
    }
}