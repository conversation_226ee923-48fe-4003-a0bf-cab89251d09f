package com.lookforx.questionformservice.domain;

import com.lookforx.common.entity.BaseEntity;
import com.lookforx.common.enums.LanguageCode;
import jakarta.persistence.*;
import lombok.*;

import java.util.*;

@Entity
@Table(name = "form_elements")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormElement extends BaseEntity {
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "form_template_id", nullable = false)
    private FormTemplate formTemplate;
    
    // Çoklu dil desteği için etiket çevirileri
    @ElementCollection
    @CollectionTable(
            name = "form_element_label_translations",
            joinColumns = @JoinColumn(name = "form_element_id"),
            indexes = {
                    @Index(name = "idx_form_element_label_language", columnList = "language_code")
            }
    )
    @MapKeyColumn(name = "language_code")
    @MapKeyEnumerated(EnumType.STRING)
    @Column(name = "label", nullable = false)
    private Map<LanguageCode, String> labelTranslations = new HashMap<>();
    
    // Çoklu dil desteği için açıklama çevirileri
    @ElementCollection
    @CollectionTable(
            name = "form_element_description_translations",
            joinColumns = @JoinColumn(name = "form_element_id"),
            indexes = {
                    @Index(name = "idx_form_element_desc_language", columnList = "language_code")
            }
    )
    @MapKeyColumn(name = "language_code")
    @MapKeyEnumerated(EnumType.STRING)
    @Column(name = "description")
    private Map<LanguageCode, String> descriptionTranslations = new HashMap<>();
    
    // Çoklu dil desteği için placeholder çevirileri
    @ElementCollection
    @CollectionTable(
            name = "form_element_placeholder_translations",
            joinColumns = @JoinColumn(name = "form_element_id"),
            indexes = {
                    @Index(name = "idx_form_element_placeholder_language", columnList = "language_code")
            }
    )
    @MapKeyColumn(name = "language_code")
    @MapKeyEnumerated(EnumType.STRING)
    @Column(name = "placeholder")
    private Map<LanguageCode, String> placeholderTranslations = new HashMap<>();
    
    // Çoklu dil desteği için yardım metni çevirileri
    @ElementCollection
    @CollectionTable(
            name = "form_element_help_text_translations",
            joinColumns = @JoinColumn(name = "form_element_id"),
            indexes = {
                    @Index(name = "idx_form_element_help_text_language", columnList = "language_code")
            }
    )
    @MapKeyColumn(name = "language_code")
    @MapKeyEnumerated(EnumType.STRING)
    @Column(name = "help_text")
    @Builder.Default
    private Map<LanguageCode, String> helpTextTranslations = new HashMap<>();
    
    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private ElementType type;
    
    @Column(nullable = false)
    private boolean required;
    
    @Column(nullable = false)
    private int displayOrder;
    
    @OneToMany(mappedBy = "formElement", cascade = CascadeType.ALL, orphanRemoval = true)
    @OrderBy("displayOrder ASC")
    @Builder.Default
    private List<ElementOption> options = new ArrayList<>();
    
    // Validasyon kuralları
    private String validationRegex;
    private Integer minLength;
    private Integer maxLength;
    private Double minValue;
    private Double maxValue;
    
    // Özel özellikler
    @ElementCollection
    @CollectionTable(name = "form_element_properties", joinColumns = @JoinColumn(name = "form_element_id"))
    @MapKeyColumn(name = "property_key")
    @Column(name = "property_value")
    @Builder.Default
    private Map<String, String> properties = new HashMap<>();
    

    
    // Helper method to add option
    public void addOption(ElementOption option) {
        options.add(option);
        option.setFormElement(this);
    }
    
    // Helper method to remove option
    public void removeOption(ElementOption option) {
        options.remove(option);
        option.setFormElement(null);
    }
    
    // Helper method to get label in specific language with fallback
    public String getLabel(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String label = labelTranslations.get(languageCode);
        if (label == null || label.isEmpty()) {
            label = labelTranslations.get(fallbackLanguageCode);
        }
        return label;
    }
    
    // Helper method to get description in specific language with fallback
    public String getDescription(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String description = descriptionTranslations.get(languageCode);
        if (description == null || description.isEmpty()) {
            description = descriptionTranslations.get(fallbackLanguageCode);
        }
        return description;
    }
    
    // Helper method to get placeholder in specific language with fallback
    public String getPlaceholder(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String placeholder = placeholderTranslations.get(languageCode);
        if (placeholder == null || placeholder.isEmpty()) {
            placeholder = placeholderTranslations.get(fallbackLanguageCode);
        }
        return placeholder;
    }
    
    // Helper method to get help text in specific language with fallback
    public String getHelpText(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String helpText = helpTextTranslations.get(languageCode);
        if (helpText == null || helpText.isEmpty()) {
            helpText = helpTextTranslations.get(fallbackLanguageCode);
        }
        return helpText;
    }
}
