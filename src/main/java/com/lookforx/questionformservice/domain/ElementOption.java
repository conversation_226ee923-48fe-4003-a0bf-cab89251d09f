package com.lookforx.questionformservice.domain;

import com.lookforx.common.enums.LanguageCode;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Entity
@Table(name = "element_options")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ElementOption {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "form_element_id", nullable = false)
    private FormElement formElement;
    
    @Column(nullable = false)
    private String value;
    
    // Çoklu dil desteği için etiket çevirileri
    @ElementCollection
    @CollectionTable(
            name = "element_option_label_translations",
            joinColumns = @JoinColumn(name = "element_option_id"),
            indexes = {
                    @Index(name = "idx_element_option_label_language", columnList = "language_code")
            }
    )
    @MapKeyColumn(name = "language_code")
    @MapKeyEnumerated(EnumType.STRING)
    @Column(name = "label", nullable = false)
    @Builder.Default
    private Map<LanguageCode, String> labelTranslations = new HashMap<>();
    
    @Column(nullable = false)
    private int displayOrder;
    
    @Column(nullable = false)
    private boolean defaultSelected;
    
    // Özel özellikler - çok dilli
    @ElementCollection
    @CollectionTable(
            name = "element_option_property_translations",
            joinColumns = @JoinColumn(name = "element_option_id"),
            indexes = {
                    @Index(name = "idx_element_option_property", columnList = "property_key,language_code")
            }
    )
    @MapKeyClass(ElementOptionPropertyKey.class)
    @Builder.Default
    private Map<ElementOptionPropertyKey, String> propertyTranslations = new HashMap<>();
    
    @Column(nullable = false)
    private LocalDateTime createdAt;
    
    private LocalDateTime updatedAt;
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Helper method to get label in specific language with fallback
    public String getLabel(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String label = labelTranslations.get(languageCode);
        if (label == null || label.isEmpty()) {
            label = labelTranslations.get(fallbackLanguageCode);
        }
        return label;
    }
    
    // Helper method to set a property value for a specific language
    public void setPropertyValue(String propertyKey, LanguageCode languageCode, String value) {
        propertyTranslations.put(ElementOptionPropertyKey.of(propertyKey, languageCode), value);
    }
    
    // Helper method to get a property value for a specific language with fallback
    public String getPropertyValue(String propertyKey, LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String value = propertyTranslations.get(ElementOptionPropertyKey.of(propertyKey, languageCode));
        if (value == null || value.isEmpty()) {
            value = propertyTranslations.get(ElementOptionPropertyKey.of(propertyKey, fallbackLanguageCode));
        }
        return value;
    }
    
    // Helper method to get all property keys (distinct)
    public Set<String> getPropertyKeys() {
        return propertyTranslations.keySet().stream()
                .map(ElementOptionPropertyKey::getKey)
                .collect(Collectors.toSet());
    }
    
    // Helper method to get all values for a specific property key across all languages
    public Map<LanguageCode, String> getPropertyTranslations(String propertyKey) {
        Map<LanguageCode, String> translations = new HashMap<>();
        propertyTranslations.forEach((key, value) -> {
            if (key.getKey().equals(propertyKey)) {
                translations.put(key.getLanguageCode(), value);
            }
        });
        return translations;
    }
}
