package com.lookforx.questionformservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(
        scanBasePackages = {
                "com.lookforx.questionformservice",
                "com.lookforx.common"
        }
)
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.lookforx.common.client")
public class QuestionformServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(QuestionformServiceApplication.class, args);
    }

}
