package com.lookforx.questionformservice.mapper;

import com.lookforx.questionformservice.domain.ElementOption;
import com.lookforx.questionformservice.domain.ElementOptionPropertyKey;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.questionformservice.dto.ElementOptionDTO;
import com.lookforx.questionformservice.dto.LocalizedElementOptionDTO;
import com.lookforx.questionformservice.dto.request.CreateElementOptionRequest;
import com.lookforx.questionformservice.dto.request.UpdateElementOptionRequest;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class ElementOptionMapper {
    
    public ElementOption toEntity(CreateElementOptionRequest request) {
        ElementOption option = ElementOption.builder()
                .value(request.getValue())
                .labelTranslations(request.getLabelTranslations())
                .displayOrder(request.getDisplayOrder())
                .defaultSelected(request.isDefaultSelected())
                .build();
        
        // Çok dilli özellikleri dönüştür
        if (request.getPropertyTranslations() != null) {
            request.getPropertyTranslations().forEach((propertyKey, translations) -> {
                translations.forEach((languageCode, value) -> {
                    option.setPropertyValue(propertyKey, languageCode, value);
                });
            });
        }
        
        return option;
    }
    
    public ElementOption toEntity(UpdateElementOptionRequest request) {
        ElementOption option = ElementOption.builder()
                .id(request.getId())
                .value(request.getValue())
                .labelTranslations(request.getLabelTranslations())
                .displayOrder(request.getDisplayOrder())
                .defaultSelected(request.isDefaultSelected())
                .build();
        
        // Çok dilli özellikleri dönüştür
        if (request.getPropertyTranslations() != null) {
            request.getPropertyTranslations().forEach((propertyKey, translations) -> {
                translations.forEach((languageCode, value) -> {
                    option.setPropertyValue(propertyKey, languageCode, value);
                });
            });
        }
        
        return option;
    }
    
    public ElementOptionDTO toDTO(ElementOption option) {
        ElementOptionDTO dto = ElementOptionDTO.builder()
                .id(option.getId())
                .value(option.getValue())
                .labelTranslations(option.getLabelTranslations())
                .displayOrder(option.getDisplayOrder())
                .defaultSelected(option.isDefaultSelected())
                .createdAt(option.getCreatedAt())
                .updatedAt(option.getUpdatedAt())
                .build();
        
        // Çok dilli özellikleri dönüştür
        Map<String, Map<LanguageCode, String>> propertyTranslations = new HashMap<>();
        
        // Tüm özellik anahtarlarını al
        option.getPropertyKeys().forEach(propertyKey -> {
            propertyTranslations.put(propertyKey, option.getPropertyTranslations(propertyKey));
        });
        
        dto.setPropertyTranslations(propertyTranslations);
        
        return dto;
    }

    /**
     * Convert ElementOption to LocalizedElementOptionDTO with specific language content
     */
    public LocalizedElementOptionDTO toLocalizedDTO(ElementOption option, LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String label = option.getLabel(languageCode, fallbackLanguageCode);

        // Check if fallback was used
        boolean labelFromFallback = option.getLabelTranslations().get(languageCode) == null ||
                                   option.getLabelTranslations().get(languageCode).isEmpty();

        // Get localized properties
        Map<String, String> localizedProperties = new HashMap<>();
        option.getPropertyKeys().forEach(propertyKey -> {
            Map<LanguageCode, String> propertyTranslations = option.getPropertyTranslations(propertyKey);
            String propertyValue = propertyTranslations.get(languageCode);
            if (propertyValue == null || propertyValue.isEmpty()) {
                propertyValue = propertyTranslations.get(fallbackLanguageCode);
            }
            if (propertyValue != null) {
                localizedProperties.put(propertyKey, propertyValue);
            }
        });

        return LocalizedElementOptionDTO.builder()
                .id(option.getId())
                .value(option.getValue())
                .label(label)
                .displayOrder(option.getDisplayOrder())
                .defaultSelected(option.isDefaultSelected())
                .properties(localizedProperties)
                .createdAt(option.getCreatedAt())
                .updatedAt(option.getUpdatedAt())
                .languageCode(languageCode)
                .fallbackLanguageCode(fallbackLanguageCode)
                .labelFromFallback(labelFromFallback)
                .build();
    }

    // Mevcut bir entity'yi güncelleme metodu
    public void updateEntityFromRequest(ElementOption option, UpdateElementOptionRequest request) {
        option.setValue(request.getValue());
        option.setLabelTranslations(request.getLabelTranslations());
        option.setDisplayOrder(request.getDisplayOrder());
        option.setDefaultSelected(request.isDefaultSelected());
        
        // Mevcut özellikleri temizle
        option.getPropertyTranslations().clear();
        
        // Yeni özellikleri ekle
        if (request.getPropertyTranslations() != null) {
            request.getPropertyTranslations().forEach((propertyKey, translations) -> {
                translations.forEach((languageCode, value) -> {
                    option.setPropertyValue(propertyKey, languageCode, value);
                });
            });
        }
    }
}
