package com.lookforx.questionformservice.mapper;

import com.lookforx.questionformservice.domain.FormTemplate;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.questionformservice.dto.FormTemplateDTO;
import com.lookforx.questionformservice.dto.LocalizedFormTemplateDTO;
import com.lookforx.questionformservice.dto.request.CreateFormTemplateRequest;
import com.lookforx.questionformservice.dto.request.UpdateFormTemplateRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class FormTemplateMapper {
    
    private final FormElementMapper formElementMapper;
    
    public FormTemplate toEntity(CreateFormTemplateRequest request) {
        FormTemplate template = FormTemplate.builder()
                .nameTranslations(request.getNameTranslations())
                .categoryId(request.getCategoryId())
                .descriptionTranslations(request.getDescriptionTranslations())
                .active(request.isActive())
                .build();

        request.getElements().forEach(elementRequest -> {
            template.addElement(formElementMapper.toEntity(elementRequest));
        });

        return template;
    }
    
    public void updateEntity(FormTemplate template, UpdateFormTemplateRequest request) {
        template.setNameTranslations(request.getNameTranslations());
        template.setCategoryId(request.getCategoryId());
        template.setDescriptionTranslations(request.getDescriptionTranslations());
        template.setActive(request.isActive());

        // Clear existing elements and add new ones
        template.getElements().clear();
        request.getElements().forEach(elementRequest -> {
            template.addElement(formElementMapper.toEntity(elementRequest));
        });
    }
    
    public FormTemplateDTO toDTO(FormTemplate template) {
        return FormTemplateDTO.builder()
                .id(template.getId())
                .nameTranslations(template.getNameTranslations())
                .categoryId(template.getCategoryId())
                .descriptionTranslations(template.getDescriptionTranslations())
                .elements(template.getElements().stream()
                        .map(formElementMapper::toDTO)
                        .collect(Collectors.toList()))
                .active(template.isActive())
                .createdBy(template.getCreatedBy())
                .createdAt(template.getCreatedAt())
                .updatedAt(template.getUpdatedAt())
                .updatedBy(template.getUpdatedBy())
                .build();
    }

    /**
     * Convert FormTemplate to LocalizedFormTemplateDTO with specific language content
     */
    public LocalizedFormTemplateDTO toLocalizedDTO(FormTemplate template, LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String name = template.getName(languageCode, fallbackLanguageCode);
        String description = template.getDescription(languageCode, fallbackLanguageCode);

        // Check if fallback was used
        boolean nameFromFallback = template.getNameTranslations().get(languageCode) == null ||
                                  template.getNameTranslations().get(languageCode).isEmpty();
        boolean descriptionFromFallback = template.getDescriptionTranslations().get(languageCode) == null ||
                                         template.getDescriptionTranslations().get(languageCode).isEmpty();

        return LocalizedFormTemplateDTO.builder()
                .id(template.getId())
                .name(name)
                .categoryId(template.getCategoryId())
                .description(description)
                .elements(template.getElements().stream()
                        .map(element -> formElementMapper.toLocalizedDTO(element, languageCode, fallbackLanguageCode))
                        .collect(Collectors.toList()))
                .active(template.isActive())
                .createdBy(template.getCreatedBy())
                .createdAt(template.getCreatedAt())
                .updatedAt(template.getUpdatedAt())
                .updatedBy(template.getUpdatedBy())
                .languageCode(languageCode)
                .fallbackLanguageCode(fallbackLanguageCode)
                .nameFromFallback(nameFromFallback)
                .descriptionFromFallback(descriptionFromFallback)
                .build();
    }
}