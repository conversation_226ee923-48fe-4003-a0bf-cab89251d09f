package com.lookforx.questionformservice.mapper;

import com.lookforx.questionformservice.domain.FormSubmission;
import com.lookforx.questionformservice.dto.FormSubmissionDTO;
import com.lookforx.questionformservice.dto.request.SubmitFormRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class FormSubmissionMapper {
    
    private final FormResponseMapper responseMapper;
    
    public FormSubmission toEntity(SubmitFormRequest request) {
        return FormSubmission.builder()
                .categoryId(request.getCategoryId())
                .userId(request.getUserId())
                .requestId(request.getRequestId())
                .build();
    }

    public FormSubmissionDTO toDTO(FormSubmission submission) {
        return FormSubmissionDTO.builder()
                .id(submission.getId())
                .formTemplateId(submission.getFormTemplateId())
                .categoryId(submission.getCategoryId())
                .userId(submission.getUserId())
                .requestId(submission.getRequestId())
                .submittedBy(submission.getSubmittedBy())
                .responses(submission.getResponses().stream()
                        .map(responseMapper::toDTO)
                        .collect(Collectors.toList()))
                .createdAt(submission.getCreatedAt())
                .updatedAt(submission.getUpdatedAt())
                .build();
    }
}