package com.lookforx.questionformservice.mapper;

import com.lookforx.questionformservice.domain.ElementOption;
import com.lookforx.common.enums.LanguageCode;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ElementOptionDTO {
    private Long id;
    private String value;
    private Map<LanguageCode, String> labelTranslations = new HashMap<>();
    private Integer displayOrder;
    private boolean defaultSelected;
    private Map<String, String> properties = new HashMap<>();
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Helper method to get label in specific language with fallback
    public String getLabel(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String label = labelTranslations.get(languageCode);
        if (label == null || label.isEmpty()) {
            label = labelTranslations.get(fallbackLanguageCode);
        }
        return label;
    }
}