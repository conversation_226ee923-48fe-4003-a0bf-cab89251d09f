package com.lookforx.questionformservice.mapper;

import com.lookforx.questionformservice.domain.FormResponse;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.questionformservice.dto.FormResponseDTO;
import com.lookforx.questionformservice.dto.request.FormResponseRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FormResponseMapper {
    
    // Varsayılan dil kodu - fallback olarak kullanıla<PERSON>k
    private final LanguageCode DEFAULT_LANGUAGE = LanguageCode.EN;
    
    public FormResponse toEntity(FormResponseRequest request) {
        return FormResponse.builder()
                .formElementId(request.getFormElementId())
                .value(request.getValue())
                .build();
    }

    public FormResponseDTO toDTO(FormResponse response) {
        return FormResponseDTO.builder()
                .formElementId(response.getFormElementId())
                .value(response.getValue())
                .createdAt(response.getCreatedAt())
                .updatedAt(response.getUpdatedAt())
                .build();
    }

    // FormElement bilgisi ile birlikte DTO oluşturan metod
    public FormResponseDTO toDTOWithElement(FormResponse response, String elementLabel) {
        return FormResponseDTO.builder()
                .formElementId(response.getFormElementId())
                .elementLabel(elementLabel)
                .value(response.getValue())
                .createdAt(response.getCreatedAt())
                .updatedAt(response.getUpdatedAt())
                .build();
    }
}
