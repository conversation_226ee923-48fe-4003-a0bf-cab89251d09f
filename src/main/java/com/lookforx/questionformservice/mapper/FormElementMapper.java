package com.lookforx.questionformservice.mapper;

import com.lookforx.questionformservice.domain.FormElement;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.questionformservice.dto.FormElementDTO;
import com.lookforx.questionformservice.dto.LocalizedFormElementDTO;
import com.lookforx.questionformservice.dto.request.CreateFormElementRequest;
import com.lookforx.questionformservice.dto.request.UpdateFormElementRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class FormElementMapper {
    
    private final ElementOptionMapper optionMapper;
    
    public FormElement toEntity(CreateFormElementRequest request) {
        FormElement element = FormElement.builder()
                .labelTranslations(request.getLabelTranslations())
                .descriptionTranslations(request.getDescriptionTranslations())
                .placeholderTranslations(request.getPlaceholderTranslations())
                .helpTextTranslations(request.getHelpTextTranslations())
                .type(request.getType())
                .displayOrder(request.getDisplayOrder())
                .required(request.isRequired())
                .validationRegex(request.getValidationRegex())
                .minLength(request.getMinLength())
                .maxLength(request.getMaxLength())
                .minValue(request.getMinValue())
                .maxValue(request.getMaxValue())
                .properties(request.getProperties())
                .build();
        
        request.getOptions().forEach(optionRequest -> {
            element.addOption(optionMapper.toEntity(optionRequest));
        });
        
        return element;
    }
    
    public FormElement toEntity(UpdateFormElementRequest request) {
        FormElement element = FormElement.builder()
                .labelTranslations(request.getLabelTranslations())
                .descriptionTranslations(request.getDescriptionTranslations())
                .placeholderTranslations(request.getPlaceholderTranslations())
                .helpTextTranslations(request.getHelpTextTranslations())
                .type(request.getType())
                .displayOrder(request.getDisplayOrder())
                .required(request.isRequired())
                .validationRegex(request.getValidationRegex())
                .minLength(request.getMinLength())
                .maxLength(request.getMaxLength())
                .minValue(request.getMinValue())
                .maxValue(request.getMaxValue())
                .properties(request.getProperties())
                .build();
        element.setId(request.getId());
        
        request.getOptions().forEach(optionRequest -> {
            element.addOption(optionMapper.toEntity(optionRequest));
        });
        
        return element;
    }
    
    public FormElementDTO toDTO(FormElement element) {
        return FormElementDTO.builder()
                .id(element.getId())
                .labelTranslations(element.getLabelTranslations())
                .descriptionTranslations(element.getDescriptionTranslations())
                .placeholderTranslations(element.getPlaceholderTranslations())
                .helpTextTranslations(element.getHelpTextTranslations())
                .type(element.getType())
                .displayOrder(element.getDisplayOrder())
                .required(element.isRequired())
                .validationRegex(element.getValidationRegex())
                .minLength(element.getMinLength())
                .maxLength(element.getMaxLength())
                .minValue(element.getMinValue())
                .maxValue(element.getMaxValue())
                .properties(element.getProperties())
                .options(element.getOptions().stream()
                        .map(optionMapper::toDTO)
                        .collect(Collectors.toList()))
                .createdAt(element.getCreatedAt())
                .updatedAt(element.getUpdatedAt())
                .build();
    }

    /**
     * Convert FormElement to LocalizedFormElementDTO with specific language content
     */
    public LocalizedFormElementDTO toLocalizedDTO(FormElement element, LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String label = element.getLabel(languageCode, fallbackLanguageCode);
        String description = element.getDescription(languageCode, fallbackLanguageCode);
        String placeholder = element.getPlaceholder(languageCode, fallbackLanguageCode);
        String helpText = element.getHelpText(languageCode, fallbackLanguageCode);

        // Check if fallback was used
        boolean labelFromFallback = element.getLabelTranslations().get(languageCode) == null ||
                                   element.getLabelTranslations().get(languageCode).isEmpty();
        boolean descriptionFromFallback = element.getDescriptionTranslations().get(languageCode) == null ||
                                         element.getDescriptionTranslations().get(languageCode).isEmpty();
        boolean placeholderFromFallback = element.getPlaceholderTranslations().get(languageCode) == null ||
                                         element.getPlaceholderTranslations().get(languageCode).isEmpty();
        boolean helpTextFromFallback = element.getHelpTextTranslations().get(languageCode) == null ||
                                      element.getHelpTextTranslations().get(languageCode).isEmpty();

        return LocalizedFormElementDTO.builder()
                .id(element.getId())
                .label(label)
                .description(description)
                .placeholder(placeholder)
                .helpText(helpText)
                .type(element.getType())
                .displayOrder(element.getDisplayOrder())
                .required(element.isRequired())
                .validationRegex(element.getValidationRegex())
                .minLength(element.getMinLength())
                .maxLength(element.getMaxLength())
                .minValue(element.getMinValue())
                .maxValue(element.getMaxValue())
                .properties(element.getProperties())
                .options(element.getOptions().stream()
                        .map(option -> optionMapper.toLocalizedDTO(option, languageCode, fallbackLanguageCode))
                        .collect(Collectors.toList()))
                .createdAt(element.getCreatedAt())
                .updatedAt(element.getUpdatedAt())
                .languageCode(languageCode)
                .fallbackLanguageCode(fallbackLanguageCode)
                .labelFromFallback(labelFromFallback)
                .descriptionFromFallback(descriptionFromFallback)
                .placeholderFromFallback(placeholderFromFallback)
                .helpTextFromFallback(helpTextFromFallback)
                .build();
    }

    // Mevcut bir entity'yi güncellemek için yardımcı metod
    public void updateEntityFromRequest(FormElement element, UpdateFormElementRequest request) {
        element.setLabelTranslations(request.getLabelTranslations());
        element.setDescriptionTranslations(request.getDescriptionTranslations());
        element.setPlaceholderTranslations(request.getPlaceholderTranslations());
        element.setHelpTextTranslations(request.getHelpTextTranslations());
        element.setType(request.getType());
        element.setDisplayOrder(request.getDisplayOrder());
        element.setRequired(request.isRequired());
        element.setValidationRegex(request.getValidationRegex());
        element.setMinLength(request.getMinLength());
        element.setMaxLength(request.getMaxLength());
        element.setMinValue(request.getMinValue());
        element.setMaxValue(request.getMaxValue());
        element.setProperties(request.getProperties());
        
        // Mevcut seçenekleri temizle ve yenilerini ekle
        element.getOptions().clear();
        request.getOptions().forEach(optionRequest -> {
            element.addOption(optionMapper.toEntity(optionRequest));
        });
    }
}
