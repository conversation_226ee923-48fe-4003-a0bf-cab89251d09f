package com.lookforx.questionformservice.controller;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.questionformservice.dto.FormTemplateDTO;
import com.lookforx.questionformservice.dto.LocalizedFormTemplateDTO;
import com.lookforx.questionformservice.dto.request.CreateFormTemplateRequest;
import com.lookforx.questionformservice.dto.request.UpdateFormTemplateRequest;
import com.lookforx.questionformservice.service.FormTemplateService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/form-templates")
@RequiredArgsConstructor
public class FormTemplateController {
    
    private final FormTemplateService formTemplateService;
    
    @GetMapping
    public ResponseEntity<?> getAllFormTemplates(
            @RequestParam(required = false, defaultValue = "0") int page,
            @RequestParam(required = false, defaultValue = "10") int size,
            @RequestParam(required = false, defaultValue = "id") String sortBy,
            @RequestParam(required = false, defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Boolean active,
            @RequestParam(required = false, defaultValue = "false") boolean unpaged) {

        if (unpaged) {
            // Return all forms without pagination for backward compatibility
            return ResponseEntity.ok(formTemplateService.getAllFormTemplates());
        }

        // Create pageable with sorting
        Sort sort = sortDir.equalsIgnoreCase("desc") ?
            Sort.by(sortBy).descending() :
            Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        // Use search and filter service method
        Page<FormTemplateDTO> result = formTemplateService.searchFormTemplatesWithPagination(
            search, categoryId, active, pageable);

        return ResponseEntity.ok(result);
    }

    @GetMapping("/paged")
    @Deprecated
    public ResponseEntity<Page<FormTemplateDTO>> getAllFormTemplatesPaged(Pageable pageable) {
        return ResponseEntity.ok(formTemplateService.getAllFormTemplates(pageable));
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<FormTemplateDTO> getFormTemplateById(@PathVariable Long id) {
        return ResponseEntity.ok(formTemplateService.getFormTemplateById(id));
    }
    
    @GetMapping("/category/{categoryId}")
    public ResponseEntity<List<FormTemplateDTO>> getFormTemplatesByCategoryId(@PathVariable Long categoryId) {
        return ResponseEntity.ok(formTemplateService.getFormTemplatesByCategoryId(categoryId));
    }
    
    @PostMapping
    public ResponseEntity<FormTemplateDTO> createFormTemplate(@Valid @RequestBody CreateFormTemplateRequest request) {
        return new ResponseEntity<>(formTemplateService.createFormTemplate(request), HttpStatus.CREATED);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<FormTemplateDTO> updateFormTemplate(
            @PathVariable Long id,
            @Valid @RequestBody UpdateFormTemplateRequest request) {
        return ResponseEntity.ok(formTemplateService.updateFormTemplate(id, request));
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteFormTemplate(@PathVariable Long id) {
        formTemplateService.deleteFormTemplate(id);
        return ResponseEntity.noContent().build();
    }
    
    @PatchMapping("/{id}/activate")
    public ResponseEntity<FormTemplateDTO> activateFormTemplate(@PathVariable Long id) {
        return ResponseEntity.ok(formTemplateService.activateFormTemplate(id));
    }
    
    @PatchMapping("/{id}/deactivate")
    public ResponseEntity<FormTemplateDTO> deactivateFormTemplate(@PathVariable Long id) {
        return ResponseEntity.ok(formTemplateService.deactivateFormTemplate(id));
    }

    // Language-aware endpoints

    /**
     * Get all form templates with localized content
     * @param languageCode The preferred language code
     * @param fallbackLanguageCode Optional fallback language code (defaults to EN)
     */
    @GetMapping("/localized")
    public ResponseEntity<List<LocalizedFormTemplateDTO>> getAllFormTemplatesLocalized(
            @RequestParam LanguageCode languageCode,
            @RequestParam(required = false, defaultValue = "EN") LanguageCode fallbackLanguageCode) {
        return ResponseEntity.ok(formTemplateService.getAllFormTemplatesLocalized(languageCode, fallbackLanguageCode));
    }

    /**
     * Get form template by ID with localized content
     * @param id Form template ID
     * @param languageCode The preferred language code
     * @param fallbackLanguageCode Optional fallback language code (defaults to EN)
     */
    @GetMapping("/{id}/localized")
    public ResponseEntity<LocalizedFormTemplateDTO> getFormTemplateByIdLocalized(
            @PathVariable Long id,
            @RequestParam LanguageCode languageCode,
            @RequestParam(required = false, defaultValue = "EN") LanguageCode fallbackLanguageCode) {
        return ResponseEntity.ok(formTemplateService.getFormTemplateByIdLocalized(id, languageCode, fallbackLanguageCode));
    }

    /**
     * Get form templates by category with localized content
     * @param categoryId Category ID
     * @param languageCode The preferred language code
     * @param fallbackLanguageCode Optional fallback language code (defaults to EN)
     */
    @GetMapping("/category/{categoryId}/localized")
    public ResponseEntity<List<LocalizedFormTemplateDTO>> getFormTemplatesByCategoryIdLocalized(
            @PathVariable Long categoryId,
            @RequestParam LanguageCode languageCode,
            @RequestParam(required = false, defaultValue = "EN") LanguageCode fallbackLanguageCode) {
        return ResponseEntity.ok(formTemplateService.getFormTemplatesByCategoryIdLocalized(categoryId, languageCode, fallbackLanguageCode));
    }

    /**
     * Search form templates by name in specific language
     * @param searchTerm Search term
     * @param languageCode The preferred language code
     * @param fallbackLanguageCode Optional fallback language code (defaults to EN)
     */
    @GetMapping("/search/name")
    public ResponseEntity<List<LocalizedFormTemplateDTO>> searchFormTemplatesByName(
            @RequestParam String searchTerm,
            @RequestParam LanguageCode languageCode,
            @RequestParam(required = false, defaultValue = "EN") LanguageCode fallbackLanguageCode) {
        return ResponseEntity.ok(formTemplateService.searchFormTemplates(searchTerm, languageCode, fallbackLanguageCode));
    }

    /**
     * Search form templates by name or description in specific language
     * @param searchTerm Search term
     * @param languageCode The preferred language code
     * @param fallbackLanguageCode Optional fallback language code (defaults to EN)
     */
    @GetMapping("/search")
    public ResponseEntity<List<LocalizedFormTemplateDTO>> searchFormTemplates(
            @RequestParam String searchTerm,
            @RequestParam LanguageCode languageCode,
            @RequestParam(required = false, defaultValue = "EN") LanguageCode fallbackLanguageCode) {
        return ResponseEntity.ok(formTemplateService.searchFormTemplates(searchTerm, languageCode, fallbackLanguageCode));
    }

    /**
     * Get form templates that have content in specific language
     * @param languageCode The language code to filter by
     */
    @GetMapping("/by-language/{languageCode}")
    public ResponseEntity<List<LocalizedFormTemplateDTO>> getFormTemplatesByLanguage(
            @PathVariable LanguageCode languageCode) {
        return ResponseEntity.ok(formTemplateService.getAllFormTemplatesLocalized(languageCode));
    }
}