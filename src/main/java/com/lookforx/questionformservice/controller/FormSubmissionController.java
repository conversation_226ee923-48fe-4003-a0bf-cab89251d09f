package com.lookforx.questionformservice.controller;

import com.lookforx.questionformservice.dto.FormSubmissionDTO;
import com.lookforx.questionformservice.dto.request.SubmitFormRequest;
import com.lookforx.questionformservice.service.FormSubmissionService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/form-submissions")
@RequiredArgsConstructor
public class FormSubmissionController {
    
    private final FormSubmissionService formSubmissionService;
    
    @GetMapping
    public ResponseEntity<?> getAllFormSubmissions(
            @RequestParam(required = false, defaultValue = "0") int page,
            @RequestParam(required = false, defaultValue = "20") int size,
            @RequestParam(required = false, defaultValue = "createdAt") String sortBy,
            @RequestParam(required = false, defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) Long formTemplateId,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false, defaultValue = "false") boolean unpaged) {

        if (unpaged) {
            // Return all submissions without pagination for backward compatibility
            return ResponseEntity.ok(formSubmissionService.getAllFormSubmissions());
        }

        // Create pageable with sorting
        Sort sort = sortDir.equalsIgnoreCase("desc") ?
            Sort.by(sortBy).descending() :
            Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        // Use search and filter service method
        Page<FormSubmissionDTO> result = formSubmissionService.searchFormSubmissions(
            search, categoryId, formTemplateId, userId, pageable);

        return ResponseEntity.ok(result);
    }

    @GetMapping("/paged")
    @Deprecated
    public ResponseEntity<Page<FormSubmissionDTO>> getAllFormSubmissionsPaged(Pageable pageable) {
        return ResponseEntity.ok(formSubmissionService.getAllFormSubmissions(pageable));
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<FormSubmissionDTO> getFormSubmissionById(@PathVariable String id) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionById(id));
    }
    
    @GetMapping("/category/{categoryId}")
    public ResponseEntity<List<FormSubmissionDTO>> getFormSubmissionsByCategoryId(@PathVariable Long categoryId) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionsByCategoryId(categoryId));
    }
    
    @GetMapping("/category/{categoryId}/paged")
    public ResponseEntity<Page<FormSubmissionDTO>> getFormSubmissionsByCategoryIdPaged(
            @PathVariable Long categoryId,
            Pageable pageable) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionsByCategoryId(categoryId, pageable));
    }
    
    @GetMapping("/form-template/{formTemplateId}")
    public ResponseEntity<List<FormSubmissionDTO>> getFormSubmissionsByFormTemplateId(@PathVariable Long formTemplateId) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionsByFormTemplateId(formTemplateId));
    }
    
    @GetMapping("/form-template/{formTemplateId}/paged")
    public ResponseEntity<Page<FormSubmissionDTO>> getFormSubmissionsByFormTemplateIdPaged(
            @PathVariable Long formTemplateId,
            Pageable pageable) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionsByFormTemplateId(formTemplateId, pageable));
    }
    
    @PostMapping("/submit")
    public ResponseEntity<FormSubmissionDTO> submitForm(@Valid @RequestBody SubmitFormRequest request) {
        return new ResponseEntity<>(formSubmissionService.submitForm(request), HttpStatus.CREATED);
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteFormSubmission(@PathVariable String id) {
        formSubmissionService.deleteFormSubmission(id);
        return ResponseEntity.noContent().build();
    }

    // User-based endpoints

    /**
     * Get all form submissions by user ID
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<FormSubmissionDTO>> getFormSubmissionsByUserId(@PathVariable Long userId) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionsByUserId(userId));
    }

    /**
     * Get form submissions by user ID with pagination
     */
    @GetMapping("/user/{userId}/paged")
    public ResponseEntity<Page<FormSubmissionDTO>> getFormSubmissionsByUserIdPaged(
            @PathVariable Long userId,
            Pageable pageable) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionsByUserId(userId, pageable));
    }

    /**
     * Get form submissions by user ID and category ID
     */
    @GetMapping("/user/{userId}/category/{categoryId}")
    public ResponseEntity<List<FormSubmissionDTO>> getFormSubmissionsByUserIdAndCategoryId(
            @PathVariable Long userId,
            @PathVariable Long categoryId) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionsByUserIdAndCategoryId(userId, categoryId));
    }

    /**
     * Get form submissions by user ID and category ID with pagination
     */
    @GetMapping("/user/{userId}/category/{categoryId}/paged")
    public ResponseEntity<Page<FormSubmissionDTO>> getFormSubmissionsByUserIdAndCategoryIdPaged(
            @PathVariable Long userId,
            @PathVariable Long categoryId,
            Pageable pageable) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionsByUserIdAndCategoryId(userId, categoryId, pageable));
    }

    /**
     * Get form submissions by user ID and form template ID
     */
    @GetMapping("/user/{userId}/form-template/{formTemplateId}")
    public ResponseEntity<List<FormSubmissionDTO>> getFormSubmissionsByUserIdAndFormTemplateId(
            @PathVariable Long userId,
            @PathVariable Long formTemplateId) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionsByUserIdAndFormTemplateId(userId, formTemplateId));
    }

    /**
     * Get form submissions by user ID and form template ID with pagination
     */
    @GetMapping("/user/{userId}/form-template/{formTemplateId}/paged")
    public ResponseEntity<Page<FormSubmissionDTO>> getFormSubmissionsByUserIdAndFormTemplateIdPaged(
            @PathVariable Long userId,
            @PathVariable Long formTemplateId,
            Pageable pageable) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionsByUserIdAndFormTemplateId(userId, formTemplateId, pageable));
    }

    // Request ID based endpoints

    /**
     * Get form submission by request ID
     */
    @GetMapping("/request/{requestId}")
    public ResponseEntity<FormSubmissionDTO> getFormSubmissionByRequestId(@PathVariable String requestId) {
        return ResponseEntity.ok(formSubmissionService.getFormSubmissionByRequestId(requestId));
    }

    /**
     * Check if request ID exists
     */
    @GetMapping("/request/{requestId}/exists")
    public ResponseEntity<Boolean> checkRequestIdExists(@PathVariable String requestId) {
        return ResponseEntity.ok(formSubmissionService.existsByRequestId(requestId));
    }
}