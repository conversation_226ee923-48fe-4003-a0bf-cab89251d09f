package com.lookforx.questionformservice.service;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.common.exception.ValidationException;
import com.lookforx.questionformservice.domain.*;
import com.lookforx.questionformservice.dto.FormSubmissionDTO;
import com.lookforx.questionformservice.dto.request.FormResponseRequest;
import com.lookforx.questionformservice.dto.request.SubmitFormRequest;
import com.lookforx.common.exception.ResourceNotFoundException;
import com.lookforx.questionformservice.mapper.FormResponseMapper;
import com.lookforx.questionformservice.mapper.FormSubmissionMapper;
import com.lookforx.questionformservice.repository.FormElementRepository;
import com.lookforx.questionformservice.repository.FormSubmissionRepository;
import com.lookforx.questionformservice.repository.FormTemplateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FormSubmissionService {
    
    private final FormSubmissionRepository formSubmissionRepository;
    private final FormTemplateRepository formTemplateRepository;
    private final FormElementRepository formElementRepository;
    private final FormSubmissionMapper formSubmissionMapper;
    private final FormResponseMapper formResponseMapper;
    private final UserContextService userContextService;
    
    @Transactional(readOnly = true)
    public List<FormSubmissionDTO> getAllFormSubmissions() {
        return formSubmissionRepository.findAll().stream()
                .map(formSubmissionMapper::toDTO)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Page<FormSubmissionDTO> getAllFormSubmissions(Pageable pageable) {
        return formSubmissionRepository.findAll(pageable)
                .map(formSubmissionMapper::toDTO);
    }
    
    @Transactional(readOnly = true)
    public FormSubmissionDTO getFormSubmissionById(String id) {
        FormSubmission submission = formSubmissionRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Form submission", id));
        return formSubmissionMapper.toDTO(submission);
    }
    
    @Transactional(readOnly = true)
    public List<FormSubmissionDTO> getFormSubmissionsByCategoryId(Long categoryId) {
        return formSubmissionRepository.findByCategoryId(categoryId).stream()
                .map(formSubmissionMapper::toDTO)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Page<FormSubmissionDTO> getFormSubmissionsByCategoryId(Long categoryId, Pageable pageable) {
        return formSubmissionRepository.findByCategoryId(categoryId, pageable)
                .map(formSubmissionMapper::toDTO);
    }
    
    @Transactional(readOnly = true)
    public List<FormSubmissionDTO> getFormSubmissionsByFormTemplateId(Long formTemplateId) {
        return formSubmissionRepository.findByFormTemplateId(formTemplateId).stream()
                .map(formSubmissionMapper::toDTO)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Page<FormSubmissionDTO> getFormSubmissionsByFormTemplateId(Long formTemplateId, Pageable pageable) {
        return formSubmissionRepository.findByFormTemplateId(formTemplateId, pageable)
                .map(formSubmissionMapper::toDTO);
    }
    
    @Transactional
    public FormSubmissionDTO submitForm(SubmitFormRequest request) {
        // Check if request ID already exists
        if (formSubmissionRepository.existsByRequestId(request.getRequestId())) {
            throw new ValidationException("Request ID already exists: " + request.getRequestId());
        }

        // Get form template
        FormTemplate template = formTemplateRepository.findByIdWithElements(request.getFormTemplateId())
                .orElseThrow(() -> new ResourceNotFoundException("Form template", request.getFormTemplateId()));

        // Validate form submission
        validateFormSubmission(template, request);

        // Create form submission
        FormSubmission submission = formSubmissionMapper.toEntity(request);
        submission.setFormTemplateId(template.getId());
        submission.setSubmittedBy(userContextService.getCurrentUsername());

        // Create form responses
        Map<Long, FormElement> elementMap = template.getElements().stream()
                .collect(Collectors.toMap(FormElement::getId, element -> element));

        List<FormResponse> responses = new ArrayList<>();
        for (FormResponseRequest responseRequest : request.getResponses()) {
            FormElement element = elementMap.get(responseRequest.getFormElementId());
            if (element == null) {
                throw new ResourceNotFoundException("Form element", responseRequest.getFormElementId());
            }

            FormResponse response = formResponseMapper.toEntity(responseRequest);
            responses.add(response);
        }
        submission.setResponses(responses);

        FormSubmission savedSubmission = formSubmissionRepository.save(submission);
        return formSubmissionMapper.toDTO(savedSubmission);
    }
    
    private void validateFormSubmission(FormTemplate template, SubmitFormRequest request) {
        // Check if form is active
        if (!template.isActive()) {
            throw new ValidationException("Form template is not active");
        }
        
        // Check if category ID matches
        if (!template.getCategoryId().equals(request.getCategoryId())) {
            throw new ValidationException("Category ID does not match form template");
        }
        
        // Kullanıcının tercih ettiği dil ve varsayılan dil
        LanguageCode preferredLanguage = userContextService.getPreferredLanguage();
        LanguageCode defaultLanguage = LanguageCode.EN;
        
        // Check required fields
        Map<Long, String> responseMap = new HashMap<>();
        for (FormResponseRequest response : request.getResponses()) {
            responseMap.put(response.getFormElementId(), response.getValue());
        }
        
        for (FormElement element : template.getElements()) {
            if (element.isRequired()) {
                String value = responseMap.get(element.getId());
                if (value == null || value.trim().isEmpty()) {
                    throw new ValidationException("Required field missing: " + 
                            element.getLabel(preferredLanguage, defaultLanguage));
                }
            }
        }
    }
    
    @Transactional
    public void deleteFormSubmission(String id) {
        if (!formSubmissionRepository.existsById(id)) {
            throw new ResourceNotFoundException("Form submission", id);
        }
        formSubmissionRepository.deleteById(id);
    }

    // User-based query methods

    /**
     * Get all form submissions by user ID
     */
    @Transactional(readOnly = true)
    public List<FormSubmissionDTO> getFormSubmissionsByUserId(Long userId) {
        return formSubmissionRepository.findByUserId(userId).stream()
                .map(formSubmissionMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get form submissions by user ID with pagination
     */
    @Transactional(readOnly = true)
    public Page<FormSubmissionDTO> getFormSubmissionsByUserId(Long userId, Pageable pageable) {
        return formSubmissionRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable)
                .map(formSubmissionMapper::toDTO);
    }

    /**
     * Get form submissions by user ID and category ID
     */
    @Transactional(readOnly = true)
    public List<FormSubmissionDTO> getFormSubmissionsByUserIdAndCategoryId(Long userId, Long categoryId) {
        return formSubmissionRepository.findByUserIdAndCategoryIdOrderByCreatedAtDesc(userId, categoryId).stream()
                .map(formSubmissionMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get form submissions by user ID and category ID with pagination
     */
    @Transactional(readOnly = true)
    public Page<FormSubmissionDTO> getFormSubmissionsByUserIdAndCategoryId(Long userId, Long categoryId, Pageable pageable) {
        return formSubmissionRepository.findByUserIdAndCategoryId(userId, categoryId, pageable)
                .map(formSubmissionMapper::toDTO);
    }

    /**
     * Get form submissions by user ID and form template ID
     */
    @Transactional(readOnly = true)
    public List<FormSubmissionDTO> getFormSubmissionsByUserIdAndFormTemplateId(Long userId, Long formTemplateId) {
        return formSubmissionRepository.findByUserIdAndFormTemplateId(userId, formTemplateId).stream()
                .map(formSubmissionMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Get form submissions by user ID and form template ID with pagination
     */
    @Transactional(readOnly = true)
    public Page<FormSubmissionDTO> getFormSubmissionsByUserIdAndFormTemplateId(Long userId, Long formTemplateId, Pageable pageable) {
        return formSubmissionRepository.findByUserIdAndFormTemplateId(userId, formTemplateId, pageable)
                .map(formSubmissionMapper::toDTO);
    }

    // Request ID based methods

    /**
     * Get form submission by request ID
     */
    @Transactional(readOnly = true)
    public FormSubmissionDTO getFormSubmissionByRequestId(String requestId) {
        FormSubmission submission = formSubmissionRepository.findByRequestId(requestId)
                .orElseThrow(() -> new ResourceNotFoundException("Form submission with request ID: " + requestId));
        return formSubmissionMapper.toDTO(submission);
    }

    /**
     * Check if request ID exists
     */
    @Transactional(readOnly = true)
    public boolean existsByRequestId(String requestId) {
        return formSubmissionRepository.existsByRequestId(requestId);
    }

    /**
     * Search and filter form submissions with pagination
     * @param search Search term for form data
     * @param categoryId Filter by category ID
     * @param formTemplateId Filter by form template ID
     * @param userId Filter by user ID
     * @param pageable Pagination parameters
     * @return Page of form submissions matching the criteria
     */
    @Transactional(readOnly = true)
    public Page<FormSubmissionDTO> searchFormSubmissions(String search, Long categoryId, Long formTemplateId, Long userId, Pageable pageable) {
        Page<FormSubmission> submissions;

        if (search != null && !search.trim().isEmpty()) {
            // Search in form data (responses)
            if (categoryId != null && formTemplateId != null && userId != null) {
                submissions = formSubmissionRepository.findBySearchAndCategoryIdAndFormTemplateIdAndUserId(
                    search.trim(), categoryId, formTemplateId, userId, pageable);
            } else if (categoryId != null && formTemplateId != null) {
                submissions = formSubmissionRepository.findBySearchAndCategoryIdAndFormTemplateId(
                    search.trim(), categoryId, formTemplateId, pageable);
            } else if (categoryId != null && userId != null) {
                submissions = formSubmissionRepository.findBySearchAndCategoryIdAndUserId(
                    search.trim(), categoryId, userId, pageable);
            } else if (formTemplateId != null && userId != null) {
                submissions = formSubmissionRepository.findBySearchAndFormTemplateIdAndUserId(
                    search.trim(), formTemplateId, userId, pageable);
            } else if (categoryId != null) {
                submissions = formSubmissionRepository.findBySearchAndCategoryId(
                    search.trim(), categoryId, pageable);
            } else if (formTemplateId != null) {
                submissions = formSubmissionRepository.findBySearchAndFormTemplateId(
                    search.trim(), formTemplateId, pageable);
            } else if (userId != null) {
                submissions = formSubmissionRepository.findBySearchAndUserId(
                    search.trim(), userId, pageable);
            } else {
                submissions = formSubmissionRepository.findBySearch(
                    search.trim(), pageable);
            }
        } else {
            // No search, just filter
            if (categoryId != null && formTemplateId != null && userId != null) {
                submissions = formSubmissionRepository.findByCategoryIdAndFormTemplateIdAndUserId(categoryId, formTemplateId, userId, pageable);
            } else if (categoryId != null && formTemplateId != null) {
                submissions = formSubmissionRepository.findByCategoryIdAndFormTemplateId(categoryId, formTemplateId, pageable);
            } else if (categoryId != null && userId != null) {
                submissions = formSubmissionRepository.findByCategoryIdAndUserId(categoryId, userId, pageable);
            } else if (formTemplateId != null && userId != null) {
                submissions = formSubmissionRepository.findByFormTemplateIdAndUserId(formTemplateId, userId, pageable);
            } else if (categoryId != null) {
                submissions = formSubmissionRepository.findByCategoryId(categoryId, pageable);
            } else if (formTemplateId != null) {
                submissions = formSubmissionRepository.findByFormTemplateId(formTemplateId, pageable);
            } else if (userId != null) {
                submissions = formSubmissionRepository.findByUserId(userId, pageable);
            } else {
                submissions = formSubmissionRepository.findAll(pageable);
            }
        }

        return submissions.map(formSubmissionMapper::toDTO);
    }
}