package com.lookforx.questionformservice.service;

import com.lookforx.questionformservice.domain.FormTemplate;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.questionformservice.dto.FormTemplateDTO;
import com.lookforx.questionformservice.dto.LocalizedFormTemplateDTO;
import com.lookforx.questionformservice.dto.request.CreateFormTemplateRequest;
import com.lookforx.questionformservice.dto.request.UpdateFormTemplateRequest;
import com.lookforx.common.exception.ResourceNotFoundException;
import com.lookforx.questionformservice.mapper.FormTemplateMapper;
import com.lookforx.questionformservice.repository.FormTemplateRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FormTemplateService {
    
    private final FormTemplateRepository formTemplateRepository;
    private final FormTemplateMapper formTemplateMapper;
    private final UserContextService userContextService;
    
    @Transactional(readOnly = true)
    // @Cacheable(value = "formTemplates", key = "'all'") // Temporarily disabled due to casting issue
    public List<FormTemplateDTO> getAllFormTemplates() {
        return formTemplateRepository.findAll().stream()
                .map(formTemplateMapper::toDTO)
                .collect(Collectors.toList());
    }
    
    @Transactional(readOnly = true)
    public Page<FormTemplateDTO> getAllFormTemplates(Pageable pageable) {
        return formTemplateRepository.findAll(pageable)
                .map(formTemplateMapper::toDTO);
    }
    
    @Transactional(readOnly = true)
    // @Cacheable(value = "formTemplates", key = "#id") // Temporarily disabled due to casting issue
    public FormTemplateDTO getFormTemplateById(Long id) {
        FormTemplate template = formTemplateRepository.findByIdWithElements(id)
                .orElseThrow(() -> new ResourceNotFoundException("Form template", id));
        return formTemplateMapper.toDTO(template);
    }
    
    @Transactional(readOnly = true)
    // @Cacheable(value = "formTemplatesByCategory", key = "#categoryId") // Temporarily disabled due to casting issue
    public List<FormTemplateDTO> getFormTemplatesByCategoryId(Long categoryId) {
        return formTemplateRepository.findByCategoryIdWithElements(categoryId).stream()
                .map(formTemplateMapper::toDTO)
                .collect(Collectors.toList());
    }
    
    @Transactional
    // @CacheEvict(value = {"formTemplates", "formTemplatesByCategory"}, allEntries = true) // Temporarily disabled
    public FormTemplateDTO createFormTemplate(CreateFormTemplateRequest request) {
        // Check if category already has an active form
        List<FormTemplate> existingForms = formTemplateRepository.findByCategoryIdAndActiveTrue(request.getCategoryId());
        if (!existingForms.isEmpty()) {
            throw new IllegalArgumentException("Category already has an active form template. Each category can only have one form template.");
        }

        FormTemplate template = formTemplateMapper.toEntity(request);
        template.setCreatedBy(userContextService.getCurrentUsername());

        FormTemplate savedTemplate = formTemplateRepository.save(template);
        return formTemplateMapper.toDTO(savedTemplate);
    }
    
    @Transactional
    // @CacheEvict(value = {"formTemplates", "formTemplatesByCategory"}, allEntries = true) // Temporarily disabled
    public FormTemplateDTO updateFormTemplate(Long id, UpdateFormTemplateRequest request) {
        FormTemplate template = formTemplateRepository.findByIdWithElements(id)
                .orElseThrow(() -> new ResourceNotFoundException("Form template", id));

        // Check if category is being changed and if the new category already has an active form
        if (request.getCategoryId() != null && !request.getCategoryId().equals(template.getCategoryId())) {
            List<FormTemplate> existingForms = formTemplateRepository.findByCategoryIdAndActiveTrue(request.getCategoryId());
            if (!existingForms.isEmpty()) {
                throw new IllegalArgumentException("Target category already has an active form template. Each category can only have one form template.");
            }
        }

        formTemplateMapper.updateEntity(template, request);
        template.setUpdatedBy(userContextService.getCurrentUsername());

        FormTemplate updatedTemplate = formTemplateRepository.save(template);
        return formTemplateMapper.toDTO(updatedTemplate);
    }
    
    @Transactional
    // @CacheEvict(value = {"formTemplates", "formTemplatesByCategory"}, allEntries = true) // Temporarily disabled
    public void deleteFormTemplate(Long id) {
        if (!formTemplateRepository.existsById(id)) {
            throw new ResourceNotFoundException("Form template", id);
        }
        formTemplateRepository.deleteById(id);
    }
    
    @Transactional
    // @CacheEvict(value = {"formTemplates", "formTemplatesByCategory"}, allEntries = true) // Temporarily disabled
    public FormTemplateDTO activateFormTemplate(Long id) {
        FormTemplate template = formTemplateRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Form template", id));
        
        template.setActive(true);
        template.setUpdatedBy(userContextService.getCurrentUsername());
        
        FormTemplate updatedTemplate = formTemplateRepository.save(template);
        return formTemplateMapper.toDTO(updatedTemplate);
    }
    
    @Transactional
    // @CacheEvict(value = {"formTemplates", "formTemplatesByCategory"}, allEntries = true) // Temporarily disabled
    public FormTemplateDTO deactivateFormTemplate(Long id) {
        FormTemplate template = formTemplateRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Form template", id));
        
        template.setActive(false);
        template.setUpdatedBy(userContextService.getCurrentUsername());
        
        FormTemplate updatedTemplate = formTemplateRepository.save(template);
        return formTemplateMapper.toDTO(updatedTemplate);
    }

    // Language-aware methods

    /**
     * Get all form templates with localized content in specific language
     */
    @Transactional(readOnly = true)
    public List<LocalizedFormTemplateDTO> getAllFormTemplatesLocalized(LanguageCode languageCode) {
        LanguageCode fallbackLanguageCode = LanguageCode.EN;
        return formTemplateRepository.findByLanguageCodeAndActiveTrue(languageCode).stream()
                .map(template -> formTemplateMapper.toLocalizedDTO(template, languageCode, fallbackLanguageCode))
                .collect(Collectors.toList());
    }

    /**
     * Get all form templates with localized content in specific language with custom fallback
     */
    @Transactional(readOnly = true)
    public List<LocalizedFormTemplateDTO> getAllFormTemplatesLocalized(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        return formTemplateRepository.findAll().stream()
                .filter(FormTemplate::isActive)
                .map(template -> formTemplateMapper.toLocalizedDTO(template, languageCode, fallbackLanguageCode))
                .collect(Collectors.toList());
    }

    /**
     * Get form template by ID with localized content
     */
    @Transactional(readOnly = true)
    public LocalizedFormTemplateDTO getFormTemplateByIdLocalized(Long id, LanguageCode languageCode) {
        LanguageCode fallbackLanguageCode = LanguageCode.EN;
        FormTemplate template = formTemplateRepository.findByIdWithElements(id)
                .orElseThrow(() -> new ResourceNotFoundException("Form template", id));
        return formTemplateMapper.toLocalizedDTO(template, languageCode, fallbackLanguageCode);
    }

    /**
     * Get form template by ID with localized content and custom fallback
     */
    @Transactional(readOnly = true)
    public LocalizedFormTemplateDTO getFormTemplateByIdLocalized(Long id, LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        FormTemplate template = formTemplateRepository.findByIdWithElements(id)
                .orElseThrow(() -> new ResourceNotFoundException("Form template", id));
        return formTemplateMapper.toLocalizedDTO(template, languageCode, fallbackLanguageCode);
    }

    /**
     * Get form templates by category with localized content
     */
    @Transactional(readOnly = true)
    public List<LocalizedFormTemplateDTO> getFormTemplatesByCategoryIdLocalized(Long categoryId, LanguageCode languageCode) {
        LanguageCode fallbackLanguageCode = LanguageCode.EN;
        return formTemplateRepository.findByCategoryIdAndLanguageCodeWithElements(categoryId, languageCode).stream()
                .map(template -> formTemplateMapper.toLocalizedDTO(template, languageCode, fallbackLanguageCode))
                .collect(Collectors.toList());
    }

    /**
     * Get form templates by category with localized content and custom fallback
     */
    @Transactional(readOnly = true)
    public List<LocalizedFormTemplateDTO> getFormTemplatesByCategoryIdLocalized(Long categoryId, LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        return formTemplateRepository.findByCategoryIdWithElements(categoryId).stream()
                .filter(FormTemplate::isActive)
                .map(template -> formTemplateMapper.toLocalizedDTO(template, languageCode, fallbackLanguageCode))
                .collect(Collectors.toList());
    }

    /**
     * Search form templates by name in specific language
     */
    @Transactional(readOnly = true)
    public List<LocalizedFormTemplateDTO> searchFormTemplatesByName(String searchTerm, LanguageCode languageCode) {
        LanguageCode fallbackLanguageCode = LanguageCode.EN;
        return formTemplateRepository.searchByNameInLanguage(searchTerm, languageCode).stream()
                .map(template -> formTemplateMapper.toLocalizedDTO(template, languageCode, fallbackLanguageCode))
                .collect(Collectors.toList());
    }

    /**
     * Search form templates by name or description in specific language
     */
    @Transactional(readOnly = true)
    public List<LocalizedFormTemplateDTO> searchFormTemplates(String searchTerm, LanguageCode languageCode) {
        LanguageCode fallbackLanguageCode = LanguageCode.EN;
        return formTemplateRepository.searchByNameOrDescriptionInLanguage(searchTerm, languageCode).stream()
                .map(template -> formTemplateMapper.toLocalizedDTO(template, languageCode, fallbackLanguageCode))
                .collect(Collectors.toList());
    }

    /**
     * Search form templates by name or description in specific language with custom fallback
     */
    @Transactional(readOnly = true)
    public List<LocalizedFormTemplateDTO> searchFormTemplates(String searchTerm, LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        return formTemplateRepository.searchByNameOrDescriptionInLanguage(searchTerm, languageCode).stream()
                .map(template -> formTemplateMapper.toLocalizedDTO(template, languageCode, fallbackLanguageCode))
                .collect(Collectors.toList());
    }

    /**
     * Search and filter form templates with pagination
     * @param search Search term for form names
     * @param categoryId Filter by category ID
     * @param active Filter by active status
     * @param pageable Pagination parameters
     * @return Page of form templates matching the criteria
     */
    @Transactional(readOnly = true)
    public Page<FormTemplateDTO> searchFormTemplatesWithPagination(String search, Long categoryId, Boolean active, Pageable pageable) {
        Page<FormTemplate> formTemplates;

        if (search != null && !search.trim().isEmpty()) {
            // Search in form names (translations)
            if (categoryId != null && active != null) {
                formTemplates = formTemplateRepository.findByNameContainingAndCategoryIdAndActive(
                    search.trim(), categoryId, active, pageable);
            } else if (categoryId != null) {
                formTemplates = formTemplateRepository.findByNameContainingAndCategoryId(
                    search.trim(), categoryId, pageable);
            } else if (active != null) {
                formTemplates = formTemplateRepository.findByNameContainingAndActive(
                    search.trim(), active, pageable);
            } else {
                formTemplates = formTemplateRepository.findByNameContaining(
                    search.trim(), pageable);
            }
        } else {
            // No search, just filter
            if (categoryId != null && active != null) {
                formTemplates = formTemplateRepository.findByCategoryIdAndActive(categoryId, active, pageable);
            } else if (categoryId != null) {
                formTemplates = formTemplateRepository.findByCategoryId(categoryId, pageable);
            } else if (active != null) {
                formTemplates = formTemplateRepository.findByActive(active, pageable);
            } else {
                formTemplates = formTemplateRepository.findAll(pageable);
            }
        }

        return formTemplates.map(formTemplateMapper::toDTO);
    }
}