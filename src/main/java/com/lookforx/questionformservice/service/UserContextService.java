package com.lookforx.questionformservice.service;

import com.lookforx.common.enums.LanguageCode;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Locale;

@Service
public class UserContextService {
    
    public String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return "system";
        }
        return authentication.getName();
    }

    /**
     * Kullanıcının tercih ettiği dil kodunu döndürür.
     * Önce Accept-Language header'ını kontrol eder, sonra kullanıcı ayarlarını,
     * en son varsayılan olarak İngilizce döner.
     *
     * @return Kullanıcının tercih ettiği dil kodu
     */
    public LanguageCode getPreferredLanguage() {
        // 1. Accept-Language header'ından dil tercihini al
        LanguageCode headerLanguage = getLanguageFromAcceptHeader();
        if (headerLanguage != null) {
            return headerLanguage;
        }

        // 2. Kullanıcının kayıtlı tercihini al (gelecekte implement edilebilir)
        // LanguageCode userPreference = getUserLanguagePreference();
        // if (userPreference != null) {
        //     return userPreference;
        // }

        // 3. Varsayılan olarak İngilizce dön
        return LanguageCode.EN;
    }

    /**
     * Accept-Language header'ından dil kodunu çıkarır
     */
    private LanguageCode getLanguageFromAcceptHeader() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                String acceptLanguage = request.getHeader("Accept-Language");

                if (acceptLanguage != null && !acceptLanguage.isEmpty()) {
                    // İlk dil tercihini al (en yüksek priority)
                    String primaryLanguage = acceptLanguage.split(",")[0].split("-")[0].toUpperCase();

                    // LanguageCode enum'ında var mı kontrol et
                    try {
                        return LanguageCode.valueOf(primaryLanguage);
                    } catch (IllegalArgumentException e) {
                        // Desteklenmeyen dil kodu
                        return null;
                    }
                }
            }
        } catch (Exception e) {
            // Header parse hatası durumunda null dön
            return null;
        }
        return null;
    }

    /**
     * Varsayılan fallback dil kodunu döndürür
     */
    public LanguageCode getFallbackLanguage() {
        return LanguageCode.EN;
    }

    /**
     * Kullanıcının tercih ettiği dil ve fallback dil ile birlikte döndürür
     */
    public LanguagePreference getLanguagePreference() {
        return new LanguagePreference(getPreferredLanguage(), getFallbackLanguage());
    }

    /**
     * Dil tercihi bilgilerini tutan inner class
     */
    public static class LanguagePreference {
        private final LanguageCode preferred;
        private final LanguageCode fallback;

        public LanguagePreference(LanguageCode preferred, LanguageCode fallback) {
            this.preferred = preferred;
            this.fallback = fallback;
        }

        public LanguageCode getPreferred() {
            return preferred;
        }

        public LanguageCode getFallback() {
            return fallback;
        }
    }
}