package com.lookforx.questionformservice.repository;

import com.lookforx.questionformservice.domain.ElementOption;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ElementOptionRepository extends JpaRepository<ElementOption, Long> {
    
    List<ElementOption> findByFormElementIdOrderByDisplayOrderAsc(Long formElementId);
}