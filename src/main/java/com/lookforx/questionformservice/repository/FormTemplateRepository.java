package com.lookforx.questionformservice.repository;

import com.lookforx.questionformservice.domain.FormTemplate;
import com.lookforx.common.enums.LanguageCode;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FormTemplateRepository extends JpaRepository<FormTemplate, Long> {

    List<FormTemplate> findByCategoryId(Long categoryId);

    List<FormTemplate> findByCategoryIdAndActiveTrue(Long categoryId);

    Optional<FormTemplate> findByIdAndActiveTrue(Long id);

    @Query("SELECT ft FROM FormTemplate ft LEFT JOIN FETCH ft.elements WHERE ft.id = :id")
    Optional<FormTemplate> findByIdWithElements(Long id);

    @Query("SELECT ft FROM FormTemplate ft LEFT JOIN FETCH ft.elements WHERE ft.categoryId = :categoryId AND ft.active = true")
    List<FormTemplate> findByCategoryIdWithElements(Long categoryId);

    // Language-aware query methods

    /**
     * Find form templates that have name translation in the specified language
     */
    @Query("SELECT DISTINCT ft FROM FormTemplate ft WHERE KEY(ft.nameTranslations) = :languageCode AND ft.active = true")
    List<FormTemplate> findByLanguageCodeAndActiveTrue(@Param("languageCode") LanguageCode languageCode);

    /**
     * Find form templates by category that have name translation in the specified language
     */
    @Query("SELECT DISTINCT ft FROM FormTemplate ft WHERE ft.categoryId = :categoryId AND KEY(ft.nameTranslations) = :languageCode AND ft.active = true")
    List<FormTemplate> findByCategoryIdAndLanguageCodeAndActiveTrue(@Param("categoryId") Long categoryId, @Param("languageCode") LanguageCode languageCode);

    /**
     * Search form templates by name content in specific language
     */
    @Query("SELECT DISTINCT ft FROM FormTemplate ft JOIN ft.nameTranslations nt WHERE KEY(nt) = :languageCode AND LOWER(nt) LIKE LOWER(CONCAT('%', :searchTerm, '%')) AND ft.active = true")
    List<FormTemplate> searchByNameInLanguage(@Param("searchTerm") String searchTerm, @Param("languageCode") LanguageCode languageCode);

    /**
     * Search form templates by name or description content in specific language
     */
    @Query("""
        SELECT DISTINCT ft FROM FormTemplate ft
        LEFT JOIN ft.nameTranslations nt
        LEFT JOIN ft.descriptionTranslations dt
        WHERE ft.active = true AND (
            (KEY(nt) = :languageCode AND LOWER(nt) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) OR
            (KEY(dt) = :languageCode AND LOWER(dt) LIKE LOWER(CONCAT('%', :searchTerm, '%')))
        )
        """)
    List<FormTemplate> searchByNameOrDescriptionInLanguage(@Param("searchTerm") String searchTerm, @Param("languageCode") LanguageCode languageCode);

    /**
     * Find form templates with elements by category and language
     */
    @Query("""
        SELECT DISTINCT ft FROM FormTemplate ft
        LEFT JOIN FETCH ft.elements e
        LEFT JOIN FETCH e.options
        WHERE ft.categoryId = :categoryId
        AND KEY(ft.nameTranslations) = :languageCode
        AND ft.active = true
        ORDER BY ft.createdAt DESC
        """)
    List<FormTemplate> findByCategoryIdAndLanguageCodeWithElements(@Param("categoryId") Long categoryId, @Param("languageCode") LanguageCode languageCode);

    // Pagination and search methods

    Page<FormTemplate> findByCategoryId(Long categoryId, Pageable pageable);

    Page<FormTemplate> findByActive(Boolean active, Pageable pageable);

    Page<FormTemplate> findByCategoryIdAndActive(Long categoryId, Boolean active, Pageable pageable);

    @Query("""
        SELECT DISTINCT ft FROM FormTemplate ft
        JOIN ft.nameTranslations nt
        WHERE LOWER(nt) LIKE LOWER(CONCAT('%', :search, '%'))
        """)
    Page<FormTemplate> findByNameContaining(@Param("search") String search, Pageable pageable);

    @Query("""
        SELECT DISTINCT ft FROM FormTemplate ft
        JOIN ft.nameTranslations nt
        WHERE LOWER(nt) LIKE LOWER(CONCAT('%', :search, '%'))
        AND ft.categoryId = :categoryId
        """)
    Page<FormTemplate> findByNameContainingAndCategoryId(@Param("search") String search, @Param("categoryId") Long categoryId, Pageable pageable);

    @Query("""
        SELECT DISTINCT ft FROM FormTemplate ft
        JOIN ft.nameTranslations nt
        WHERE LOWER(nt) LIKE LOWER(CONCAT('%', :search, '%'))
        AND ft.active = :active
        """)
    Page<FormTemplate> findByNameContainingAndActive(@Param("search") String search, @Param("active") Boolean active, Pageable pageable);

    @Query("""
        SELECT DISTINCT ft FROM FormTemplate ft
        JOIN ft.nameTranslations nt
        WHERE LOWER(nt) LIKE LOWER(CONCAT('%', :search, '%'))
        AND ft.categoryId = :categoryId
        AND ft.active = :active
        """)
    Page<FormTemplate> findByNameContainingAndCategoryIdAndActive(@Param("search") String search, @Param("categoryId") Long categoryId, @Param("active") Boolean active, Pageable pageable);
}