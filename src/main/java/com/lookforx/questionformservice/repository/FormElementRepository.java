package com.lookforx.questionformservice.repository;

import com.lookforx.questionformservice.domain.FormElement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FormElementRepository extends JpaRepository<FormElement, Long> {
    
    List<FormElement> findByFormTemplateIdOrderByDisplayOrderAsc(Long formTemplateId);
    
    @Query("SELECT fe FROM FormElement fe LEFT JOIN FETCH fe.options WHERE fe.id = :id")
    Optional<FormElement> findByIdWithOptions(Long id);
    
    @Query("SELECT fe FROM FormElement fe LEFT JOIN FETCH fe.options WHERE fe.formTemplate.id = :formTemplateId ORDER BY fe.displayOrder ASC")
    List<FormElement> findByFormTemplateIdWithOptions(Long formTemplateId);
}