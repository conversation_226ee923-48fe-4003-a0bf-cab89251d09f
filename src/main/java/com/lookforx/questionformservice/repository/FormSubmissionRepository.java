package com.lookforx.questionformservice.repository;

import com.lookforx.questionformservice.domain.FormSubmission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FormSubmissionRepository extends MongoRepository<FormSubmission, String> {

    List<FormSubmission> findByCategoryId(Long categoryId);

    List<FormSubmission> findByFormTemplateId(Long formTemplateId);

    Page<FormSubmission> findByCategoryId(Long categoryId, Pageable pageable);

    Page<FormSubmission> findByFormTemplateId(Long formTemplateId, Pageable pageable);

    // User ID based queries
    List<FormSubmission> findByUserId(Long userId);

    Page<FormSubmission> findByUserId(Long userId, Pageable pageable);

    List<FormSubmission> findByUserIdAndCategoryId(Long userId, Long categoryId);

    Page<FormSubmission> findByUserIdAndCategoryId(Long userId, Long categoryId, Pageable pageable);

    List<FormSubmission> findByUserIdAndFormTemplateId(Long userId, Long formTemplateId);

    Page<FormSubmission> findByUserIdAndFormTemplateId(Long userId, Long formTemplateId, Pageable pageable);

    // Request ID based queries
    Optional<FormSubmission> findByRequestId(String requestId);

    // Check if request ID already exists
    boolean existsByRequestId(String requestId);

    // Combined queries
    List<FormSubmission> findByUserIdAndCategoryIdOrderByCreatedAtDesc(Long userId, Long categoryId);

    Page<FormSubmission> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    // Additional filter combinations for search
    Page<FormSubmission> findByCategoryIdAndFormTemplateId(Long categoryId, Long formTemplateId, Pageable pageable);

    Page<FormSubmission> findByCategoryIdAndUserId(Long categoryId, Long userId, Pageable pageable);

    Page<FormSubmission> findByFormTemplateIdAndUserId(Long formTemplateId, Long userId, Pageable pageable);

    Page<FormSubmission> findByCategoryIdAndFormTemplateIdAndUserId(Long categoryId, Long formTemplateId, Long userId, Pageable pageable);

    // Search methods - MongoDB text search in responses
    @Query("{ $text: { $search: ?0 } }")
    Page<FormSubmission> findBySearch(String search, Pageable pageable);

    @Query("{ $and: [ { $text: { $search: ?0 } }, { categoryId: ?1 } ] }")
    Page<FormSubmission> findBySearchAndCategoryId(String search, Long categoryId, Pageable pageable);

    @Query("{ $and: [ { $text: { $search: ?0 } }, { formTemplateId: ?1 } ] }")
    Page<FormSubmission> findBySearchAndFormTemplateId(String search, Long formTemplateId, Pageable pageable);

    @Query("{ $and: [ { $text: { $search: ?0 } }, { userId: ?1 } ] }")
    Page<FormSubmission> findBySearchAndUserId(String search, Long userId, Pageable pageable);

    @Query("{ $and: [ { $text: { $search: ?0 } }, { categoryId: ?1 }, { formTemplateId: ?2 } ] }")
    Page<FormSubmission> findBySearchAndCategoryIdAndFormTemplateId(String search, Long categoryId, Long formTemplateId, Pageable pageable);

    @Query("{ $and: [ { $text: { $search: ?0 } }, { categoryId: ?1 }, { userId: ?2 } ] }")
    Page<FormSubmission> findBySearchAndCategoryIdAndUserId(String search, Long categoryId, Long userId, Pageable pageable);

    @Query("{ $and: [ { $text: { $search: ?0 } }, { formTemplateId: ?1 }, { userId: ?2 } ] }")
    Page<FormSubmission> findBySearchAndFormTemplateIdAndUserId(String search, Long formTemplateId, Long userId, Pageable pageable);

    @Query("{ $and: [ { $text: { $search: ?0 } }, { categoryId: ?1 }, { formTemplateId: ?2 }, { userId: ?3 } ] }")
    Page<FormSubmission> findBySearchAndCategoryIdAndFormTemplateIdAndUserId(String search, Long categoryId, Long formTemplateId, Long userId, Pageable pageable);
}