package com.lookforx.questionformservice.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

/**
 * MongoDB Configuration for Form Submissions
 * Enables auditing for automatic createdAt/updatedAt timestamps
 */
@Configuration
@EnableMongoAuditing
@EnableMongoRepositories(basePackages = "com.lookforx.questionformservice.repository")
public class MongoConfig {
    
    // MongoDB configuration is handled by Spring Boot auto-configuration
    // Additional custom configurations can be added here if needed
    
}
