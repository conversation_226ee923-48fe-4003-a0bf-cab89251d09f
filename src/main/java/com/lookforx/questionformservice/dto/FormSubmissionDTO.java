package com.lookforx.questionformservice.dto;

import lombok.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FormSubmissionDTO {
    private String id;
    private Long formTemplateId;
    private Long categoryId;
    private Long userId;
    private String requestId;
    private String submittedBy;
    private List<FormResponseDTO> responses = new ArrayList<>();
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}