package com.lookforx.questionformservice.dto;

import com.lookforx.common.enums.LanguageCode;
import lombok.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Localized DTO for FormTemplate that contains only the content in a specific language
 * This is useful for client applications that need data in a single language
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LocalizedFormTemplateDTO {
    
    private Long id;
    private String name;
    private Long categoryId;
    private String description;
    private List<LocalizedFormElementDTO> elements = new ArrayList<>();
    private boolean active;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Language information
    private LanguageCode languageCode;
    private LanguageCode fallbackLanguageCode;
    
    // Indicates if fallback language was used for any field
    private boolean nameFromFallback;
    private boolean descriptionFromFallback;
}
