package com.lookforx.questionformservice.dto;

import com.lookforx.common.enums.LanguageCode;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ElementOptionDTO {
    private Long id;
    private String value;
    private Map<LanguageCode, String> labelTranslations = new HashMap<>();
    private Integer displayOrder;
    private boolean defaultSelected;
    
    // Çok dilli özellikler - her özellik anahtarı için dil bazlı değerler
    private Map<String, Map<LanguageCode, String>> propertyTranslations = new HashMap<>();
    
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Helper method to get label in specific language with fallback
    public String getLabel(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String label = labelTranslations.get(languageCode);
        if (label == null || label.isEmpty()) {
            label = labelTranslations.get(fallbackLanguageCode);
        }
        return label;
    }
    
    // Helper method to get property value in specific language with fallback
    public String getPropertyValue(String propertyKey, LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        Map<LanguageCode, String> translations = propertyTranslations.get(propertyKey);
        if (translations == null) {
            return null;
        }
        
        String value = translations.get(languageCode);
        if (value == null || value.isEmpty()) {
            value = translations.get(fallbackLanguageCode);
        }
        return value;
    }
    
    // Helper method to get all property keys
    public Set<String> getPropertyKeys() {
        return propertyTranslations.keySet();
    }
}
