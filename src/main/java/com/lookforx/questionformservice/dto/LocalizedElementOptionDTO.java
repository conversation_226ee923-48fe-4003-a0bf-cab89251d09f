package com.lookforx.questionformservice.dto;

import com.lookforx.common.enums.LanguageCode;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Localized DTO for ElementOption that contains only the content in a specific language
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LocalizedElementOptionDTO {
    
    private Long id;
    private String value;
    private String label;
    private Integer displayOrder;
    private boolean defaultSelected;
    
    // Localized properties - each property key with its localized value
    private Map<String, String> properties = new HashMap<>();
    
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Language information
    private LanguageCode languageCode;
    private LanguageCode fallbackLanguageCode;
    
    // Indicates if fallback language was used for any field
    private boolean labelFromFallback;
}
