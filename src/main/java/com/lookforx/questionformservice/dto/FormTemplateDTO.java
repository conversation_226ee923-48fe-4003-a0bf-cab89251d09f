package com.lookforx.questionformservice.dto;

import com.lookforx.common.enums.LanguageCode;
import lombok.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FormTemplateDTO {
    private Long id;
    private Map<LanguageCode, String> nameTranslations = new HashMap<>();
    private Long categoryId;
    private Map<LanguageCode, String> descriptionTranslations = new HashMap<>();
    private List<FormElementDTO> elements = new ArrayList<>();
    private boolean active;
    private String createdBy;
    private String updatedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Helper method to get name in specific language with fallback
    public String getName(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String name = nameTranslations.get(languageCode);
        if (name == null || name.isEmpty()) {
            name = nameTranslations.get(fallbackLanguageCode);
        }
        return name;
    }
    
    // Helper method to get description in specific language with fallback
    public String getDescription(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String description = descriptionTranslations.get(languageCode);
        if (description == null || description.isEmpty()) {
            description = descriptionTranslations.get(fallbackLanguageCode);
        }
        return description;
    }
}
