package com.lookforx.questionformservice.dto;

import com.lookforx.questionformservice.domain.ElementType;
import com.lookforx.common.enums.LanguageCode;
import lombok.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FormElementDTO {
    private Long id;
    private Map<LanguageCode, String> labelTranslations = new HashMap<>();
    private Map<LanguageCode, String> descriptionTranslations = new HashMap<>();
    private Map<LanguageCode, String> placeholderTranslations = new HashMap<>();
    private Map<LanguageCode, String> helpTextTranslations = new HashMap<>();
    private ElementType type;
    private boolean required;
    private Integer displayOrder;
    private List<ElementOptionDTO> options = new ArrayList<>();
    private String validationRegex;
    private Integer minLength;
    private Integer maxLength;
    private Double minValue;
    private Double maxValue;
    private Map<String, String> properties = new HashMap<>();
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Helper method to get label in specific language with fallback
    public String getLabel(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String label = labelTranslations.get(languageCode);
        if (label == null || label.isEmpty()) {
            label = labelTranslations.get(fallbackLanguageCode);
        }
        return label;
    }
    
    // Helper method to get description in specific language with fallback
    public String getDescription(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String description = descriptionTranslations.get(languageCode);
        if (description == null || description.isEmpty()) {
            description = descriptionTranslations.get(fallbackLanguageCode);
        }
        return description;
    }
    
    // Helper method to get placeholder in specific language with fallback
    public String getPlaceholder(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String placeholder = placeholderTranslations.get(languageCode);
        if (placeholder == null || placeholder.isEmpty()) {
            placeholder = placeholderTranslations.get(fallbackLanguageCode);
        }
        return placeholder;
    }
    
    // Helper method to get help text in specific language with fallback
    public String getHelpText(LanguageCode languageCode, LanguageCode fallbackLanguageCode) {
        String helpText = helpTextTranslations.get(languageCode);
        if (helpText == null || helpText.isEmpty()) {
            helpText = helpTextTranslations.get(fallbackLanguageCode);
        }
        return helpText;
    }
}
