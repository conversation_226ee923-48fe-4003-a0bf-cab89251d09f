package com.lookforx.questionformservice.dto;

import com.lookforx.questionformservice.domain.ElementType;
import com.lookforx.common.enums.LanguageCode;
import lombok.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Localized DTO for FormElement that contains only the content in a specific language
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LocalizedFormElementDTO {
    
    private Long id;
    private String label;
    private String description;
    private String placeholder;
    private String helpText;
    private ElementType type;
    private boolean required;
    private Integer displayOrder;
    private List<LocalizedElementOptionDTO> options = new ArrayList<>();
    private String validationRegex;
    private Integer minLength;
    private Integer maxLength;
    private Double minValue;
    private Double maxValue;
    private Map<String, String> properties = new HashMap<>();
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Language information
    private LanguageCode languageCode;
    private LanguageCode fallbackLanguageCode;
    
    // Indicates if fallback language was used for any field
    private boolean labelFromFallback;
    private boolean descriptionFromFallback;
    private boolean placeholderFromFallback;
    private boolean helpTextFromFallback;
}
