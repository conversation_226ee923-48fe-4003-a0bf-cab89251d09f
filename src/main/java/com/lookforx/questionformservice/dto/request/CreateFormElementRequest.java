package com.lookforx.questionformservice.dto.request;

import com.lookforx.questionformservice.domain.ElementType;
import com.lookforx.common.enums.LanguageCode;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateFormElementRequest {
    
    @NotEmpty(message = "Element label translations are required")
    private Map<LanguageCode, String> labelTranslations = new HashMap<>();
    
    private Map<LanguageCode, String> descriptionTranslations = new HashMap<>();
    
    private Map<LanguageCode, String> placeholderTranslations = new HashMap<>();
    
    private Map<LanguageCode, String> helpTextTranslations = new HashMap<>();
    
    @NotNull(message = "Element type is required")
    private ElementType type;
    
    private boolean required;
    
    private int displayOrder;
    
    @Valid
    private List<CreateElementOptionRequest> options = new ArrayList<>();
    
    // Validasyon kuralları
    private String validationRegex;
    private Integer minLength;
    private Integer maxLength;
    private Double minValue;
    private Double maxValue;
    
    // Özel özellikler
    private Map<String, String> properties = new HashMap<>();
}
