package com.lookforx.questionformservice.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SubmitFormRequest {

    @NotNull(message = "Form template ID is required")
    private Long formTemplateId;

    @NotNull(message = "Category ID is required")
    private Long categoryId;

    @NotNull(message = "User ID is required")
    private Long userId;

    @NotNull(message = "Request ID is required")
    private String requestId;

    @NotEmpty(message = "At least one form response is required")
    @Valid
    private List<FormResponseRequest> responses = new ArrayList<>();
}