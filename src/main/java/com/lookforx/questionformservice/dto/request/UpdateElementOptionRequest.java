package com.lookforx.questionformservice.dto.request;

import com.lookforx.common.enums.LanguageCode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateElementOptionRequest {
    
    private Long id;
    
    @NotBlank(message = "Option value is required")
    private String value;
    
    @NotEmpty(message = "Option label translations are required")
    private Map<LanguageCode, String> labelTranslations = new HashMap<>();
    
    @NotNull(message = "Display order is required")
    private Integer displayOrder;
    
    private boolean defaultSelected;
    
    // Çok dilli özellikler - her özellik anahtarı için dil bazlı değerler
    private Map<String, Map<LanguageCode, String>> propertyTranslations = new HashMap<>();
}
