package com.lookforx.questionformservice.dto.request;

import com.lookforx.common.enums.LanguageCode;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UpdateFormTemplateRequest {
    
    @NotEmpty(message = "Form name translations are required")
    private Map<LanguageCode, String> nameTranslations = new HashMap<>();
    
    @NotNull(message = "Category ID is required")
    private Long categoryId;
    
    private Map<LanguageCode, String> descriptionTranslations = new HashMap<>();
    
    @NotEmpty(message = "At least one form element is required")
    @Valid
    private List<UpdateFormElementRequest> elements = new ArrayList<>();
    
    private boolean active = true;
}