package com.lookforx.configserver;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.config.server.EnableConfigServer;

/**
 * Spring Cloud Config Server application.
 * Bootstraps a Spring Boot application configured to act as a
 * central configuration server, exposing configuration properties
 * to client applications.
 *
 */
@SpringBootApplication
@EnableConfigServer
public class ConfigServerApplication {

    /**
     * Entry point for the Config Server.
     * Launches the Spring context and starts the embedded web server
     * to serve configuration properties.
     *
     * @param args command-line arguments (ignored)
     */
    public static void main(String[] args) {
        SpringApplication.run(ConfigServerApplication.class, args);
    }

}