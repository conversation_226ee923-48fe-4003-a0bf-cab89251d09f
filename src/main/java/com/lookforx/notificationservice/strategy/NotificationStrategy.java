package com.lookforx.notificationservice.strategy;

import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;

/**
 * Strategy interface for different notification types
 */
public interface NotificationStrategy {
    
    /**
     * Send notification using this strategy
     * @param notification The notification to send
     * @return true if sent successfully, false otherwise
     */
    boolean sendNotification(Notification notification);
    
    /**
     * Get the notification type this strategy handles
     * @return NotificationType
     */
    NotificationType getNotificationType();
    
    /**
     * Check if this strategy is available/configured
     * @return true if available, false otherwise
     */
    boolean isAvailable();
    
    /**
     * Get the maximum retry attempts for this strategy
     * @return maximum retry attempts
     */
    default int getMaxRetryAttempts() {
        return 3;
    }
    
    /**
     * Calculate next retry delay in minutes
     * @param attemptNumber current attempt number (1-based)
     * @return delay in minutes
     */
    default long getRetryDelayMinutes(int attemptNumber) {
        // Exponential backoff: 1, 2, 4, 8, 16 minutes
        return (long) Math.pow(2, attemptNumber - 1);
    }
}
