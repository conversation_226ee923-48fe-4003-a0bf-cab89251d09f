package com.lookforx.notificationservice.strategy.impl;

import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;
import com.lookforx.notificationservice.strategy.NotificationStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.UUID;

/**
 * Email notification strategy implementation
 */
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "notification.email.enabled", havingValue = "true", matchIfMissing = false)
@Slf4j
public class EmailNotificationStrategy implements NotificationStrategy {
    
    private final JavaMailSender mailSender;
    private final TemplateEngine templateEngine;
    
    @Value("${notification.email.from:<EMAIL>}")
    private String fromEmail;
    
    @Value("${notification.email.from-name:LookForX}")
    private String fromName;
    
    @Value("${notification.email.enabled:true}")
    private boolean emailEnabled;
    
    @Override
    public boolean sendNotification(Notification notification) {
        if (!isAvailable()) {
            log.warn("Email notification is not available");
            return false;
        }
        
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            
            // Set basic email properties
            helper.setFrom(fromEmail, fromName);
            helper.setTo(notification.getRecipient());
            helper.setSubject(notification.getSubject());
            
            // Generate email content
            String emailContent = generateEmailContent(notification);
            helper.setText(emailContent, true); // true = HTML content
            
            // Generate unique message ID for tracking
            String messageId = UUID.randomUUID().toString() + "@lookforx.com";
            message.setHeader("Message-ID", messageId);
            
            // Send email
            mailSender.send(message);
            
            // Update notification with external ID
            notification.setExternalId(messageId);
            
            log.info("Email sent successfully: eventId={}, recipient={}, messageId={}", 
                notification.getEventId(), notification.getRecipient(), messageId);
            
            return true;
            
        } catch (MailException | MessagingException | java.io.UnsupportedEncodingException e) {
            log.error("Failed to send email: eventId={}, recipient={}, error={}", 
                notification.getEventId(), notification.getRecipient(), e.getMessage(), e);
            
            notification.setErrorMessage(e.getMessage());
            return false;
        }
    }
    
    private String generateEmailContent(Notification notification) {
        if (StringUtils.hasText(notification.getTemplateName())) {
            // Use Thymeleaf template
            Context context = new Context();
            
            // Add template parameters
            if (notification.getTemplateParameters() != null) {
                notification.getTemplateParameters().forEach(context::setVariable);
            }
            
            // Add common variables
            context.setVariable("subject", notification.getSubject());
            context.setVariable("recipient", notification.getRecipient());
            
            try {
                return templateEngine.process(notification.getTemplateName(), context);
            } catch (Exception e) {
                log.warn("Failed to process email template: {}, falling back to plain content", 
                    notification.getTemplateName(), e);
                return notification.getContent();
            }
        } else {
            // Use plain content
            return notification.getContent();
        }
    }
    
    @Override
    public NotificationType getNotificationType() {
        return NotificationType.EMAIL;
    }
    
    @Override
    public boolean isAvailable() {
        return emailEnabled && mailSender != null;
    }
    
    @Override
    public int getMaxRetryAttempts() {
        return 3;
    }
}
