package com.lookforx.notificationservice.strategy.impl;

import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;
import com.lookforx.notificationservice.strategy.NotificationStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * SMS notification strategy implementation
 * This is a mock implementation - replace with actual SMS provider (Twilio, AWS SNS, etc.)
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SmsNotificationStrategy implements NotificationStrategy {
    
    private final RestTemplate restTemplate = new RestTemplate();
    
    @Value("${notification.sms.enabled:false}")
    private boolean smsEnabled;
    
    @Value("${notification.sms.provider.url:}")
    private String smsProviderUrl;
    
    @Value("${notification.sms.provider.api-key:}")
    private String apiKey;
    
    @Value("${notification.sms.provider.sender-id:LookForX}")
    private String senderId;
    
    @Override
    public boolean sendNotification(Notification notification) {
        if (!isAvailable()) {
            log.warn("SMS notification is not available");
            return false;
        }
        
        try {
            // Prepare SMS request
            Map<String, Object> smsRequest = prepareSmsRequest(notification);
            
            // Set headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(apiKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(smsRequest, headers);
            
            // Send SMS via external provider
            ResponseEntity<Map> response = restTemplate.exchange(
                smsProviderUrl,
                HttpMethod.POST,
                entity,
                Map.class
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                // Extract message ID from response
                Map<String, Object> responseBody = response.getBody();
                String messageId = extractMessageId(responseBody);
                notification.setExternalId(messageId);
                
                log.info("SMS sent successfully: eventId={}, recipient={}, messageId={}", 
                    notification.getEventId(), notification.getRecipient(), messageId);
                
                return true;
            } else {
                log.error("SMS provider returned error: eventId={}, status={}", 
                    notification.getEventId(), response.getStatusCode());
                
                notification.setErrorMessage("SMS provider error: " + response.getStatusCode());
                return false;
            }
            
        } catch (Exception e) {
            log.error("Failed to send SMS: eventId={}, recipient={}, error={}", 
                notification.getEventId(), notification.getRecipient(), e.getMessage(), e);
            
            notification.setErrorMessage(e.getMessage());
            return false;
        }
    }
    
    private Map<String, Object> prepareSmsRequest(Notification notification) {
        Map<String, Object> request = new HashMap<>();
        request.put("to", notification.getRecipient());
        request.put("from", senderId);
        request.put("text", notification.getContent());
        request.put("reference", notification.getEventId());
        
        return request;
    }
    
    private String extractMessageId(Map<String, Object> responseBody) {
        if (responseBody != null && responseBody.containsKey("messageId")) {
            return responseBody.get("messageId").toString();
        }
        return UUID.randomUUID().toString();
    }
    
    @Override
    public NotificationType getNotificationType() {
        return NotificationType.SMS;
    }
    
    @Override
    public boolean isAvailable() {
        return smsEnabled && 
               StringUtils.hasText(smsProviderUrl) && 
               StringUtils.hasText(apiKey);
    }
    
    @Override
    public int getMaxRetryAttempts() {
        return 2; // SMS typically has fewer retry attempts
    }
    
    @Override
    public long getRetryDelayMinutes(int attemptNumber) {
        // Shorter delays for SMS: 1, 3 minutes
        return attemptNumber == 1 ? 1 : 3;
    }
}
