package com.lookforx.notificationservice.strategy.impl;

import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;
import com.lookforx.notificationservice.strategy.NotificationStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Push notification strategy implementation
 * This is a mock implementation - replace with actual push provider (Firebase FCM, Apple APNS, etc.)
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PushNotificationStrategy implements NotificationStrategy {
    
    private final RestTemplate restTemplate = new RestTemplate();
    
    @Value("${notification.push.enabled:false}")
    private boolean pushEnabled;
    
    @Value("${notification.push.fcm.url:https://fcm.googleapis.com/fcm/send}")
    private String fcmUrl;
    
    @Value("${notification.push.fcm.server-key:}")
    private String fcmServerKey;
    
    @Override
    public boolean sendNotification(Notification notification) {
        if (!isAvailable()) {
            log.warn("Push notification is not available");
            return false;
        }
        
        try {
            // Prepare FCM request
            Map<String, Object> fcmRequest = prepareFcmRequest(notification);
            
            // Set headers
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "key=" + fcmServerKey);
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(fcmRequest, headers);
            
            // Send push notification via FCM
            ResponseEntity<Map> response = restTemplate.exchange(
                fcmUrl,
                HttpMethod.POST,
                entity,
                Map.class
            );
            
            if (response.getStatusCode().is2xxSuccessful()) {
                // Extract message ID from response
                Map<String, Object> responseBody = response.getBody();
                String messageId = extractMessageId(responseBody);
                notification.setExternalId(messageId);
                
                log.info("Push notification sent successfully: eventId={}, recipient={}, messageId={}", 
                    notification.getEventId(), notification.getRecipient(), messageId);
                
                return true;
            } else {
                log.error("FCM returned error: eventId={}, status={}", 
                    notification.getEventId(), response.getStatusCode());
                
                notification.setErrorMessage("FCM error: " + response.getStatusCode());
                return false;
            }
            
        } catch (Exception e) {
            log.error("Failed to send push notification: eventId={}, recipient={}, error={}", 
                notification.getEventId(), notification.getRecipient(), e.getMessage(), e);
            
            notification.setErrorMessage(e.getMessage());
            return false;
        }
    }
    
    private Map<String, Object> prepareFcmRequest(Notification notification) {
        Map<String, Object> request = new HashMap<>();
        request.put("to", notification.getRecipient()); // FCM token
        
        // Notification payload
        Map<String, Object> notificationPayload = new HashMap<>();
        notificationPayload.put("title", notification.getSubject());
        notificationPayload.put("body", notification.getContent());
        notificationPayload.put("sound", "default");
        
        request.put("notification", notificationPayload);
        
        // Data payload
        Map<String, Object> dataPayload = new HashMap<>();
        dataPayload.put("eventId", notification.getEventId());
        dataPayload.put("eventType", notification.getEventType());
        dataPayload.put("userId", notification.getUserId().toString());
        
        // Add template parameters as data
        if (notification.getTemplateParameters() != null) {
            dataPayload.putAll(notification.getTemplateParameters());
        }
        
        request.put("data", dataPayload);
        
        return request;
    }
    
    private String extractMessageId(Map<String, Object> responseBody) {
        if (responseBody != null && responseBody.containsKey("multicast_id")) {
            return responseBody.get("multicast_id").toString();
        }
        return UUID.randomUUID().toString();
    }
    
    @Override
    public NotificationType getNotificationType() {
        return NotificationType.PUSH_NOTIFICATION;
    }
    
    @Override
    public boolean isAvailable() {
        return pushEnabled && 
               StringUtils.hasText(fcmUrl) && 
               StringUtils.hasText(fcmServerKey);
    }
    
    @Override
    public int getMaxRetryAttempts() {
        return 2; // Push notifications typically have fewer retry attempts
    }
    
    @Override
    public long getRetryDelayMinutes(int attemptNumber) {
        // Shorter delays for push: 2, 5 minutes
        return attemptNumber == 1 ? 2 : 5;
    }
}
