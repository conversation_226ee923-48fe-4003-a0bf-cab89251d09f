package com.lookforx.notificationservice.strategy;

import com.lookforx.common.enums.NotificationType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Factory for getting appropriate notification strategy
 */
@Component
@Slf4j
public class NotificationStrategyFactory {

    private final List<NotificationStrategy> strategies;
    private final Map<NotificationType, NotificationStrategy> strategyMap;

    public NotificationStrategyFactory(List<NotificationStrategy> strategies) {
        this.strategies = strategies;
        this.strategyMap = strategies.stream()
                .collect(Collectors.toMap(
                    NotificationStrategy::getNotificationType,
                    Function.identity()
                ));

        log.info("Initialized notification strategies: {}",
            strategyMap.keySet().stream()
                .map(Enum::name)
                .collect(Collectors.joining(", ")));
    }
    
    /**
     * Get strategy for specific notification type
     * @param notificationType the notification type
     * @return Optional containing the strategy if available
     */
    public Optional<NotificationStrategy> getStrategy(NotificationType notificationType) {
        NotificationStrategy strategy = strategyMap.get(notificationType);
        
        if (strategy == null) {
            log.warn("No strategy found for notification type: {}", notificationType);
            return Optional.empty();
        }
        
        if (!strategy.isAvailable()) {
            log.warn("Strategy for {} is not available", notificationType);
            return Optional.empty();
        }
        
        return Optional.of(strategy);
    }
    
    /**
     * Get all available strategies
     * @return list of available strategies
     */
    public List<NotificationStrategy> getAvailableStrategies() {
        return strategies.stream()
                .filter(NotificationStrategy::isAvailable)
                .collect(Collectors.toList());
    }
    
    /**
     * Check if strategy is available for notification type
     * @param notificationType the notification type
     * @return true if available, false otherwise
     */
    public boolean isStrategyAvailable(NotificationType notificationType) {
        return getStrategy(notificationType).isPresent();
    }
    
    /**
     * Get all supported notification types
     * @return list of supported notification types
     */
    public List<NotificationType> getSupportedTypes() {
        return strategies.stream()
                .filter(NotificationStrategy::isAvailable)
                .map(NotificationStrategy::getNotificationType)
                .collect(Collectors.toList());
    }
}
