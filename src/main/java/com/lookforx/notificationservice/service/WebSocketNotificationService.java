package com.lookforx.notificationservice.service;

import com.lookforx.notificationservice.dto.NotificationDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

/**
 * Service for sending real-time notifications via WebSocket
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class WebSocketNotificationService {

    private final SimpMessagingTemplate messagingTemplate;

    /**
     * Send notification update to all admin users
     */
    public void sendAdminNotificationUpdate() {
        try {
            log.info("Sending admin notification update via WebSocket");
            messagingTemplate.convertAndSend("/topic/admin/notifications", "update");
        } catch (Exception e) {
            log.error("Error sending admin notification update", e);
        }
    }

    /**
     * Send bell notification count update to specific user
     */
    public void sendBellNotificationCountUpdate(String userId, long count) {
        try {
            log.info("Sending bell notification count update for user {} via WebSocket: {}", userId, count);
            messagingTemplate.convertAndSendToUser(userId, "/queue/bell-count", count);
        } catch (Exception e) {
            log.error("Error sending bell notification count update for user {}", userId, e);
        }
    }

    /**
     * Send new notification to specific user
     */
    public void sendNewNotificationToUser(String userId, NotificationDto notification) {
        try {
            log.info("Sending new notification to user {} via WebSocket", userId);
            messagingTemplate.convertAndSendToUser(userId, "/queue/notifications", notification);
        } catch (Exception e) {
            log.error("Error sending new notification to user {}", userId, e);
        }
    }

    /**
     * Send notification to all admin users
     */
    public void sendAdminNotification(NotificationDto notification) {
        try {
            log.info("Sending admin notification via WebSocket");
            messagingTemplate.convertAndSend("/topic/admin/new-notification", notification);
        } catch (Exception e) {
            log.error("Error sending admin notification", e);
        }
    }
}
