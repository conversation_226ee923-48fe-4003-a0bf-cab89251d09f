package com.lookforx.notificationservice.service;

import com.lookforx.notificationservice.dto.BulkNotificationCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * Service for processing bulk notification commands from Kafka
 */
@Service
@Slf4j
public class BulkNotificationProcessor {

    private final OptimizedBulkNotificationService optimizedBulkNotificationService;

    public BulkNotificationProcessor(OptimizedBulkNotificationService optimizedBulkNotificationService) {
        this.optimizedBulkNotificationService = optimizedBulkNotificationService;
    }

    /**
     * Process bulk notification command from Kafka using optimized approach
     */
    public void processBulkNotificationCommand(BulkNotificationCommand command) {
        log.info("Processing optimized bulk notification command. CommandId: {}, CorrelationId: {}",
                command.getCommandId(), command.getCorrelationId());

        long startTime = System.currentTimeMillis();

        try {
            // Use optimized bulk notification service
            optimizedBulkNotificationService.createBulkNotification(command);

            long processingTime = System.currentTimeMillis() - startTime;

            log.info("Optimized bulk notification processing completed. CommandId: {}, Time: {}ms",
                    command.getCommandId(), processingTime);
            
        } catch (Exception e) {
            log.error("Error during bulk notification processing. CommandId: {}", command.getCommandId(), e);
            throw new RuntimeException("Bulk notification processing failed", e);
        }
    }
}
