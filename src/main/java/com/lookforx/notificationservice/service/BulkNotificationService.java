package com.lookforx.notificationservice.service;

import com.lookforx.notificationservice.domain.BulkNotification;
import com.lookforx.notificationservice.domain.Notification;
import com.lookforx.notificationservice.domain.BulkNotificationStatus;
import com.lookforx.notificationservice.dto.CreateBulkNotificationRequest;
import com.lookforx.notificationservice.repository.BulkNotificationRepository;
import com.lookforx.notificationservice.repository.BulkNotificationStatusRepository;
import com.lookforx.common.enums.LanguageCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.stream.Collectors;

import java.time.LocalDateTime;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class BulkNotificationService {

    private final BulkNotificationRepository bulkNotificationRepository;
    private final BulkNotificationStatusRepository bulkNotificationStatusRepository;

    /**
     * Create bulk notification for all users
     */
    @Transactional
    @CacheEvict(value = {"bulkNotifications", "notificationCounts"}, allEntries = true)
    public Map<String, Object> createBulkNotification(CreateBulkNotificationRequest request) {
        try {
            BulkNotification bulkNotification = BulkNotification.builder()
                .notificationType(request.getNotificationType())
                .priority(request.getPriority())
                .titles(request.getTitles())
                .messages(request.getMessages())
                .actionUrl(request.getActionUrl())
                .iconUrl(request.getIconUrl())
                .expiresAt(request.getExpiresAt())
                .relatedEntityType(request.getRelatedEntityType())
                .relatedEntityId(request.getRelatedEntityId())
                .showInBell(request.getShowInBell())
                .sendEmail(request.getSendEmail())
                .sendPush(request.getSendPush())
                .status(Notification.NotificationStatus.SENT)
                .build();

            BulkNotification savedNotification = bulkNotificationRepository.save(bulkNotification);
            
            log.info("Bulk notification created successfully with ID: {}", savedNotification.getId());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("id", savedNotification.getId());
            result.put("message", "Bulk notification created successfully");
            result.put("createdAt", savedNotification.getCreatedAt());
            
            return result;
        } catch (Exception e) {
            log.error("Error creating bulk notification", e);
            throw new RuntimeException("Failed to create bulk notification", e);
        }
    }

    /**
     * Get bulk notifications for dashboard with user read status
     */
    @Cacheable(value = "bulkNotifications", key = "#userId + '_' + #page + '_' + #size")
    public Map<String, Object> getDashboardBulkNotifications(Long userId, int page, int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            Page<BulkNotification> bulkNotifications = bulkNotificationRepository.findActiveNotifications(LocalDateTime.now(), pageable);
            
            List<Map<String, Object>> notificationList = new ArrayList<>();
            
            for (BulkNotification bulkNotification : bulkNotifications.getContent()) {
                Map<String, Object> notificationData = new HashMap<>();
                notificationData.put("id", bulkNotification.getId());
                notificationData.put("titles", bulkNotification.getTitles());
                notificationData.put("messages", bulkNotification.getMessages());
                notificationData.put("notificationType", bulkNotification.getNotificationType());
                notificationData.put("priority", bulkNotification.getPriority());
                notificationData.put("createdAt", bulkNotification.getCreatedAt());
                notificationData.put("expiresAt", bulkNotification.getExpiresAt());
                notificationData.put("actionUrl", bulkNotification.getActionUrl());
                notificationData.put("iconUrl", bulkNotification.getIconUrl());
                
                // Check if user has read this notification
                boolean isRead = bulkNotificationStatusRepository.findByUserIdAndBulkNotificationId(
                    userId, bulkNotification.getId()).map(status -> status.getIsRead()).orElse(false);
                notificationData.put("isRead", isRead);
                
                notificationList.add(notificationData);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("notifications", notificationList);
            result.put("totalElements", bulkNotifications.getTotalElements());
            result.put("totalPages", bulkNotifications.getTotalPages());
            result.put("currentPage", page);
            result.put("size", size);
            
            return result;
        } catch (Exception e) {
            log.error("Error getting dashboard bulk notifications for user: {}", userId, e);
            throw new RuntimeException("Failed to get dashboard bulk notifications", e);
        }
    }

    /**
     * Mark bulk notification as read for user
     */
    @Transactional
    @CacheEvict(value = {"bulkNotifications", "userNotificationStatus", "notificationCounts"}, allEntries = true)
    public void markBulkNotificationAsRead(Long userId, String bulkNotificationId) {
        try {
            // Check if status record exists
            Optional<BulkNotificationStatus> existingStatus = bulkNotificationStatusRepository
                .findByUserIdAndBulkNotificationId(userId, bulkNotificationId);

            if (existingStatus.isPresent()) {
                BulkNotificationStatus status = existingStatus.get();
                if (!status.getIsRead()) {
                    status.setIsRead(true);
                    status.setReadAt(LocalDateTime.now());
                    bulkNotificationStatusRepository.save(status);
                    log.info("Marked bulk notification {} as read for user {}", bulkNotificationId, userId);
                }
            } else {
                // Create new status record
                BulkNotification bulkNotification = bulkNotificationRepository.findById(bulkNotificationId)
                    .orElseThrow(() -> new RuntimeException("Bulk notification not found: " + bulkNotificationId));

                BulkNotificationStatus status = BulkNotificationStatus.builder()
                    .userId(userId)
                    .bulkNotification(bulkNotification)
                    .isRead(true)
                    .isClicked(false)
                    .showInBell(false) // Bulk notifications are not shown in bell
                    .readAt(LocalDateTime.now())
                    .build();

                bulkNotificationStatusRepository.save(status);
                log.info("Created read status for bulk notification {} for user {}", bulkNotificationId, userId);
            }
        } catch (Exception e) {
            log.error("Error marking bulk notification as read for user: {} notification: {}", userId, bulkNotificationId, e);
            throw new RuntimeException("Failed to mark bulk notification as read", e);
        }
    }

    /**
     * Mark all bulk notifications as read for user
     */
    @Transactional
    public int markAllBulkNotificationsAsRead(Long userId) {
        try {
            LocalDateTime now = LocalDateTime.now();
            List<BulkNotification> activeNotifications = bulkNotificationRepository.findSentActiveNotifications(now);
            
            int updatedCount = 0;
            for (BulkNotification bulkNotification : activeNotifications) {
                Optional<BulkNotificationStatus> existingStatus = bulkNotificationStatusRepository
                    .findByUserIdAndBulkNotificationId(userId, bulkNotification.getId());

                if (existingStatus.isPresent()) {
                    BulkNotificationStatus status = existingStatus.get();
                    if (!status.getIsRead()) {
                        status.setIsRead(true);
                        status.setReadAt(now);
                        bulkNotificationStatusRepository.save(status);
                        updatedCount++;
                    }
                } else {
                    // Create new status record
                    BulkNotificationStatus status = BulkNotificationStatus.builder()
                        .userId(userId)
                        .bulkNotification(bulkNotification)
                        .isRead(true)
                        .isClicked(false)
                        .showInBell(false) // Bulk notifications are not shown in bell
                        .readAt(now)
                        .build();

                    bulkNotificationStatusRepository.save(status);
                    updatedCount++;
                }
            }
            
            log.info("Marked {} bulk notifications as read for user {}", updatedCount, userId);
            return updatedCount;
        } catch (Exception e) {
            log.error("Error marking all bulk notifications as read for user: {}", userId, e);
            throw new RuntimeException("Failed to mark all bulk notifications as read", e);
        }
    }

    /**
     * Get bulk notification statistics
     */
    @Cacheable(value = "notificationCounts", key = "'bulkStats'")
    public Map<String, Object> getBulkNotificationStats() {
        try {
            LocalDateTime now = LocalDateTime.now();
            long totalActive = bulkNotificationRepository.countActiveNotifications(now);

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalActiveBulkNotifications", totalActive);
            stats.put("timestamp", now);

            return stats;
        } catch (Exception e) {
            log.error("Error getting bulk notification stats", e);
            throw new RuntimeException("Failed to get bulk notification statistics", e);
        }
    }

    /**
     * Get recent bulk notifications for dashboard display (last 10 days)
     */
    public List<BulkNotification> getRecentBulkNotificationsLast10Days(int page, int size) {
        try {
            LocalDateTime tenDaysAgo = LocalDateTime.now().minusDays(10);
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            Page<BulkNotification> pageResult = bulkNotificationRepository.findByCreatedAtAfter(tenDaysAgo, pageable);
            return pageResult.getContent();
        } catch (Exception e) {
            log.error("Error getting recent bulk notifications for last 10 days", e);
            throw new RuntimeException("Failed to get recent bulk notifications", e);
        }
    }

    /**
     * Get bulk notifications for admin panel with pagination, language support and filtering
     */
    @Cacheable(value = "bulkNotifications", key = "#page + '_' + #size + '_' + #languageCode + '_' + #search + '_' + #status + '_' + #priority")
    public Map<String, Object> getBulkNotificationsForAdmin(int page, int size, LanguageCode languageCode,
                                                           String search, String status, String priority) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            Page<BulkNotification> bulkNotifications;

            // Apply filters if provided
            if (search != null && !search.trim().isEmpty()) {
                // Search in titles and messages for the given language
                bulkNotifications = bulkNotificationRepository.findAll(pageable);
                // Filter by search term in memory (could be optimized with custom query)
                List<BulkNotification> filteredList = bulkNotifications.getContent().stream()
                    .filter(notification -> {
                        String title = getTitleByLanguage(notification, languageCode);
                        String message = getMessageByLanguage(notification, languageCode);
                        return title.toLowerCase().contains(search.toLowerCase()) ||
                               message.toLowerCase().contains(search.toLowerCase());
                    })
                    .collect(Collectors.toList());
                bulkNotifications = new PageImpl<>(filteredList, pageable, filteredList.size());
            } else {
                bulkNotifications = bulkNotificationRepository.findAll(pageable);
            }

            List<Map<String, Object>> notificationList = bulkNotifications.getContent().stream()
                .map(notification -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("id", notification.getId());
                    item.put("title", getTitleByLanguage(notification, languageCode));
                    item.put("message", getMessageByLanguage(notification, languageCode));
                    item.put("status", notification.getStatus().toString());
                    item.put("priority", notification.getPriority().toString());
                    item.put("createdAt", notification.getCreatedAt());
                    item.put("targetUserCount", notification.getTargetUserCount());
                    item.put("readCount", notification.getReadCount());
                    item.put("clickedCount", notification.getClickedCount());
                    item.put("showInBell", notification.getShowInBell());
                    item.put("sendEmail", notification.getSendEmail());
                    item.put("sendPush", notification.getSendPush());
                    item.put("language", languageCode.toString().toLowerCase());
                    return item;
                })
                .collect(Collectors.toList());

            Map<String, Object> result = new HashMap<>();
            result.put("content", notificationList);
            result.put("totalElements", bulkNotifications.getTotalElements());
            result.put("totalPages", bulkNotifications.getTotalPages());
            result.put("currentPage", page);
            result.put("size", size);
            result.put("hasNext", bulkNotifications.hasNext());
            result.put("hasPrevious", bulkNotifications.hasPrevious());

            return result;
        } catch (Exception e) {
            log.error("Error getting bulk notifications for admin panel", e);
            throw new RuntimeException("Failed to get bulk notifications for admin", e);
        }
    }

    /**
     * Get title by language with fallback to English
     */
    public String getTitleByLanguage(BulkNotification notification, LanguageCode languageCode) {
        if (notification == null || notification.getTitles() == null) {
            log.warn("Notification or titles is null");
            return null;
        }

        log.info("Getting title for language: {}, available titles: {}", languageCode, notification.getTitles());

        // Try requested language first (convert to uppercase for database lookup)
        String requestedLanguageKey = languageCode.toString().toUpperCase();
        String title = notification.getTitles().get(requestedLanguageKey);
        if (title != null && !title.trim().isEmpty()) {
            log.info("Found title for requested language {}: {}", languageCode, title);
            return title;
        }

        // Fallback to English
        title = notification.getTitles().get("EN");
        if (title != null && !title.trim().isEmpty()) {
            log.info("Using English fallback title: {}", title);
            return title;
        }

        // Fallback to any available language
        String fallbackTitle = notification.getTitles().values().stream()
                .filter(t -> t != null && !t.trim().isEmpty())
                .findFirst()
                .orElse(null);
        log.info("Using any available language fallback title: {}", fallbackTitle);
        return fallbackTitle;
    }

    /**
     * Get message by language with fallback to English
     */
    public String getMessageByLanguage(BulkNotification notification, LanguageCode languageCode) {
        if (notification == null || notification.getMessages() == null) {
            log.warn("Notification or messages is null");
            return null;
        }

        log.info("Getting message for language: {}, available messages: {}", languageCode, notification.getMessages());

        // Try requested language first (convert to uppercase for database lookup)
        String requestedLanguageKey = languageCode.toString().toUpperCase();
        String message = notification.getMessages().get(requestedLanguageKey);
        if (message != null && !message.trim().isEmpty()) {
            log.info("Found message for requested language {}: {}", languageCode, message);
            return message;
        }

        // Fallback to English
        message = notification.getMessages().get("EN");
        if (message != null && !message.trim().isEmpty()) {
            log.info("Using English fallback message: {}", message);
            return message;
        }

        // Fallback to any available language
        return notification.getMessages().values().stream()
                .filter(m -> m != null && !m.trim().isEmpty())
                .findFirst()
                .orElse(null);
    }

    /**
     * Delete bulk notification by ID
     */
    @Transactional
    @CacheEvict(value = "bulkNotifications", allEntries = true)
    public void deleteBulkNotification(String id) {
        try {
            log.info("Deleting bulk notification with ID: {}", id);

            // Check if notification exists
            if (!bulkNotificationRepository.existsById(id)) {
                throw new RuntimeException("Bulk notification not found with ID: " + id);
            }

            // Delete all related status records first
            log.info("Deleting status records for bulk notification ID: {}", id);
            bulkNotificationStatusRepository.deleteByBulkNotificationId(id);
            log.info("Deleted all status records for bulk notification ID: {}", id);

            // Delete the notification using repository deleteById method
            bulkNotificationRepository.deleteById(id);
            log.info("Successfully deleted bulk notification with ID: {}", id);

        } catch (Exception e) {
            log.error("Error deleting bulk notification with ID: {}", id, e);
            throw new RuntimeException("Failed to delete bulk notification: " + e.getMessage(), e);
        }
    }

    /**
     * Update bulk notification
     */
    @Transactional
    @CacheEvict(value = "bulkNotifications", allEntries = true)
    public BulkNotification updateBulkNotification(String id, CreateBulkNotificationRequest request) {
        try {
            log.info("Updating bulk notification with ID: {}", id);

            // Check if notification exists
            BulkNotification notification = bulkNotificationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Bulk notification not found with ID: " + id));

            // Update notification fields
            notification.getTitles().clear();
            notification.getTitles().putAll(request.getTitles());

            notification.getMessages().clear();
            notification.getMessages().putAll(request.getMessages());

            notification.setPriority(request.getPriority());
            notification.setSendEmail(request.getSendEmail());
            notification.setSendPush(request.getSendPush());
            notification.setShowInBell(request.getShowInBell());
            notification.setActionUrl(request.getActionUrl());
            notification.setIconUrl(request.getIconUrl());
            notification.setExpiresAt(request.getExpiresAt());
            notification.setRelatedEntityType(request.getRelatedEntityType());
            notification.setRelatedEntityId(request.getRelatedEntityId());

            // Save updated notification
            BulkNotification updatedNotification = bulkNotificationRepository.save(notification);
            log.info("Successfully updated bulk notification with ID: {}", id);

            return updatedNotification;

        } catch (Exception e) {
            log.error("Error updating bulk notification with ID: {}", id, e);
            throw new RuntimeException("Failed to update bulk notification: " + e.getMessage(), e);
        }
    }

    /**
     * Get bulk notification by ID with language-specific content
     */
    public Map<String, Object> getBulkNotificationById(String id, LanguageCode languageCode) {
        try {
            log.info("Getting bulk notification with ID: {} for language: {}", id, languageCode);

            BulkNotification notification = bulkNotificationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Bulk notification not found with ID: " + id));

            Map<String, Object> result = new HashMap<>();
            result.put("id", notification.getId());
            result.put("title", getTitleByLanguage(notification, languageCode));
            result.put("message", getMessageByLanguage(notification, languageCode));
            result.put("status", notification.getStatus().toString());
            result.put("priority", notification.getPriority().toString());
            result.put("notificationType", notification.getNotificationType().toString());
            result.put("createdAt", notification.getCreatedAt());
            result.put("targetUserCount", notification.getTargetUserCount());
            result.put("readCount", notification.getReadCount());
            result.put("clickedCount", notification.getClickedCount());
            result.put("showInBell", notification.getShowInBell());
            result.put("sendEmail", notification.getSendEmail());
            result.put("sendPush", notification.getSendPush());

            // Include all translations for editing
            Map<String, String> allTitles = notification.getTitles() != null ? notification.getTitles() : new HashMap<>();
            Map<String, String> allMessages = notification.getMessages() != null ? notification.getMessages() : new HashMap<>();

            result.put("titles", allTitles);
            result.put("messages", allMessages);
            result.put("language", languageCode.toString().toLowerCase());

            log.info("Successfully retrieved bulk notification with ID: {}", id);
            return result;

        } catch (Exception e) {
            log.error("Error getting bulk notification with ID: {}", id, e);
            throw new RuntimeException("Failed to get bulk notification", e);
        }
    }

    /**
     * Fix missing createdAt timestamps for existing bulk notifications
     */
    public void fixMissingCreatedAtTimestamps() {
        try {
            List<BulkNotification> notificationsWithoutCreatedAt = bulkNotificationRepository.findAll()
                .stream()
                .filter(notification -> notification.getCreatedAt() == null)
                .collect(java.util.stream.Collectors.toList());

            if (!notificationsWithoutCreatedAt.isEmpty()) {
                log.info("Found {} bulk notifications without createdAt timestamp, fixing...",
                    notificationsWithoutCreatedAt.size());

                LocalDateTime now = LocalDateTime.now();
                for (BulkNotification notification : notificationsWithoutCreatedAt) {
                    notification.setCreatedAt(now);
                    notification.setUpdatedAt(now);
                    bulkNotificationRepository.save(notification);
                }

                log.info("Fixed createdAt timestamps for {} bulk notifications",
                    notificationsWithoutCreatedAt.size());
            } else {
                log.info("All bulk notifications already have createdAt timestamps");
            }
        } catch (Exception e) {
            log.error("Error fixing missing createdAt timestamps", e);
            throw new RuntimeException("Failed to fix missing timestamps", e);
        }
    }
}
