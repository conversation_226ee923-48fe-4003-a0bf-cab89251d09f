package com.lookforx.notificationservice.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.common.events.BaseEvent;
import com.lookforx.notificationservice.domain.EventStore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * Service for replaying events from event store
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class EventReplayService {
    
    private final EventStoreService eventStoreService;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final ObjectMapper objectMapper;
    
    /**
     * Replay events by date range
     */
    public CompletableFuture<ReplayResult> replayEventsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return CompletableFuture.supplyAsync(() -> {
            String replayId = UUID.randomUUID().toString();
            log.info("Starting event replay by date range: replayId={}, startDate={}, endDate={}", 
                replayId, startDate, endDate);
            
            try {
                List<EventStore> events = eventStoreService.getEventsForReplay(startDate, endDate);
                return replayEvents(events, replayId, "DATE_RANGE");
                
            } catch (Exception e) {
                log.error("Failed to replay events by date range: replayId={}", replayId, e);
                return ReplayResult.builder()
                    .replayId(replayId)
                    .success(false)
                    .errorMessage(e.getMessage())
                    .build();
            }
        });
    }
    
    /**
     * Replay events by service
     */
    public CompletableFuture<ReplayResult> replayEventsByService(String serviceName, 
                                                               LocalDateTime startDate, 
                                                               LocalDateTime endDate) {
        return CompletableFuture.supplyAsync(() -> {
            String replayId = UUID.randomUUID().toString();
            log.info("Starting event replay by service: replayId={}, service={}, startDate={}, endDate={}", 
                replayId, serviceName, startDate, endDate);
            
            try {
                List<EventStore> events = eventStoreService.getEventsForReplayByService(serviceName, startDate, endDate);
                return replayEvents(events, replayId, "SERVICE");
                
            } catch (Exception e) {
                log.error("Failed to replay events by service: replayId={}, service={}", replayId, serviceName, e);
                return ReplayResult.builder()
                    .replayId(replayId)
                    .success(false)
                    .errorMessage(e.getMessage())
                    .build();
            }
        });
    }
    
    /**
     * Replay events by aggregate
     */
    public CompletableFuture<ReplayResult> replayEventsByAggregate(String aggregateId, 
                                                                 String aggregateType,
                                                                 LocalDateTime startDate, 
                                                                 LocalDateTime endDate) {
        return CompletableFuture.supplyAsync(() -> {
            String replayId = UUID.randomUUID().toString();
            log.info("Starting event replay by aggregate: replayId={}, aggregate={}:{}, startDate={}, endDate={}", 
                replayId, aggregateType, aggregateId, startDate, endDate);
            
            try {
                List<EventStore> events = eventStoreService.getEventsForReplayByAggregate(
                    aggregateId, aggregateType, startDate, endDate);
                return replayEvents(events, replayId, "AGGREGATE");
                
            } catch (Exception e) {
                log.error("Failed to replay events by aggregate: replayId={}, aggregate={}:{}", 
                    replayId, aggregateType, aggregateId, e);
                return ReplayResult.builder()
                    .replayId(replayId)
                    .success(false)
                    .errorMessage(e.getMessage())
                    .build();
            }
        });
    }
    
    /**
     * Replay specific events by IDs
     */
    public CompletableFuture<ReplayResult> replayEventsByIds(List<String> eventIds) {
        return CompletableFuture.supplyAsync(() -> {
            String replayId = UUID.randomUUID().toString();
            log.info("Starting event replay by IDs: replayId={}, eventCount={}", replayId, eventIds.size());
            
            try {
                List<EventStore> events = eventIds.stream()
                    .map(eventStoreService::getEventById)
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .toList();
                
                return replayEvents(events, replayId, "SPECIFIC_IDS");
                
            } catch (Exception e) {
                log.error("Failed to replay events by IDs: replayId={}", replayId, e);
                return ReplayResult.builder()
                    .replayId(replayId)
                    .success(false)
                    .errorMessage(e.getMessage())
                    .build();
            }
        });
    }
    
    /**
     * Core replay logic with type-specific behavior
     */
    private ReplayResult replayEvents(List<EventStore> events, String replayId, String replayType) {
        if (events.isEmpty()) {
            log.info("No events found for replay: replayId={}, type={}", replayId, replayType);
            return ReplayResult.builder()
                .replayId(replayId)
                .success(true)
                .totalEvents(0)
                .successfulEvents(0)
                .failedEvents(0)
                .replayType(replayType)
                .build();
        }

        LocalDateTime startTime = LocalDateTime.now();
        int totalEvents = events.size();
        int successfulEvents = 0;
        int failedEvents = 0;

        log.info("Replaying {} events: replayId={}, type={}", totalEvents, replayId, replayType);

        // Apply replay type specific logic
        events = applyReplayTypeLogic(events, replayType);

        for (EventStore eventStore : events) {
            try {
                // Deserialize event
                BaseEvent event = deserializeEvent(eventStore);

                // Apply replay type specific modifications to event
                event = modifyEventForReplay(event, replayType, replayId);

                // Determine target topic based on replay type
                String targetTopic = determineTargetTopicForReplay(event.getEventType(), replayType);

                // Send to Kafka with replay headers
                kafkaTemplate.send(targetTopic, event)
                    .whenComplete((result, ex) -> {
                        if (ex != null) {
                            log.error("Failed to replay event: eventId={}, replayId={}, type={}",
                                eventStore.getEventId(), replayId, replayType, ex);
                        } else {
                            log.debug("Successfully replayed event: eventId={}, replayId={}, type={}",
                                eventStore.getEventId(), replayId, replayType);
                        }
                    });

                successfulEvents++;

                // Add delay between events for certain replay types
                addReplayDelay(replayType);

            } catch (Exception e) {
                log.error("Failed to replay event: eventId={}, replayId={}, type={}",
                    eventStore.getEventId(), replayId, replayType, e);
                failedEvents++;

                // Handle failure based on replay type
                handleReplayFailure(eventStore, replayType, e);
            }
        }

        // Mark events as replayed
        List<String> eventIds = events.stream()
            .map(EventStore::getEventId)
            .toList();
        eventStoreService.markEventsAsReplayed(eventIds, replayId);

        LocalDateTime endTime = LocalDateTime.now();

        log.info("Completed event replay: replayId={}, type={}, total={}, successful={}, failed={}, duration={}ms",
            replayId, replayType, totalEvents, successfulEvents, failedEvents,
            java.time.Duration.between(startTime, endTime).toMillis());

        return ReplayResult.builder()
            .replayId(replayId)
            .success(failedEvents == 0)
            .totalEvents(totalEvents)
            .successfulEvents(successfulEvents)
            .failedEvents(failedEvents)
            .replayType(replayType)
            .startTime(startTime)
            .endTime(endTime)
            .build();
    }
    
    /**
     * Deserialize event from JSON
     */
    private BaseEvent deserializeEvent(EventStore eventStore) throws Exception {
        Class<?> eventClass = getEventClass(eventStore.getEventType());
        return (BaseEvent) objectMapper.readValue(eventStore.getEventData(), eventClass);
    }
    
    /**
     * Get event class by type
     */
    private Class<?> getEventClass(String eventType) {
        return switch (eventType) {
            case "USER_REGISTERED" -> com.lookforx.common.events.UserRegisteredEvent.class;
            case "USER_LOGIN" -> com.lookforx.common.events.UserLoginEvent.class;
            case "FORM_SUBMITTED" -> com.lookforx.common.events.FormSubmittedEvent.class;
            case "REQUEST_CREATED" -> com.lookforx.common.events.RequestCreatedEvent.class;
            case "REQUEST_STATUS_CHANGED" -> com.lookforx.common.events.RequestStatusChangedEvent.class;
            case "BID_PLACED" -> com.lookforx.common.events.BidPlacedEvent.class;
            case "PAYMENT_COMPLETED" -> com.lookforx.common.events.PaymentCompletedEvent.class;
            case "CAMPAIGN_CREATED" -> com.lookforx.common.events.CampaignCreatedEvent.class;
            case "MEMBERSHIP_UPGRADED" -> com.lookforx.common.events.MembershipUpgradedEvent.class;
            default -> throw new IllegalArgumentException("Unknown event type: " + eventType);
        };
    }
    
    /**
     * Apply replay type specific logic to event list
     */
    private List<EventStore> applyReplayTypeLogic(List<EventStore> events, String replayType) {
        return switch (replayType) {
            case "DATE_RANGE" -> {
                // Sort by timestamp for chronological replay
                yield events.stream()
                    .sorted((e1, e2) -> e1.getTimestamp().compareTo(e2.getTimestamp()))
                    .toList();
            }
            case "SERVICE" -> {
                // Group by service and sort by timestamp within each service
                yield events.stream()
                    .sorted((e1, e2) -> {
                        int serviceCompare = e1.getServiceName().compareTo(e2.getServiceName());
                        return serviceCompare != 0 ? serviceCompare :
                               e1.getTimestamp().compareTo(e2.getTimestamp());
                    })
                    .toList();
            }
            case "AGGREGATE" -> {
                // Sort by version for proper aggregate reconstruction
                yield events.stream()
                    .sorted((e1, e2) -> e1.getVersion().compareTo(e2.getVersion()))
                    .toList();
            }
            case "SPECIFIC_IDS" -> {
                // Keep original order for specific IDs
                yield events;
            }
            default -> events;
        };
    }

    /**
     * Modify event for replay based on type
     */
    private BaseEvent modifyEventForReplay(BaseEvent event, String replayType, String replayId) {
        // Add replay metadata
        if (event.getMetadata() == null) {
            event.setMetadata(new java.util.HashMap<>());
        }

        event.getMetadata().put("replayId", replayId);
        event.getMetadata().put("replayType", replayType);
        event.getMetadata().put("isReplay", true);
        event.getMetadata().put("originalTimestamp", event.getTimestamp().toString());

        // Update timestamp for replay
        event.setTimestamp(LocalDateTime.now());

        // Type-specific modifications
        switch (replayType) {
            case "AGGREGATE" -> {
                // For aggregate replay, maintain correlation
                event.getMetadata().put("aggregateReplay", true);
            }
            case "SERVICE" -> {
                // For service replay, add service context
                event.getMetadata().put("serviceReplay", true);
                event.getMetadata().put("targetService", event.getServiceName());
            }
            case "DATE_RANGE" -> {
                // For date range replay, add temporal context
                event.getMetadata().put("temporalReplay", true);
            }
        }

        return event;
    }

    /**
     * Determine target topic for replay based on type
     */
    private String determineTargetTopicForReplay(String eventType, String replayType) {
        String baseTopic = determineTargetTopic(eventType);

        // Add replay suffix for certain types
        return switch (replayType) {
            case "AGGREGATE" -> baseTopic + "-replay-aggregate";
            case "SERVICE" -> baseTopic + "-replay-service";
            default -> baseTopic + "-replay";
        };
    }

    /**
     * Determine target topic for event type
     */
    private String determineTargetTopic(String eventType) {
        return switch (eventType) {
            case "USER_REGISTERED", "USER_LOGIN", "MEMBERSHIP_UPGRADED" -> "user-events";
            case "FORM_SUBMITTED" -> "form-events";
            case "REQUEST_CREATED", "REQUEST_STATUS_CHANGED" -> "request-events";
            case "BID_PLACED" -> "bid-events";
            case "PAYMENT_COMPLETED" -> "payment-events";
            case "CAMPAIGN_CREATED" -> "campaign-events";
            default -> "unknown-events";
        };
    }

    /**
     * Create replay headers for Kafka message
     */
    private java.util.Map<String, Object> createReplayHeaders(String replayId, String replayType, EventStore eventStore) {
        java.util.Map<String, Object> headers = new java.util.HashMap<>();
        headers.put("replayId", replayId);
        headers.put("replayType", replayType);
        headers.put("originalEventId", eventStore.getEventId());
        headers.put("originalTimestamp", eventStore.getTimestamp().toString());
        headers.put("replayTimestamp", LocalDateTime.now().toString());
        return headers;
    }

    /**
     * Add delay between events for certain replay types
     */
    private void addReplayDelay(String replayType) {
        try {
            switch (replayType) {
                case "AGGREGATE" -> Thread.sleep(100); // 100ms delay for aggregate consistency
                case "SERVICE" -> Thread.sleep(50);    // 50ms delay for service replay
                case "DATE_RANGE" -> Thread.sleep(10); // 10ms delay for temporal replay
                // No delay for SPECIFIC_IDS
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Replay delay interrupted: {}", e.getMessage());
        }
    }

    /**
     * Handle replay failure based on type
     */
    private void handleReplayFailure(EventStore eventStore, String replayType, Exception error) {
        switch (replayType) {
            case "AGGREGATE" -> {
                // For aggregate replay, failure might break consistency
                log.error("CRITICAL: Aggregate replay failure for eventId={}, aggregateId={}, error={}",
                    eventStore.getEventId(), eventStore.getAggregateId(), error.getMessage());
            }
            case "SERVICE" -> {
                // For service replay, log service-specific context
                log.error("Service replay failure for eventId={}, service={}, error={}",
                    eventStore.getEventId(), eventStore.getServiceName(), error.getMessage());
            }
            case "DATE_RANGE" -> {
                // For temporal replay, continue with next events
                log.warn("Temporal replay failure for eventId={}, continuing with next events, error={}",
                    eventStore.getEventId(), error.getMessage());
            }
            case "SPECIFIC_IDS" -> {
                // For specific replay, each failure is important
                log.error("Specific event replay failure for eventId={}, error={}",
                    eventStore.getEventId(), error.getMessage());
            }
        }
    }
    
    /**
     * Replay result DTO
     */
    @lombok.Data
    @lombok.Builder
    public static class ReplayResult {
        private String replayId;
        private boolean success;
        private int totalEvents;
        private int successfulEvents;
        private int failedEvents;
        private String replayType;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private String errorMessage;
    }
}
