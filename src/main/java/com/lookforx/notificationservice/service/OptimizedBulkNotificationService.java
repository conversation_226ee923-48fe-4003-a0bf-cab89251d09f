package com.lookforx.notificationservice.service;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.notificationservice.domain.BulkNotification;
import com.lookforx.notificationservice.domain.Notification.NotificationStatus;
import com.lookforx.notificationservice.domain.User;
import com.lookforx.notificationservice.domain.BulkNotificationStatus;
import com.lookforx.notificationservice.dto.BulkNotificationCommand;
import com.lookforx.notificationservice.repository.BulkNotificationRepository;
import com.lookforx.notificationservice.repository.BulkNotificationStatusRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Optimized bulk notification service that uses single bulk notification record
 * with separate user status tracking for better performance
 */
@Service
@Slf4j
public class OptimizedBulkNotificationService {

    private final BulkNotificationRepository bulkNotificationRepository;
    private final BulkNotificationStatusRepository bulkNotificationStatusRepository;
    private final WebSocketNotificationService webSocketNotificationService;
    private final MongoTemplate authMongoTemplate;

    public OptimizedBulkNotificationService(
            BulkNotificationRepository bulkNotificationRepository,
            BulkNotificationStatusRepository bulkNotificationStatusRepository,
            WebSocketNotificationService webSocketNotificationService,
            @Qualifier("authMongoTemplate") MongoTemplate authMongoTemplate) {
        this.bulkNotificationRepository = bulkNotificationRepository;
        this.bulkNotificationStatusRepository = bulkNotificationStatusRepository;
        this.webSocketNotificationService = webSocketNotificationService;
        this.authMongoTemplate = authMongoTemplate;
    }

    /**
     * Create and process bulk notification with optimized approach
     */
    @Transactional
    public BulkNotification createBulkNotification(BulkNotificationCommand command) {
        log.info("Creating optimized bulk notification: {}", command.getNotificationType());

        // Create single bulk notification record
        BulkNotification bulkNotification = createBulkNotificationEntity(command);
        bulkNotification = bulkNotificationRepository.save(bulkNotification);
        log.info("Created bulk notification with ID: {}", bulkNotification.getId());

        // Get active users from MongoDB
        List<User> activeUsers = getActiveUsers();
        log.info("Found {} active users for bulk notification", activeUsers.size());

        if (activeUsers.isEmpty()) {
            log.warn("No active users found for bulk notification");
            bulkNotification.setStatus(NotificationStatus.FAILED);
            return bulkNotificationRepository.save(bulkNotification);
        }

        // Create user status records for all active users
        createUserStatusRecords(bulkNotification, activeUsers);

        // Update bulk notification with target count and mark as sent
        bulkNotification.setTargetUserCount(activeUsers.size());
        bulkNotification.setDeliveredCount(activeUsers.size());
        bulkNotification.setStatus(NotificationStatus.SENT);
        bulkNotification.setSentAt(LocalDateTime.now());
        bulkNotification = bulkNotificationRepository.save(bulkNotification);

        // Send WebSocket updates to all users
        sendWebSocketUpdates(activeUsers);

        log.info("Successfully created optimized bulk notification for {} users", activeUsers.size());
        return bulkNotification;
    }

    /**
     * Get bell notifications for a user with pagination - using bulk notification status
     */
    public Page<BulkNotificationStatus> getBellNotifications(Long userId, Pageable pageable) {
        return bulkNotificationStatusRepository.findUnreadByUserId(userId, pageable);
    }

    /**
     * Get unread notification count for a user - combines both legacy notifications and bulk notifications
     * Uses native SQL for optimal performance
     */
    public long getUnreadCount(Long userId) {
        return getCombinedUnreadCount(userId);
    }

    /**
     * Get unread count from bulk_notifications table only
     */
    private long getCombinedUnreadCount(Long userId) {
        // Count from bulk notifications system only
        long bulkCount = bulkNotificationStatusRepository.countUnreadByUserId(userId);

        log.debug("User {} - Bulk notifications: {}", userId, bulkCount);

        return bulkCount;
    }

    /**
     * Mark notification as read for a user
     */
    @Transactional
    public boolean markAsRead(Long userId, String bulkNotificationId) {
        Optional<BulkNotificationStatus> statusOpt = bulkNotificationStatusRepository.findByUserIdAndBulkNotificationId(userId, bulkNotificationId);
        if (statusOpt.isPresent()) {
            BulkNotificationStatus status = statusOpt.get();
            status.setIsRead(true);
            status.setReadAt(LocalDateTime.now());
            bulkNotificationStatusRepository.save(status);

            // Update bulk notification read count
            updateBulkNotificationReadCount(bulkNotificationId);

            // Send WebSocket update
            webSocketNotificationService.sendBellNotificationCountUpdate(userId.toString(), getUnreadCount(userId));

            log.info("Marked bulk notification {} as read for user {}", bulkNotificationId, userId);
            return true;
        }

        return false;
    }

    /**
     * Mark notification as clicked for a user
     */
    @Transactional
    public boolean markAsClicked(Long userId, String bulkNotificationId) {
        Optional<BulkNotificationStatus> statusOpt = bulkNotificationStatusRepository.findByUserIdAndBulkNotificationId(userId, bulkNotificationId);
        if (statusOpt.isPresent()) {
            BulkNotificationStatus status = statusOpt.get();
            status.setIsClicked(true);
            status.setClickedAt(LocalDateTime.now());
            if (!status.getIsRead()) {
                status.setIsRead(true);
                status.setReadAt(LocalDateTime.now());
            }
            bulkNotificationStatusRepository.save(status);

            // Update bulk notification counts
            updateBulkNotificationClickCount(bulkNotificationId);
            updateBulkNotificationReadCount(bulkNotificationId);

            // Send WebSocket update
            webSocketNotificationService.sendBellNotificationCountUpdate(userId.toString(), getUnreadCount(userId));

            log.info("Marked bulk notification {} as clicked for user {}", bulkNotificationId, userId);
            return true;
        }

        return false;
    }

    /**
     * Mark all notifications as read for a user
     */
    @Transactional
    public int markAllAsRead(Long userId) {
        // For now, just return 0 - this method needs proper implementation
        // Send WebSocket update
        webSocketNotificationService.sendBellNotificationCountUpdate(userId.toString(), 0L);
        log.info("Mark all as read called for user {}", userId);
        return 0;
    }

    /**
     * Get bulk notification by ID
     */
    public Optional<BulkNotification> getBulkNotification(String id) {
        return bulkNotificationRepository.findById(id);
    }

    /**
     * Get all bulk notifications with pagination
     */
    public Page<BulkNotification> getAllBulkNotifications(Pageable pageable) {
        return bulkNotificationRepository.findActiveNotifications(LocalDateTime.now(), pageable);
    }

    private BulkNotification createBulkNotificationEntity(BulkNotificationCommand command) {
        BulkNotification notification = new BulkNotification();
        
        notification.setNotificationType(command.getNotificationType());
        notification.setPriority(command.getPriority());
        notification.setStatus(NotificationStatus.PENDING);
        notification.setCorrelationId(command.getCorrelationId());
        notification.setSourceService(command.getSourceService());
        notification.setTemplateName(command.getTemplateName());
        notification.setRelatedEntityType(command.getRelatedEntityType());
        notification.setRelatedEntityId(command.getRelatedEntityId());
        notification.setActionUrl(command.getActionUrl());
        notification.setIconUrl(command.getIconUrl());
        notification.setExpiresAt(command.getExpiresAt());
        notification.setSendEmail(command.getSendEmail() != null ? command.getSendEmail() : false);
        notification.setSendPush(command.getSendPush() != null ? command.getSendPush() : false);
        notification.setShowInBell(command.getShowInBell() != null ? command.getShowInBell() : true);
        
        // Set multi-language content
        if (command.getTitles() != null) {
            notification.setTitles(convertToStringMap(command.getTitles()));
        }
        if (command.getMessages() != null) {
            notification.setMessages(convertToStringMap(command.getMessages()));
        }
        if (command.getTemplateParameters() != null) {
            notification.setParameters(command.getTemplateParameters());
        }
        
        return notification;
    }

    private List<User> getActiveUsers() {
        Query query = new Query(Criteria.where("active").is(true));
        return authMongoTemplate.find(query, User.class);
    }

    private void createUserStatusRecords(BulkNotification bulkNotification, List<User> users) {
        log.info("Creating user status records for {} users", users.size());
        
        for (User user : users) {
            try {
                Long userId = Long.valueOf(user.getId());
                
                BulkNotificationStatus status = new BulkNotificationStatus();
                status.setUserId(userId);
                status.setBulkNotification(bulkNotification);
                status.setShowInBell(bulkNotification.getShowInBell());
                status.setDeliveredAt(LocalDateTime.now());

                log.debug("Creating bulk notification status for user {} with showInBell: {}", userId, bulkNotification.getShowInBell());
                bulkNotificationStatusRepository.save(status);
                log.debug("Saved user notification status for user {} with ID: {}", userId, status.getId());
                
            } catch (NumberFormatException e) {
                log.error("Invalid user ID format: {}", user.getId(), e);
            }
        }
        
        log.info("Successfully created user status records");
    }

    private void sendWebSocketUpdates(List<User> users) {
        for (User user : users) {
            try {
                Long userId = Long.valueOf(user.getId());
                long unreadCount = getUnreadCount(userId);
                webSocketNotificationService.sendBellNotificationCountUpdate(userId.toString(), unreadCount);
            } catch (NumberFormatException e) {
                log.error("Invalid user ID format for WebSocket update: {}", user.getId(), e);
            }
        }
    }

    private void updateBulkNotificationReadCount(String bulkNotificationId) {
        // TODO: Implement proper count update
        log.debug("Update read count for bulk notification: {}", bulkNotificationId);
    }

    private void updateBulkNotificationClickCount(String bulkNotificationId) {
        // TODO: Implement proper count update
        log.debug("Update click count for bulk notification: {}", bulkNotificationId);
    }

    private Map<String, String> convertToStringMap(Map<LanguageCode, String> languageCodeMap) {
        Map<String, String> stringMap = new HashMap<>();
        for (Map.Entry<LanguageCode, String> entry : languageCodeMap.entrySet()) {
            stringMap.put(entry.getKey().name(), entry.getValue());
        }
        return stringMap;
    }
}
