package com.lookforx.notificationservice.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.stream.LongStream;

/**
 * Service for user-related operations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserService {

    private final RestTemplate restTemplate;

    /**
     * Get all active user IDs
     * In a real implementation, this would call the user service
     * For now, we'll simulate with a range of user IDs
     */
    public List<Long> getAllActiveUserIds() {
        try {
            // TODO: Replace with actual user service call
            // String userServiceUrl = "http://user-service/api/users/active-ids";
            // Long[] userIds = restTemplate.getForObject(userServiceUrl, Long[].class);
            // return Arrays.asList(userIds);
            
            // For now, simulate with user IDs 1-100
            log.info("Simulating active users with IDs 1-100");
            return LongStream.rangeClosed(1, 100)
                    .boxed()
                    .toList();
                    
        } catch (Exception e) {
            log.error("Error fetching active user IDs", e);
            // Fallback to a small set of test users
            return Arrays.asList(1L, 2L, 3L, 4L, 5L);
        }
    }

    /**
     * Get user details by ID
     * In a real implementation, this would call the user service
     */
    public UserDetails getUserDetails(Long userId) {
        try {
            // TODO: Replace with actual user service call
            // String userServiceUrl = "http://user-service/api/users/" + userId;
            // return restTemplate.getForObject(userServiceUrl, UserDetails.class);
            
            // For now, simulate user details
            return UserDetails.builder()
                    .id(userId)
                    .email("user" + userId + "@example.com")
                    .firstName("User")
                    .lastName(String.valueOf(userId))
                    .isActive(true)
                    .preferredLanguage("EN")
                    .build();
                    
        } catch (Exception e) {
            log.error("Error fetching user details for ID: {}", userId, e);
            return null;
        }
    }

    /**
     * Check if user exists and is active
     */
    public boolean isUserActive(Long userId) {
        try {
            UserDetails user = getUserDetails(userId);
            return user != null && user.isActive();
        } catch (Exception e) {
            log.error("Error checking if user {} is active", userId, e);
            return false;
        }
    }

    /**
     * Get users in batches for bulk operations
     */
    public List<Long> getActiveUserIdsBatch(int offset, int limit) {
        try {
            List<Long> allUsers = getAllActiveUserIds();
            int startIndex = Math.min(offset, allUsers.size());
            int endIndex = Math.min(offset + limit, allUsers.size());
            
            if (startIndex >= endIndex) {
                return List.of();
            }
            
            return allUsers.subList(startIndex, endIndex);
            
        } catch (Exception e) {
            log.error("Error fetching user batch (offset: {}, limit: {})", offset, limit, e);
            return List.of();
        }
    }

    /**
     * User details DTO
     */
    @lombok.Builder
    @lombok.Data
    public static class UserDetails {
        private Long id;
        private String email;
        private String firstName;
        private String lastName;
        private boolean isActive;
        private String preferredLanguage;
    }
}
