package com.lookforx.notificationservice.service;

import com.lookforx.notificationservice.dto.BulkNotificationCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Service;

/**
 * Kafka consumer for bulk notification commands
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BulkNotificationConsumer {

    private final BulkNotificationProcessor bulkNotificationProcessor;

    /**
     * Listen for bulk notification commands
     */
    @KafkaListener(
        topics = "bulk-notification-commands",
        groupId = "notification-service",
        containerFactory = "kafkaListenerContainerFactory"
    )
    public void handleBulkNotificationCommand(
            @Payload BulkNotificationCommand command,
            @Header(KafkaHeaders.RECEIVED_KEY) String key,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
            @Header(KafkaHeaders.OFFSET) long offset,
            Acknowledgment acknowledgment) {
        
        log.info("Received bulk notification command. CommandId: {}, Topic: {}, Partition: {}, Offset: {}", 
                command.getCommandId(), topic, partition, offset);
        
        try {
            // Process the bulk notification command
            bulkNotificationProcessor.processBulkNotificationCommand(command);
            
            // Acknowledge the message after successful processing
            acknowledgment.acknowledge();
            
            log.info("Successfully processed bulk notification command. CommandId: {}", command.getCommandId());
            
        } catch (Exception e) {
            log.error("Error processing bulk notification command. CommandId: {}", command.getCommandId(), e);
            
            // Don't acknowledge the message on error - it will be retried
            // You might want to implement dead letter queue logic here
            throw new RuntimeException("Failed to process bulk notification command", e);
        }
    }

    /**
     * Handle processing errors and dead letter queue
     */
    @KafkaListener(
        topics = "bulk-notification-commands-dlt",
        groupId = "notification-service-dlt",
        containerFactory = "kafkaListenerContainerFactory"
    )
    public void handleDeadLetterQueue(
            @Payload BulkNotificationCommand command,
            @Header(KafkaHeaders.RECEIVED_KEY) String key,
            @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
            Acknowledgment acknowledgment) {
        
        log.error("Received bulk notification command in dead letter queue. CommandId: {}, Topic: {}", 
                command.getCommandId(), topic);
        
        try {
            // Log the failed command for manual investigation
            log.error("Dead letter command details: {}", command);
            
            // You could implement notification to administrators here
            // Or store the failed command in a database for later retry
            
            acknowledgment.acknowledge();
            
        } catch (Exception e) {
            log.error("Error handling dead letter queue message. CommandId: {}", command.getCommandId(), e);
        }
    }
}
