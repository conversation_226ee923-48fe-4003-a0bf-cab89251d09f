package com.lookforx.notificationservice.service;

import com.lookforx.notificationservice.dto.BulkNotificationCommand;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * Kafka producer for bulk notification commands
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BulkNotificationProducer {

    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    private static final String BULK_NOTIFICATION_TOPIC = "bulk-notification-commands";

    /**
     * Send bulk notification command to Kafka (async, non-blocking)
     */
    public CompletableFuture<SendResult<String, Object>> sendBulkNotificationCommand(BulkNotificationCommand command) {
        log.info("Sending bulk notification command to Kafka. CommandId: {}, CorrelationId: {}",
                command.getCommandId(), command.getCorrelationId());

        try {
            // Send asynchronously and return immediately
            CompletableFuture<SendResult<String, Object>> future = kafkaTemplate.send(
                BULK_NOTIFICATION_TOPIC,
                command.getCommandId(),
                command
            );

            log.info("Bulk notification command queued for Kafka. CommandId: {}", command.getCommandId());
            return future;

        } catch (Exception e) {
            log.error("Error queueing bulk notification command to Kafka. CommandId: {}",
                    command.getCommandId(), e);

            // Return a failed future
            CompletableFuture<SendResult<String, Object>> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }
    }

    /**
     * Send bulk notification command synchronously (for testing)
     */
    public SendResult<String, Object> sendBulkNotificationCommandSync(BulkNotificationCommand command) {
        log.info("Sending bulk notification command synchronously. CommandId: {}", command.getCommandId());
        
        try {
            SendResult<String, Object> result = kafkaTemplate.send(
                BULK_NOTIFICATION_TOPIC, 
                command.getCommandId(), 
                command
            ).get(); // Wait for completion
            
            log.info("Successfully sent bulk notification command synchronously. CommandId: {}, Offset: {}", 
                    command.getCommandId(), result.getRecordMetadata().offset());
            
            return result;
            
        } catch (Exception e) {
            log.error("Error sending bulk notification command synchronously. CommandId: {}", 
                    command.getCommandId(), e);
            throw new RuntimeException("Failed to send bulk notification command", e);
        }
    }

    /**
     * Send command with custom topic
     */
    public CompletableFuture<SendResult<String, Object>> sendCommand(String topic, String key, Object command) {
        log.debug("Sending command to topic: {}, key: {}", topic, key);
        
        try {
            return kafkaTemplate.send(topic, key, command);
        } catch (Exception e) {
            log.error("Error sending command to topic: {}, key: {}", topic, key, e);
            CompletableFuture<SendResult<String, Object>> failedFuture = new CompletableFuture<>();
            failedFuture.completeExceptionally(e);
            return failedFuture;
        }
    }

    /**
     * Get the topic name for bulk notifications
     */
    public String getBulkNotificationTopic() {
        return BULK_NOTIFICATION_TOPIC;
    }
}
