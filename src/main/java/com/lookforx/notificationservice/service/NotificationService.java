package com.lookforx.notificationservice.service;

import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;
import com.lookforx.notificationservice.repository.NotificationRepository;
import com.lookforx.notificationservice.strategy.NotificationStrategy;
import com.lookforx.notificationservice.strategy.NotificationStrategyFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing notifications
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationService {
    
    private final NotificationRepository notificationRepository;
    private final NotificationStrategyFactory strategyFactory;
    private final MongoTemplate mongoTemplate;
    
    /**
     * Send notification using appropriate strategy
     */
    @Transactional
    public boolean sendNotification(Notification notification) {
        try {
            // Save notification to database first
            notification.setStatus(Notification.NotificationStatus.PENDING);
            notification.setDeliveryAttempts(notification.getDeliveryAttempts() + 1);
            Notification savedNotification = notificationRepository.save(notification);
            
            // Get appropriate strategy
            Optional<NotificationStrategy> strategyOpt = strategyFactory.getStrategy(notification.getNotificationType());
            
            if (strategyOpt.isEmpty()) {
                log.error("No strategy available for notification type: {}", notification.getNotificationType());
                savedNotification.setStatus(Notification.NotificationStatus.FAILED);
                savedNotification.setErrorMessage("No strategy available for notification type: " + notification.getNotificationType());
                notificationRepository.save(savedNotification);
                return false;
            }
            
            NotificationStrategy strategy = strategyOpt.get();
            
            // Send notification
            boolean success = strategy.sendNotification(savedNotification);
            
            if (success) {
                savedNotification.setStatus(Notification.NotificationStatus.SENT);
                savedNotification.setSentAt(LocalDateTime.now());
                savedNotification.setErrorMessage(null);
                log.info("Notification sent successfully: eventId={}, type={}", 
                    savedNotification.getEventId(), savedNotification.getNotificationType());
            } else {
                savedNotification.setStatus(Notification.NotificationStatus.FAILED);
                scheduleRetry(savedNotification, strategy);
                log.error("Failed to send notification: eventId={}, type={}", 
                    savedNotification.getEventId(), savedNotification.getNotificationType());
            }
            
            notificationRepository.save(savedNotification);
            return success;
            
        } catch (Exception e) {
            log.error("Error sending notification: eventId={}, type={}", 
                notification.getEventId(), notification.getNotificationType(), e);
            
            notification.setStatus(Notification.NotificationStatus.FAILED);
            notification.setErrorMessage(e.getMessage());
            notificationRepository.save(notification);
            return false;
        }
    }
    
    /**
     * Schedule retry for failed notification
     */
    private void scheduleRetry(Notification notification, NotificationStrategy strategy) {
        if (notification.getDeliveryAttempts() < strategy.getMaxRetryAttempts()) {
            long delayMinutes = strategy.getRetryDelayMinutes(notification.getDeliveryAttempts());
            notification.setNextRetryAt(LocalDateTime.now().plusMinutes(delayMinutes));
            log.info("Scheduled retry for notification: eventId={}, attempt={}, nextRetry={}", 
                notification.getEventId(), notification.getDeliveryAttempts(), notification.getNextRetryAt());
        } else {
            log.warn("Max retry attempts reached for notification: eventId={}, attempts={}", 
                notification.getEventId(), notification.getDeliveryAttempts());
        }
    }
    
    /**
     * Retry failed notifications
     */
    @Transactional
    public void retryFailedNotifications() {
        List<Notification> failedNotifications = notificationRepository.findFailedNotificationsForRetry(LocalDateTime.now());
        
        log.info("Found {} notifications to retry", failedNotifications.size());
        
        for (Notification notification : failedNotifications) {
            log.info("Retrying notification: eventId={}, attempt={}", 
                notification.getEventId(), notification.getDeliveryAttempts() + 1);
            sendNotification(notification);
        }
    }
    
    /**
     * Get notification by event ID
     */
    public Optional<Notification> getByEventId(String eventId) {
        return notificationRepository.findByEventId(eventId);
    }
    
    /**
     * Get notifications by user ID
     */
    public List<Notification> getByUserId(Long userId) {
        return notificationRepository.findByUserIdOrderByCreatedAtDesc(userId);
    }
    
    /**
     * Get notifications by user ID with pagination
     */
    public Page<Notification> getByUserId(Long userId, Pageable pageable) {
        return notificationRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }
    
    /**
     * Get notifications by status
     */
    public List<Notification> getByStatus(Notification.NotificationStatus status) {
        return notificationRepository.findByStatus(status);
    }
    
    /**
     * Get notifications by type
     */
    public List<Notification> getByNotificationType(NotificationType notificationType) {
        return notificationRepository.findByNotificationType(notificationType);
    }
    
    /**
     * Get notification statistics
     */
    public NotificationStats getStats() {
        return NotificationStats.builder()
            .totalNotifications(notificationRepository.count())
            .pendingNotifications(notificationRepository.countByStatus(Notification.NotificationStatus.PENDING))
            .sentNotifications(notificationRepository.countByStatus(Notification.NotificationStatus.SENT))
            .failedNotifications(notificationRepository.countByStatus(Notification.NotificationStatus.FAILED))
            .emailNotifications(notificationRepository.countByNotificationType(NotificationType.EMAIL))
            .smsNotifications(notificationRepository.countByNotificationType(NotificationType.SMS))
            .pushNotifications(notificationRepository.countByNotificationType(NotificationType.PUSH_NOTIFICATION))
            .build();
    }
    
    /**
     * Delete old notifications using MongoTemplate
     */
    @Transactional
    public void deleteOldNotifications(int daysToKeep) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysToKeep);
        Query query = new Query(Criteria.where("createdAt").lt(cutoffDate));
        long deletedCount = mongoTemplate.remove(query, Notification.class).getDeletedCount();
        log.info("Deleted {} old notifications before: {}", deletedCount, cutoffDate);
    }
    
    /**
     * Notification statistics DTO
     */
    public static class NotificationStats {
        public final long totalNotifications;
        public final long pendingNotifications;
        public final long sentNotifications;
        public final long failedNotifications;
        public final long emailNotifications;
        public final long smsNotifications;
        public final long pushNotifications;
        
        private NotificationStats(Builder builder) {
            this.totalNotifications = builder.totalNotifications;
            this.pendingNotifications = builder.pendingNotifications;
            this.sentNotifications = builder.sentNotifications;
            this.failedNotifications = builder.failedNotifications;
            this.emailNotifications = builder.emailNotifications;
            this.smsNotifications = builder.smsNotifications;
            this.pushNotifications = builder.pushNotifications;
        }
        
        public static Builder builder() {
            return new Builder();
        }
        
        public static class Builder {
            private long totalNotifications;
            private long pendingNotifications;
            private long sentNotifications;
            private long failedNotifications;
            private long emailNotifications;
            private long smsNotifications;
            private long pushNotifications;
            
            public Builder totalNotifications(long totalNotifications) {
                this.totalNotifications = totalNotifications;
                return this;
            }
            
            public Builder pendingNotifications(long pendingNotifications) {
                this.pendingNotifications = pendingNotifications;
                return this;
            }
            
            public Builder sentNotifications(long sentNotifications) {
                this.sentNotifications = sentNotifications;
                return this;
            }
            
            public Builder failedNotifications(long failedNotifications) {
                this.failedNotifications = failedNotifications;
                return this;
            }
            
            public Builder emailNotifications(long emailNotifications) {
                this.emailNotifications = emailNotifications;
                return this;
            }
            
            public Builder smsNotifications(long smsNotifications) {
                this.smsNotifications = smsNotifications;
                return this;
            }
            
            public Builder pushNotifications(long pushNotifications) {
                this.pushNotifications = pushNotifications;
                return this;
            }
            
            public NotificationStats build() {
                return new NotificationStats(this);
            }
        }
    }
}
