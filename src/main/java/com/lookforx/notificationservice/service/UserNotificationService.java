package com.lookforx.notificationservice.service;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.notificationservice.domain.Notification;
import com.lookforx.notificationservice.repository.NotificationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserNotificationService {

    private final NotificationRepository notificationRepository;
    private final MongoTemplate mongoTemplate;

    /**
     * Get unread notification count for user (for bell icon)
     */
    public long getUnreadNotificationCount(Long userId) {
        try {
            return notificationRepository.countUnreadBellNotificationsByUserId(userId, LocalDateTime.now());
        } catch (Exception e) {
            log.error("Error getting unread notification count for user: {}", userId, e);
            return 0;
        }
    }

    /**
     * Get user notifications for bell dropdown with pagination
     */
    public Map<String, Object> getUserNotifications(Long userId, int page, int size) {
        try {
            log.info("Getting user notifications for userId: {}, page: {}, size: {}", userId, page, size);
            // Use unsorted pageable since native query already has ORDER BY clause
            Pageable pageable = PageRequest.of(page, size);
            Page<Notification> notifications = notificationRepository.findBellNotificationsByUserId(userId, LocalDateTime.now(), pageable);
            log.info("Found {} notifications for userId: {}", notifications.getTotalElements(), userId);
            
            List<Map<String, Object>> notificationList = new ArrayList<>();
            
            for (Notification notification : notifications.getContent()) {
                Map<String, Object> notificationData = new HashMap<>();
                notificationData.put("id", notification.getId());
                notificationData.put("titles", notification.getTitles());
                notificationData.put("messages", notification.getMessages());
                notificationData.put("notificationType", notification.getNotificationType());
                notificationData.put("priority", notification.getPriority());
                notificationData.put("createdAt", notification.getCreatedAt());
                notificationData.put("isRead", notification.getIsRead());
                notificationData.put("isClicked", notification.getIsClicked());
                notificationData.put("actionUrl", notification.getActionUrl());
                notificationData.put("iconUrl", notification.getIconUrl());
                
                notificationList.add(notificationData);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("notifications", notificationList);
            result.put("totalElements", notifications.getTotalElements());
            result.put("totalPages", notifications.getTotalPages());
            result.put("currentPage", page);
            result.put("size", size);
            
            return result;
        } catch (Exception e) {
            log.error("Error getting user notifications for user: {}", userId, e);
            throw new RuntimeException("Failed to get user notifications", e);
        }
    }

    /**
     * Mark user notification as read
     */
    @Transactional
    public void markNotificationAsRead(Long userId, String notificationId) {
        try {
            Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);
            if (notificationOpt.isPresent()) {
                Notification notification = notificationOpt.get();
                if (notification.getUserId().equals(userId) && !notification.getIsRead()) {
                    notification.setIsRead(true);
                    notification.setReadAt(LocalDateTime.now());
                    notification.setUpdatedAt(LocalDateTime.now());
                    notificationRepository.save(notification);
                    log.info("Marked notification {} as read for user {}", notificationId, userId);
                }
            } else {
                log.warn("Notification not found: {}", notificationId);
                throw new RuntimeException("Notification not found: " + notificationId);
            }
        } catch (Exception e) {
            log.error("Error marking notification as read for user: {} notification: {}", userId, notificationId, e);
            throw new RuntimeException("Failed to mark notification as read", e);
        }
    }

    /**
     * Mark all user notifications as read using MongoTemplate
     */
    @Transactional
    public int markAllNotificationsAsRead(Long userId) {
        try {
            LocalDateTime now = LocalDateTime.now();
            Query query = new Query(Criteria.where("userId").is(userId).and("isRead").is(false));
            Update update = new Update()
                .set("isRead", true)
                .set("readAt", now)
                .set("updatedAt", now);
            long updatedCount = mongoTemplate.updateMulti(query, update, Notification.class).getModifiedCount();
            log.info("Marked {} notifications as read for user {}", updatedCount, userId);
            return (int) updatedCount;
        } catch (Exception e) {
            log.error("Error marking all notifications as read for user: {}", userId, e);
            throw new RuntimeException("Failed to mark all notifications as read", e);
        }
    }

    /**
     * Mark user notification as clicked
     */
    @Transactional
    public void markNotificationAsClicked(Long userId, String notificationId) {
        try {
            Optional<Notification> notificationOpt = notificationRepository.findById(notificationId);
            if (notificationOpt.isPresent()) {
                Notification notification = notificationOpt.get();
                if (notification.getUserId().equals(userId)) {
                    notification.setIsClicked(true);
                    notification.setClickedAt(LocalDateTime.now());
                    notification.setUpdatedAt(LocalDateTime.now());
                    
                    // Also mark as read if not already read
                    if (!notification.getIsRead()) {
                        notification.setIsRead(true);
                        notification.setReadAt(LocalDateTime.now());
                    }
                    
                    notificationRepository.save(notification);
                    log.info("Marked notification {} as clicked for user {}", notificationId, userId);
                }
            } else {
                log.warn("Notification not found: {}", notificationId);
                throw new RuntimeException("Notification not found: " + notificationId);
            }
        } catch (Exception e) {
            log.error("Error marking notification as clicked for user: {} notification: {}", userId, notificationId, e);
            throw new RuntimeException("Failed to mark notification as clicked", e);
        }
    }

    /**
     * Create user notification (called by event handlers)
     */
    @Transactional
    public Notification createUserNotification(Long userId, Map<String, String> titles, Map<String, String> messages,
                                             String notificationType, String priority, String actionUrl, String iconUrl,
                                             String relatedEntityType, String relatedEntityId) {
        try {
            // Create complete language maps for all supported languages
            Map<String, String> completeTitles = createCompleteLanguageMap(titles, "Notification");
            Map<String, String> completeMessages = createCompleteLanguageMap(messages, "You have a new notification");

            Notification notification = Notification.builder()
                .userId(userId)
                .titles(convertToLanguageCodeMap(completeTitles))
                .messages(convertToLanguageCodeMap(completeMessages))
                .notificationType(com.lookforx.common.enums.NotificationType.valueOf(notificationType))
                .priority(com.lookforx.common.enums.NotificationPriority.valueOf(priority))
                .actionUrl(actionUrl)
                .iconUrl(iconUrl)
                .relatedEntityType(relatedEntityType)
                .relatedEntityId(relatedEntityId)
                .showInBell(true) // User notifications are always shown in bell
                .sendEmail(false)
                .sendPush(false)
                .isRead(false)
                .isClicked(false)
                .status(Notification.NotificationStatus.SENT)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

            Notification savedNotification = notificationRepository.save(notification);
            log.info("User notification created successfully with ID: {} for user: {} with {} languages",
                    savedNotification.getId(), userId, completeTitles.size());

            return savedNotification;
        } catch (Exception e) {
            log.error("Error creating user notification for user: {}", userId, e);
            throw new RuntimeException("Failed to create user notification", e);
        }
    }



    private Map<LanguageCode, String> convertToLanguageCodeMap(Map<String, String> stringMap) {
        Map<LanguageCode, String> languageCodeMap = new HashMap<>();
        for (Map.Entry<String, String> entry : stringMap.entrySet()) {
            try {
                LanguageCode langCode = LanguageCode.valueOf(entry.getKey().toUpperCase());
                languageCodeMap.put(langCode, entry.getValue());
            } catch (IllegalArgumentException e) {
                log.warn("Invalid language code: {}, skipping", entry.getKey());
            }
        }
        return languageCodeMap;
    }

    /**
     * Create complete language map with all supported languages
     * Falls back to English if translation not provided
     */
    private Map<String, String> createCompleteLanguageMap(Map<String, String> providedTranslations, String defaultEnglishText) {
        Map<String, String> completeMap = new HashMap<>();

        // Get English text (fallback)
        String englishText = providedTranslations.getOrDefault("EN", defaultEnglishText);

        // Add all supported language codes
        for (LanguageCode langCode : LanguageCode.values()) {
            String langCodeStr = langCode.name(); // This gives us the enum name (e.g., "EN", "TR", "DE")
            String translation = providedTranslations.getOrDefault(langCodeStr, englishText);
            completeMap.put(langCodeStr, translation);
        }

        return completeMap;
    }
}
