package com.lookforx.notificationservice.dto;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Request DTO for creating notifications
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreateNotificationRequest {
    
    private Long userId;
    private String eventId;
    private String eventType;
    private NotificationType notificationType;
    private NotificationPriority priority;
    private Map<LanguageCode, String> titles; // Multi-language titles
    private Map<LanguageCode, String> messages; // Multi-language messages
    private Boolean showInBell;
    private Boolean sendEmail;
    private Boolean sendPush;
    private String actionUrl;
    private String iconUrl;
    private LocalDateTime expiresAt;
    private String relatedEntityId;
    private String relatedEntityType;
    private String sourceService;
    private String templateName;
    private Map<String, String> templateParameters;
}
