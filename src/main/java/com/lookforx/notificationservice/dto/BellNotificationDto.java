package com.lookforx.notificationservice.dto;

import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for bell notifications (header notification icon)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BellNotificationDto {

    private String id;
    private String title;
    private String message;
    private NotificationType notificationType;
    private NotificationPriority priority;
    private Boolean read;
    private Boolean clicked;
    private LocalDateTime createdAt;
    private String actionUrl;
    private String iconUrl;
    private String relatedEntityId;
    private String relatedEntityType;

    // Additional fields for optimized bulk notifications
    private LocalDateTime deliveredAt;
    private LocalDateTime readAt;
    private LocalDateTime clickedAt;
    private LocalDateTime expiresAt;
}
