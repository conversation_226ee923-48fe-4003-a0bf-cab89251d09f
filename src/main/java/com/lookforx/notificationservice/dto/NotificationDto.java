package com.lookforx.notificationservice.dto;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO for notification management
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NotificationDto {
    
    private Long id;
    private Long userId;
    private String eventId;
    private String eventType;
    private NotificationType notificationType;
    private NotificationPriority priority;
    private Map<LanguageCode, String> titles;
    private Map<LanguageCode, String> messages;
    private Boolean showInBell;
    private Boolean isRead;
    private Boolean isClicked;
    private LocalDateTime readAt;
    private LocalDateTime clickedAt;
    private Boolean sendEmail;
    private Boolean sendPush;
    private String actionUrl;
    private String iconUrl;
    private LocalDateTime expiresAt;
    private String relatedEntityId;
    private String relatedEntityType;
    private String sourceService;
    private String templateName;
    private Map<String, String> templateParameters;
    private Notification.NotificationStatus status;
    private LocalDateTime sentAt;
    private Integer deliveryAttempts;
    private String errorMessage;
    private String correlationId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
