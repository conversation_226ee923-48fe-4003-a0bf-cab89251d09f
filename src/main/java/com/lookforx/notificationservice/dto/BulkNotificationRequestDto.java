package com.lookforx.notificationservice.dto;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO for bulk notification creation requests
 */
@Data
public class BulkNotificationRequestDto {
    
    private NotificationType notificationType;
    private NotificationPriority priority;
    private Map<LanguageCode, String> titles;
    private Map<LanguageCode, String> messages;
    private String relatedEntityType;
    private String relatedEntityId;
    private String actionUrl;
    private LocalDateTime expiresAt;
    private boolean showInBell = true;
    private boolean sendEmail = false;
    private boolean sendSms = false;
    private boolean sendPush = false;
}
