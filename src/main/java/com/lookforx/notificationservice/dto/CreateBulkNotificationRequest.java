package com.lookforx.notificationservice.dto;

import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateBulkNotificationRequest {
    
    @NotNull(message = "Notification type is required")
    private NotificationType notificationType;
    
    @NotNull(message = "Priority is required")
    private NotificationPriority priority;
    
    @NotNull(message = "Titles are required")
    private Map<String, String> titles;
    
    @NotNull(message = "Messages are required")
    private Map<String, String> messages;
    
    private String actionUrl;
    private String iconUrl;
    private LocalDateTime expiresAt;
    private String relatedEntityType;
    private String relatedEntityId;
    
    // Bulk notifications are not shown in bell by default
    @Builder.Default
    private Boolean showInBell = false;
    
    @Builder.Default
    private Boolean sendEmail = false;
    
    @Builder.Default
    private Boolean sendPush = false;
}
