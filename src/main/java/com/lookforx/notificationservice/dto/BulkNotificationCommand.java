package com.lookforx.notificationservice.dto;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Kafka command for bulk notification processing
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkNotificationCommand {

    private String commandId;
    private String correlationId;
    private LocalDateTime timestamp;
    
    // Notification details
    private NotificationType notificationType;
    private NotificationPriority priority;
    private Map<LanguageCode, String> titles;
    private Map<LanguageCode, String> messages;
    
    // Delivery options
    private Boolean showInBell;
    private Boolean sendEmail;
    private Boolean sendPush;
    
    // Additional data
    private String actionUrl;
    private String iconUrl;
    private LocalDateTime expiresAt;
    private String relatedEntityId;
    private String relatedEntityType;
    private String sourceService;
    private String templateName;
    private Map<String, String> templateParameters;
    
    // Targeting options
    private TargetingOptions targetingOptions;
    
    // Processing options
    private ProcessingOptions processingOptions;

    /**
     * Targeting options for bulk notifications
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TargetingOptions {
        private Boolean allUsers;
        private java.util.List<String> userIds;
        private java.util.List<String> roles;
        private java.util.List<String> countries;
        private java.util.List<String> cities;
        private java.util.List<String> languages;
        private Boolean emailNotificationEnabled;
        private Boolean pushNotificationEnabled;
        private LocalDateTime createdAfter;
        private LocalDateTime createdBefore;
    }

    /**
     * Processing options for bulk notifications
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProcessingOptions {
        private Integer batchSize;
        private Integer maxThreads;
        private Integer retryAttempts;
        private Long delayBetweenBatches;
        private Boolean skipInvalidUsers;
        private Boolean trackProgress;
    }

    /**
     * Create default targeting options for all users
     */
    public static TargetingOptions createAllUsersTargeting() {
        return TargetingOptions.builder()
                .allUsers(true)
                .emailNotificationEnabled(null) // Don't filter by notification preferences
                .pushNotificationEnabled(null)
                .build();
    }

    /**
     * Create default processing options
     */
    public static ProcessingOptions createDefaultProcessingOptions() {
        return ProcessingOptions.builder()
                .batchSize(100)
                .maxThreads(10)
                .retryAttempts(3)
                .delayBetweenBatches(100L) // 100ms delay between batches
                .skipInvalidUsers(true)
                .trackProgress(true)
                .build();
    }

    /**
     * Create a bulk notification command with default options
     */
    public static BulkNotificationCommand createDefault(CreateNotificationRequest request) {
        return BulkNotificationCommand.builder()
                .commandId(java.util.UUID.randomUUID().toString())
                .correlationId(java.util.UUID.randomUUID().toString())
                .timestamp(LocalDateTime.now())
                .notificationType(request.getNotificationType())
                .priority(request.getPriority())
                .titles(request.getTitles())
                .messages(request.getMessages())
                .showInBell(request.getShowInBell())
                .sendEmail(request.getSendEmail())
                .sendPush(request.getSendPush())
                .actionUrl(request.getActionUrl())
                .iconUrl(request.getIconUrl())
                .expiresAt(request.getExpiresAt())
                .relatedEntityId(request.getRelatedEntityId())
                .relatedEntityType(request.getRelatedEntityType())
                .sourceService(request.getSourceService())
                .templateName(request.getTemplateName())
                .templateParameters(request.getTemplateParameters())
                .targetingOptions(createAllUsersTargeting())
                .processingOptions(createDefaultProcessingOptions())
                .build();
    }
}
