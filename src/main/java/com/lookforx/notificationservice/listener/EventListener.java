package com.lookforx.notificationservice.listener;

import com.lookforx.common.events.*;
import com.lookforx.notificationservice.service.EventNotificationService;
// import com.lookforx.notificationservice.service.EventStoreService; // TODO: Implement EventStoreService
import com.lookforx.notificationservice.domain.EventStore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * Kafka event listeners for all microservice events
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class EventListener {

    private final EventNotificationService eventNotificationService;
    // private final EventStoreService eventStoreService; // TODO: Implement EventStoreService
    
    /**
     * Listen to user events
     */
    @KafkaListener(topics = "user-events", groupId = "notification-service")
    public void handleUserEvent(@Payload BaseEvent event,
                               @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                               @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                               @Header(KafkaHeaders.OFFSET) long offset,
                               Acknowledgment acknowledgment) {

        log.info("Received user event: eventType={}, eventId={}, topic={}, partition={}, offset={}",
            event.getEventType(), event.getEventId(), topic, partition, offset);

        // Store event in event store first
        // eventStoreService.storeEvent(event, topic, partition, offset); // TODO: Implement EventStoreService

        try {
            // Update event status to processing
            // eventStoreService.updateEventStatus(event.getEventId(), EventStore.EventStatus.PROCESSING, null); // TODO: Implement EventStoreService

            switch (event.getEventType()) {
                case "USER_REGISTERED" -> eventNotificationService.handleUserRegisteredEvent((UserRegisteredEvent) event);
                case "USER_WELCOME" -> eventNotificationService.handleUserWelcomeEvent((UserWelcomeEvent) event);
                case "USER_LOGIN" -> eventNotificationService.handleUserLoginEvent((UserLoginEvent) event);
                default -> log.warn("Unknown user event type: {}", event.getEventType());
            }

            // Update event status to processed
            // eventStoreService.updateEventStatus(event.getEventId(), EventStore.EventStatus.PROCESSED, null); // TODO: Implement EventStoreService

            acknowledgment.acknowledge();
            log.info("Successfully processed user event: {}", event.getEventId());

        } catch (Exception e) {
            log.error("Error processing user event: eventId={}, error={}", event.getEventId(), e.getMessage(), e);

            // Update event status to failed
            // eventStoreService.updateEventStatus(event.getEventId(), EventStore.EventStatus.FAILED, e.getMessage()); // TODO: Implement EventStoreService

            // Don't acknowledge - message will be retried
        }
    }
    
    /**
     * Listen to form events
     */
    @KafkaListener(topics = "form-events", groupId = "notification-service")
    public void handleFormEvent(@Payload BaseEvent event,
                               @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                               @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                               @Header(KafkaHeaders.OFFSET) long offset,
                               Acknowledgment acknowledgment) {
        
        log.info("Received form event: eventType={}, eventId={}, topic={}, partition={}, offset={}",
            event.getEventType(), event.getEventId(), topic, partition, offset);

        // Store event in event store first
        // eventStoreService.storeEvent(event, topic, partition, offset); // TODO: Implement EventStoreService

        try {
            // Update event status to processing
            // eventStoreService.updateEventStatus(event.getEventId(), EventStore.EventStatus.PROCESSING, null); // TODO: Implement EventStoreService

            switch (event.getEventType()) {
                case "FORM_SUBMITTED" -> eventNotificationService.handleFormSubmittedEvent((FormSubmittedEvent) event);
                default -> log.warn("Unknown form event type: {}", event.getEventType());
            }

            // Update event status to processed
            // eventStoreService.updateEventStatus(event.getEventId(), EventStore.EventStatus.PROCESSED, null); // TODO: Implement EventStoreService

            acknowledgment.acknowledge();
            log.debug("Successfully processed form event: {}", event.getEventId());

        } catch (Exception e) {
            log.error("Error processing form event: eventId={}, error={}", event.getEventId(), e.getMessage(), e);

            // Update event status to failed
            // eventStoreService.updateEventStatus(event.getEventId(), EventStore.EventStatus.FAILED, e.getMessage()); // TODO: Implement EventStoreService
        }
    }
    
    /**
     * Listen to request events
     */
    @KafkaListener(topics = "request-events", groupId = "notification-service")
    public void handleRequestEvent(@Payload BaseEvent event,
                                  @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                  @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                  @Header(KafkaHeaders.OFFSET) long offset,
                                  Acknowledgment acknowledgment) {
        
        log.info("Received request event: eventType={}, eventId={}, topic={}, partition={}, offset={}", 
            event.getEventType(), event.getEventId(), topic, partition, offset);
        
        try {
            switch (event.getEventType()) {
                case "REQUEST_CREATED" -> eventNotificationService.handleRequestCreatedEvent((RequestCreatedEvent) event);
                case "REQUEST_STATUS_CHANGED" -> eventNotificationService.handleRequestStatusChangedEvent((RequestStatusChangedEvent) event);
                default -> log.warn("Unknown request event type: {}", event.getEventType());
            }
            
            acknowledgment.acknowledge();
            log.info("Successfully processed request event: {}", event.getEventId());
            
        } catch (Exception e) {
            log.error("Error processing request event: eventId={}, error={}", event.getEventId(), e.getMessage(), e);
        }
    }
    
    /**
     * Listen to bid events
     */
    @KafkaListener(topics = "bid-events", groupId = "notification-service")
    public void handleBidEvent(@Payload BaseEvent event,
                              @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                              @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                              @Header(KafkaHeaders.OFFSET) long offset,
                              Acknowledgment acknowledgment) {
        
        log.info("Received bid event: eventType={}, eventId={}, topic={}, partition={}, offset={}", 
            event.getEventType(), event.getEventId(), topic, partition, offset);
        
        try {
            switch (event.getEventType()) {
                case "BID_PLACED" -> eventNotificationService.handleBidPlacedEvent((BidPlacedEvent) event);
                default -> log.warn("Unknown bid event type: {}", event.getEventType());
            }
            
            acknowledgment.acknowledge();
            log.info("Successfully processed bid event: {}", event.getEventId());
            
        } catch (Exception e) {
            log.error("Error processing bid event: eventId={}, error={}", event.getEventId(), e.getMessage(), e);
        }
    }
    
    /**
     * Listen to payment events
     */
    @KafkaListener(topics = "payment-events", groupId = "notification-service")
    public void handlePaymentEvent(@Payload BaseEvent event,
                                  @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                  @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                  @Header(KafkaHeaders.OFFSET) long offset,
                                  Acknowledgment acknowledgment) {
        
        log.info("Received payment event: eventType={}, eventId={}, topic={}, partition={}, offset={}", 
            event.getEventType(), event.getEventId(), topic, partition, offset);
        
        try {
            switch (event.getEventType()) {
                case "PAYMENT_COMPLETED" -> eventNotificationService.handlePaymentCompletedEvent((PaymentCompletedEvent) event);
                default -> log.warn("Unknown payment event type: {}", event.getEventType());
            }
            
            acknowledgment.acknowledge();
            log.debug("Successfully processed payment event: {}", event.getEventId());
            
        } catch (Exception e) {
            log.error("Error processing payment event: eventId={}, error={}", event.getEventId(), e.getMessage(), e);
        }
    }
    
    /**
     * Listen to campaign events
     */
    @KafkaListener(topics = "campaign-events", groupId = "notification-service")
    public void handleCampaignEvent(@Payload BaseEvent event,
                                   @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                   @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                   @Header(KafkaHeaders.OFFSET) long offset,
                                   Acknowledgment acknowledgment) {
        
        log.info("Received campaign event: eventType={}, eventId={}, topic={}, partition={}, offset={}", 
            event.getEventType(), event.getEventId(), topic, partition, offset);
        
        try {
            switch (event.getEventType()) {
                case "CAMPAIGN_CREATED" -> eventNotificationService.handleCampaignCreatedEvent((CampaignCreatedEvent) event);
                default -> log.warn("Unknown campaign event type: {}", event.getEventType());
            }
            
            acknowledgment.acknowledge();
            log.debug("Successfully processed campaign event: {}", event.getEventId());
            
        } catch (Exception e) {
            log.error("Error processing campaign event: eventId={}, error={}", event.getEventId(), e.getMessage(), e);
        }
    }
    
    /**
     * Listen to membership events
     */
    @KafkaListener(topics = "membership-events", groupId = "notification-service")
    public void handleMembershipEvent(@Payload BaseEvent event,
                                     @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                     @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                     @Header(KafkaHeaders.OFFSET) long offset,
                                     Acknowledgment acknowledgment) {
        
        log.info("Received membership event: eventType={}, eventId={}, topic={}, partition={}, offset={}", 
            event.getEventType(), event.getEventId(), topic, partition, offset);
        
        try {
            switch (event.getEventType()) {
                case "MEMBERSHIP_UPGRADED" -> eventNotificationService.handleMembershipUpgradedEvent((MembershipUpgradedEvent) event);
                default -> log.warn("Unknown membership event type: {}", event.getEventType());
            }
            
            acknowledgment.acknowledge();
            log.info("Successfully processed membership event: {}", event.getEventId());
            
        } catch (Exception e) {
            log.error("Error processing membership event: eventId={}, error={}", event.getEventId(), e.getMessage(), e);
        }
    }

    /**
     * Listen to customer support ticket events
     */
    @KafkaListener(topics = "support.ticket.events", groupId = "notification-service")
    public void handleSupportTicketEvent(@Payload BaseEvent event,
                                        @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                        @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                        @Header(KafkaHeaders.OFFSET) long offset,
                                        Acknowledgment acknowledgment) {

        log.info("Received support ticket event: eventType={}, eventId={}, topic={}, partition={}, offset={}",
            event.getEventType(), event.getEventId(), topic, partition, offset);

        try {
            switch (event.getEventType()) {
                case "TICKET_CREATED" -> eventNotificationService.handleTicketCreatedEvent((TicketCreatedEvent) event);
                case "TICKET_STATUS_CHANGED" -> eventNotificationService.handleTicketStatusChangedEvent((TicketStatusChangedEvent) event);
                case "TICKET_ASSIGNED" -> eventNotificationService.handleTicketAssignedEvent((TicketAssignedEvent) event);
                case "TICKET_RESOLVED" -> eventNotificationService.handleTicketResolvedEvent((TicketResolvedEvent) event);
                case "TICKET_COMMENT_ADDED" -> eventNotificationService.handleTicketCommentAddedEvent((TicketCommentAddedEvent) event);
                default -> log.warn("Unknown support ticket event type: {}", event.getEventType());
            }

            acknowledgment.acknowledge();
            log.info("Successfully processed support ticket event: {}", event.getEventId());

        } catch (Exception e) {
            log.error("Error processing support ticket event: eventId={}, error={}", event.getEventId(), e.getMessage(), e);
        }
    }
}
