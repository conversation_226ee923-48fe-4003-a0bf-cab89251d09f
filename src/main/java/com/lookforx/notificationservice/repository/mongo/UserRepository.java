package com.lookforx.notificationservice.repository.mongo;

import com.lookforx.notificationservice.domain.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for User entity from auth database
 */
@Repository
public interface UserRepository extends MongoRepository<User, String> {

    /**
     * Find user by email
     */
    Optional<User> findByEmail(String email);

    /**
     * Find user by username
     */
    Optional<User> findByUsername(String username);

    /**
     * Find all active users
     */
    @Query("{ 'active': true, 'email_verified': true }")
    List<User> findAllActiveUsers();

    /**
     * Find active users with pagination
     */
    @Query("{ 'active': true, 'email_verified': true }")
    Page<User> findActiveUsers(Pageable pageable);

    /**
     * Count active users
     */
    @Query(value = "{ 'active': true, 'email_verified': true }", count = true)
    long countActiveUsers();

    /**
     * Find users by preferred language
     */
    @Query("{ 'active': true, 'email_verified': true, 'preferredLanguage': ?0 }")
    List<User> findActiveUsersByPreferredLanguage(String language);

    /**
     * Find users who want email notifications
     */
    @Query("{ 'active': true, 'email_verified': true, 'notificationSettings.emailNotifications': true }")
    List<User> findUsersWantingEmailNotifications();

    /**
     * Find users who want push notifications
     */
    @Query("{ 'active': true, 'email_verified': true, 'notificationSettings.pushNotifications': true }")
    List<User> findUsersWantingPushNotifications();

    /**
     * Find users by role
     */
    @Query("{ 'active': true, 'roles': { $in: [?0] } }")
    List<User> findActiveUsersByRole(String role);

    /**
     * Find users by multiple roles
     */
    @Query("{ 'active': true, 'roles': { $in: ?0 } }")
    List<User> findActiveUsersByRoles(List<String> roles);

    /**
     * Find users by country
     */
    @Query("{ 'active': true, 'email_verified': true, 'country': ?0 }")
    List<User> findActiveUsersByCountry(String country);

    /**
     * Find users by city
     */
    @Query("{ 'active': true, 'email_verified': true, 'city': ?0 }")
    List<User> findActiveUsersByCity(String city);

    /**
     * Find users created after a specific date
     */
    @Query("{ 'active': true, 'email_verified': true, 'created_at': { $gte: ?0 } }")
    List<User> findActiveUsersCreatedAfter(java.time.LocalDateTime date);

    /**
     * Find users with specific notification settings
     */
    @Query("{ 'active': true, 'email_verified': true, " +
           "'notificationSettings.emailNotifications': ?0, " +
           "'notificationSettings.pushNotifications': ?1 }")
    List<User> findActiveUsersByNotificationSettings(boolean emailNotifications, boolean pushNotifications);

    /**
     * Get user IDs only for bulk operations
     */
    @Query(value = "{ 'active': true, 'email_verified': true }", fields = "{ '_id': 1 }")
    List<User> findActiveUserIds();

    /**
     * Find users in batches for bulk processing
     */
    @Query("{ 'active': true, 'email_verified': true }")
    List<User> findActiveUsersInBatch(Pageable pageable);
}
