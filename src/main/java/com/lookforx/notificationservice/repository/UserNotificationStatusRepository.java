package com.lookforx.notificationservice.repository;

import com.lookforx.notificationservice.domain.UserNotificationStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface UserNotificationStatusRepository extends MongoRepository<UserNotificationStatus, String> {



    /**
     * Count unread bell notifications for a user
     */
    @Query("{ 'userId': ?0, 'showInBell': true, 'isRead': false }")
    long countUnreadBellNotificationsByUserId(Long userId);

    /**
     * Find bell notifications for a user with pagination
     */
    @Query("{ 'userId': ?0, 'showInBell': true }")
    Page<UserNotificationStatus> findBellNotificationsByUserId(Long userId, Pageable pageable);

    /**
     * Find all unread notifications for a user
     */
    @Query("{ 'userId': ?0, 'isRead': false }")
    List<UserNotificationStatus> findUnreadNotificationsByUserId(Long userId);



    /**
     * Check if user has any unread individual notifications
     */
    @Query("{ 'userId': ?0, 'isRead': false }")
    boolean existsByUserIdAndIsReadFalse(Long userId);

    // Note: Complex operations like markAllAsRead, deleteExpiredNotificationStatus,
    // countLegacyUnreadNotifications, and countCombinedUnreadNotifications
    // should be implemented in service layer using MongoTemplate
}
