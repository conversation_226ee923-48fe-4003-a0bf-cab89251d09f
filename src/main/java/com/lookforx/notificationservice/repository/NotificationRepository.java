package com.lookforx.notificationservice.repository;

import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository for Notification document
 */
@Repository
public interface NotificationRepository extends MongoRepository<Notification, String> {
    
    /**
     * Find notification by event ID
     */
    Optional<Notification> findByEventId(String eventId);
    
    /**
     * Find notifications by user ID
     */
    List<Notification> findByUserIdOrderByCreatedAtDesc(Long userId);
    
    /**
     * Find notifications by user ID with pagination
     */
    Page<Notification> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * Find bell notifications for user (unread, not expired, showInBell=true)
     */
    @Query("{ 'userId': ?0, 'showInBell': true, 'isRead': false, $or: [{'expiresAt': null}, {'expiresAt': {$gt: ?1}}] }")
    List<Notification> findBellNotificationsByUserId(Long userId, LocalDateTime now);

    /**
     * Count unread bell notifications for user
     */
    @Query(value = "{ 'userId': ?0, 'showInBell': true, 'isRead': false, $or: [{'expiresAt': null}, {'expiresAt': {$gt: ?1}}] }", count = true)
    long countUnreadBellNotificationsByUserId(Long userId, LocalDateTime now);

    /**
     * Find bell notifications for user with pagination (only unread to match count query)
     */
    @Query("{ 'userId': ?0, 'showInBell': true, 'isRead': false, $or: [{'expiresAt': null}, {'expiresAt': {$gt: ?1}}] }")
    Page<Notification> findBellNotificationsByUserId(Long userId, LocalDateTime now, Pageable pageable);

    // Note: MongoDB doesn't support bulk updates in repository queries like JPA
    // These operations will need to be implemented in service layer using MongoTemplate

    // Note: Update operations will be handled in service layer using MongoTemplate

    /**
     * Delete notifications by userId and eventType (for test cleanup)
     */
    void deleteByUserIdAndEventType(Long userId, String eventType);

    /**
     * Find notifications by status
     */
    List<Notification> findByStatus(Notification.NotificationStatus status);

    /**
     * Find notifications by notification type
     */
    List<Notification> findByNotificationType(NotificationType notificationType);

    /**
     * Find failed notifications that need retry
     */
    @Query("{ 'status': 'FAILED', 'deliveryAttempts': {$lt: ?#{[0].maxAttempts}}, $or: [{'nextRetryAt': null}, {'nextRetryAt': {$lte: ?0}}] }")
    List<Notification> findFailedNotificationsForRetry(LocalDateTime now);

    /**
     * Find pending notifications
     */
    List<Notification> findByStatusOrderByCreatedAtAsc(Notification.NotificationStatus status);

    /**
     * Find notifications by event type
     */
    List<Notification> findByEventType(String eventType);

    /**
     * Find notifications by service name
     */
    List<Notification> findByServiceName(String serviceName);
    
    /**
     * Find notifications by correlation ID
     */
    List<Notification> findByCorrelationId(String correlationId);
    
    /**
     * Find notifications created between dates
     */
    List<Notification> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    /**
     * Count notifications by status
     */
    long countByStatus(Notification.NotificationStatus status);
    
    /**
     * Count notifications by notification type
     */
    long countByNotificationType(NotificationType notificationType);
    
    /**
     * Count notifications by user ID and status
     */
    long countByUserIdAndStatus(Long userId, Notification.NotificationStatus status);

    /**
     * Find recent notifications
     */
    @Query("SELECT n FROM Notification n WHERE n.createdAt >= :since ORDER BY n.createdAt DESC")
    List<Notification> findRecentNotifications(@Param("since") LocalDateTime since);
    
    /**
     * Admin panel: Find notifications with filters
     */
    @Query("SELECT n FROM Notification n WHERE " +
           "(:userId IS NULL OR n.userId = :userId) AND " +
           "(:notificationType IS NULL OR n.notificationType = :notificationType) AND " +
           "(:status IS NULL OR n.status = :status) AND " +
           "(:showInBell IS NULL OR n.showInBell = :showInBell) AND " +
           "(:startDate IS NULL OR n.createdAt >= :startDate) AND " +
           "(:endDate IS NULL OR n.createdAt <= :endDate) " +
           "ORDER BY n.createdAt DESC")
    Page<Notification> findWithFilters(@Param("userId") Long userId,
                                     @Param("notificationType") NotificationType notificationType,
                                     @Param("status") Notification.NotificationStatus status,
                                     @Param("showInBell") Boolean showInBell,
                                     @Param("startDate") LocalDateTime startDate,
                                     @Param("endDate") LocalDateTime endDate,
                                     Pageable pageable);

    /**
     * Admin panel: Find all notifications with pagination (no filters)
     */
    Page<Notification> findAllByOrderByCreatedAtDesc(Pageable pageable);

    // Note: Delete operations will be handled in service layer using MongoTemplate
    // MongoDB doesn't support bulk delete operations in repository queries like JPA
}
