package com.lookforx.notificationservice.repository;

import com.lookforx.notificationservice.domain.BulkNotification;
import com.lookforx.notificationservice.domain.Notification.NotificationStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface BulkNotificationRepository extends MongoRepository<BulkNotification, String> {

    /**
     * Find all bulk notifications that are active (not expired)
     */
    @Query("{ $or: [{'expiresAt': null}, {'expiresAt': {$gt: ?0}}] }")
    Page<BulkNotification> findActiveNotifications(LocalDateTime now, Pageable pageable);

    /**
     * Find bulk notifications by status
     */
    Page<BulkNotification> findByStatusOrderByCreatedAtDesc(NotificationStatus status, Pageable pageable);

    /**
     * Find bulk notifications that should be shown in bell and are not expired
     */
    @Query("{ 'showInBell': true, $or: [{'expiresAt': null}, {'expiresAt': {$gt: ?0}}] }")
    List<BulkNotification> findActiveBellNotifications(LocalDateTime now);

    /**
     * Find bulk notifications by event ID
     */
    List<BulkNotification> findByEventId(String eventId);

    /**
     * Find bulk notifications by related entity
     */
    List<BulkNotification> findByRelatedEntityTypeAndRelatedEntityId(String entityType, String entityId);

    /**
     * Count active bulk notifications
     */
    @Query(value = "{ $or: [{'expiresAt': null}, {'expiresAt': {$gt: ?0}}] }", count = true)
    long countActiveNotifications(LocalDateTime now);

    /**
     * Find bulk notifications that are sent and active
     */
    @Query("{ 'status': 'SENT', $or: [{'expiresAt': null}, {'expiresAt': {$gt: ?0}}] }")
    List<BulkNotification> findSentActiveNotifications(LocalDateTime now);

    /**
     * Find bulk notifications created after a specific date
     */
    Page<BulkNotification> findByCreatedAtAfter(LocalDateTime createdAt, Pageable pageable);
}
