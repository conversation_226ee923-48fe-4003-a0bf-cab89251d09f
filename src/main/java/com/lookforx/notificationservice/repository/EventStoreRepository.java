package com.lookforx.notificationservice.repository;

import com.lookforx.notificationservice.domain.EventStore;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository for Event Store operations
 */
@Repository
public interface EventStoreRepository extends MongoRepository<EventStore, String> {
    
    // Find by event ID
    Optional<EventStore> findByEventId(String eventId);
    
    // Find events by service
    Page<EventStore> findByServiceNameOrderByTimestampDesc(String serviceName, Pageable pageable);
    
    // Find events by aggregate
    List<EventStore> findByAggregateIdAndAggregateTypeOrderByVersionAsc(String aggregateId, String aggregateType);
    
    // Find events by user
    Page<EventStore> findByUserIdOrderByTimestampDesc(Long userId, Pageable pageable);
    
    // Find events by type
    Page<EventStore> findByEventTypeOrderByTimestampDesc(String eventType, Pageable pageable);
    
    // Find events by status
    Page<EventStore> findByStatusOrderByTimestampDesc(EventStore.EventStatus status, Pageable pageable);
    
    // Find events by date range
    Page<EventStore> findByTimestampBetweenOrderByTimestampDesc(
        LocalDateTime startDate, 
        LocalDateTime endDate, 
        Pageable pageable
    );
    
    // Find events for replay
    @Query("{ 'timestamp': { $gte: ?0, $lte: ?1 }, 'status': { $in: ['PROCESSED', 'FAILED'] } }")
    List<EventStore> findEventsForReplay(LocalDateTime startDate, LocalDateTime endDate);
    
    // Find events by service and date range for replay
    @Query("{ 'serviceName': ?0, 'timestamp': { $gte: ?1, $lte: ?2 }, 'status': { $in: ['PROCESSED', 'FAILED'] } }")
    List<EventStore> findEventsForReplayByService(String serviceName, LocalDateTime startDate, LocalDateTime endDate);
    
    // Find events by aggregate for replay
    @Query("{ 'aggregateId': ?0, 'aggregateType': ?1, 'timestamp': { $gte: ?2, $lte: ?3 }, 'status': { $in: ['PROCESSED', 'FAILED'] } }")
    List<EventStore> findEventsForReplayByAggregate(
        String aggregateId, 
        String aggregateType, 
        LocalDateTime startDate, 
        LocalDateTime endDate
    );
    
    // Find replayed events
    Page<EventStore> findByIsReplayedTrueOrderByReplayedAtDesc(Pageable pageable);
    
    // Find events by replay ID
    List<EventStore> findByReplayIdOrderByTimestampAsc(String replayId);
    
    // Count events by service
    long countByServiceName(String serviceName);
    
    // Count events by status
    long countByStatus(EventStore.EventStatus status);
    
    // Count events by date range
    long countByTimestampBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    // Find failed events for retry
    @Query("{ 'status': 'FAILED', 'retryCount': { $lt: ?0 } }")
    List<EventStore> findFailedEventsForRetry(int maxRetryCount);
    
    // Statistics queries
    @Query(value = "{ 'timestamp': { $gte: ?0, $lte: ?1 } }", count = true)
    long countEventsByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    @Query("{ 'serviceName': ?0, 'timestamp': { $gte: ?1, $lte: ?2 } }")
    List<EventStore> findByServiceAndDateRange(String serviceName, LocalDateTime startDate, LocalDateTime endDate);
}
