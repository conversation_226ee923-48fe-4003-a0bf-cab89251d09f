package com.lookforx.notificationservice.repository;

import com.lookforx.notificationservice.domain.BulkNotificationStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BulkNotificationStatusRepository extends MongoRepository<BulkNotificationStatus, String> {

    /**
     * Find status by user ID and bulk notification ID
     */
    Optional<BulkNotificationStatus> findByUserIdAndBulkNotificationId(Long userId, String bulkNotificationId);

    /**
     * Find all statuses for a user with pagination
     */
    Page<BulkNotificationStatus> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * Find unread notifications for a user
     */
    @Query("{ 'userId': ?0, 'isRead': false, 'showInBell': true }")
    Page<BulkNotificationStatus> findUnreadByUserId(Long userId, Pageable pageable);

    /**
     * Count unread notifications for a user
     */
    @Query(value = "{ 'userId': ?0, 'isRead': false, 'showInBell': true }", count = true)
    long countUnreadByUserId(Long userId);

    // Note: Update operations will be handled in service layer using MongoTemplate

    /**
     * Find all statuses for a bulk notification
     */
    List<BulkNotificationStatus> findByBulkNotificationId(String bulkNotificationId);

    /**
     * Count read statuses for a bulk notification
     */
    @Query(value = "{ 'bulkNotificationId': ?0, 'isRead': true }", count = true)
    long countReadByBulkNotificationId(String bulkNotificationId);

    /**
     * Count clicked statuses for a bulk notification
     */
    @Query(value = "{ 'bulkNotificationId': ?0, 'isClicked': true }", count = true)
    long countClickedByBulkNotificationId(String bulkNotificationId);

    /**
     * Delete all statuses for a bulk notification
     */
    void deleteByBulkNotificationId(String bulkNotificationId);
}
