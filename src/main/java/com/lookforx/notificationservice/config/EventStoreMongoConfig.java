package com.lookforx.notificationservice.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

/**
 * MongoDB configuration for EventStore repositories
 */
@Configuration
@EnableMongoRepositories(
    basePackages = "com.lookforx.notificationservice.repository",
    includeFilters = @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*EventStoreRepository"),
    excludeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*\\.mongo\\..*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*NotificationRepository")
    },
    mongoTemplateRef = "eventMongoTemplate"
)
@Slf4j
public class EventStoreMongoConfig {
    
    // This configuration enables EventStoreRepository to use eventMongoTemplate
    // The actual beans are defined in MongoConfig.java
    
}
