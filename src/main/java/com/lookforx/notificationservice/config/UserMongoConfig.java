package com.lookforx.notificationservice.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

/**
 * MongoDB configuration for User repositories (auth database)
 */
@Configuration
@EnableMongoRepositories(
    basePackages = "com.lookforx.notificationservice.repository.mongo",
    mongoTemplateRef = "authMongoTemplate"
)
@Slf4j
public class UserMongoConfig {
    
    // This configuration enables UserRepository to use authMongoTemplate
    // The actual beans are defined in MongoConfig.java
    
}
