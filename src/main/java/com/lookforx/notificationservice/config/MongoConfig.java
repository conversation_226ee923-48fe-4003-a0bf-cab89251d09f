package com.lookforx.notificationservice.config;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.mongo.MongoProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.mongodb.config.EnableMongoAuditing;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;


/**
 * MongoDB configuration for multiple databases
 */
@Configuration
@EnableMongoRepositories(basePackages = "com.lookforx.notificationservice.repository")
@EnableMongoAuditing
@Slf4j
public class MongoConfig {

    /**
     * Primary MongoDB configuration for events (existing)
     */
    @Primary
    @Bean(name = "eventMongoProperties")
    @ConfigurationProperties(prefix = "spring.data.mongodb")
    public MongoProperties eventMongoProperties() {
        return new MongoProperties();
    }

    @Primary
    @Bean(name = "eventMongoClient")
    public MongoClient eventMongoClient(@Qualifier("eventMongoProperties") MongoProperties mongoProperties) {
        log.info("Configuring event MongoDB client with URI: {}", mongoProperties.getUri());
        return MongoClients.create(mongoProperties.getUri());
    }

    @Primary
    @Bean(name = {"eventMongoTemplate", "mongoTemplate"})
    public MongoTemplate eventMongoTemplate(@Qualifier("eventMongoClient") MongoClient mongoClient,
                                           @Qualifier("eventMongoProperties") MongoProperties mongoProperties) {
        String database = mongoProperties.getDatabase();
        if (database == null) {
            // Extract database name from URI if not explicitly set
            String uri = mongoProperties.getUri();
            if (uri != null && uri.contains("/")) {
                String[] parts = uri.split("/");
                if (parts.length > 3) {
                    database = parts[3].split("\\?")[0]; // Remove query parameters
                }
            }
        }
        log.info("Creating event MongoTemplate for database: {}", database);
        return new MongoTemplate(mongoClient, database != null ? database : "notificationdb");
    }

    /**
     * Secondary MongoDB configuration for auth database (users)
     */
    @Bean(name = "authMongoClient")
    public MongoClient authMongoClient() {
        String uri = "mongodb+srv://lookforx:<EMAIL>/authdb?retryWrites=true&w=majority&appName=lookforx";
        log.info("Configuring auth MongoDB client for authdb");
        return MongoClients.create(uri);
    }

    @Bean(name = "authMongoTemplate")
    public MongoTemplate authMongoTemplate(@Qualifier("authMongoClient") MongoClient mongoClient) {
        log.info("Creating auth MongoTemplate for database: authdb");
        return new MongoTemplate(mongoClient, "authdb");
    }
}
