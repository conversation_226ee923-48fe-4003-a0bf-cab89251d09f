package com.lookforx.notificationservice.controller;

import com.lookforx.notificationservice.service.UserNotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.stereotype.Controller;

/**
 * WebSocket controller for real-time notifications
 */
@Controller
@RequiredArgsConstructor
@Slf4j
public class WebSocketController {

    private final UserNotificationService userNotificationService;

    /**
     * Handle bell notification count requests
     */
    @MessageMapping("/bell-count")
    public void getBellNotificationCount(@Payload String userId, SimpMessageHeaderAccessor headerAccessor) {
        try {
            log.info("WebSocket request for bell notification count for user: {}", userId);

            // Convert String userId to Long for compatibility
            Long userIdLong = convertUserIdToLong(userId);

            // Get current count and send via WebSocket
            long count = userNotificationService.getUnreadNotificationCount(userIdLong);

            // The count will be sent automatically via the service's WebSocket integration
            log.info("Bell notification count for user {} (converted to {}): {}", userId, userIdLong, count);

        } catch (Exception e) {
            log.error("Error handling bell notification count request for user: {}", userId, e);
        }
    }

    /**
     * Handle user connection events
     */
    @MessageMapping("/connect")
    public void handleConnect(@Payload String userId, SimpMessageHeaderAccessor headerAccessor) {
        try {
            log.info("User connected via WebSocket: {}", userId);
            
            // Store user session for targeted messaging
            headerAccessor.getSessionAttributes().put("userId", userId);
            
        } catch (Exception e) {
            log.error("Error handling user connection: {}", userId, e);
        }
    }

    /**
     * Convert String user ID to Long for compatibility
     * Handles both numeric strings and MongoDB ObjectIds
     */
    private Long convertUserIdToLong(String userId) {
        try {
            // Try to parse as Long first
            return Long.parseLong(userId);
        } catch (NumberFormatException e) {
            // If not a number, use hash of the string
            long hash = (long) userId.hashCode();
            return hash < 0 ? -hash : hash; // Ensure positive
        }
    }
}
