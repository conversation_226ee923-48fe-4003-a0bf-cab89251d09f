package com.lookforx.notificationservice.controller;

import com.lookforx.notificationservice.dto.CreateBulkNotificationRequest;
import com.lookforx.notificationservice.service.BulkNotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/admin/bulk-notifications")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Bulk Notification Management", description = "APIs for managing bulk notifications in admin panel")
public class BulkNotificationController {

    private final BulkNotificationService bulkNotificationService;

    /**
     * Create bulk notification for all users
     */
    @PostMapping
    @Operation(summary = "Create bulk notification for all users")
    public ResponseEntity<Map<String, Object>> createBulkNotification(
            @RequestBody CreateBulkNotificationRequest request) {
        try {
            Map<String, Object> result = bulkNotificationService.createBulkNotification(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error creating bulk notification", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to create bulk notification"));
        }
    }

    /**
     * Get bulk notifications for dashboard with user read status
     */
    @GetMapping("/dashboard")
    @Operation(summary = "Get bulk notifications for dashboard")
    public ResponseEntity<Map<String, Object>> getDashboardBulkNotifications(
            @RequestParam String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Long userIdLong = Long.valueOf(userId.hashCode() & 0x7fffffffL);
            Map<String, Object> result = bulkNotificationService.getDashboardBulkNotifications(userIdLong, page, size);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error getting dashboard bulk notifications for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get bulk notifications"));
        }
    }

    /**
     * Mark bulk notification as read for user
     */
    @PostMapping("/{bulkNotificationId}/read")
    @Operation(summary = "Mark bulk notification as read for user")
    public ResponseEntity<Map<String, Object>> markBulkNotificationAsRead(
            @PathVariable String bulkNotificationId,
            @RequestParam String userId) {
        try {
            Long userIdLong = Long.valueOf(userId.hashCode() & 0x7fffffffL);
            bulkNotificationService.markBulkNotificationAsRead(userIdLong, bulkNotificationId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Bulk notification marked as read");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error marking bulk notification as read for user: {} notification: {}", userId, bulkNotificationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to mark notification as read"));
        }
    }

    /**
     * Mark all bulk notifications as read for user
     */
    @PostMapping("/read-all")
    @Operation(summary = "Mark all bulk notifications as read for user")
    public ResponseEntity<Map<String, Object>> markAllBulkNotificationsAsRead(@RequestParam String userId) {
        try {
            Long userIdLong = Long.valueOf(userId.hashCode() & 0x7fffffffL);
            int updatedCount = bulkNotificationService.markAllBulkNotificationsAsRead(userIdLong);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("updatedCount", updatedCount);
            response.put("message", "All bulk notifications marked as read");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error marking all bulk notifications as read for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to mark all notifications as read"));
        }
    }

    /**
     * Get bulk notification statistics
     */
    @GetMapping("/stats")
    @Operation(summary = "Get bulk notification statistics")
    public ResponseEntity<Map<String, Object>> getBulkNotificationStats() {
        try {
            Map<String, Object> stats = bulkNotificationService.getBulkNotificationStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("Error getting bulk notification stats", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get statistics"));
        }
    }
}
