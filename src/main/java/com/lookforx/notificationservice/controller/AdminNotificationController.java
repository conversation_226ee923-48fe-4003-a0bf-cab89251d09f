package com.lookforx.notificationservice.controller;

import com.lookforx.notificationservice.dto.CreateBulkNotificationRequest;
import com.lookforx.notificationservice.service.BulkNotificationService;
import com.lookforx.notificationservice.service.UserNotificationService;
import com.lookforx.common.enums.LanguageCode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.lookforx.notificationservice.domain.BulkNotification;

/**
 * REST Controller for admin bulk notification management
 * This controller handles only bulk notifications that are displayed in admin dashboard
 */
@RestController
@RequestMapping("/api/admin/notifications")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Admin Bulk Notifications", description = "Admin bulk notification management operations")
public class AdminNotificationController {

    private final BulkNotificationService bulkNotificationService;
    private final UserNotificationService userNotificationService;

    /**
     * Create bulk notification for all users
     */
    @PostMapping("/bulk")
    @Operation(summary = "Create bulk notification for all users")
    public ResponseEntity<Map<String, Object>> createBulkNotification(
            @RequestBody CreateBulkNotificationRequest request) {
        try {
            Map<String, Object> result = bulkNotificationService.createBulkNotification(request);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error creating bulk notification", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to create bulk notification"));
        }
    }

    /**
     * Get bulk notifications for admin panel with pagination and language support
     */
    @GetMapping("/bulk")
    @Operation(summary = "Get bulk notifications for admin panel")
    public ResponseEntity<Map<String, Object>> getBulkNotificationsForAdmin(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "EN") String language,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String priority) {
        try {
            // Validate language code
            LanguageCode validatedLanguageCode;
            try {
                validatedLanguageCode = LanguageCode.valueOf(language.toUpperCase());
            } catch (IllegalArgumentException e) {
                log.warn("Invalid language code provided: {}, defaulting to EN", language);
                validatedLanguageCode = LanguageCode.EN;
            }

            Map<String, Object> result = bulkNotificationService.getBulkNotificationsForAdmin(
                page, size, validatedLanguageCode, search, status, priority);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error getting bulk notifications for admin", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get bulk notifications"));
        }
    }

    /**
     * Get bulk notification statistics
     */
    @GetMapping("/stats")
    @Operation(summary = "Get bulk notification statistics")
    public ResponseEntity<Map<String, Object>> getBulkNotificationStats() {
        try {
            Map<String, Object> stats = bulkNotificationService.getBulkNotificationStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("Error getting bulk notification stats", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get statistics"));
        }
    }

    /**
     * Get comprehensive notification statistics for admin dashboard
     */
    @GetMapping("/statistics")
    @Operation(summary = "Get comprehensive notification statistics")
    public ResponseEntity<Map<String, Object>> getNotificationStatistics() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // Get bulk notification stats
            Map<String, Object> bulkStats = bulkNotificationService.getBulkNotificationStats();

            // Get user notification counts (simulated for now since we don't have user notifications yet)
            LocalDateTime now = LocalDateTime.now();

            // Create comprehensive stats structure expected by frontend
            stats.put("totalNotifications", bulkStats.getOrDefault("totalActiveBulkNotifications", 0L));
            stats.put("recentCount", 0L); // Will be updated when user notifications are implemented

            // Create byStatus map
            Map<String, Long> byStatus = new HashMap<>();
            byStatus.put("SENT", (Long) bulkStats.getOrDefault("totalActiveBulkNotifications", 0L));
            byStatus.put("FAILED", 0L);
            byStatus.put("PENDING", 0L);
            byStatus.put("DELIVERED", 0L);
            byStatus.put("CANCELLED", 0L);
            stats.put("byStatus", byStatus);

            // Create byType map
            Map<String, Long> byType = new HashMap<>();
            byType.put("BULK", (Long) bulkStats.getOrDefault("totalActiveBulkNotifications", 0L));
            byType.put("USER", 0L); // Will be updated when user notifications are implemented
            stats.put("byType", byType);

            stats.put("timestamp", now);

            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("Error getting notification statistics", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get statistics"));
        }
    }

    /**
     * Get recent bulk notifications for dashboard display
     */
    @GetMapping("/dashboard/recent")
    @Operation(summary = "Get recent bulk notifications for dashboard (last 10 days)")
    public ResponseEntity<List<Map<String, Object>>> getRecentBulkNotificationsForDashboard(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "EN") String language) {
        try {
            // Validate language code
            LanguageCode validatedLanguageCode;
            try {
                validatedLanguageCode = LanguageCode.valueOf(language.toUpperCase());
            } catch (IllegalArgumentException e) {
                log.warn("Invalid language code provided: {}, defaulting to EN", language);
                validatedLanguageCode = LanguageCode.EN;
            }
            final LanguageCode languageCode = validatedLanguageCode;

            List<BulkNotification> recentNotifications = bulkNotificationService.getRecentBulkNotificationsLast10Days(page, size);

            List<Map<String, Object>> dashboardNotifications = recentNotifications.stream()
                .map(notification -> {
                    Map<String, Object> item = new HashMap<>();
                    item.put("id", notification.getId());

                    // Get title and message in requested language with fallback to EN
                    String title = bulkNotificationService.getTitleByLanguage(notification, languageCode);
                    String message = bulkNotificationService.getMessageByLanguage(notification, languageCode);

                    item.put("title", title != null ? title : "No Title");
                    item.put("message", message != null ? message : "No Message");
                    item.put("status", notification.getStatus().toString());
                    item.put("priority", notification.getPriority().toString());
                    item.put("createdAt", notification.getCreatedAt());
                    item.put("targetUserCount", notification.getTargetUserCount());
                    item.put("readCount", notification.getReadCount());
                    item.put("clickedCount", notification.getClickedCount());
                    item.put("language", languageCode.toString());

                    return item;
                })
                .collect(Collectors.toList());

            return ResponseEntity.ok(dashboardNotifications);
        } catch (Exception e) {
            log.error("Error getting recent bulk notifications for dashboard", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Delete bulk notification
     */
    @DeleteMapping("/bulk/{id}")
    @Operation(summary = "Delete bulk notification")
    public ResponseEntity<Map<String, Object>> deleteBulkNotification(@PathVariable String id) {
        try {
            log.info("Deleting bulk notification with ID: {}", id);
            bulkNotificationService.deleteBulkNotification(id);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "message", "Bulk notification deleted successfully"
            ));
        } catch (Exception e) {
            log.error("Error deleting bulk notification with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "success", false,
                    "error", "Failed to delete bulk notification: " + e.getMessage()
                ));
        }
    }

    /**
     * Update bulk notification
     */
    @PutMapping("/bulk/{id}")
    @Operation(summary = "Update bulk notification")
    public ResponseEntity<Map<String, Object>> updateBulkNotification(
            @PathVariable String id,
            @RequestBody CreateBulkNotificationRequest request) {
        try {
            log.info("Updating bulk notification with ID: {}", id);
            BulkNotification updatedNotification = bulkNotificationService.updateBulkNotification(id, request);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", updatedNotification,
                "message", "Bulk notification updated successfully"
            ));
        } catch (Exception e) {
            log.error("Error updating bulk notification with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of(
                    "success", false,
                    "error", "Failed to update bulk notification: " + e.getMessage()
                ));
        }
    }

    /**
     * Get bulk notification by ID for editing
     */
    @GetMapping("/bulk/{id}")
    @Operation(summary = "Get bulk notification by ID")
    public ResponseEntity<Map<String, Object>> getBulkNotificationById(
            @PathVariable String id,
            @RequestParam(defaultValue = "EN") String language) {
        try {
            LanguageCode validatedLanguageCode;
            try {
                validatedLanguageCode = LanguageCode.valueOf(language.toUpperCase());
            } catch (IllegalArgumentException e) {
                log.warn("Invalid language code provided: {}, defaulting to EN", language);
                validatedLanguageCode = LanguageCode.EN;
            }

            Map<String, Object> result = bulkNotificationService.getBulkNotificationById(id, validatedLanguageCode);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error getting bulk notification with ID: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get bulk notification"));
        }
    }
}
