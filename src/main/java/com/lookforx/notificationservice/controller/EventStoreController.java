package com.lookforx.notificationservice.controller;

import com.lookforx.notificationservice.domain.EventStore;
import com.lookforx.notificationservice.service.EventReplayService;
import com.lookforx.notificationservice.service.EventStoreService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * REST Controller for Event Store operations and replay functionality
 */
@RestController
@RequestMapping("/api/events")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Event Store", description = "Event sourcing and replay operations")
public class EventStoreController {
    
    private final EventStoreService eventStoreService;
    private final EventReplayService eventReplayService;
    
    /**
     * Get events with pagination and filtering
     */
    @GetMapping
    @Operation(summary = "Get events with pagination and filtering")
    public ResponseEntity<Page<EventStore>> getEvents(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String serviceName,
            @RequestParam(required = false) String eventType,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) EventStore.EventStatus status,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<EventStore> events;
        
        if (startDate != null && endDate != null) {
            events = eventStoreService.getEventsByDateRange(startDate, endDate, pageable);
        } else if (serviceName != null) {
            events = eventStoreService.getEventsByService(serviceName, pageable);
        } else if (eventType != null) {
            events = eventStoreService.getEventsByType(eventType, pageable);
        } else if (userId != null) {
            events = eventStoreService.getEventsByUser(userId, pageable);
        } else if (status != null) {
            events = eventStoreService.getEventsByStatus(status, pageable);
        } else {
            // Default: get all events with pagination
            events = eventStoreService.getEventsByDateRange(
                LocalDateTime.now().minusDays(7), 
                LocalDateTime.now(), 
                pageable
            );
        }
        
        return ResponseEntity.ok(events);
    }
    
    /**
     * Get event statistics
     */
    @GetMapping("/statistics")
    @Operation(summary = "Get event statistics")
    public ResponseEntity<EventStoreService.EventStatistics> getEventStatistics(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        if (startDate == null) {
            startDate = LocalDateTime.now().minusDays(30);
        }
        if (endDate == null) {
            endDate = LocalDateTime.now();
        }
        
        EventStoreService.EventStatistics statistics = eventStoreService.getEventStatistics(startDate, endDate);
        return ResponseEntity.ok(statistics);
    }
    
    /**
     * Replay events by date range
     */
    @PostMapping("/replay/date-range")
    @Operation(summary = "Replay events by date range")
    public ResponseEntity<CompletableFuture<EventReplayService.ReplayResult>> replayEventsByDateRange(
            @Parameter(description = "Start date for replay") 
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date for replay")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        log.info("Replay request by date range: startDate={}, endDate={}", startDate, endDate);
        CompletableFuture<EventReplayService.ReplayResult> result = 
            eventReplayService.replayEventsByDateRange(startDate, endDate);
        
        return ResponseEntity.accepted().body(result);
    }
    
    /**
     * Replay events by service
     */
    @PostMapping("/replay/service")
    @Operation(summary = "Replay events by service")
    public ResponseEntity<CompletableFuture<EventReplayService.ReplayResult>> replayEventsByService(
            @Parameter(description = "Service name")
            @RequestParam String serviceName,
            @Parameter(description = "Start date for replay")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date for replay")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        log.info("Replay request by service: service={}, startDate={}, endDate={}", serviceName, startDate, endDate);
        CompletableFuture<EventReplayService.ReplayResult> result = 
            eventReplayService.replayEventsByService(serviceName, startDate, endDate);
        
        return ResponseEntity.accepted().body(result);
    }
    
    /**
     * Replay events by aggregate
     */
    @PostMapping("/replay/aggregate")
    @Operation(summary = "Replay events by aggregate")
    public ResponseEntity<CompletableFuture<EventReplayService.ReplayResult>> replayEventsByAggregate(
            @Parameter(description = "Aggregate ID")
            @RequestParam String aggregateId,
            @Parameter(description = "Aggregate type")
            @RequestParam String aggregateType,
            @Parameter(description = "Start date for replay")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "End date for replay")
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        
        log.info("Replay request by aggregate: aggregate={}:{}, startDate={}, endDate={}", 
            aggregateType, aggregateId, startDate, endDate);
        CompletableFuture<EventReplayService.ReplayResult> result = 
            eventReplayService.replayEventsByAggregate(aggregateId, aggregateType, startDate, endDate);
        
        return ResponseEntity.accepted().body(result);
    }
    
    /**
     * Replay specific events by IDs
     */
    @PostMapping("/replay/ids")
    @Operation(summary = "Replay specific events by IDs")
    public ResponseEntity<CompletableFuture<EventReplayService.ReplayResult>> replayEventsByIds(
            @Parameter(description = "List of event IDs to replay")
            @RequestBody List<String> eventIds) {
        
        log.info("Replay request by IDs: eventCount={}", eventIds.size());
        CompletableFuture<EventReplayService.ReplayResult> result = 
            eventReplayService.replayEventsByIds(eventIds);
        
        return ResponseEntity.accepted().body(result);
    }
    
    /**
     * Get failed events for retry
     */
    @GetMapping("/failed")
    @Operation(summary = "Get failed events for retry")
    public ResponseEntity<List<EventStore>> getFailedEvents(
            @RequestParam(defaultValue = "3") int maxRetryCount) {
        
        List<EventStore> failedEvents = eventStoreService.getFailedEventsForRetry(maxRetryCount);
        return ResponseEntity.ok(failedEvents);
    }
    
    /**
     * Get events by service names (for admin panel dropdown)
     */
    @GetMapping("/services")
    @Operation(summary = "Get distinct service names")
    public ResponseEntity<List<String>> getServiceNames() {
        // This would need a custom repository method to get distinct service names
        List<String> services = List.of(
            "user-service",
            "category-service", 
            "form-service",
            "request-service",
            "bid-service",
            "payment-service",
            "campaign-service",
            "notification-service"
        );
        return ResponseEntity.ok(services);
    }
    
    /**
     * Get event types (for admin panel dropdown)
     */
    @GetMapping("/types")
    @Operation(summary = "Get distinct event types")
    public ResponseEntity<List<String>> getEventTypes() {
        List<String> eventTypes = List.of(
            "USER_REGISTERED",
            "USER_LOGIN",
            "FORM_SUBMITTED",
            "REQUEST_CREATED",
            "REQUEST_STATUS_CHANGED",
            "BID_PLACED",
            "PAYMENT_COMPLETED",
            "CAMPAIGN_CREATED",
            "MEMBERSHIP_UPGRADED"
        );
        return ResponseEntity.ok(eventTypes);
    }
}
