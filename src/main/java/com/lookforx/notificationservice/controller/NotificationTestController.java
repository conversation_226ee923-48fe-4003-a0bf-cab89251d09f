package com.lookforx.notificationservice.controller;

import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;
import com.lookforx.notificationservice.service.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for notification service
 */
@RestController
@RequestMapping("/api/v1/notifications")
@RequiredArgsConstructor
@Slf4j
public class NotificationTestController {
    
    private final NotificationService notificationService;
    
    /**
     * Test endpoint to create a notification
     */
    @PostMapping("/test")
    public ResponseEntity<String> createTestNotification(@RequestBody TestNotificationRequest request) {
        log.info("Creating test notification: {}", request);
        
        Notification notification = Notification.builder()
                .eventId("TEST-" + System.currentTimeMillis())
                .eventType("TEST_EVENT")
                .notificationType(request.getNotificationType())
                .priority(request.getPriority())
                .userId(request.getUserId())
                .recipient(request.getRecipient())
                .subject(request.getSubject())
                .content(request.getContent())
                .serviceName("notification-service")
                .build();
        
        boolean success = notificationService.sendNotification(notification);
        
        if (success) {
            return ResponseEntity.ok("Notification sent successfully");
        } else {
            return ResponseEntity.badRequest().body("Failed to send notification");
        }
    }
    
    /**
     * Get all notifications for a user
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<Notification>> getUserNotifications(@PathVariable Long userId) {
        List<Notification> notifications = notificationService.getByUserId(userId);
        return ResponseEntity.ok(notifications);
    }
    
    /**
     * Get notification statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<NotificationService.NotificationStats> getStats() {
        NotificationService.NotificationStats stats = notificationService.getStats();
        return ResponseEntity.ok(stats);
    }
    
    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "notification-service");
        health.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(health);
    }
    
    /**
     * Test notification request DTO
     */
    public static class TestNotificationRequest {
        private NotificationType notificationType = NotificationType.EMAIL;
        private NotificationPriority priority = NotificationPriority.NORMAL;
        private Long userId;
        private String recipient;
        private String subject;
        private String content;
        
        // Getters and setters
        public NotificationType getNotificationType() { return notificationType; }
        public void setNotificationType(NotificationType notificationType) { this.notificationType = notificationType; }
        
        public NotificationPriority getPriority() { return priority; }
        public void setPriority(NotificationPriority priority) { this.priority = priority; }
        
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }
        
        public String getRecipient() { return recipient; }
        public void setRecipient(String recipient) { this.recipient = recipient; }
        
        public String getSubject() { return subject; }
        public void setSubject(String subject) { this.subject = subject; }
        
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        @Override
        public String toString() {
            return "TestNotificationRequest{" +
                    "notificationType=" + notificationType +
                    ", priority=" + priority +
                    ", userId=" + userId +
                    ", recipient='" + recipient + '\'' +
                    ", subject='" + subject + '\'' +
                    ", content='" + content + '\'' +
                    '}';
        }
    }
}
