package com.lookforx.notificationservice.controller;

import com.lookforx.notificationservice.service.UserNotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/user-notifications")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "User Notification Management", description = "APIs for user-specific notifications (bell notifications)")
public class UserNotificationController {

    private final UserNotificationService userNotificationService;

    /**
     * Get unread notification count for user (for bell icon)
     */
    @GetMapping("/user/{userId}/count")
    @Operation(summary = "Get unread notification count for user")
    public ResponseEntity<Map<String, Object>> getUnreadCount(@PathVariable String userId) {
        try {
            Long userIdLong = convertUserIdToLong(userId);
            long count = userNotificationService.getUnreadNotificationCount(userIdLong);
            
            Map<String, Object> response = new HashMap<>();
            response.put("count", count);
            response.put("userId", userId);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting unread notification count for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get notification count"));
        }
    }

    /**
     * Get user notifications for bell dropdown
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "Get user notifications for bell dropdown")
    public ResponseEntity<Map<String, Object>> getUserNotifications(
            @PathVariable String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Long userIdLong = convertUserIdToLong(userId);
            Map<String, Object> result = userNotificationService.getUserNotifications(userIdLong, page, size);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Error getting user notifications for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to get notifications"));
        }
    }

    /**
     * Mark user notification as read
     */
    @PostMapping("/user/{userId}/notification/{notificationId}/read")
    @Operation(summary = "Mark user notification as read")
    public ResponseEntity<Map<String, Object>> markNotificationAsRead(
            @PathVariable String userId,
            @PathVariable String notificationId) {
        try {
            Long userIdLong = convertUserIdToLong(userId);
            userNotificationService.markNotificationAsRead(userIdLong, notificationId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Notification marked as read");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error marking notification as read for user: {} notification: {}", userId, notificationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to mark notification as read"));
        }
    }

    /**
     * Mark all user notifications as read
     */
    @PostMapping("/user/{userId}/read-all")
    @Operation(summary = "Mark all user notifications as read")
    public ResponseEntity<Map<String, Object>> markAllNotificationsAsRead(@PathVariable String userId) {
        try {
            Long userIdLong = convertUserIdToLong(userId);
            int updatedCount = userNotificationService.markAllNotificationsAsRead(userIdLong);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("updatedCount", updatedCount);
            response.put("message", "All notifications marked as read");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error marking all notifications as read for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to mark all notifications as read"));
        }
    }

    /**
     * Mark user notification as clicked
     */
    @PostMapping("/user/{userId}/notification/{notificationId}/click")
    @Operation(summary = "Mark user notification as clicked")
    public ResponseEntity<Map<String, Object>> markNotificationAsClicked(
            @PathVariable String userId,
            @PathVariable String notificationId) {
        try {
            Long userIdLong = convertUserIdToLong(userId);
            userNotificationService.markNotificationAsClicked(userIdLong, notificationId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Notification marked as clicked");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error marking notification as clicked for user: {} notification: {}", userId, notificationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to mark notification as clicked"));
        }
    }

    /**
     * Convert MongoDB ObjectId string to Long for database compatibility
     */
    private Long convertUserIdToLong(String userId) {
        return Long.valueOf(userId.hashCode() & 0x7fffffffL);
    }
}
