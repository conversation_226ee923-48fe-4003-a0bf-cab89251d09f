package com.lookforx.notificationservice.controller;

import com.lookforx.notificationservice.dto.BulkNotificationCommand;
import com.lookforx.notificationservice.service.BulkNotificationProducer;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * Test controller for Kafka functionality
 */
@RestController
@RequestMapping("/api/test/kafka")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Kafka Test", description = "Test endpoints for Kafka functionality")
public class KafkaTestController {

    private final BulkNotificationProducer bulkNotificationProducer;

    /**
     * Test Kafka producer
     */
    @PostMapping("/test-producer")
    @Operation(summary = "Test Kafka producer")
    public ResponseEntity<Map<String, Object>> testProducer() {
        log.info("Testing Kafka producer");

        try {
            // Create a simple test command
            BulkNotificationCommand testCommand = BulkNotificationCommand.builder()
                    .commandId(UUID.randomUUID().toString())
                    .correlationId(UUID.randomUUID().toString())
                    .timestamp(LocalDateTime.now())
                    .notificationType(com.lookforx.common.enums.NotificationType.INFO)
                    .priority(com.lookforx.common.enums.NotificationPriority.NORMAL)
                    .titles(Map.of(com.lookforx.common.enums.LanguageCode.EN, "Test Title"))
                    .messages(Map.of(com.lookforx.common.enums.LanguageCode.EN, "Test Message"))
                    .showInBell(true)
                    .sendEmail(false)
                    .sendPush(false)
                    .sourceService("test-controller")
                    .targetingOptions(BulkNotificationCommand.TargetingOptions.builder()
                            .allUsers(true)
                            .build())
                    .processingOptions(BulkNotificationCommand.ProcessingOptions.builder()
                            .batchSize(10)
                            .maxThreads(2)
                            .build())
                    .build();

            // Send to Kafka
            bulkNotificationProducer.sendBulkNotificationCommand(testCommand);

            return ResponseEntity.ok(Map.of(
                    "status", "SUCCESS",
                    "message", "Test command sent to Kafka",
                    "commandId", testCommand.getCommandId(),
                    "timestamp", LocalDateTime.now()
            ));

        } catch (Exception e) {
            log.error("Error testing Kafka producer", e);
            return ResponseEntity.status(500).body(Map.of(
                    "status", "ERROR",
                    "message", "Failed to send test command: " + e.getMessage(),
                    "timestamp", LocalDateTime.now()
            ));
        }
    }

    /**
     * Get Kafka topic info
     */
    @GetMapping("/topic-info")
    @Operation(summary = "Get Kafka topic information")
    public ResponseEntity<Map<String, Object>> getTopicInfo() {
        return ResponseEntity.ok(Map.of(
                "bulkNotificationTopic", bulkNotificationProducer.getBulkNotificationTopic(),
                "status", "CONFIGURED",
                "timestamp", LocalDateTime.now()
        ));
    }
}
