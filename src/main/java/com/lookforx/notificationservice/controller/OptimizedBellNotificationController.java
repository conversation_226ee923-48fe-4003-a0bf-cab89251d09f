package com.lookforx.notificationservice.controller;

import com.lookforx.notificationservice.service.UserNotificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Admin Panel Bell Notification Controller - API Gateway Compatible
 */
@RestController
@RequestMapping("/api/admin/bell-notifications")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Admin Bell Notifications", description = "Admin panel bell notification management")
public class OptimizedBellNotificationController {

    private final UserNotificationService userNotificationService;

    /**
     * Get bell notifications for admin panel user
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "Get bell notifications for admin panel user")
    public ResponseEntity<Map<String, Object>> getBellNotifications(
            @PathVariable String userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "EN") String language) {

        log.info("Admin panel: Getting bell notifications for user {} with language {}", userId, language);

        try {
            Long userIdLong = Long.valueOf(userId);
            log.info("Admin panel: Converted userId {} to Long: {}", userId, userIdLong);

            // Get user notifications using existing service
            Map<String, Object> result = userNotificationService.getUserNotifications(userIdLong, page, size);
            log.info("Admin panel: Service returned result: {}", result);

            // Transform the result for admin panel compatibility
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", result);
            response.put("userId", userId);
            response.put("language", language);

            log.info("Admin panel: Retrieved notifications for user {} successfully", userIdLong);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Admin panel: Error getting notifications for user {}: {}", userId, e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to fetch notifications: " + e.getMessage());
            errorResponse.put("userId", userId);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Get unread notification count for admin panel user
     */
    @GetMapping("/user/{userId}/count")
    @Operation(summary = "Get unread notification count for admin panel user")
    public ResponseEntity<Map<String, Object>> getUnreadCount(@PathVariable String userId) {
        log.info("Admin panel: Getting unread notification count for user {}", userId);

        try {
            Long userIdLong = Long.valueOf(userId);
            long count = userNotificationService.getUnreadNotificationCount(userIdLong);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("count", count);
            response.put("userId", userId);

            log.info("Admin panel: User {} has {} unread notifications", userIdLong, count);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Admin panel: Error getting notification count for user {}: {}", userId, e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("count", 0);
            errorResponse.put("error", "Failed to fetch count: " + e.getMessage());
            errorResponse.put("userId", userId);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Mark notification as read for admin panel
     */
    @PostMapping("/user/{userId}/notification/{notificationId}/read")
    @Operation(summary = "Mark notification as read for admin panel")
    public ResponseEntity<Map<String, Object>> markNotificationAsRead(
            @PathVariable String userId,
            @PathVariable String notificationId) {

        log.info("Admin panel: Marking notification {} as read for user {}", notificationId, userId);

        try {
            Long userIdLong = Long.valueOf(userId);
            userNotificationService.markNotificationAsRead(userIdLong, notificationId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Notification marked as read successfully");
            response.put("userId", userId);
            response.put("notificationId", notificationId);

            log.info("Admin panel: Successfully marked notification {} as read for user {}", notificationId, userIdLong);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Admin panel: Failed to mark notification {} as read for user {}: {}", notificationId, userId, e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to mark as read: " + e.getMessage());
            errorResponse.put("userId", userId);
            errorResponse.put("notificationId", notificationId);
            return ResponseEntity.ok(errorResponse);
        }
    }

    /**
     * Mark all notifications as read for admin panel
     */
    @PostMapping("/user/{userId}/mark-all-read")
    @Operation(summary = "Mark all notifications as read for admin panel")
    public ResponseEntity<Map<String, Object>> markAllAsRead(@PathVariable String userId) {
        log.info("Admin panel: Marking all notifications as read for user {}", userId);

        try {
            Long userIdLong = Long.valueOf(userId);
            int updatedCount = userNotificationService.markAllNotificationsAsRead(userIdLong);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "All notifications marked as read successfully");
            response.put("updatedCount", updatedCount);
            response.put("userId", userId);

            log.info("Admin panel: Marked {} notifications as read for user {}", updatedCount, userIdLong);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Admin panel: Failed to mark all notifications as read for user {}: {}", userId, e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to mark all as read: " + e.getMessage());
            errorResponse.put("updatedCount", 0);
            errorResponse.put("userId", userId);
            return ResponseEntity.ok(errorResponse);
        }
    }
}
