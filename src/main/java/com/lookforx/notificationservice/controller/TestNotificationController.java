package com.lookforx.notificationservice.controller;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.notificationservice.domain.Notification;
import com.lookforx.notificationservice.repository.NotificationRepository;
import com.lookforx.notificationservice.service.BulkNotificationService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Test controller for creating notifications
 */
@RestController
@RequestMapping("/api/test")
@RequiredArgsConstructor
@Slf4j
public class TestNotificationController {


    private final NotificationRepository notificationRepository;
    private final BulkNotificationService bulkNotificationService;
    
    /**
     * Create a test notification
     */
    @PostMapping("/notification/{userId}")
    public ResponseEntity<Map<String, Object>> createTestNotification(@PathVariable String userId) {
        try {
            Long userIdLong = convertUserIdToLong(userId);

            // First, delete existing test notifications for this user to avoid duplicates
            notificationRepository.deleteByUserIdAndEventType(userIdLong, "USER_REGISTERED");
            log.info("Deleted existing test notifications for user: {}", userIdLong);

            Map<String, String> titles = new HashMap<>();
            titles.put("EN", "Welcome to LookForX!");
            titles.put("TR", "LookForX'e Hoş Geldiniz!");
            titles.put("DE", "Willkommen bei LookForX!");
            
            Map<String, String> messages = new HashMap<>();
            messages.put("EN", "Your account has been successfully created. Start exploring our platform!");
            messages.put("TR", "Hesabınız başarıyla oluşturuldu. Platformumuzu keşfetmeye başlayın!");
            messages.put("DE", "Ihr Konto wurde erfolgreich erstellt. Beginnen Sie mit der Erkundung unserer Plattform!");
            
            // Create notification manually to avoid content constraint
            Map<String, String> templateParams = new HashMap<>();
            templateParams.put("userName", "Test User");

            // Create complete language maps for all supported languages
            Map<String, String> completeTitles = createCompleteLanguageMap(titles, "Welcome to LookForX!");
            Map<String, String> completeMessages = createCompleteLanguageMap(messages, "Your account has been successfully created. Start exploring our platform!");

            // Convert to LanguageCode maps
            Map<LanguageCode, String> convertedTitles = convertToLanguageCodeMap(completeTitles);
            Map<LanguageCode, String> convertedMessages = convertToLanguageCodeMap(completeMessages);

            Notification notification = Notification.builder()
                .userId(userIdLong)
                .eventId("test-event-" + System.currentTimeMillis())
                .eventType("USER_REGISTERED")
                .notificationType(com.lookforx.common.enums.NotificationType.IN_APP_NOTIFICATION)
                .priority(com.lookforx.common.enums.NotificationPriority.NORMAL)
                .titles(convertedTitles)
                .messages(convertedMessages)
                .showInBell(true)
                .isRead(false)
                .isClicked(false)
                .actionUrl("/dashboard")
                .relatedEntityId(userIdLong.toString())
                .relatedEntityType("USER")
                .sourceService("test-service")
                .serviceName("test-service")
                .status(Notification.NotificationStatus.SENT)
                .expiresAt(java.time.LocalDateTime.now().plusDays(30))
                .content("Test notification content") // Add content to avoid constraint
                .subject("Test notification") // Add subject to avoid constraint
                .recipient("user" + userIdLong + "@example.com") // Add recipient to avoid constraint
                .templateParameters(templateParams)
                .deliveryAttempts(0)
                .maxAttempts(3)
                .build();

            notification = notificationRepository.save(notification);
            log.info("Saved notification with ID: {} for userId: {} (userIdLong: {})",
                    notification.getId(), userId, userIdLong);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("notificationId", notification.getId());
            response.put("userId", userId);
            response.put("userIdLong", userIdLong);
            response.put("message", "Test notification created successfully");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error creating test notification", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * Test repository queries directly
     */
    @GetMapping("/repository/user/{userId}/bell-notifications")
    public ResponseEntity<Map<String, Object>> testBellNotifications(@PathVariable String userId) {
        try {
            Long userIdLong = convertUserIdToLong(userId);
            LocalDateTime now = LocalDateTime.now();

            // Test count query
            long count = notificationRepository.countUnreadBellNotificationsByUserId(userIdLong, now);

            // Test list query without pagination
            List<Notification> notifications = notificationRepository.findBellNotificationsByUserId(userIdLong, now);

            // Test list query with pagination
            Pageable pageable = PageRequest.of(0, 10);
            Page<Notification> pagedNotifications = notificationRepository.findBellNotificationsByUserId(userIdLong, now, pageable);

            Map<String, Object> response = new HashMap<>();
            response.put("count", count);
            response.put("notificationsCount", notifications.size());
            response.put("pagedNotificationsCount", pagedNotifications.getContent().size());
            response.put("totalElements", pagedNotifications.getTotalElements());
            response.put("notifications", notifications);
            response.put("userIdLong", userIdLong);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error testing bell notifications for user: {}", userId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * Test user ID conversion
     */
    @GetMapping("/repository/user/{userId}/test-conversion")
    public ResponseEntity<Map<String, Object>> testUserIdConversion(@PathVariable String userId) {
        try {
            Long userIdLong = convertUserIdToLong(userId);

            Map<String, Object> response = new HashMap<>();
            response.put("userId", userId);
            response.put("userIdLong", userIdLong);
            response.put("success", true);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error converting user ID: {}", userId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * Get all notifications for user (no filters)
     */
    @GetMapping("/repository/user/{userId}/all-notifications")
    public ResponseEntity<Map<String, Object>> getAllNotifications(@PathVariable String userId) {
        try {
            Long userIdLong = convertUserIdToLong(userId);
            List<Notification> notifications = notificationRepository.findByUserIdOrderByCreatedAtDesc(userIdLong);

            Map<String, Object> response = new HashMap<>();
            response.put("count", notifications.size());
            response.put("notifications", notifications);
            response.put("userIdLong", userIdLong);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting all notifications for user: {}", userId, e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    /**
     * Convert userId string to Long for database compatibility
     */
    private Long convertUserIdToLong(String userId) {
        return Long.valueOf(userId);
    }

    /**
     * Convert String-based language map to LanguageCode-based map
     */
    private Map<LanguageCode, String> convertToLanguageCodeMap(Map<String, String> stringMap) {
        if (stringMap == null) {
            return new HashMap<>();
        }

        Map<LanguageCode, String> result = new HashMap<>();
        for (Map.Entry<String, String> entry : stringMap.entrySet()) {
            try {
                LanguageCode languageCode = LanguageCode.valueOf(entry.getKey().toUpperCase());
                result.put(languageCode, entry.getValue());
            } catch (IllegalArgumentException e) {
                log.warn("Invalid language code: {}, skipping", entry.getKey());
            }
        }
        return result;
    }

    /**
     * Create complete language map with all supported languages
     * Falls back to English if translation not provided
     */
    private Map<String, String> createCompleteLanguageMap(Map<String, String> providedTranslations, String defaultEnglishText) {
        Map<String, String> completeMap = new HashMap<>();

        // Get English text (fallback)
        String englishText = providedTranslations.getOrDefault("EN", defaultEnglishText);

        // Add all supported language codes
        for (LanguageCode langCode : LanguageCode.values()) {
            String langCodeStr = langCode.name(); // This gives us the enum name (e.g., "EN", "TR", "DE")
            String translation = providedTranslations.getOrDefault(langCodeStr, englishText);
            completeMap.put(langCodeStr, translation);
        }

        return completeMap;
    }

    /**
     * Get raw notification count and all notifications from MongoDB
     */
    @GetMapping("/repository/raw-count")
    public ResponseEntity<Map<String, Object>> getRawNotificationCount() {
        Map<String, Object> response = new HashMap<>();
        response.put("message", "Raw count endpoint working");
        response.put("totalCount", "Testing...");

        return ResponseEntity.ok(response);
    }

    @PostMapping("/fix-bulk-timestamps")
    public ResponseEntity<Map<String, Object>> fixBulkNotificationTimestamps() {
        try {
            bulkNotificationService.fixMissingCreatedAtTimestamps();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Fixed missing createdAt timestamps for bulk notifications");

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error fixing bulk notification timestamps", e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}
