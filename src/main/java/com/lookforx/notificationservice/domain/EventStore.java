package com.lookforx.notificationservice.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Event Store entity for MongoDB - stores all events for event sourcing and replay
 */
@Document(collection = "event_store")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EventStore {
    
    @Id
    private String id;
    
    @Indexed
    private String eventId;
    
    @Indexed
    private String eventType;
    
    @Indexed
    private String serviceName;
    
    @Indexed
    private String aggregateId; // Entity ID that this event relates to (userId, requestId, etc.)
    
    @Indexed
    private String aggregateType; // Entity type (User, Request, Form, etc.)
    
    @Indexed
    private Long version; // Event version for ordering
    
    @Indexed
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime timestamp;
    
    @Indexed
    private Long userId;
    
    private String correlationId;
    
    // Original event data as JSON
    private String eventData;
    
    // Event metadata
    private Map<String, Object> metadata;
    
    // Kafka metadata
    private String topic;
    private Integer partition;
    private Long offset;
    
    // Processing status
    @Indexed
    private EventStatus status;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime processedAt;
    
    private String errorMessage;
    private Integer retryCount;
    
    // Replay information
    @Indexed
    private String replayId; // For grouping replayed events
    
    @Indexed
    private Boolean isReplayed;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime replayedAt;
    
    public enum EventStatus {
        RECEIVED,
        PROCESSING,
        PROCESSED,
        FAILED,
        REPLAYED
    }
}
