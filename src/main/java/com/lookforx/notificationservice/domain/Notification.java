package com.lookforx.notificationservice.domain;

import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Document for storing user notifications with multi-language support
 */
@Document(collection = "notifications")
@CompoundIndexes({
    @CompoundIndex(name = "idx_user_read", def = "{'userId': 1, 'isRead': 1}"),
    @CompoundIndex(name = "idx_user_clicked", def = "{'userId': 1, 'isClicked': 1}"),
    @CompoundIndex(name = "idx_show_in_bell", def = "{'showInBell': 1, 'isRead': 1, 'expiresAt': 1}"),
    @CompoundIndex(name = "idx_event_id", def = "{'eventId': 1}")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Notification {

    @Id
    private String id;

    @Indexed
    private Long userId;

    @Indexed
    private String eventId; // Related event ID from event store

    private String eventType;

    private NotificationType notificationType;

    @Builder.Default
    private NotificationPriority priority = NotificationPriority.NORMAL;

    // Multi-language title support
    @Builder.Default
    private Map<com.lookforx.common.enums.LanguageCode, String> titles = new HashMap<>();

    // Multi-language message support
    @Builder.Default
    private Map<com.lookforx.common.enums.LanguageCode, String> messages = new HashMap<>();

    // Helper methods for language-specific content
    public String getTitle(String languageCode) {
        try {
            com.lookforx.common.enums.LanguageCode langCode = com.lookforx.common.enums.LanguageCode.valueOf(languageCode.toUpperCase());
            String title = titles.get(langCode);
            if (title != null && !title.trim().isEmpty()) {
                return title;
            }
            // Fallback to English
            title = titles.get(com.lookforx.common.enums.LanguageCode.EN);
            if (title != null && !title.trim().isEmpty()) {
                return title;
            }
            // Fallback to first available translation
            return titles.values().stream()
                    .filter(t -> t != null && !t.trim().isEmpty())
                    .findFirst()
                    .orElse("No Title");
        } catch (Exception e) {
            return titles.values().stream()
                    .filter(t -> t != null && !t.trim().isEmpty())
                    .findFirst()
                    .orElse("No Title");
        }
    }

    public String getMessage(String languageCode) {
        try {
            com.lookforx.common.enums.LanguageCode langCode = com.lookforx.common.enums.LanguageCode.valueOf(languageCode.toUpperCase());
            String message = messages.get(langCode);
            if (message != null && !message.trim().isEmpty()) {
                return message;
            }
            // Fallback to English
            message = messages.get(com.lookforx.common.enums.LanguageCode.EN);
            if (message != null && !message.trim().isEmpty()) {
                return message;
            }
            // Fallback to first available translation
            return messages.values().stream()
                    .filter(m -> m != null && !m.trim().isEmpty())
                    .findFirst()
                    .orElse("No Message");
        } catch (Exception e) {
            return messages.values().stream()
                    .filter(m -> m != null && !m.trim().isEmpty())
                    .findFirst()
                    .orElse("No Message");
        }
    }

    // Bell notification settings
    @Builder.Default
    private Boolean showInBell = true; // Show in bell notification

    @Builder.Default
    private Boolean isRead = false;

    @Builder.Default
    private Boolean isClicked = false;

    private LocalDateTime readAt;

    private LocalDateTime clickedAt;

    // Additional notification settings
    @Builder.Default
    private Boolean sendEmail = false; // Send email notification

    @Builder.Default
    private Boolean sendPush = false; // Send push notification

    private String actionUrl; // URL to redirect when clicked

    private String iconUrl; // Custom icon for notification

    private LocalDateTime expiresAt; // When notification expires

    // Related entity information
    private String relatedEntityId;

    private String relatedEntityType;

    private String sourceService; // Which service created this notification

    private String serviceName; // For backward compatibility

    // Template support
    private String templateName;

    @Builder.Default
    private Map<String, String> templateParameters = new HashMap<>();

    // Status tracking
    @Builder.Default
    private NotificationStatus status = NotificationStatus.PENDING;

    private LocalDateTime sentAt;

    @Builder.Default
    private Integer deliveryAttempts = 0;

    @Builder.Default
    private Integer maxAttempts = 3;

    private LocalDateTime nextRetryAt;

    private String errorMessage;

    private String externalId; // ID from external service

    private String recipient; // email, phone number, device token etc.

    private String subject; // For backward compatibility

    private String content; // For backward compatibility

    private String correlationId;

    @CreatedDate
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    // Helper methods
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(LocalDateTime.now());
    }

    public boolean shouldShowInBell() {
        return showInBell && !isRead && !isExpired();
    }

    public enum NotificationStatus {
        PENDING,
        SENT,
        DELIVERED,
        FAILED,
        CANCELLED
    }
}
