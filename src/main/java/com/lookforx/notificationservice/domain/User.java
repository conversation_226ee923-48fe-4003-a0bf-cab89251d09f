package com.lookforx.notificationservice.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * User entity from auth database
 */
@Document(collection = "users")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User {

    @Id
    private String id;

    @Field("username")
    private String username;

    @Field("email")
    private String email;

    @Field("firstName")
    private String firstName;

    @Field("lastName")
    private String lastName;

    @Field("phoneNumber")
    private String phoneNumber;

    @Field("active")
    private Boolean isActive;

    @Field("email_verified")
    private Boolean isEmailVerified;

    @Field("isPhoneVerified")
    private Boolean isPhoneVerified;

    @Field("preferredLanguage")
    private String preferredLanguage;

    @Field("roles")
    private java.util.List<String> roles;

    @Field("createdAt")
    private LocalDateTime createdAt;

    @Field("updatedAt")
    private LocalDateTime updatedAt;

    @Field("lastLoginAt")
    private LocalDateTime lastLoginAt;

    @Field("profileImageUrl")
    private String profileImageUrl;

    @Field("dateOfBirth")
    private LocalDateTime dateOfBirth;

    @Field("gender")
    private String gender;

    @Field("country")
    private String country;

    @Field("city")
    private String city;

    @Field("timezone")
    private String timezone;

    @Field("notificationSettings")
    private NotificationSettings notificationSettings;

    /**
     * Notification settings for user
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NotificationSettings {
        private Boolean emailNotifications;
        private Boolean pushNotifications;
        private Boolean smsNotifications;
        private Boolean marketingEmails;
        private Boolean securityAlerts;
    }

    /**
     * Check if user is active and can receive notifications
     */
    public boolean canReceiveNotifications() {
        return Boolean.TRUE.equals(isActive) && 
               Boolean.TRUE.equals(isEmailVerified);
    }

    /**
     * Get user's preferred language or default to English
     */
    public String getPreferredLanguageOrDefault() {
        return preferredLanguage != null && !preferredLanguage.isEmpty() 
            ? preferredLanguage.toUpperCase() 
            : "EN";
    }

    /**
     * Check if user wants to receive email notifications
     */
    public boolean wantsEmailNotifications() {
        return notificationSettings != null && 
               Boolean.TRUE.equals(notificationSettings.getEmailNotifications());
    }

    /**
     * Check if user wants to receive push notifications
     */
    public boolean wantsPushNotifications() {
        return notificationSettings != null && 
               Boolean.TRUE.equals(notificationSettings.getPushNotifications());
    }

    /**
     * Get user's full name
     */
    public String getFullName() {
        StringBuilder fullName = new StringBuilder();
        if (firstName != null && !firstName.isEmpty()) {
            fullName.append(firstName);
        }
        if (lastName != null && !lastName.isEmpty()) {
            if (fullName.length() > 0) {
                fullName.append(" ");
            }
            fullName.append(lastName);
        }
        return fullName.length() > 0 ? fullName.toString() : username;
    }
}
