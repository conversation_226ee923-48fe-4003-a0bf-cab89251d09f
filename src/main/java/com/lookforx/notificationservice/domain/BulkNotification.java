package com.lookforx.notificationservice.domain;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification.NotificationStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Document for bulk notifications sent to all users
 * This approach is more performant than creating individual notification records for each user
 */
@Document(collection = "bulk_notifications")
@CompoundIndexes({
    @CompoundIndex(name = "idx_status_created", def = "{'status': 1, 'createdAt': -1}"),
    @CompoundIndex(name = "idx_expires_at", def = "{'expiresAt': 1}")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkNotification {

    @Id
    private String id;

    private NotificationType notificationType;

    private NotificationPriority priority;

    private NotificationStatus status;

    @Indexed
    private String eventId;

    private String eventType;

    private String correlationId;

    private String externalId;

    private String serviceName;

    private String sourceService;

    private String templateName;

    private String relatedEntityType;

    private String relatedEntityId;

    private String actionUrl;

    private String iconUrl;

    private LocalDateTime expiresAt;

    private LocalDateTime sentAt;

    @Builder.Default
    private Boolean sendEmail = false;

    @Builder.Default
    private Boolean sendPush = false;

    @Builder.Default
    private Boolean showInBell = true;

    @Builder.Default
    private Integer targetUserCount = 0;

    @Builder.Default
    private Integer deliveredCount = 0;

    @Builder.Default
    private Integer readCount = 0;

    @Builder.Default
    private Integer clickedCount = 0;

    // Multi-language titles stored as JSON
    @Builder.Default
    private Map<String, String> titles = new HashMap<>();

    // Multi-language messages stored as JSON
    @Builder.Default
    private Map<String, String> messages = new HashMap<>();

    // Parameters for template processing
    @Builder.Default
    private Map<String, String> parameters = new HashMap<>();

    // Audit fields
    @CreatedDate
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    // Helper methods
    public String getTitle(LanguageCode languageCode) {
        return titles.getOrDefault(languageCode.name(), titles.get(LanguageCode.EN.name()));
    }

    public String getMessage(LanguageCode languageCode) {
        return messages.getOrDefault(languageCode.name(), messages.get(LanguageCode.EN.name()));
    }

    public void incrementDeliveredCount() {
        this.deliveredCount = (this.deliveredCount == null ? 0 : this.deliveredCount) + 1;
    }

    public void incrementReadCount() {
        this.readCount = (this.readCount == null ? 0 : this.readCount) + 1;
    }

    public void incrementClickedCount() {
        this.clickedCount = (this.clickedCount == null ? 0 : this.clickedCount) + 1;
    }

    public boolean isExpired() {
        return expiresAt != null && LocalDateTime.now().isAfter(expiresAt);
    }
}
