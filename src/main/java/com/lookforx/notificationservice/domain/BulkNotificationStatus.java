package com.lookforx.notificationservice.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DBRef;

import java.time.LocalDateTime;

/**
 * Document to track individual user's read/click status for bulk notifications
 * This provides a more performant approach than creating individual notification records
 */
@Document(collection = "bulk_notification_status")
@CompoundIndexes({
    @CompoundIndex(name = "idx_user_bulk_notification", def = "{'userId': 1, 'bulkNotificationId': 1}", unique = true),
    @CompoundIndex(name = "idx_user_read_status", def = "{'userId': 1, 'isRead': 1}"),
    @CompoundIndex(name = "idx_user_clicked_status", def = "{'userId': 1, 'isClicked': 1}"),
    @CompoundIndex(name = "idx_bulk_notification_status", def = "{'bulkNotificationId': 1, 'isRead': 1}"),
    @CompoundIndex(name = "idx_user_show_in_bell", def = "{'userId': 1, 'showInBell': 1, 'isRead': 1}")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BulkNotificationStatus {

    @Id
    private String id;

    @Indexed
    private Long userId;

    @DBRef
    private BulkNotification bulkNotification;

    @Indexed
    private String bulkNotificationId;

    @Builder.Default
    private Boolean isRead = false;

    @Builder.Default
    private Boolean isClicked = false;

    @Builder.Default
    private Boolean showInBell = true;

    private LocalDateTime readAt;

    private LocalDateTime clickedAt;

    private LocalDateTime deliveredAt;

    // Audit fields
    @CreatedDate
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    // Helper methods
    public void markAsRead() {
        if (!this.isRead) {
            this.isRead = true;
            this.readAt = LocalDateTime.now();
        }
    }

    public void markAsClicked() {
        if (!this.isClicked) {
            this.isClicked = true;
            this.clickedAt = LocalDateTime.now();
            // Auto-mark as read when clicked
            if (!this.isRead) {
                markAsRead();
            }
        }
    }

    public void markAsDelivered() {
        if (this.deliveredAt == null) {
            this.deliveredAt = LocalDateTime.now();
        }
    }

    public boolean isExpired() {
        return bulkNotification != null && bulkNotification.isExpired();
    }
}
