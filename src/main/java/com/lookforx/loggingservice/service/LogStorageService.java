package com.lookforx.loggingservice.service;

import com.lookforx.common.events.LogEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Service for storing log events in MongoDB
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "lookforx.logging.storage.enabled", havingValue = "true", matchIfMissing = true)
public class LogStorageService {

    private final MongoTemplate mongoTemplate;

    // Thread pool for async batch operations
    private final Executor batchExecutor = Executors.newFixedThreadPool(3);

    // Batch size for optimal performance
    private static final int OPTIMAL_BATCH_SIZE = 1000;
    
    private static final String APPLICATION_LOGS_COLLECTION = "application_logs";
    private static final String ERROR_LOGS_COLLECTION = "error_logs";
    private static final String AUDIT_LOGS_COLLECTION = "audit_logs";
    private static final String PERFORMANCE_LOGS_COLLECTION = "performance_logs";
    private static final String SECURITY_LOGS_COLLECTION = "security_logs";
    
    public void storeLogEvent(LogEvent logEvent) {
        try {
            if (logEvent == null) {
                return;
            }

            // Add storage timestamp
            if (logEvent.getCustomFields() == null) {
                logEvent.setCustomFields(new java.util.HashMap<>());
            }
            logEvent.getCustomFields().put("storedAt", LocalDateTime.now());

            mongoTemplate.insert(logEvent, APPLICATION_LOGS_COLLECTION);

            log.debug("Stored log event in MongoDB: {}", logEvent.getMessage());

        } catch (Exception e) {
            log.error("Failed to store log event in MongoDB", e);
            throw new RuntimeException("Failed to store log event", e);
        }
    }

    /**
     * Store multiple log events in batch for better performance
     */
    public void storeLogEvents(List<LogEvent> logEvents) {
        if (logEvents == null || logEvents.isEmpty()) {
            return;
        }

        // Process large batches in chunks for optimal performance
        if (logEvents.size() > OPTIMAL_BATCH_SIZE) {
            storeLogEventsInChunks(logEvents);
        } else {
            storeLogEventsBatch(logEvents, APPLICATION_LOGS_COLLECTION);
        }
    }

    /**
     * Store log events in optimized chunks
     */
    private void storeLogEventsInChunks(List<LogEvent> logEvents) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (int i = 0; i < logEvents.size(); i += OPTIMAL_BATCH_SIZE) {
            int endIndex = Math.min(i + OPTIMAL_BATCH_SIZE, logEvents.size());
            List<LogEvent> chunk = logEvents.subList(i, endIndex);

            CompletableFuture<Void> future = CompletableFuture.runAsync(() ->
                storeLogEventsBatch(chunk, APPLICATION_LOGS_COLLECTION), batchExecutor);
            futures.add(future);
        }

        // Wait for all chunks to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("Stored {} log events in MongoDB using {} chunks",
                logEvents.size(), futures.size());
    }

    /**
     * Optimized batch insert with bulk operations
     */
    private void storeLogEventsBatch(List<LogEvent> logEvents, String collection) {
        try {
            if (logEvents == null || logEvents.isEmpty()) {
                return;
            }

            LocalDateTime storedAt = LocalDateTime.now();

            // Add storage timestamp to all events
            logEvents.forEach(logEvent -> {
                if (logEvent.getCustomFields() == null) {
                    logEvent.setCustomFields(new java.util.HashMap<>());
                }
                logEvent.getCustomFields().put("storedAt", storedAt);
            });

            // Use bulk operations for better performance
            BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collection);
            logEvents.forEach(bulkOps::insert);
            bulkOps.execute();

            log.debug("Stored {} log events in MongoDB batch to collection: {}",
                     logEvents.size(), collection);

        } catch (Exception e) {
            log.error("Failed to store {} log events in MongoDB batch to collection: {}",
                     logEvents.size(), collection, e);

            // Fallback to individual inserts on bulk failure
            fallbackIndividualInserts(logEvents, collection);
        }
    }

    /**
     * Fallback method for individual inserts when bulk operations fail
     */
    private void fallbackIndividualInserts(List<LogEvent> logEvents, String collection) {
        int successCount = 0;
        int failureCount = 0;

        for (LogEvent logEvent : logEvents) {
            try {
                mongoTemplate.insert(logEvent, collection);
                successCount++;
            } catch (Exception e) {
                failureCount++;
                log.warn("Failed to store individual log event: {}", e.getMessage());
            }
        }

        log.info("Fallback insert completed: {} success, {} failures", successCount, failureCount);
    }

    public void storeErrorLogs(List<LogEvent> errorLogs) {
        if (errorLogs == null || errorLogs.isEmpty()) {
            return;
        }

        // Add storage timestamp and priority
        LocalDateTime storedAt = LocalDateTime.now();
        errorLogs.forEach(event -> {
            if (event.getCustomFields() == null) {
                event.setCustomFields(new java.util.HashMap<>());
            }
            event.getCustomFields().put("storedAt", storedAt);
            event.getCustomFields().put("priority", "HIGH");
            event.getCustomFields().put("logType", "ERROR");
        });

        // Store in both collections for redundancy using async operations
        CompletableFuture<Void> errorCollectionFuture = CompletableFuture.runAsync(() ->
            storeLogEventsBatch(errorLogs, ERROR_LOGS_COLLECTION), batchExecutor);

        CompletableFuture<Void> appCollectionFuture = CompletableFuture.runAsync(() ->
            storeLogEventsBatch(errorLogs, APPLICATION_LOGS_COLLECTION), batchExecutor);

        // Wait for both operations to complete
        CompletableFuture.allOf(errorCollectionFuture, appCollectionFuture).join();

        log.info("Stored {} error logs in MongoDB (both collections)", errorLogs.size());
    }
    
    public void storeAuditLogs(List<LogEvent> auditLogs) {
        try {
            if (auditLogs == null || auditLogs.isEmpty()) {
                return;
            }
            
            // Add storage timestamp and audit metadata
            auditLogs.forEach(event -> {
                if (event.getCustomFields() == null) {
                    event.setCustomFields(new java.util.HashMap<>());
                }
                event.getCustomFields().put("storedAt", LocalDateTime.now());
                event.getCustomFields().put("logType", "AUDIT");
                event.getCustomFields().put("retention", "LONG_TERM");
            });
            
            mongoTemplate.insert(auditLogs, AUDIT_LOGS_COLLECTION);
            
            log.debug("Stored {} audit logs in MongoDB", auditLogs.size());
            
        } catch (Exception e) {
            log.error("Failed to store audit logs in MongoDB", e);
            throw new RuntimeException("Failed to store audit logs", e);
        }
    }
    
    public void storePerformanceLogs(List<LogEvent> performanceLogs) {
        try {
            if (performanceLogs == null || performanceLogs.isEmpty()) {
                return;
            }
            
            // Add storage timestamp and performance metadata
            performanceLogs.forEach(event -> {
                if (event.getCustomFields() == null) {
                    event.setCustomFields(new java.util.HashMap<>());
                }
                event.getCustomFields().put("storedAt", LocalDateTime.now());
                event.getCustomFields().put("logType", "PERFORMANCE");
                
                // Add performance analysis flags
                if (event.getDuration() != null) {
                    if (event.getDuration() > 5000) {
                        event.getCustomFields().put("performanceFlag", "SLOW");
                    } else if (event.getDuration() > 1000) {
                        event.getCustomFields().put("performanceFlag", "MEDIUM");
                    } else {
                        event.getCustomFields().put("performanceFlag", "FAST");
                    }
                }
            });
            
            mongoTemplate.insert(performanceLogs, PERFORMANCE_LOGS_COLLECTION);
            
            log.debug("Stored {} performance logs in MongoDB", performanceLogs.size());
            
        } catch (Exception e) {
            log.error("Failed to store performance logs in MongoDB", e);
            throw new RuntimeException("Failed to store performance logs", e);
        }
    }
    
    public void storeSecurityLogs(List<LogEvent> securityLogs) {
        try {
            if (securityLogs == null || securityLogs.isEmpty()) {
                return;
            }
            
            // Add storage timestamp and security metadata
            securityLogs.forEach(event -> {
                if (event.getCustomFields() == null) {
                    event.setCustomFields(new java.util.HashMap<>());
                }
                event.getCustomFields().put("storedAt", LocalDateTime.now());
                event.getCustomFields().put("logType", "SECURITY");
                event.getCustomFields().put("priority", "HIGH");
                event.getCustomFields().put("retention", "LONG_TERM");
                
                // Add security analysis flags
                if (event.getLevel() != null && event.getLevel().equals("ERROR")) {
                    event.getCustomFields().put("securityFlag", "POTENTIAL_THREAT");
                }
            });
            
            mongoTemplate.insert(securityLogs, SECURITY_LOGS_COLLECTION);
            
            log.debug("Stored {} security logs in MongoDB", securityLogs.size());
            
        } catch (Exception e) {
            log.error("Failed to store security logs in MongoDB", e);
            throw new RuntimeException("Failed to store security logs", e);
        }
    }
}
