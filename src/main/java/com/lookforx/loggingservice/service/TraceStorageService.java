package com.lookforx.loggingservice.service;

import com.lookforx.common.events.TraceEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for storing trace events in MongoDB
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "lookforx.logging.storage.enabled", havingValue = "true", matchIfMissing = true)
public class TraceStorageService {
    
    private final MongoTemplate mongoTemplate;
    
    private static final String TRACES_COLLECTION = "application_traces";
    
    public void storeTraceEvents(List<TraceEvent> traceEvents) {
        try {
            if (traceEvents == null || traceEvents.isEmpty()) {
                return;
            }
            
            // Add storage timestamp and trace metadata
            traceEvents.forEach(event -> {
                if (event.getCustomFields() == null) {
                    event.setCustomFields(new java.util.HashMap<>());
                }
                event.getCustomFields().put("storedAt", LocalDateTime.now());
                event.getCustomFields().put("dataType", "TRACE");
                
                // Add trace analysis flags
                if (event.getDuration() != null) {
                    if (event.getDuration() > 10000) {
                        event.getCustomFields().put("durationFlag", "VERY_SLOW");
                    } else if (event.getDuration() > 5000) {
                        event.getCustomFields().put("durationFlag", "SLOW");
                    } else if (event.getDuration() > 1000) {
                        event.getCustomFields().put("durationFlag", "MEDIUM");
                    } else {
                        event.getCustomFields().put("durationFlag", "FAST");
                    }
                }
                
                if ("ERROR".equals(event.getStatus())) {
                    event.getCustomFields().put("errorFlag", "TRUE");
                    event.getCustomFields().put("priority", "HIGH");
                }
                
                // Add span type analysis
                if (event.getSpanKind() != null) {
                    switch (event.getSpanKind()) {
                        case "SERVER":
                            event.getCustomFields().put("spanCategory", "HTTP_REQUEST");
                            break;
                        case "CLIENT":
                            event.getCustomFields().put("spanCategory", "HTTP_CLIENT");
                            break;
                        case "PRODUCER":
                            event.getCustomFields().put("spanCategory", "MESSAGE_PRODUCER");
                            break;
                        case "CONSUMER":
                            event.getCustomFields().put("spanCategory", "MESSAGE_CONSUMER");
                            break;
                        case "INTERNAL":
                            event.getCustomFields().put("spanCategory", "INTERNAL_OPERATION");
                            break;
                        default:
                            event.getCustomFields().put("spanCategory", "UNKNOWN");
                    }
                }
            });
            
            mongoTemplate.insert(traceEvents, TRACES_COLLECTION);
            
            log.debug("Stored {} trace events in MongoDB", traceEvents.size());
            
        } catch (Exception e) {
            log.error("Failed to store trace events in MongoDB", e);
            throw new RuntimeException("Failed to store trace events", e);
        }
    }
}
