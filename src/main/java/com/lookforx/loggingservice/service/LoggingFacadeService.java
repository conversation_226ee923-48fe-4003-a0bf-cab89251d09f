package com.lookforx.loggingservice.service;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.chain.LogProcessingChain;
import com.lookforx.loggingservice.domain.LogEventNotification;
import com.lookforx.loggingservice.domain.LogProcessingResult;
import com.lookforx.loggingservice.domain.LogStorageResult;
import com.lookforx.loggingservice.factory.LogEventFactory;
import com.lookforx.loggingservice.observer.LogEventObserver;
import com.lookforx.loggingservice.strategy.LogStorageStrategy;
import com.lookforx.loggingservice.template.LogProcessingTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Facade service that orchestrates all logging operations.
 * 
 * This service provides a unified interface for log processing,
 * coordinating all design patterns and components.
 * 
 * Following the Facade Pattern from Gang of Four design patterns.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoggingFacadeService {
    
    private final List<LogEventFactory> logEventFactories;
    private final List<LogStorageStrategy> storageStrategies;
    private final List<LogProcessingChain> processingChain;
    private final List<LogEventObserver> observers;
    private final List<LogProcessingTemplate> processingTemplates;
    
    // Async executor for non-blocking operations
    private final Executor asyncExecutor = Executors.newFixedThreadPool(10);
    
    /**
     * Processes a log event from raw data using the complete pipeline.
     */
    public CompletableFuture<LogProcessingResult> processLogEvent(Map<String, Object> rawData) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Step 1: Create log event using factory pattern
                LogEvent logEvent = createLogEvent(rawData);
                if (logEvent == null) {
                    return LogProcessingResult.failure(null, "LoggingFacadeService", 0, 
                                                     "Failed to create log event from raw data");
                }
                
                // Step 2: Notify observers about received event
                notifyObservers(LogEventNotification.received(logEvent, "LoggingFacadeService"));
                
                // Step 3: Process through chain of responsibility
                LogProcessingResult chainResult = processWithChain(logEvent);
                
                // Step 4: If chain processing succeeded, use template method for final processing
                if (chainResult.isSuccess() && chainResult.isAccepted()) {
                    LogProcessingResult templateResult = processWithTemplate(chainResult.getProcessedEvent());
                    
                    // Step 5: Notify observers about final result
                    if (templateResult.isSuccess()) {
                        notifyObservers(LogEventNotification.processed(templateResult.getProcessedEvent(), 
                                                                     templateResult, "LoggingFacadeService"));
                    } else {
                        notifyObservers(LogEventNotification.failed(logEvent, templateResult.getErrorMessage(), 
                                                                   null, "LoggingFacadeService"));
                    }
                    
                    return templateResult;
                } else {
                    // Chain processing failed or filtered the event
                    if (chainResult.isFiltered()) {
                        notifyObservers(LogEventNotification.filtered(logEvent, chainResult, "LoggingFacadeService"));
                    } else {
                        notifyObservers(LogEventNotification.failed(logEvent, chainResult.getErrorMessage(), 
                                                                   null, "LoggingFacadeService"));
                    }
                    
                    return chainResult;
                }
                
            } catch (Exception e) {
                log.error("Error in log processing pipeline", e);
                notifyObservers(LogEventNotification.failed(null, e.getMessage(), e, "LoggingFacadeService"));
                return LogProcessingResult.failure(null, "LoggingFacadeService", 0, e.getMessage());
            }
        }, asyncExecutor);
    }
    
    /**
     * Processes a pre-created log event.
     */
    public CompletableFuture<LogProcessingResult> processLogEvent(LogEvent logEvent) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                notifyObservers(LogEventNotification.received(logEvent, "LoggingFacadeService"));
                
                LogProcessingResult chainResult = processWithChain(logEvent);
                
                if (chainResult.isSuccess() && chainResult.isAccepted()) {
                    LogProcessingResult templateResult = processWithTemplate(chainResult.getProcessedEvent());
                    
                    if (templateResult.isSuccess()) {
                        notifyObservers(LogEventNotification.processed(templateResult.getProcessedEvent(), 
                                                                     templateResult, "LoggingFacadeService"));
                    } else {
                        notifyObservers(LogEventNotification.failed(logEvent, templateResult.getErrorMessage(), 
                                                                   null, "LoggingFacadeService"));
                    }
                    
                    return templateResult;
                } else {
                    if (chainResult.isFiltered()) {
                        notifyObservers(LogEventNotification.filtered(logEvent, chainResult, "LoggingFacadeService"));
                    } else {
                        notifyObservers(LogEventNotification.failed(logEvent, chainResult.getErrorMessage(), 
                                                                   null, "LoggingFacadeService"));
                    }
                    
                    return chainResult;
                }
                
            } catch (Exception e) {
                log.error("Error processing log event: {}", logEvent.getEventId(), e);
                notifyObservers(LogEventNotification.failed(logEvent, e.getMessage(), e, "LoggingFacadeService"));
                return LogProcessingResult.failure(logEvent, "LoggingFacadeService", 0, e.getMessage());
            }
        }, asyncExecutor);
    }
    
    /**
     * Stores a log event directly using storage strategies.
     */
    public CompletableFuture<LogStorageResult> storeLogEvent(LogEvent logEvent) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                LogStorageStrategy strategy = findBestStorageStrategy(logEvent);
                
                if (strategy == null) {
                    LogStorageResult failureResult = LogStorageResult.failure(1, 0, "No strategy", 
                                                                             "No available storage strategy");
                    notifyObservers(LogEventNotification.storageFailed(logEvent, failureResult, "LoggingFacadeService"));
                    return failureResult;
                }
                
                LogStorageResult result = strategy.store(logEvent);
                
                if (result.isSuccess()) {
                    notifyObservers(LogEventNotification.stored(logEvent, result, "LoggingFacadeService"));
                } else {
                    notifyObservers(LogEventNotification.storageFailed(logEvent, result, "LoggingFacadeService"));
                }
                
                return result;
                
            } catch (Exception e) {
                log.error("Error storing log event: {}", logEvent.getEventId(), e);
                LogStorageResult failureResult = LogStorageResult.failure(1, 0, "Error", e.getMessage());
                notifyObservers(LogEventNotification.storageFailed(logEvent, failureResult, "LoggingFacadeService"));
                return failureResult;
            }
        }, asyncExecutor);
    }
    
    // Private helper methods
    
    private LogEvent createLogEvent(Map<String, Object> rawData) {
        LogEventFactory factory = logEventFactories.stream()
                .filter(f -> f.canCreateFromRawData(rawData))
                .min(Comparator.comparingInt(LogEventFactory::getPriority))
                .orElse(null);
        
        if (factory == null) {
            log.warn("No suitable factory found for raw data");
            return null;
        }
        
        try {
            return factory.createFromRawData(rawData);
        } catch (Exception e) {
            log.error("Error creating log event with factory: {}", factory.getClass().getSimpleName(), e);
            return null;
        }
    }
    
    private LogProcessingResult processWithChain(LogEvent logEvent) {
        List<LogProcessingChain> sortedChain = processingChain.stream()
                .filter(handler -> handler.canHandle(logEvent))
                .sorted(Comparator.comparingInt(LogProcessingChain::getPriority))
                .toList();
        
        if (sortedChain.isEmpty()) {
            return LogProcessingResult.success(logEvent, "NoChain", 0);
        }
        
        // Link the chain
        for (int i = 0; i < sortedChain.size() - 1; i++) {
            sortedChain.get(i).setNext(sortedChain.get(i + 1));
        }
        
        return sortedChain.get(0).process(logEvent, sortedChain.get(0).getNext());
    }
    
    private LogProcessingResult processWithTemplate(LogEvent logEvent) {
        LogProcessingTemplate template = processingTemplates.stream()
                .findFirst()
                .orElse(null);
        
        if (template == null) {
            log.warn("No processing template available");
            return LogProcessingResult.success(logEvent, "NoTemplate", 0);
        }
        
        return template.processLogEvent(logEvent);
    }
    
    private LogStorageStrategy findBestStorageStrategy(LogEvent logEvent) {
        return storageStrategies.stream()
                .filter(strategy -> strategy.canHandle(logEvent) && strategy.isAvailable())
                .min(Comparator.comparingInt(LogStorageStrategy::getPriority))
                .orElse(null);
    }
    
    private void notifyObservers(LogEventNotification notification) {
        observers.stream()
                .filter(observer -> observer.isActive() && observer.isInterestedIn(notification))
                .sorted(Comparator.comparingInt(LogEventObserver::getPriority))
                .forEach(observer -> {
                    try {
                        switch (notification.getType()) {
                            case RECEIVED:
                                observer.onLogEventReceived(notification);
                                break;
                            case PROCESSED:
                                observer.onLogEventProcessed(notification);
                                break;
                            case FAILED:
                                observer.onLogEventFailed(notification);
                                break;
                            case FILTERED:
                                observer.onLogEventFiltered(notification);
                                break;
                            case STORED:
                                observer.onLogEventStored(notification);
                                break;
                            case STORAGE_FAILED:
                                observer.onLogEventStorageFailed(notification);
                                break;
                        }
                    } catch (Exception e) {
                        log.error("Error notifying observer: {}", observer.getObserverName(), e);
                    }
                });
    }
}
