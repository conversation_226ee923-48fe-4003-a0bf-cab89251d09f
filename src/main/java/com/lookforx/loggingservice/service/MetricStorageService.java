package com.lookforx.loggingservice.service;

import com.lookforx.common.events.MetricEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for storing metric events in MongoDB
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "lookforx.logging.storage.enabled", havingValue = "true", matchIfMissing = true)
public class MetricStorageService {
    
    private final MongoTemplate mongoTemplate;
    
    private static final String METRICS_COLLECTION = "application_metrics";
    
    public void storeMetricEvents(List<MetricEvent> metricEvents) {
        try {
            if (metricEvents == null || metricEvents.isEmpty()) {
                return;
            }
            
            // Add storage timestamp and metric metadata
            metricEvents.forEach(event -> {
                if (event.getCustomFields() == null) {
                    event.setCustomFields(new java.util.HashMap<>());
                }
                event.getCustomFields().put("storedAt", LocalDateTime.now());
                event.getCustomFields().put("dataType", "METRIC");
                
                // Add metric analysis flags
                if ("COUNTER".equals(event.getMetricType()) && event.getValue() != null) {
                    if (event.getValue() > 1000) {
                        event.getCustomFields().put("volumeFlag", "HIGH");
                    } else if (event.getValue() > 100) {
                        event.getCustomFields().put("volumeFlag", "MEDIUM");
                    } else {
                        event.getCustomFields().put("volumeFlag", "LOW");
                    }
                }
                
                if ("TIMER".equals(event.getMetricType()) && event.getValue() != null) {
                    if (event.getValue() > 5000) {
                        event.getCustomFields().put("performanceFlag", "SLOW");
                    } else if (event.getValue() > 1000) {
                        event.getCustomFields().put("performanceFlag", "MEDIUM");
                    } else {
                        event.getCustomFields().put("performanceFlag", "FAST");
                    }
                }
            });
            
            mongoTemplate.insert(metricEvents, METRICS_COLLECTION);
            
            log.debug("Stored {} metric events in MongoDB", metricEvents.size());
            
        } catch (Exception e) {
            log.error("Failed to store metric events in MongoDB", e);
            throw new RuntimeException("Failed to store metric events", e);
        }
    }
}
