package com.lookforx.loggingservice.chain;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Performance metrics for log processing chain handlers.
 * 
 * Tracks performance and processing statistics for monitoring
 * and optimization of chain handlers.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Data
@Builder(toBuilder = true)
public class LogProcessingMetrics {
    
    /**
     * Total number of events processed by this handler
     */
    private final long totalProcessed;
    
    /**
     * Number of events successfully processed
     */
    private final long successfullyProcessed;
    
    /**
     * Number of events that failed processing
     */
    private final long failedProcessing;
    
    /**
     * Number of events filtered out by this handler
     */
    private final long filteredEvents;
    
    /**
     * Number of events modified by this handler
     */
    private final long modifiedEvents;
    
    /**
     * Average processing time in milliseconds
     */
    private final double averageProcessingTimeMs;
    
    /**
     * Minimum processing time in milliseconds
     */
    private final long minProcessingTimeMs;
    
    /**
     * Maximum processing time in milliseconds
     */
    private final long maxProcessingTimeMs;
    
    /**
     * Current throughput (events per second)
     */
    private final double currentThroughput;
    
    /**
     * Whether this handler is currently available
     */
    private final boolean available;
    
    /**
     * Last successful processing timestamp
     */
    private final LocalDateTime lastSuccessAt;
    
    /**
     * Last failed processing timestamp
     */
    private final LocalDateTime lastFailureAt;
    
    /**
     * Metrics collection start time
     */
    private final LocalDateTime metricsStartTime;
    
    /**
     * Last metrics update time
     */
    private final LocalDateTime lastUpdated;
    
    /**
     * Gets the success rate as a percentage
     */
    public double getSuccessRate() {
        if (totalProcessed == 0) {
            return 1.0;
        }
        return (double) successfullyProcessed / totalProcessed;
    }
    
    /**
     * Gets the failure rate as a percentage
     */
    public double getFailureRate() {
        return 1.0 - getSuccessRate();
    }
    
    /**
     * Gets the filter rate as a percentage
     */
    public double getFilterRate() {
        if (totalProcessed == 0) {
            return 0.0;
        }
        return (double) filteredEvents / totalProcessed;
    }
    
    /**
     * Gets the modification rate as a percentage
     */
    public double getModificationRate() {
        if (totalProcessed == 0) {
            return 0.0;
        }
        return (double) modifiedEvents / totalProcessed;
    }
    
    /**
     * Creates empty metrics for a new handler
     */
    public static LogProcessingMetrics empty() {
        LocalDateTime now = LocalDateTime.now();
        return LogProcessingMetrics.builder()
                .totalProcessed(0)
                .successfullyProcessed(0)
                .failedProcessing(0)
                .filteredEvents(0)
                .modifiedEvents(0)
                .averageProcessingTimeMs(0.0)
                .minProcessingTimeMs(0)
                .maxProcessingTimeMs(0)
                .currentThroughput(0.0)
                .available(true)
                .metricsStartTime(now)
                .lastUpdated(now)
                .build();
    }
}
