package com.lookforx.loggingservice.chain.impl;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.chain.LogProcessingChain;
import com.lookforx.loggingservice.chain.LogProcessingMetrics;
import com.lookforx.loggingservice.domain.LogProcessingResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Chain handler for validating log events.
 * 
 * Validates log events for required fields, data integrity,
 * and business rules before allowing further processing.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Component
public class LogValidationHandler implements LogProcessingChain {
    
    private static final String HANDLER_NAME = "LogValidationHandler";
    private static final int PRIORITY = 1; // High priority - validate early
    
    private LogProcessingChain nextHandler;
    
    // Metrics tracking
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong successfullyProcessed = new AtomicLong(0);
    private final AtomicLong failedProcessing = new AtomicLong(0);
    private final AtomicLong filteredEvents = new AtomicLong(0);
    private volatile LocalDateTime lastSuccessAt;
    private volatile LocalDateTime lastFailureAt;
    private final LocalDateTime metricsStartTime = LocalDateTime.now();
    
    @Override
    public String getHandlerName() {
        return HANDLER_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public boolean canHandle(LogEvent logEvent) {
        // Validation handler can process all log events
        return logEvent != null;
    }
    
    @Override
    public LogProcessingResult process(LogEvent logEvent, LogProcessingChain nextHandler) {
        long startTime = System.currentTimeMillis();
        totalProcessed.incrementAndGet();
        
        try {
            // Perform validation
            List<String> validationErrors = validateLogEvent(logEvent);
            
            if (!validationErrors.isEmpty()) {
                // Log event failed validation
                long processingTime = System.currentTimeMillis() - startTime;
                failedProcessing.incrementAndGet();
                filteredEvents.incrementAndGet();
                lastFailureAt = LocalDateTime.now();
                
                String errorMessage = "Validation failed: " + String.join(", ", validationErrors);
                log.warn("Log event validation failed: {}", errorMessage);
                
                return LogProcessingResult.filtered(logEvent, HANDLER_NAME, processingTime, errorMessage);
            }
            
            // Validation passed - continue to next handler
            long processingTime = System.currentTimeMillis() - startTime;
            successfullyProcessed.incrementAndGet();
            lastSuccessAt = LocalDateTime.now();
            
            log.debug("Log event validation passed for event: {}", logEvent.getEventId());
            
            // Continue to next handler if available
            if (nextHandler != null && nextHandler.canHandle(logEvent)) {
                return nextHandler.process(logEvent, nextHandler.getNext());
            }
            
            return LogProcessingResult.success(logEvent, HANDLER_NAME, processingTime);
            
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            failedProcessing.incrementAndGet();
            lastFailureAt = LocalDateTime.now();
            
            log.error("Error during log event validation", e);
            
            return LogProcessingResult.failure(logEvent, HANDLER_NAME, processingTime, e.getMessage());
        }
    }
    
    @Override
    public void setNext(LogProcessingChain nextHandler) {
        this.nextHandler = nextHandler;
    }
    
    @Override
    public LogProcessingChain getNext() {
        return nextHandler;
    }
    
    @Override
    public boolean shouldContinueChain(LogEvent logEvent, LogProcessingResult processingResult) {
        // Continue only if validation passed
        return processingResult.isSuccess() && processingResult.isAccepted() && !processingResult.isFiltered();
    }
    
    @Override
    public LogProcessingMetrics getMetrics() {
        return LogProcessingMetrics.builder()
                .totalProcessed(totalProcessed.get())
                .successfullyProcessed(successfullyProcessed.get())
                .failedProcessing(failedProcessing.get())
                .filteredEvents(filteredEvents.get())
                .modifiedEvents(0) // Validation doesn't modify events
                .available(true)
                .lastSuccessAt(lastSuccessAt)
                .lastFailureAt(lastFailureAt)
                .metricsStartTime(metricsStartTime)
                .lastUpdated(LocalDateTime.now())
                .build();
    }
    
    @Override
    public void cleanup() {
        log.info("Log validation handler cleanup completed");
    }
    
    /**
     * Validates a log event and returns list of validation errors
     */
    private List<String> validateLogEvent(LogEvent logEvent) {
        List<String> errors = new ArrayList<>();
        
        // Check required fields
        if (logEvent.getEventId() == null || logEvent.getEventId().trim().isEmpty()) {
            errors.add("Event ID is required");
        }
        
        if (logEvent.getMessage() == null || logEvent.getMessage().trim().isEmpty()) {
            errors.add("Message is required");
        }
        
        if (logEvent.getTimestamp() == null) {
            errors.add("Timestamp is required");
        }
        
        // Validate log level
        if (logEvent.getLevel() != null && !isValidLogLevel(logEvent.getLevel())) {
            errors.add("Invalid log level: " + logEvent.getLevel());
        }
        
        // Validate message length
        if (logEvent.getMessage() != null && logEvent.getMessage().length() > 10000) {
            errors.add("Message too long (max 10000 characters)");
        }
        
        // Validate timestamp is not in the future
        if (logEvent.getTimestamp() != null && logEvent.getTimestamp().isAfter(LocalDateTime.now().plusMinutes(5))) {
            errors.add("Timestamp cannot be more than 5 minutes in the future");
        }
        
        // Validate timestamp is not too old (more than 30 days)
        if (logEvent.getTimestamp() != null && logEvent.getTimestamp().isBefore(LocalDateTime.now().minusDays(30))) {
            errors.add("Timestamp cannot be more than 30 days old");
        }
        
        // Validate service name if present
        if (logEvent.getServiceName() != null && logEvent.getServiceName().trim().isEmpty()) {
            errors.add("Service name cannot be empty if provided");
        }
        
        // Validate custom fields size
        if (logEvent.getMetadata() != null && logEvent.getMetadata().size() > 50) {
            errors.add("Too many custom fields (max 50)");
        }
        
        return errors;
    }
    
    /**
     * Checks if the log level is valid
     */
    private boolean isValidLogLevel(String level) {
        if (level == null) {
            return true; // null is acceptable
        }
        
        String upperLevel = level.toUpperCase();
        return upperLevel.equals("TRACE") || 
               upperLevel.equals("DEBUG") || 
               upperLevel.equals("INFO") || 
               upperLevel.equals("WARN") || 
               upperLevel.equals("ERROR") || 
               upperLevel.equals("FATAL") ||
               upperLevel.equals("AUDIT") ||
               upperLevel.equals("SECURITY") ||
               upperLevel.equals("PERFORMANCE");
    }
}
