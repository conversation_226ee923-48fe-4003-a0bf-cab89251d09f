package com.lookforx.loggingservice.chain.impl;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.chain.LogProcessingChain;
import com.lookforx.loggingservice.chain.LogProcessingMetrics;
import com.lookforx.loggingservice.domain.LogProcessingResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Chain handler for enriching log events with additional metadata.
 * 
 * Adds contextual information, system metadata, and derived fields
 * to log events to improve their value for analysis and monitoring.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Component
public class LogEnrichmentHandler implements LogProcessingChain {
    
    private static final String HANDLER_NAME = "LogEnrichmentHandler";
    private static final int PRIORITY = 2; // After validation
    
    @Value("${spring.application.name:logging-service}")
    private String applicationName;
    
    @Value("${spring.profiles.active:dev}")
    private String activeProfile;
    
    private LogProcessingChain nextHandler;
    
    // Metrics tracking
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong successfullyProcessed = new AtomicLong(0);
    private final AtomicLong failedProcessing = new AtomicLong(0);
    private final AtomicLong modifiedEvents = new AtomicLong(0);
    private volatile LocalDateTime lastSuccessAt;
    private volatile LocalDateTime lastFailureAt;
    private final LocalDateTime metricsStartTime = LocalDateTime.now();
    
    // Cache for expensive operations
    private volatile String cachedHostname;
    private volatile String cachedIpAddress;
    
    @Override
    public String getHandlerName() {
        return HANDLER_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public boolean canHandle(LogEvent logEvent) {
        // Enrichment handler can process all valid log events
        return logEvent != null && logEvent.getEventId() != null;
    }
    
    @Override
    public LogProcessingResult process(LogEvent logEvent, LogProcessingChain nextHandler) {
        long startTime = System.currentTimeMillis();
        totalProcessed.incrementAndGet();
        
        try {
            // Create enriched copy of the log event
            LogEvent enrichedEvent = enrichLogEvent(logEvent);
            
            // Track modifications
            List<String> modifications = getModifications(logEvent, enrichedEvent);
            boolean wasModified = !modifications.isEmpty();
            
            if (wasModified) {
                modifiedEvents.incrementAndGet();
            }
            
            long processingTime = System.currentTimeMillis() - startTime;
            successfullyProcessed.incrementAndGet();
            lastSuccessAt = LocalDateTime.now();
            
            log.debug("Log event enriched with {} modifications for event: {}", 
                     modifications.size(), logEvent.getEventId());
            
            // Continue to next handler if available
            if (nextHandler != null && nextHandler.canHandle(enrichedEvent)) {
                return nextHandler.process(enrichedEvent, nextHandler.getNext());
            }
            
            // Return result based on whether modifications were made
            if (wasModified) {
                return LogProcessingResult.successWithModifications(enrichedEvent, HANDLER_NAME, 
                                                                  processingTime, modifications);
            } else {
                return LogProcessingResult.success(enrichedEvent, HANDLER_NAME, processingTime);
            }
            
        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            failedProcessing.incrementAndGet();
            lastFailureAt = LocalDateTime.now();
            
            log.error("Error during log event enrichment", e);
            
            return LogProcessingResult.failure(logEvent, HANDLER_NAME, processingTime, e.getMessage());
        }
    }
    
    @Override
    public void setNext(LogProcessingChain nextHandler) {
        this.nextHandler = nextHandler;
    }
    
    @Override
    public LogProcessingChain getNext() {
        return nextHandler;
    }
    
    @Override
    public boolean shouldContinueChain(LogEvent logEvent, LogProcessingResult processingResult) {
        // Continue if enrichment was successful
        return processingResult.isSuccess() && processingResult.isAccepted();
    }
    
    @Override
    public LogProcessingMetrics getMetrics() {
        return LogProcessingMetrics.builder()
                .totalProcessed(totalProcessed.get())
                .successfullyProcessed(successfullyProcessed.get())
                .failedProcessing(failedProcessing.get())
                .filteredEvents(0) // Enrichment doesn't filter events
                .modifiedEvents(modifiedEvents.get())
                .available(true)
                .lastSuccessAt(lastSuccessAt)
                .lastFailureAt(lastFailureAt)
                .metricsStartTime(metricsStartTime)
                .lastUpdated(LocalDateTime.now())
                .build();
    }
    
    @Override
    public void cleanup() {
        log.info("Log enrichment handler cleanup completed");
    }
    
    /**
     * Enriches a log event with additional metadata
     */
    private LogEvent enrichLogEvent(LogEvent logEvent) {
        // Create enriched copy using builder
        LogEvent.LogEventBuilder<?, ?> builder = LogEvent.builder()
                .eventId(logEvent.getEventId())
                .timestamp(logEvent.getTimestamp())
                .serviceName(logEvent.getServiceName())
                .version(logEvent.getVersion())
                .userId(logEvent.getUserId())
                .correlationId(logEvent.getCorrelationId())
                .metadata(logEvent.getMetadata())
                .logServiceName(logEvent.getLogServiceName())
                .logEnvironment(logEvent.getLogEnvironment())
                .logVersion(logEvent.getLogVersion())
                .level(logEvent.getLevel())
                .logger(logEvent.getLogger())
                .message(logEvent.getMessage())
                .thread(logEvent.getThread())
                .traceId(logEvent.getTraceId())
                .spanId(logEvent.getSpanId())
                .sessionId(logEvent.getSessionId())
                .requestId(logEvent.getRequestId())
                .className(logEvent.getClassName())
                .methodName(logEvent.getMethodName())
                .lineNumber(logEvent.getLineNumber())
                .exception(logEvent.getException())
                .stackTrace(logEvent.getStackTrace())
                .host(logEvent.getHost())
                .ip(logEvent.getIp())
                .service(logEvent.getService())
                .userAgent(logEvent.getUserAgent())
                .requestUri(logEvent.getRequestUri())
                .httpMethod(logEvent.getHttpMethod())
                .httpStatus(logEvent.getHttpStatus())
                .duration(logEvent.getDuration())
                .mdc(logEvent.getMdc());

        // Enrich basic fields
        enrichBasicFields(builder, logEvent);

        // Enrich system information
        enrichSystemInformation(builder, logEvent);

        // Enrich custom fields
        enrichCustomFields(builder, logEvent);

        // Enrich derived fields
        enrichDerivedFields(builder, logEvent);

        return builder.build();
    }
    
    /**
     * Enriches basic log event fields
     */
    private void enrichBasicFields(LogEvent.LogEventBuilder<?, ?> builder, LogEvent logEvent) {
        // Set service name if not present
        if (logEvent.getServiceName() == null || logEvent.getServiceName().trim().isEmpty()) {
            builder.serviceName(applicationName);
        }

        // Set environment if not present
        if (logEvent.getLogEnvironment() == null || logEvent.getLogEnvironment().trim().isEmpty()) {
            builder.logEnvironment(activeProfile);
        }

        // Set version if not present
        if (logEvent.getVersion() == null || logEvent.getVersion().trim().isEmpty()) {
            builder.version("1.0");
        }

        // Ensure timestamp is set
        if (logEvent.getTimestamp() == null) {
            builder.timestamp(LocalDateTime.now());
        }
    }
    
    /**
     * Enriches system information
     */
    private void enrichSystemInformation(LogEvent.LogEventBuilder<?, ?> builder, LogEvent logEvent) {
        // Add hostname if not present
        if (logEvent.getHost() == null || logEvent.getHost().trim().isEmpty()) {
            builder.host(getHostname());
        }

        // Add IP address if not present
        if (logEvent.getIp() == null || logEvent.getIp().trim().isEmpty()) {
            builder.ip(getIpAddress());
        }

        // Add thread information if not present
        if (logEvent.getThread() == null || logEvent.getThread().trim().isEmpty()) {
            builder.thread(Thread.currentThread().getName());
        }
    }
    
    /**
     * Enriches custom fields with additional metadata
     */
    private void enrichCustomFields(LogEvent.LogEventBuilder<?, ?> builder, LogEvent logEvent) {
        Map<String, Object> customFields = logEvent.getMetadata();
        if (customFields == null) {
            customFields = new HashMap<>();
        } else {
            customFields = new HashMap<>(customFields); // Create mutable copy
        }

        // Add enrichment metadata
        customFields.put("enrichedAt", LocalDateTime.now());
        customFields.put("enrichedBy", HANDLER_NAME);
        customFields.put("processingNode", getHostname());

        // Add log event classification
        customFields.put("logCategory", classifyLogEvent(logEvent));
        customFields.put("logPriority", calculateLogPriority(logEvent));

        // Add performance metadata
        customFields.put("enrichmentVersion", "1.0");

        builder.metadata(customFields);
    }
    
    /**
     * Enriches derived fields based on existing data
     */
    private void enrichDerivedFields(LogEvent.LogEventBuilder<?, ?> builder, LogEvent logEvent) {
        // Derive logger category from logger name
        if (logEvent.getLogger() != null) {
            String loggerCategory = deriveLoggerCategory(logEvent.getLogger());
            Map<String, Object> customFields = new HashMap<>(logEvent.getMetadata() != null ?
                                                            logEvent.getMetadata() : new HashMap<>());
            customFields.put("loggerCategory", loggerCategory);
            builder.metadata(customFields);
        }
    }
    
    /**
     * Gets modifications made during enrichment
     */
    private List<String> getModifications(LogEvent original, LogEvent enriched) {
        List<String> modifications = new ArrayList<>();
        
        if (!equals(original.getServiceName(), enriched.getServiceName())) {
            modifications.add("serviceName");
        }
        if (!equals(original.getLogEnvironment(), enriched.getLogEnvironment())) {
            modifications.add("environment");
        }
        if (!equals(original.getHost(), enriched.getHost())) {
            modifications.add("host");
        }
        if (!equals(original.getIp(), enriched.getIp())) {
            modifications.add("ip");
        }
        if (!equals(original.getThread(), enriched.getThread())) {
            modifications.add("thread");
        }
        
        // Check custom fields
        int originalCustomFieldsSize = original.getMetadata() != null ? original.getMetadata().size() : 0;
        int enrichedCustomFieldsSize = enriched.getMetadata() != null ? enriched.getMetadata().size() : 0;
        if (enrichedCustomFieldsSize > originalCustomFieldsSize) {
            modifications.add("customFields");
        }
        
        return modifications;
    }
    
    /**
     * Classifies log event based on content
     */
    private String classifyLogEvent(LogEvent logEvent) {
        if (logEvent.getLevel() != null) {
            switch (logEvent.getLevel().toUpperCase()) {
                case "ERROR":
                case "FATAL":
                    return "ERROR";
                case "AUDIT":
                    return "AUDIT";
                case "SECURITY":
                    return "SECURITY";
                case "PERFORMANCE":
                    return "PERFORMANCE";
                default:
                    return "APPLICATION";
            }
        }
        return "APPLICATION";
    }
    
    /**
     * Calculates log priority based on level and content
     */
    private int calculateLogPriority(LogEvent logEvent) {
        if (logEvent.getLevel() != null) {
            switch (logEvent.getLevel().toUpperCase()) {
                case "FATAL":
                    return 1;
                case "ERROR":
                    return 2;
                case "WARN":
                    return 3;
                case "INFO":
                    return 4;
                case "DEBUG":
                    return 5;
                case "TRACE":
                    return 6;
                default:
                    return 4;
            }
        }
        return 4;
    }
    
    /**
     * Derives logger category from logger name
     */
    private String deriveLoggerCategory(String loggerName) {
        if (loggerName == null) {
            return "unknown";
        }
        
        String lowerName = loggerName.toLowerCase();
        if (lowerName.contains("controller")) {
            return "web";
        }
        if (lowerName.contains("service")) {
            return "business";
        }
        if (lowerName.contains("repository") || lowerName.contains("dao")) {
            return "data";
        }
        if (lowerName.contains("security")) {
            return "security";
        }
        if (lowerName.contains("config")) {
            return "configuration";
        }
        
        return "application";
    }
    
    /**
     * Gets cached hostname
     */
    private String getHostname() {
        if (cachedHostname == null) {
            try {
                cachedHostname = java.net.InetAddress.getLocalHost().getHostName();
            } catch (Exception e) {
                cachedHostname = "unknown";
            }
        }
        return cachedHostname;
    }
    
    /**
     * Gets cached IP address
     */
    private String getIpAddress() {
        if (cachedIpAddress == null) {
            try {
                cachedIpAddress = java.net.InetAddress.getLocalHost().getHostAddress();
            } catch (Exception e) {
                cachedIpAddress = "unknown";
            }
        }
        return cachedIpAddress;
    }
    
    /**
     * Null-safe equals comparison
     */
    private boolean equals(Object a, Object b) {
        return (a == null && b == null) || (a != null && a.equals(b));
    }
}
