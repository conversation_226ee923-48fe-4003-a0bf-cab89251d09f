package com.lookforx.loggingservice.chain;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.domain.LogProcessingResult;

/**
 * Chain of Responsibility interface for log processing.
 * 
 * Defines the contract for processing log events through a chain
 * of handlers, where each handler can process, modify, or filter
 * the log event before passing it to the next handler.
 * 
 * Following the Chain of Responsibility pattern from Gang of Four design patterns.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
public interface LogProcessingChain {
    
    /**
     * Gets the name of this processing chain handler.
     * 
     * @return the handler name
     */
    String getHandlerName();
    
    /**
     * Gets the priority of this handler in the chain.
     * Lower numbers indicate higher priority (executed first).
     * 
     * @return the priority value (0 = highest priority)
     */
    int getPriority();
    
    /**
     * Checks if this handler can process the given log event.
     * 
     * @param logEvent the log event to check
     * @return true if this handler can process the event, false otherwise
     */
    boolean canHandle(LogEvent logEvent);
    
    /**
     * Processes the log event and optionally passes it to the next handler.
     * 
     * @param logEvent the log event to process
     * @param nextHandler the next handler in the chain (can be null)
     * @return the processing result
     */
    LogProcessingResult process(LogEvent logEvent, LogProcessingChain nextHandler);
    
    /**
     * Sets the next handler in the chain.
     * 
     * @param nextHandler the next handler
     */
    void setNext(LogProcessingChain nextHandler);
    
    /**
     * Gets the next handler in the chain.
     * 
     * @return the next handler, or null if this is the last handler
     */
    LogProcessingChain getNext();
    
    /**
     * Checks if this handler should continue processing after handling the event.
     * Some handlers might want to stop the chain (e.g., filters that reject events).
     * 
     * @param logEvent the log event being processed
     * @param processingResult the result of processing
     * @return true to continue the chain, false to stop
     */
    boolean shouldContinueChain(LogEvent logEvent, LogProcessingResult processingResult);
    
    /**
     * Gets performance metrics for this handler.
     * 
     * @return processing metrics
     */
    LogProcessingMetrics getMetrics();
    
    /**
     * Performs cleanup operations for this handler.
     */
    void cleanup();
}
