package com.lookforx.loggingservice.consumer;

import com.lookforx.common.events.MetricEvent;
import com.lookforx.common.kafka.LoggingKafkaTopics;
import com.lookforx.loggingservice.service.MetricStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Kafka consumer for metric events
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "lookforx.logging.consumer.enabled", havingValue = "true", matchIfMissing = false)
public class MetricEventConsumer {
    
    private final MetricStorageService metricStorageService;
    
    @KafkaListener(
        topics = LoggingKafkaTopics.APPLICATION_METRICS,
        containerFactory = "metricEventListenerContainerFactory",
        groupId = "${lookforx.logging.kafka.consumer.group-id:logging-consumer-group}-metrics"
    )
    public void consumeMetricEvents(@Payload List<MetricEvent> metricEvents,
                                   @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                   @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                   @Header(KafkaHeaders.OFFSET) long offset) {
        try {
            log.debug("Received {} metric events from topic: {}, partition: {}, offset: {}", 
                     metricEvents.size(), topic, partition, offset);
            
            // Store metric events in MongoDB
            metricStorageService.storeMetricEvents(metricEvents);
            
            // Acknowledge the batch

            
            log.debug("Successfully processed {} metric events", metricEvents.size());
            
        } catch (Exception e) {
            log.error("Failed to process metric events from topic: {}, partition: {}, offset: {}", 
                     topic, partition, offset, e);
        }
    }
}
