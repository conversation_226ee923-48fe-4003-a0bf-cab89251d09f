package com.lookforx.loggingservice.consumer;

import com.lookforx.common.events.TraceEvent;
import com.lookforx.common.kafka.LoggingKafkaTopics;
import com.lookforx.loggingservice.service.TraceStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Kafka consumer for trace events
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "lookforx.logging.consumer.enabled", havingValue = "true", matchIfMissing = false)
public class TraceEventConsumer {
    
    private final TraceStorageService traceStorageService;
    
    @KafkaListener(
        topics = LoggingKafkaTopics.APPLICATION_TRACES,
        containerFactory = "traceEventListenerContainerFactory",
        groupId = "${lookforx.logging.kafka.consumer.group-id:logging-consumer-group}-traces"
    )
    public void consumeTraceEvents(@Payload List<TraceEvent> traceEvents,
                                  @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                  @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                  @Header(KafkaHeaders.OFFSET) long offset) {
        try {
            log.debug("Received {} trace events from topic: {}, partition: {}, offset: {}", 
                     traceEvents.size(), topic, partition, offset);
            
            // Store trace events in MongoDB
            traceStorageService.storeTraceEvents(traceEvents);

            
            log.debug("Successfully processed {} trace events", traceEvents.size());
            
        } catch (Exception e) {
            log.error("Failed to process trace events from topic: {}, partition: {}, offset: {}", 
                     topic, partition, offset, e);

        }
    }
}
