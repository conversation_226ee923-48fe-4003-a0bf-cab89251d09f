package com.lookforx.loggingservice.consumer;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.common.events.LogEvent;
import com.lookforx.common.kafka.LoggingKafkaTopics;
import com.lookforx.loggingservice.service.LogStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import java.util.List;

/**
 * Kafka consumer for log events
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "lookforx.logging.consumer.enabled", havingValue = "true", matchIfMissing = false)
public class LogEventConsumer {

    private final LogStorageService logStorageService;
    private final ObjectMapper objectMapper;

    /**
     * Consume raw string log events from application-logs topic
     * This handles the case where auth service sends logs as strings
     */
    @KafkaListener(
        topics = LoggingKafkaTopics.APPLICATION_LOGS,
        containerFactory = "stringListenerContainerFactory",
        groupId = "${lookforx.logging.kafka.consumer.group-id:logging-consumer-group}-strings"
    )
    public void consumeRawLogEvents(@Payload List<String> rawLogEvents,
                                   @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                   @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                   @Header(KafkaHeaders.OFFSET) long offset) {
        try {
            log.info("Received {} raw log events from topic: {}, partition: {}, offset: {}",
                     rawLogEvents.size(), topic, partition, offset);

            // Parse string messages to LogEvent objects
            List<LogEvent> logEvents = new ArrayList<>();
            for (String rawLog : rawLogEvents) {
                try {
                    // Try to parse as LogEvent first
                    LogEvent logEvent = objectMapper.readValue(rawLog, LogEvent.class);
                    logEvents.add(logEvent);
                } catch (Exception e) {
                    // If parsing fails, create a simple LogEvent from the raw string
                    LogEvent logEvent = LogEvent.builder()
                        .message(rawLog)
                        .level("INFO")
                        .timestamp(LocalDateTime.now())
                        .service("unknown")
                        .build();
                    logEvents.add(logEvent);
                    log.debug("Failed to parse raw log as LogEvent, created simple LogEvent: {}", e.getMessage());
                }
            }

            // Store log events in MongoDB (batch processing)
            logStorageService.storeLogEvents(logEvents);

            log.info("Successfully processed {} raw log events", rawLogEvents.size());

        } catch (Exception e) {
            log.error("Failed to process raw log events from topic: {}, partition: {}, offset: {}",
                     topic, partition, offset, e);
        }
    }

    @KafkaListener(
        topics = LoggingKafkaTopics.APPLICATION_LOGS,
        containerFactory = "logEventListenerContainerFactory",
        groupId = "${lookforx.logging.kafka.consumer.group-id:logging-consumer-group}-logs"
    )
    public void consumeLogEvents(@Payload List<LogEvent> logEvents,
                                @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                @Header(KafkaHeaders.OFFSET) long offset) {
        try {
            log.info("Received {} log events from topic: {}, partition: {}, offset: {}",
                     logEvents.size(), topic, partition, offset);

            // Store log events in MongoDB (batch processing)
            logStorageService.storeLogEvents(logEvents);

            log.info("Successfully processed {} log events", logEvents.size());

        } catch (Exception e) {
            log.error("Failed to process {} log events from topic: {}, partition: {}, offset: {}",
                     logEvents.size(), topic, partition, offset, e);

            // In production, you might want to send to DLQ instead of acknowledging
            // For now, we'll acknowledge to prevent infinite retries
        }
    }
    
    @KafkaListener(
        topics = LoggingKafkaTopics.ERROR_LOGS,
        containerFactory = "logEventListenerContainerFactory",
        groupId = "${lookforx.logging.kafka.consumer.group-id:logging-consumer-group}-errors"
    )
    public void consumeErrorLogs(@Payload List<LogEvent> errorLogs,
                                @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                @Header(KafkaHeaders.OFFSET) long offset) {
        try {
            log.debug("Received {} error logs from topic: {}, partition: {}, offset: {}", 
                     errorLogs.size(), topic, partition, offset);
            
            // Store error logs with higher priority
            logStorageService.storeErrorLogs(errorLogs);
            
            // Acknowledge the batch

            
            log.debug("Successfully processed {} error logs", errorLogs.size());
            
        } catch (Exception e) {
            log.error("Failed to process error logs from topic: {}, partition: {}, offset: {}", 
                     topic, partition, offset, e);
            
            
        }
    }
    
    @KafkaListener(
        topics = LoggingKafkaTopics.AUDIT_LOGS,
        containerFactory = "logEventListenerContainerFactory",
        groupId = "${lookforx.logging.kafka.consumer.group-id:logging-consumer-group}-audit"
    )
    public void consumeAuditLogs(@Payload List<LogEvent> auditLogs,
                                @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                @Header(KafkaHeaders.OFFSET) long offset) {
        try {
            log.debug("Received {} audit logs from topic: {}, partition: {}, offset: {}", 
                     auditLogs.size(), topic, partition, offset);
            
            // Store audit logs with special handling
            logStorageService.storeAuditLogs(auditLogs);

            log.debug("Successfully processed {} audit logs", auditLogs.size());
            
        } catch (Exception e) {
            log.error("Failed to process audit logs from topic: {}, partition: {}, offset: {}",
                     topic, partition, offset, e);
        }
    }
    
    @KafkaListener(
        topics = LoggingKafkaTopics.PERFORMANCE_LOGS,
        containerFactory = "logEventListenerContainerFactory",
        groupId = "${lookforx.logging.kafka.consumer.group-id:logging-consumer-group}-performance"
    )
    public void consumePerformanceLogs(@Payload List<LogEvent> performanceLogs,
                                      @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                      @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                      @Header(KafkaHeaders.OFFSET) long offset) {
        try {
            log.debug("Received {} performance logs from topic: {}, partition: {}, offset: {}", 
                     performanceLogs.size(), topic, partition, offset);
            
            // Store performance logs
            logStorageService.storePerformanceLogs(performanceLogs);

            log.debug("Successfully processed {} performance logs", performanceLogs.size());
            
        } catch (Exception e) {
            log.error("Failed to process performance logs from topic: {}, partition: {}, offset: {}",
                     topic, partition, offset, e);
        }
    }
    
    @KafkaListener(
        topics = LoggingKafkaTopics.SECURITY_LOGS,
        containerFactory = "logEventListenerContainerFactory",
        groupId = "${lookforx.logging.kafka.consumer.group-id:logging-consumer-group}-security"
    )
    public void consumeSecurityLogs(@Payload List<LogEvent> securityLogs,
                                   @Header(KafkaHeaders.RECEIVED_TOPIC) String topic,
                                   @Header(KafkaHeaders.RECEIVED_PARTITION) int partition,
                                   @Header(KafkaHeaders.OFFSET) long offset) {
        try {
            log.debug("Received {} security logs from topic: {}, partition: {}, offset: {}", 
                     securityLogs.size(), topic, partition, offset);
            
            // Store security logs with special handling
            logStorageService.storeSecurityLogs(securityLogs);

            log.debug("Successfully processed {} security logs", securityLogs.size());
            
        } catch (Exception e) {
            log.error("Failed to process security logs from topic: {}, partition: {}, offset: {}",
                     topic, partition, offset, e);
        }
    }
}
