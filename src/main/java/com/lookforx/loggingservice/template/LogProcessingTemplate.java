package com.lookforx.loggingservice.template;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.domain.LogProcessingResult;
import com.lookforx.loggingservice.domain.LogStorageResult;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Abstract template for log processing operations.
 * 
 * Defines the skeleton of log processing algorithm with
 * customizable steps for different log types and requirements.
 * 
 * Following the Template Method pattern from Gang of Four design patterns.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
public abstract class LogProcessingTemplate {
    
    /**
     * Template method that defines the log processing algorithm.
     * This method should not be overridden by subclasses.
     * 
     * @param logEvent the log event to process
     * @return the processing result
     */
    public final LogProcessingResult processLogEvent(LogEvent logEvent) {
        long startTime = System.currentTimeMillis();
        List<String> modifications = new ArrayList<>();
        
        try {
            log.debug("Starting log processing for event: {}", logEvent.getEventId());
            
            // Step 1: Pre-processing validation
            if (!preProcessValidation(logEvent)) {
                return createFailureResult(logEvent, startTime, "Pre-processing validation failed");
            }
            
            // Step 2: Initialize processing context
            LogProcessingContext context = initializeContext(logEvent);
            
            // Step 3: Validate log event
            ValidationResult validationResult = validateLogEvent(logEvent, context);
            if (!validationResult.isValid()) {
                return createValidationFailureResult(logEvent, startTime, validationResult);
            }
            
            // Step 4: Enrich log event
            LogEvent enrichedEvent = enrichLogEvent(logEvent, context);
            if (enrichedEvent != logEvent) {
                modifications.add("enrichment");
            }
            
            // Step 5: Transform log event (if needed)
            LogEvent transformedEvent = transformLogEvent(enrichedEvent, context);
            if (transformedEvent != enrichedEvent) {
                modifications.add("transformation");
            }
            
            // Step 6: Apply business rules
            BusinessRuleResult ruleResult = applyBusinessRules(transformedEvent, context);
            if (!ruleResult.isAllowed()) {
                return createFilteredResult(transformedEvent, startTime, ruleResult.getReason());
            }
            
            // Step 7: Prepare for storage
            LogEvent finalEvent = prepareForStorage(transformedEvent, context);
            if (finalEvent != transformedEvent) {
                modifications.add("storage-preparation");
            }
            
            // Step 8: Store log event
            LogStorageResult storageResult = storeLogEvent(finalEvent, context);
            
            // Step 9: Post-processing
            postProcessing(finalEvent, storageResult, context);
            
            // Step 10: Create result
            long processingTime = System.currentTimeMillis() - startTime;
            
            if (storageResult.isSuccess()) {
                log.debug("Log processing completed successfully for event: {}", logEvent.getEventId());
                
                if (!modifications.isEmpty()) {
                    return LogProcessingResult.successWithModifications(finalEvent, getProcessorName(), 
                                                                      processingTime, modifications);
                } else {
                    return LogProcessingResult.success(finalEvent, getProcessorName(), processingTime);
                }
            } else {
                return createStorageFailureResult(finalEvent, startTime, storageResult);
            }
            
        } catch (Exception e) {
            log.error("Error during log processing for event: {}", logEvent.getEventId(), e);
            return createFailureResult(logEvent, startTime, "Processing error: " + e.getMessage());
        }
    }
    
    // Abstract methods that subclasses must implement
    
    /**
     * Gets the name of this processor.
     */
    protected abstract String getProcessorName();
    
    /**
     * Initializes the processing context for the log event.
     */
    protected abstract LogProcessingContext initializeContext(LogEvent logEvent);
    
    /**
     * Validates the log event according to processor-specific rules.
     */
    protected abstract ValidationResult validateLogEvent(LogEvent logEvent, LogProcessingContext context);
    
    /**
     * Enriches the log event with additional information.
     */
    protected abstract LogEvent enrichLogEvent(LogEvent logEvent, LogProcessingContext context);
    
    /**
     * Applies business rules to determine if the event should be processed.
     */
    protected abstract BusinessRuleResult applyBusinessRules(LogEvent logEvent, LogProcessingContext context);
    
    /**
     * Stores the log event using the appropriate storage strategy.
     */
    protected abstract LogStorageResult storeLogEvent(LogEvent logEvent, LogProcessingContext context);
    
    // Hook methods that subclasses can override
    
    /**
     * Pre-processing validation hook.
     * Default implementation performs basic null checks.
     */
    protected boolean preProcessValidation(LogEvent logEvent) {
        return logEvent != null && logEvent.getEventId() != null;
    }
    
    /**
     * Transform log event hook.
     * Default implementation returns the event unchanged.
     */
    protected LogEvent transformLogEvent(LogEvent logEvent, LogProcessingContext context) {
        return logEvent;
    }
    
    /**
     * Prepare for storage hook.
     * Default implementation returns the event unchanged.
     */
    protected LogEvent prepareForStorage(LogEvent logEvent, LogProcessingContext context) {
        return logEvent;
    }
    
    /**
     * Post-processing hook.
     * Default implementation does nothing.
     */
    protected void postProcessing(LogEvent logEvent, LogStorageResult storageResult, LogProcessingContext context) {
        // Default: no post-processing
    }
    
    // Helper methods
    
    private LogProcessingResult createFailureResult(LogEvent logEvent, long startTime, String errorMessage) {
        long processingTime = System.currentTimeMillis() - startTime;
        return LogProcessingResult.failure(logEvent, getProcessorName(), processingTime, errorMessage);
    }
    
    private LogProcessingResult createValidationFailureResult(LogEvent logEvent, long startTime, 
                                                            ValidationResult validationResult) {
        long processingTime = System.currentTimeMillis() - startTime;
        return LogProcessingResult.filtered(logEvent, getProcessorName(), processingTime, 
                                          "Validation failed: " + validationResult.getErrorMessage());
    }
    
    private LogProcessingResult createFilteredResult(LogEvent logEvent, long startTime, String reason) {
        long processingTime = System.currentTimeMillis() - startTime;
        return LogProcessingResult.filtered(logEvent, getProcessorName(), processingTime, reason);
    }
    
    private LogProcessingResult createStorageFailureResult(LogEvent logEvent, long startTime, 
                                                         LogStorageResult storageResult) {
        long processingTime = System.currentTimeMillis() - startTime;
        return LogProcessingResult.failure(logEvent, getProcessorName(), processingTime, 
                                         "Storage failed: " + storageResult.getErrorMessage());
    }
    
    // Inner classes for processing context and results
    
    protected static class LogProcessingContext {
        private final LocalDateTime processingStartTime;
        private final String processorName;
        private final java.util.Map<String, Object> contextData;
        
        public LogProcessingContext(String processorName) {
            this.processingStartTime = LocalDateTime.now();
            this.processorName = processorName;
            this.contextData = new java.util.HashMap<>();
        }
        
        public LocalDateTime getProcessingStartTime() {
            return processingStartTime;
        }
        
        public String getProcessorName() {
            return processorName;
        }
        
        public java.util.Map<String, Object> getContextData() {
            return contextData;
        }
        
        public void putContextData(String key, Object value) {
            contextData.put(key, value);
        }
        
        public Object getContextData(String key) {
            return contextData.get(key);
        }
    }
    
    protected static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        
        public ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        public static ValidationResult valid() {
            return new ValidationResult(true, null);
        }
        
        public static ValidationResult invalid(String errorMessage) {
            return new ValidationResult(false, errorMessage);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
    }
    
    protected static class BusinessRuleResult {
        private final boolean allowed;
        private final String reason;
        
        public BusinessRuleResult(boolean allowed, String reason) {
            this.allowed = allowed;
            this.reason = reason;
        }
        
        public static BusinessRuleResult allow() {
            return new BusinessRuleResult(true, null);
        }
        
        public static BusinessRuleResult deny(String reason) {
            return new BusinessRuleResult(false, reason);
        }
        
        public boolean isAllowed() {
            return allowed;
        }
        
        public String getReason() {
            return reason;
        }
    }
}
