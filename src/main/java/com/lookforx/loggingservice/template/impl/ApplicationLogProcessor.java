package com.lookforx.loggingservice.template.impl;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.domain.LogStorageResult;
import com.lookforx.loggingservice.strategy.LogStorageStrategy;
import com.lookforx.loggingservice.template.LogProcessingTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Template implementation for processing application log events.
 * 
 * Handles standard application logs with validation, enrichment,
 * and storage according to application-specific business rules.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ApplicationLogProcessor extends LogProcessingTemplate {
    
    private final List<LogStorageStrategy> storageStrategies;
    
    private static final String PROCESSOR_NAME = "ApplicationLogProcessor";
    
    // Application log specific configuration
    private static final int MAX_MESSAGE_LENGTH = 10000;
    private static final int MAX_CUSTOM_FIELDS = 50;
    private static final String[] VALID_LEVELS = {"TRACE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL"};
    
    @Override
    protected String getProcessorName() {
        return PROCESSOR_NAME;
    }
    
    @Override
    protected LogProcessingContext initializeContext(LogEvent logEvent) {
        LogProcessingContext context = new LogProcessingContext(PROCESSOR_NAME);
        
        // Add application-specific context data
        context.putContextData("logLevel", logEvent.getLevel());
        context.putContextData("loggerName", logEvent.getLogger());
        context.putContextData("serviceName", logEvent.getServiceName());
        context.putContextData("originalTimestamp", logEvent.getTimestamp());
        
        // Determine processing priority based on log level
        int priority = calculatePriority(logEvent.getLevel());
        context.putContextData("priority", priority);
        
        log.debug("Initialized context for application log: {}", logEvent.getEventId());
        
        return context;
    }
    
    @Override
    protected ValidationResult validateLogEvent(LogEvent logEvent, LogProcessingContext context) {
        // Validate required fields
        if (logEvent.getMessage() == null || logEvent.getMessage().trim().isEmpty()) {
            return ValidationResult.invalid("Message is required");
        }
        
        if (logEvent.getTimestamp() == null) {
            return ValidationResult.invalid("Timestamp is required");
        }
        
        // Validate message length
        if (logEvent.getMessage().length() > MAX_MESSAGE_LENGTH) {
            return ValidationResult.invalid("Message too long (max " + MAX_MESSAGE_LENGTH + " characters)");
        }
        
        // Validate log level
        if (logEvent.getLevel() != null && !isValidLevel(logEvent.getLevel())) {
            return ValidationResult.invalid("Invalid log level: " + logEvent.getLevel());
        }
        
        // Validate timestamp range
        LocalDateTime now = LocalDateTime.now();
        if (logEvent.getTimestamp().isAfter(now.plusMinutes(5))) {
            return ValidationResult.invalid("Timestamp cannot be more than 5 minutes in the future");
        }
        
        if (logEvent.getTimestamp().isBefore(now.minusDays(30))) {
            return ValidationResult.invalid("Timestamp cannot be more than 30 days old");
        }
        
        // Validate custom fields count
        if (logEvent.getCustomFields() != null && logEvent.getCustomFields().size() > MAX_CUSTOM_FIELDS) {
            return ValidationResult.invalid("Too many custom fields (max " + MAX_CUSTOM_FIELDS + ")");
        }
        
        log.debug("Validation passed for application log: {}", logEvent.getEventId());
        return ValidationResult.valid();
    }
    
    @Override
    protected LogEvent enrichLogEvent(LogEvent logEvent, LogProcessingContext context) {
        // Create enriched copy using builder
        LogEvent.LogEventBuilder<?, ?> builder = LogEvent.builder()
                .eventId(logEvent.getEventId())
                .timestamp(logEvent.getTimestamp())
                .serviceName(logEvent.getServiceName())
                .version(logEvent.getVersion())
                .userId(logEvent.getUserId())
                .correlationId(logEvent.getCorrelationId())
                .metadata(logEvent.getMetadata())
                .logServiceName(logEvent.getLogServiceName())
                .logEnvironment(logEvent.getLogEnvironment())
                .logVersion(logEvent.getLogVersion())
                .level(logEvent.getLevel())
                .logger(logEvent.getLogger())
                .message(logEvent.getMessage())
                .thread(logEvent.getThread())
                .traceId(logEvent.getTraceId())
                .spanId(logEvent.getSpanId())
                .sessionId(logEvent.getSessionId())
                .requestId(logEvent.getRequestId())
                .className(logEvent.getClassName())
                .methodName(logEvent.getMethodName())
                .lineNumber(logEvent.getLineNumber())
                .exception(logEvent.getException())
                .stackTrace(logEvent.getStackTrace())
                .host(logEvent.getHost())
                .ip(logEvent.getIp())
                .service(logEvent.getService())
                .userAgent(logEvent.getUserAgent())
                .requestUri(logEvent.getRequestUri())
                .httpMethod(logEvent.getHttpMethod())
                .httpStatus(logEvent.getHttpStatus())
                .duration(logEvent.getDuration())
                .mdc(logEvent.getMdc());
        
        // Enrich basic fields
        if (logEvent.getServiceName() == null || logEvent.getServiceName().trim().isEmpty()) {
            builder.serviceName("logging-service");
        }
        
        if (logEvent.getVersion() == null || logEvent.getVersion().trim().isEmpty()) {
            builder.version("1.0");
        }
        
        // Enrich system information
        if (logEvent.getHost() == null || logEvent.getHost().trim().isEmpty()) {
            builder.host(getHostname());
        }
        
        if (logEvent.getThread() == null || logEvent.getThread().trim().isEmpty()) {
            builder.thread(Thread.currentThread().getName());
        }
        
        // Enrich custom fields
        Map<String, Object> customFields = logEvent.getMetadata();
        if (customFields == null) {
            customFields = new HashMap<>();
        } else {
            customFields = new HashMap<>(customFields); // Create mutable copy
        }

        // Add enrichment metadata
        customFields.put("enrichedAt", LocalDateTime.now());
        customFields.put("enrichedBy", PROCESSOR_NAME);
        customFields.put("logCategory", "application");
        customFields.put("priority", context.getContextData("priority"));

        // Add derived fields
        if (logEvent.getLogger() != null) {
            customFields.put("loggerCategory", deriveLoggerCategory(logEvent.getLogger()));
        }

        builder.metadata(customFields);
        
        LogEvent enrichedEvent = builder.build();
        log.debug("Enrichment completed for application log: {}", logEvent.getEventId());
        
        return enrichedEvent;
    }
    
    @Override
    protected BusinessRuleResult applyBusinessRules(LogEvent logEvent, LogProcessingContext context) {
        // Rule 1: Check if log level is allowed
        if (logEvent.getLevel() != null) {
            String level = logEvent.getLevel().toUpperCase();
            
            // Skip TRACE logs in production
            String environment = logEvent.getLogEnvironment();
            if ("TRACE".equals(level) && "prod".equalsIgnoreCase(environment)) {
                return BusinessRuleResult.deny("TRACE logs not allowed in production");
            }
        }
        
        // Rule 2: Check for sensitive information in message
        if (containsSensitiveInformation(logEvent.getMessage())) {
            return BusinessRuleResult.deny("Message contains sensitive information");
        }
        
        // Rule 3: Check rate limiting (simplified)
        if (isRateLimited(logEvent, context)) {
            return BusinessRuleResult.deny("Rate limit exceeded");
        }
        
        log.debug("Business rules passed for application log: {}", logEvent.getEventId());
        return BusinessRuleResult.allow();
    }
    
    @Override
    protected LogStorageResult storeLogEvent(LogEvent logEvent, LogProcessingContext context) {
        // Find the best storage strategy
        LogStorageStrategy strategy = findBestStorageStrategy(logEvent);
        
        if (strategy == null) {
            log.error("No available storage strategy for log event: {}", logEvent.getEventId());
            return LogStorageResult.failure(1, 0, "No strategy", "No available storage strategy");
        }
        
        // Store the log event
        LogStorageResult result = strategy.store(logEvent);
        
        log.debug("Storage {} for application log: {} using strategy: {}", 
                 result.isSuccess() ? "succeeded" : "failed", 
                 logEvent.getEventId(), strategy.getStrategyName());
        
        return result;
    }
    
    @Override
    protected void postProcessing(LogEvent logEvent, LogStorageResult storageResult, LogProcessingContext context) {
        // Update context with storage result
        context.putContextData("storageResult", storageResult);
        context.putContextData("processingCompleted", LocalDateTime.now());
        
        // Log processing summary for high-priority events
        Integer priority = (Integer) context.getContextData("priority");
        if (priority != null && priority <= 2) { // ERROR and FATAL
            log.info("High-priority application log processed: {} - Storage: {}", 
                    logEvent.getEventId(), storageResult.isSuccess() ? "SUCCESS" : "FAILED");
        }
    }
    
    // Helper methods
    
    private boolean isValidLevel(String level) {
        if (level == null) {
            return true;
        }
        
        String upperLevel = level.toUpperCase();
        for (String validLevel : VALID_LEVELS) {
            if (validLevel.equals(upperLevel)) {
                return true;
            }
        }
        return false;
    }
    
    private int calculatePriority(String level) {
        if (level == null) {
            return 4; // INFO priority
        }
        
        switch (level.toUpperCase()) {
            case "FATAL":
                return 1;
            case "ERROR":
                return 2;
            case "WARN":
                return 3;
            case "INFO":
                return 4;
            case "DEBUG":
                return 5;
            case "TRACE":
                return 6;
            default:
                return 4;
        }
    }
    
    private String deriveLoggerCategory(String loggerName) {
        if (loggerName == null) {
            return "unknown";
        }
        
        String lowerName = loggerName.toLowerCase();
        if (lowerName.contains("controller")) {
            return "web";
        }
        if (lowerName.contains("service")) {
            return "business";
        }
        if (lowerName.contains("repository") || lowerName.contains("dao")) {
            return "data";
        }
        if (lowerName.contains("security")) {
            return "security";
        }
        
        return "application";
    }
    
    private boolean containsSensitiveInformation(String message) {
        if (message == null) {
            return false;
        }
        
        String lowerMessage = message.toLowerCase();
        return lowerMessage.contains("password") || 
               lowerMessage.contains("token") || 
               lowerMessage.contains("secret") ||
               lowerMessage.contains("key=") ||
               lowerMessage.contains("authorization");
    }
    
    private boolean isRateLimited(LogEvent logEvent, LogProcessingContext context) {
        // Simplified rate limiting - in real implementation, use Redis or similar
        // For now, just return false (no rate limiting)
        return false;
    }
    
    private LogStorageStrategy findBestStorageStrategy(LogEvent logEvent) {
        return storageStrategies.stream()
                .filter(strategy -> strategy.canHandle(logEvent) && strategy.isAvailable())
                .min((s1, s2) -> Integer.compare(s1.getPriority(), s2.getPriority()))
                .orElse(null);
    }
    
    private String getHostname() {
        try {
            return java.net.InetAddress.getLocalHost().getHostName();
        } catch (Exception e) {
            return "unknown";
        }
    }
}
