package com.lookforx.loggingservice.domain;

import com.lookforx.common.events.LogEvent;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Result object for log processing operations.
 * 
 * Encapsulates the result of processing a log event through
 * the chain of responsibility, including any modifications,
 * filtering decisions, and processing metadata.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Data
@Builder(toBuilder = true)
public class LogProcessingResult {
    
    /**
     * Whether the processing was successful
     */
    private final boolean success;
    
    /**
     * The processed log event (may be modified)
     */
    private final LogEvent processedEvent;
    
    /**
     * Whether the event should be accepted for further processing
     */
    private final boolean accepted;
    
    /**
     * Whether the event was filtered out
     */
    private final boolean filtered;
    
    /**
     * Whether the event was modified during processing
     */
    private final boolean modified;
    
    /**
     * Name of the handler that processed this event
     */
    private final String handlerName;
    
    /**
     * Processing duration in milliseconds
     */
    private final long processingTimeMs;
    
    /**
     * Timestamp when processing completed
     */
    private final LocalDateTime processedAt;
    
    /**
     * Reason for filtering (if filtered)
     */
    private final String filterReason;
    
    /**
     * Error message if processing failed
     */
    private final String errorMessage;
    
    /**
     * List of modifications made to the event
     */
    private final List<String> modifications;
    
    /**
     * Additional processing metadata
     */
    private final Map<String, Object> metadata;
    
    /**
     * Creates a successful processing result
     */
    public static LogProcessingResult success(LogEvent processedEvent, String handlerName, 
                                            long processingTimeMs) {
        return LogProcessingResult.builder()
                .success(true)
                .processedEvent(processedEvent)
                .accepted(true)
                .filtered(false)
                .modified(false)
                .handlerName(handlerName)
                .processingTimeMs(processingTimeMs)
                .processedAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * Creates a successful processing result with modifications
     */
    public static LogProcessingResult successWithModifications(LogEvent processedEvent, String handlerName, 
                                                             long processingTimeMs, List<String> modifications) {
        return LogProcessingResult.builder()
                .success(true)
                .processedEvent(processedEvent)
                .accepted(true)
                .filtered(false)
                .modified(true)
                .handlerName(handlerName)
                .processingTimeMs(processingTimeMs)
                .processedAt(LocalDateTime.now())
                .modifications(modifications)
                .build();
    }
    
    /**
     * Creates a filtered processing result
     */
    public static LogProcessingResult filtered(LogEvent originalEvent, String handlerName, 
                                             long processingTimeMs, String filterReason) {
        return LogProcessingResult.builder()
                .success(true)
                .processedEvent(originalEvent)
                .accepted(false)
                .filtered(true)
                .modified(false)
                .handlerName(handlerName)
                .processingTimeMs(processingTimeMs)
                .processedAt(LocalDateTime.now())
                .filterReason(filterReason)
                .build();
    }
    
    /**
     * Creates a failed processing result
     */
    public static LogProcessingResult failure(LogEvent originalEvent, String handlerName, 
                                            long processingTimeMs, String errorMessage) {
        return LogProcessingResult.builder()
                .success(false)
                .processedEvent(originalEvent)
                .accepted(false)
                .filtered(false)
                .modified(false)
                .handlerName(handlerName)
                .processingTimeMs(processingTimeMs)
                .processedAt(LocalDateTime.now())
                .errorMessage(errorMessage)
                .build();
    }
    
    /**
     * Creates a pass-through result (no processing needed)
     */
    public static LogProcessingResult passThrough(LogEvent event, String handlerName, long processingTimeMs) {
        return LogProcessingResult.builder()
                .success(true)
                .processedEvent(event)
                .accepted(true)
                .filtered(false)
                .modified(false)
                .handlerName(handlerName)
                .processingTimeMs(processingTimeMs)
                .processedAt(LocalDateTime.now())
                .build();
    }
    
    /**
     * Checks if the event should continue through the chain
     */
    public boolean shouldContinueChain() {
        return success && accepted && !filtered;
    }
    
    /**
     * Gets a summary of the processing result
     */
    public String getSummary() {
        if (!success) {
            return String.format("Failed: %s", errorMessage);
        }
        
        if (filtered) {
            return String.format("Filtered: %s", filterReason);
        }
        
        if (modified) {
            return String.format("Modified: %d changes", modifications != null ? modifications.size() : 0);
        }
        
        return "Processed successfully";
    }
}
