package com.lookforx.loggingservice.domain;

import com.lookforx.common.events.LogEvent;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Notification object for log event processing.
 * 
 * Contains information about log events and their processing
 * status for observer pattern notifications.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Data
@Builder(toBuilder = true)
public class LogEventNotification {
    
    /**
     * Type of notification
     */
    public enum NotificationType {
        RECEIVED,
        PROCESSED,
        FAILED,
        FILTERED,
        STORED,
        STORAGE_FAILED
    }
    
    /**
     * The type of this notification
     */
    private final NotificationType type;
    
    /**
     * The log event being processed
     */
    private final LogEvent logEvent;
    
    /**
     * Processing result if available
     */
    private final LogProcessingResult processingResult;
    
    /**
     * Storage result if available
     */
    private final LogStorageResult storageResult;
    
    /**
     * Timestamp when the notification was created
     */
    private final LocalDateTime notificationTime;
    
    /**
     * Source component that generated this notification
     */
    private final String source;
    
    /**
     * Error message if applicable
     */
    private final String errorMessage;
    
    /**
     * Exception if applicable
     */
    private final Throwable exception;
    
    /**
     * Additional metadata
     */
    private final Map<String, Object> metadata;
    
    /**
     * Creates a notification for log event received
     */
    public static LogEventNotification received(LogEvent logEvent, String source) {
        return LogEventNotification.builder()
                .type(NotificationType.RECEIVED)
                .logEvent(logEvent)
                .notificationTime(LocalDateTime.now())
                .source(source)
                .build();
    }
    
    /**
     * Creates a notification for log event processed
     */
    public static LogEventNotification processed(LogEvent logEvent, LogProcessingResult result, String source) {
        return LogEventNotification.builder()
                .type(NotificationType.PROCESSED)
                .logEvent(logEvent)
                .processingResult(result)
                .notificationTime(LocalDateTime.now())
                .source(source)
                .build();
    }
    
    /**
     * Creates a notification for log event processing failed
     */
    public static LogEventNotification failed(LogEvent logEvent, String errorMessage, Throwable exception, String source) {
        return LogEventNotification.builder()
                .type(NotificationType.FAILED)
                .logEvent(logEvent)
                .errorMessage(errorMessage)
                .exception(exception)
                .notificationTime(LocalDateTime.now())
                .source(source)
                .build();
    }
    
    /**
     * Creates a notification for log event filtered
     */
    public static LogEventNotification filtered(LogEvent logEvent, LogProcessingResult result, String source) {
        return LogEventNotification.builder()
                .type(NotificationType.FILTERED)
                .logEvent(logEvent)
                .processingResult(result)
                .notificationTime(LocalDateTime.now())
                .source(source)
                .build();
    }
    
    /**
     * Creates a notification for log event stored
     */
    public static LogEventNotification stored(LogEvent logEvent, LogStorageResult result, String source) {
        return LogEventNotification.builder()
                .type(NotificationType.STORED)
                .logEvent(logEvent)
                .storageResult(result)
                .notificationTime(LocalDateTime.now())
                .source(source)
                .build();
    }
    
    /**
     * Creates a notification for log event storage failed
     */
    public static LogEventNotification storageFailed(LogEvent logEvent, LogStorageResult result, String source) {
        return LogEventNotification.builder()
                .type(NotificationType.STORAGE_FAILED)
                .logEvent(logEvent)
                .storageResult(result)
                .notificationTime(LocalDateTime.now())
                .source(source)
                .build();
    }
    
    /**
     * Gets a summary of this notification
     */
    public String getSummary() {
        StringBuilder sb = new StringBuilder();
        sb.append(type.name()).append(" - ");
        
        if (logEvent != null) {
            sb.append("Event: ").append(logEvent.getEventId()).append(" ");
        }
        
        if (errorMessage != null) {
            sb.append("Error: ").append(errorMessage);
        } else if (processingResult != null) {
            sb.append("Processing: ").append(processingResult.getSummary());
        } else if (storageResult != null) {
            sb.append("Storage: ").append(storageResult.isSuccess() ? "Success" : "Failed");
        }
        
        return sb.toString();
    }
    
    /**
     * Checks if this is an error notification
     */
    public boolean isError() {
        return type == NotificationType.FAILED || type == NotificationType.STORAGE_FAILED;
    }
    
    /**
     * Checks if this is a success notification
     */
    public boolean isSuccess() {
        return type == NotificationType.PROCESSED || type == NotificationType.STORED;
    }
}
