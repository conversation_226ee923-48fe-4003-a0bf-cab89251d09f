package com.lookforx.loggingservice.domain;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Result object for log storage operations.
 * 
 * Encapsulates the result of a log storage operation, including
 * success/failure status, metrics, and error information.
 * 
 * Following the Value Object pattern for immutable result data.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Data
@Builder(toBuilder = true)
public class LogStorageResult {
    
    /**
     * Whether the storage operation was successful
     */
    private final boolean success;
    
    /**
     * Number of log events successfully stored
     */
    private final int storedCount;
    
    /**
     * Number of log events that failed to store
     */
    private final int failedCount;
    
    /**
     * Total number of log events processed
     */
    private final int totalCount;
    
    /**
     * Time taken for the storage operation in milliseconds
     */
    private final long durationMs;
    
    /**
     * Timestamp when the storage operation completed
     */
    private final LocalDateTime completedAt;
    
    /**
     * Name of the storage strategy used
     */
    private final String strategyName;
    
    /**
     * Collection or target where logs were stored
     */
    private final String storageTarget;
    
    /**
     * Error message if the operation failed
     */
    private final String errorMessage;
    
    /**
     * Detailed error information for failed log events
     */
    private final List<LogStorageError> errors;
    
    /**
     * Additional metadata about the storage operation
     */
    private final java.util.Map<String, Object> metadata;
    
    /**
     * Creates a successful storage result
     */
    public static LogStorageResult success(int storedCount, long durationMs, 
                                         String strategyName, String storageTarget) {
        return LogStorageResult.builder()
                .success(true)
                .storedCount(storedCount)
                .failedCount(0)
                .totalCount(storedCount)
                .durationMs(durationMs)
                .completedAt(LocalDateTime.now())
                .strategyName(strategyName)
                .storageTarget(storageTarget)
                .build();
    }
    
    /**
     * Creates a failed storage result
     */
    public static LogStorageResult failure(int totalCount, long durationMs,
                                         String strategyName, String errorMessage) {
        return LogStorageResult.builder()
                .success(false)
                .storedCount(0)
                .failedCount(totalCount)
                .totalCount(totalCount)
                .durationMs(durationMs)
                .completedAt(LocalDateTime.now())
                .strategyName(strategyName)
                .errorMessage(errorMessage)
                .build();
    }

    /**
     * Creates a partial success result
     */
    public static LogStorageResult partial(int storedCount, int failedCount, long durationMs,
                                         String strategyName, String storageTarget,
                                         List<LogStorageError> errors) {
        return LogStorageResult.builder()
                .success(storedCount > 0)
                .storedCount(storedCount)
                .failedCount(failedCount)
                .totalCount(storedCount + failedCount)
                .durationMs(durationMs)
                .completedAt(LocalDateTime.now())
                .strategyName(strategyName)
                .storageTarget(storageTarget)
                .errors(errors)
                .build();
    }
    
    /**
     * Gets the success rate as a percentage
     */
    public double getSuccessRate() {
        if (totalCount == 0) {
            return 1.0;
        }
        return (double) storedCount / totalCount;
    }
    
    /**
     * Checks if this is a complete success (all events stored)
     */
    public boolean isCompleteSuccess() {
        return success && failedCount == 0 && storedCount == totalCount;
    }
}
