package com.lookforx.loggingservice.domain;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Represents an error that occurred during log storage.
 * 
 * Contains detailed information about storage failures for
 * debugging and monitoring purposes.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Data
@Builder(toBuilder = true)
public class LogStorageError {
    
    /**
     * The log event that failed to store
     */
    private final String logEventId;
    
    /**
     * Error message describing the failure
     */
    private final String errorMessage;
    
    /**
     * Exception class name if available
     */
    private final String exceptionType;
    
    /**
     * Timestamp when the error occurred
     */
    private final LocalDateTime occurredAt;
    
    /**
     * Storage strategy that encountered the error
     */
    private final String strategyName;
    
    /**
     * Target storage location
     */
    private final String storageTarget;
    
    /**
     * Whether this error is retryable
     */
    private final boolean retryable;
    
    /**
     * Creates a storage error from an exception
     */
    public static LogStorageError fromException(String logEventId, Exception exception, 
                                              String strategyName, String storageTarget) {
        return LogStorageError.builder()
                .logEventId(logEventId)
                .errorMessage(exception.getMessage())
                .exceptionType(exception.getClass().getSimpleName())
                .occurredAt(LocalDateTime.now())
                .strategyName(strategyName)
                .storageTarget(storageTarget)
                .retryable(isRetryableException(exception))
                .build();
    }
    
    private static boolean isRetryableException(Exception exception) {
        String exceptionName = exception.getClass().getSimpleName().toLowerCase();
        return exceptionName.contains("timeout") || 
               exceptionName.contains("connection") ||
               exceptionName.contains("network");
    }
}
