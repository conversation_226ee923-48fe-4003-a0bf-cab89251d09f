package com.lookforx.loggingservice.domain;

/**
 * Enumeration of different log event types supported by the logging service.
 * 
 * Each type has specific characteristics and processing requirements.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
public enum LogEventType {
    
    /**
     * Standard application logs (INFO, DEBUG, WARN, ERROR)
     */
    APPLICATION("application", "Standard application logging events", 1),
    
    /**
     * Error and exception logs with stack traces
     */
    ERROR("error", "Error and exception logging events", 2),
    
    /**
     * Audit logs for security and compliance
     */
    AUDIT("audit", "Security and compliance audit events", 3),
    
    /**
     * Performance and metrics logs
     */
    PERFORMANCE("performance", "Performance monitoring and metrics events", 4),
    
    /**
     * Security-related logs (authentication, authorization, etc.)
     */
    SECURITY("security", "Security-related logging events", 5),
    
    /**
     * Business logic and transaction logs
     */
    BUSINESS("business", "Business logic and transaction events", 6),
    
    /**
     * System and infrastructure logs
     */
    SYSTEM("system", "System and infrastructure events", 7),
    
    /**
     * Integration and external service logs
     */
    INTEGRATION("integration", "Integration and external service events", 8),
    
    /**
     * User activity and behavior logs
     */
    USER_ACTIVITY("user_activity", "User activity and behavior events", 9),
    
    /**
     * Custom application-specific logs
     */
    CUSTOM("custom", "Custom application-specific events", 10);
    
    private final String code;
    private final String description;
    private final int priority;
    
    LogEventType(String code, String description, int priority) {
        this.code = code;
        this.description = description;
        this.priority = priority;
    }
    
    /**
     * Gets the type code
     */
    public String getCode() {
        return code;
    }
    
    /**
     * Gets the type description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Gets the processing priority (lower = higher priority)
     */
    public int getPriority() {
        return priority;
    }
    
    /**
     * Finds a log event type by its code
     */
    public static LogEventType fromCode(String code) {
        if (code == null) {
            return APPLICATION; // Default
        }
        
        for (LogEventType type : values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        
        return APPLICATION; // Default fallback
    }
    
    /**
     * Determines log event type from logger name
     */
    public static LogEventType fromLoggerName(String loggerName) {
        if (loggerName == null) {
            return APPLICATION;
        }
        
        String lowerName = loggerName.toLowerCase();
        
        if (lowerName.contains("audit")) {
            return AUDIT;
        }
        if (lowerName.contains("security")) {
            return SECURITY;
        }
        if (lowerName.contains("performance") || lowerName.contains("metrics")) {
            return PERFORMANCE;
        }
        if (lowerName.contains("business") || lowerName.contains("transaction")) {
            return BUSINESS;
        }
        if (lowerName.contains("system") || lowerName.contains("infrastructure")) {
            return SYSTEM;
        }
        if (lowerName.contains("integration") || lowerName.contains("external")) {
            return INTEGRATION;
        }
        if (lowerName.contains("user") || lowerName.contains("activity")) {
            return USER_ACTIVITY;
        }
        
        return APPLICATION;
    }
    
    /**
     * Determines log event type from log level
     */
    public static LogEventType fromLogLevel(String level) {
        if (level == null) {
            return APPLICATION;
        }
        
        String upperLevel = level.toUpperCase();
        if ("ERROR".equals(upperLevel) || "FATAL".equals(upperLevel)) {
            return ERROR;
        }
        
        return APPLICATION;
    }
    
    /**
     * Checks if this type requires special handling
     */
    public boolean requiresSpecialHandling() {
        return this == ERROR || this == AUDIT || this == SECURITY;
    }
    
    /**
     * Checks if this type should be stored with high priority
     */
    public boolean isHighPriority() {
        return priority <= 3;
    }
    
    /**
     * Gets the recommended storage collection/table name
     */
    public String getStorageCollectionName() {
        return code + "_logs";
    }
}
