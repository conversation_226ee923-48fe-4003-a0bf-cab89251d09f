package com.lookforx.loggingservice.observer;

import com.lookforx.loggingservice.domain.LogEventNotification;

/**
 * Observer interface for log event notifications.
 * 
 * Defines the contract for components that want to be notified
 * about log events and processing results.
 * 
 * Following the Observer Pattern from Gang of Four design patterns.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
public interface LogEventObserver {
    
    /**
     * Gets the name of this observer.
     * 
     * @return the observer name
     */
    String getObserverName();
    
    /**
     * Gets the priority of this observer.
     * Lower numbers indicate higher priority (notified first).
     * 
     * @return the priority value (0 = highest priority)
     */
    int getPriority();
    
    /**
     * Checks if this observer is interested in the given notification.
     * 
     * @param notification the log event notification
     * @return true if interested, false otherwise
     */
    boolean isInterestedIn(LogEventNotification notification);
    
    /**
     * Called when a log event is received for processing.
     * 
     * @param notification the log event notification
     */
    void onLogEventReceived(LogEventNotification notification);
    
    /**
     * Called when a log event is successfully processed.
     * 
     * @param notification the log event notification
     */
    void onLogEventProcessed(LogEventNotification notification);
    
    /**
     * Called when log event processing fails.
     * 
     * @param notification the log event notification
     */
    void onLogEventFailed(LogEventNotification notification);
    
    /**
     * Called when a log event is filtered out.
     * 
     * @param notification the log event notification
     */
    void onLogEventFiltered(LogEventNotification notification);
    
    /**
     * Called when a log event is successfully stored.
     * 
     * @param notification the log event notification
     */
    void onLogEventStored(LogEventNotification notification);
    
    /**
     * Called when log event storage fails.
     * 
     * @param notification the log event notification
     */
    void onLogEventStorageFailed(LogEventNotification notification);
    
    /**
     * Checks if this observer is currently active and should receive notifications.
     * 
     * @return true if active, false otherwise
     */
    boolean isActive();
    
    /**
     * Activates this observer to start receiving notifications.
     */
    void activate();
    
    /**
     * Deactivates this observer to stop receiving notifications.
     */
    void deactivate();
    
    /**
     * Performs cleanup operations for this observer.
     */
    void cleanup();
}
