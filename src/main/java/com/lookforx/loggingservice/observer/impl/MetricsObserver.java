package com.lookforx.loggingservice.observer.impl;

import com.lookforx.loggingservice.domain.LogEventNotification;
import com.lookforx.loggingservice.observer.LogEventObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Observer implementation for collecting metrics about log processing.
 * 
 * Tracks various metrics about log events and their processing
 * for monitoring and performance analysis.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Component
public class MetricsObserver implements LogEventObserver {
    
    private static final String OBSERVER_NAME = "MetricsObserver";
    private static final int PRIORITY = 1;
    
    private final AtomicBoolean active = new AtomicBoolean(true);
    
    // Event counters
    private final AtomicLong eventsReceived = new AtomicLong(0);
    private final AtomicLong eventsProcessed = new AtomicLong(0);
    private final AtomicLong eventsFailed = new AtomicLong(0);
    private final AtomicLong eventsFiltered = new AtomicLong(0);
    private final AtomicLong eventsStored = new AtomicLong(0);
    private final AtomicLong eventsStorageFailed = new AtomicLong(0);
    
    // Timing metrics
    private volatile LocalDateTime lastEventTime;
    private volatile LocalDateTime firstEventTime;
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong totalStorageTime = new AtomicLong(0);
    
    // Error tracking
    private volatile String lastError;
    private volatile LocalDateTime lastErrorTime;
    
    @Override
    public String getObserverName() {
        return OBSERVER_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public boolean isInterestedIn(LogEventNotification notification) {
        return active.get() && notification != null;
    }
    
    @Override
    public void onLogEventReceived(LogEventNotification notification) {
        if (!isInterestedIn(notification)) {
            return;
        }
        
        eventsReceived.incrementAndGet();
        lastEventTime = notification.getNotificationTime();
        
        if (firstEventTime == null) {
            firstEventTime = notification.getNotificationTime();
        }
    }
    
    @Override
    public void onLogEventProcessed(LogEventNotification notification) {
        if (!isInterestedIn(notification)) {
            return;
        }
        
        eventsProcessed.incrementAndGet();
        
        if (notification.getProcessingResult() != null) {
            totalProcessingTime.addAndGet(notification.getProcessingResult().getProcessingTimeMs());
        }
    }
    
    @Override
    public void onLogEventFailed(LogEventNotification notification) {
        if (!isInterestedIn(notification)) {
            return;
        }
        
        eventsFailed.incrementAndGet();
        lastError = notification.getErrorMessage();
        lastErrorTime = notification.getNotificationTime();
    }
    
    @Override
    public void onLogEventFiltered(LogEventNotification notification) {
        if (!isInterestedIn(notification)) {
            return;
        }
        
        eventsFiltered.incrementAndGet();
    }
    
    @Override
    public void onLogEventStored(LogEventNotification notification) {
        if (!isInterestedIn(notification)) {
            return;
        }
        
        eventsStored.incrementAndGet();
        
        if (notification.getStorageResult() != null) {
            totalStorageTime.addAndGet(notification.getStorageResult().getDurationMs());
        }
    }
    
    @Override
    public void onLogEventStorageFailed(LogEventNotification notification) {
        if (!isInterestedIn(notification)) {
            return;
        }
        
        eventsStorageFailed.incrementAndGet();
        lastError = notification.getStorageResult() != null ? 
                   notification.getStorageResult().getErrorMessage() : "Storage failed";
        lastErrorTime = notification.getNotificationTime();
    }
    
    @Override
    public boolean isActive() {
        return active.get();
    }
    
    @Override
    public void activate() {
        active.set(true);
        log.info("Metrics observer activated");
    }
    
    @Override
    public void deactivate() {
        active.set(false);
        log.info("Metrics observer deactivated");
    }
    
    @Override
    public void cleanup() {
        deactivate();
        log.info("Metrics observer cleanup completed");
    }
    
    // Metrics access methods
    public long getEventsReceived() {
        return eventsReceived.get();
    }
    
    public long getEventsProcessed() {
        return eventsProcessed.get();
    }
    
    public long getEventsFailed() {
        return eventsFailed.get();
    }
    
    public long getEventsStored() {
        return eventsStored.get();
    }
    
    public double getSuccessRate() {
        long total = eventsReceived.get();
        if (total == 0) {
            return 1.0;
        }
        return (double) eventsProcessed.get() / total;
    }
    
    public double getAverageProcessingTime() {
        long processed = eventsProcessed.get();
        if (processed == 0) {
            return 0.0;
        }
        return (double) totalProcessingTime.get() / processed;
    }
    
    public String getMetricsSummary() {
        return String.format(
            "Metrics - Received: %d, Processed: %d, Failed: %d, Stored: %d, Success Rate: %.2f%%",
            eventsReceived.get(), eventsProcessed.get(), eventsFailed.get(),
            eventsStored.get(), getSuccessRate() * 100
        );
    }
    
    public void resetMetrics() {
        eventsReceived.set(0);
        eventsProcessed.set(0);
        eventsFailed.set(0);
        eventsFiltered.set(0);
        eventsStored.set(0);
        eventsStorageFailed.set(0);
        totalProcessingTime.set(0);
        totalStorageTime.set(0);
        lastError = null;
        lastErrorTime = null;
        firstEventTime = null;
        lastEventTime = null;
        
        log.info("Metrics reset completed");
    }
}
