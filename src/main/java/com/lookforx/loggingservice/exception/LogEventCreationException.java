package com.lookforx.loggingservice.exception;

/**
 * Exception thrown when log event creation fails.
 * 
 * This exception is used to indicate problems during log event
 * creation, validation, or enrichment processes.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
public class LogEventCreationException extends RuntimeException {
    
    private final String eventType;
    private final String validationErrors;
    
    /**
     * Creates a new LogEventCreationException with a message.
     */
    public LogEventCreationException(String message) {
        super(message);
        this.eventType = null;
        this.validationErrors = null;
    }
    
    /**
     * Creates a new LogEventCreationException with a message and cause.
     */
    public LogEventCreationException(String message, Throwable cause) {
        super(message, cause);
        this.eventType = null;
        this.validationErrors = null;
    }
    
    /**
     * Creates a new LogEventCreationException with detailed information.
     */
    public LogEventCreationException(String message, String eventType, String validationErrors) {
        super(message);
        this.eventType = eventType;
        this.validationErrors = validationErrors;
    }
    
    /**
     * Creates a new LogEventCreationException with detailed information and cause.
     */
    public LogEventCreationException(String message, String eventType, String validationErrors, Throwable cause) {
        super(message, cause);
        this.eventType = eventType;
        this.validationErrors = validationErrors;
    }
    
    /**
     * Gets the event type that failed to be created.
     */
    public String getEventType() {
        return eventType;
    }
    
    /**
     * Gets the validation errors that occurred.
     */
    public String getValidationErrors() {
        return validationErrors;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder(super.toString());
        
        if (eventType != null) {
            sb.append(" [eventType=").append(eventType).append("]");
        }
        
        if (validationErrors != null) {
            sb.append(" [validationErrors=").append(validationErrors).append("]");
        }
        
        return sb.toString();
    }
}
