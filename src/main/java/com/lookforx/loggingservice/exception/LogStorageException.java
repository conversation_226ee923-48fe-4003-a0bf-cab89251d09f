package com.lookforx.loggingservice.exception;

/**
 * Exception thrown when log storage operations fail.
 * 
 * This exception is used to indicate problems during log storage
 * operations across different storage strategies.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
public class LogStorageException extends RuntimeException {
    
    private final String strategyName;
    private final String storageTarget;
    
    /**
     * Creates a new LogStorageException with a message.
     */
    public LogStorageException(String message) {
        super(message);
        this.strategyName = null;
        this.storageTarget = null;
    }
    
    /**
     * Creates a new LogStorageException with a message and cause.
     */
    public LogStorageException(String message, Throwable cause) {
        super(message, cause);
        this.strategyName = null;
        this.storageTarget = null;
    }
    
    /**
     * Creates a new LogStorageException with detailed information.
     */
    public LogStorageException(String message, String strategyName, String storageTarget) {
        super(message);
        this.strategyName = strategyName;
        this.storageTarget = storageTarget;
    }
    
    /**
     * Creates a new LogStorageException with detailed information and cause.
     */
    public LogStorageException(String message, String strategyName, String storageTarget, Throwable cause) {
        super(message, cause);
        this.strategyName = strategyName;
        this.storageTarget = storageTarget;
    }
    
    /**
     * Gets the storage strategy name that encountered the error.
     */
    public String getStrategyName() {
        return strategyName;
    }
    
    /**
     * Gets the storage target that encountered the error.
     */
    public String getStorageTarget() {
        return storageTarget;
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder(super.toString());
        
        if (strategyName != null) {
            sb.append(" [strategy=").append(strategyName).append("]");
        }
        
        if (storageTarget != null) {
            sb.append(" [target=").append(storageTarget).append("]");
        }
        
        return sb.toString();
    }
}
