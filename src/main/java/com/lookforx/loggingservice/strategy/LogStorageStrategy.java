package com.lookforx.loggingservice.strategy;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.domain.LogStorageResult;

import java.util.List;

/**
 * Strategy interface for different log storage mechanisms.
 * 
 * This interface defines the contract for various log storage strategies,
 * allowing the system to switch between different storage backends
 * (MongoDB, File System, Cache, etc.) without changing the client code.
 * 
 * Following the Strategy Pattern from Gang of Four design patterns.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
public interface LogStorageStrategy {
    
    /**
     * Gets the name of this storage strategy.
     * Used for identification and logging purposes.
     * 
     * @return the strategy name
     */
    String getStrategyName();
    
    /**
     * Gets the priority of this storage strategy.
     * Lower numbers indicate higher priority.
     * Used for ordering strategies in chain processing.
     * 
     * @return the priority value (0 = highest priority)
     */
    int getPriority();
    
    /**
     * Checks if this strategy can handle the given log event.
     * Allows for conditional strategy selection based on log event properties.
     * 
     * @param logEvent the log event to check
     * @return true if this strategy can handle the event, false otherwise
     */
    boolean canHandle(LogEvent logEvent);
    
    /**
     * Stores a single log event using this strategy.
     * 
     * @param logEvent the log event to store
     * @return the storage result containing success/failure information
     * @throws LogStorageException if storage fails
     */
    LogStorageResult store(LogEvent logEvent);
    
    /**
     * Stores multiple log events in batch using this strategy.
     * Implementations should optimize for batch operations when possible.
     * 
     * @param logEvents the list of log events to store
     * @return the storage result containing success/failure information
     * @throws LogStorageException if storage fails
     */
    LogStorageResult storeBatch(List<LogEvent> logEvents);
    
    /**
     * Checks if this storage strategy is currently available and healthy.
     * Used for health checks and failover scenarios.
     * 
     * @return true if the strategy is available, false otherwise
     */
    boolean isAvailable();
    
    /**
     * Gets the maximum batch size supported by this strategy.
     * Used for optimizing batch operations.
     * 
     * @return the maximum batch size, or -1 if unlimited
     */
    int getMaxBatchSize();
    
    /**
     * Performs cleanup operations for this strategy.
     * Called during application shutdown or strategy replacement.
     */
    void cleanup();
    
    /**
     * Gets performance metrics for this strategy.
     * Used for monitoring and optimization.
     * 
     * @return performance metrics
     */
    LogStorageMetrics getMetrics();
}
