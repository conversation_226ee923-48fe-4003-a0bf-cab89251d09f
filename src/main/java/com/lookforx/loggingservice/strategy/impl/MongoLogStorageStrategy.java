package com.lookforx.loggingservice.strategy.impl;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.domain.LogStorageError;
import com.lookforx.loggingservice.domain.LogStorageResult;
import com.lookforx.loggingservice.exception.LogStorageException;
import com.lookforx.loggingservice.strategy.LogStorageMetrics;
import com.lookforx.loggingservice.strategy.LogStorageStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * MongoDB implementation of LogStorageStrategy.
 * 
 * Provides high-performance log storage using MongoDB with
 * optimized batch operations and error handling.
 * 
 * Features:
 * - Bulk operations for better performance
 * - Automatic collection routing based on log type
 * - Comprehensive error handling and metrics
 * - Health monitoring and availability checks
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MongoLogStorageStrategy implements LogStorageStrategy {
    
    private final MongoTemplate mongoTemplate;
    
    // Strategy configuration
    private static final String STRATEGY_NAME = "MongoDB";
    private static final int PRIORITY = 1; // High priority
    private static final int MAX_BATCH_SIZE = 1000;
    
    // Collection names
    private static final String APPLICATION_LOGS_COLLECTION = "application_logs";
    private static final String ERROR_LOGS_COLLECTION = "error_logs";
    private static final String AUDIT_LOGS_COLLECTION = "audit_logs";
    private static final String PERFORMANCE_LOGS_COLLECTION = "performance_logs";
    private static final String SECURITY_LOGS_COLLECTION = "security_logs";
    
    // Metrics tracking
    private final AtomicLong totalOperations = new AtomicLong(0);
    private final AtomicLong successfulOperations = new AtomicLong(0);
    private final AtomicLong failedOperations = new AtomicLong(0);
    private final AtomicLong totalLogEvents = new AtomicLong(0);
    private final AtomicLong storedLogEvents = new AtomicLong(0);
    private volatile LocalDateTime lastSuccessAt;
    private volatile LocalDateTime lastFailureAt;
    private final LocalDateTime metricsStartTime = LocalDateTime.now();
    
    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public boolean canHandle(LogEvent logEvent) {
        // MongoDB can handle all types of log events
        return logEvent != null && isAvailable();
    }
    
    @Override
    public LogStorageResult store(LogEvent logEvent) {
        if (logEvent == null) {
            return LogStorageResult.failure(0, 0, STRATEGY_NAME, "Log event is null");
        }
        
        long startTime = System.currentTimeMillis();
        totalOperations.incrementAndGet();
        totalLogEvents.incrementAndGet();
        
        try {
            // Enrich log event with storage metadata
            enrichLogEvent(logEvent);
            
            // Determine target collection
            String collection = determineCollection(logEvent);
            
            // Store the log event
            mongoTemplate.insert(logEvent, collection);
            
            // Update metrics
            long duration = System.currentTimeMillis() - startTime;
            successfulOperations.incrementAndGet();
            storedLogEvents.incrementAndGet();
            lastSuccessAt = LocalDateTime.now();
            
            log.debug("Successfully stored log event in MongoDB collection: {}", collection);
            
            return LogStorageResult.success(1, duration, STRATEGY_NAME, collection);
            
        } catch (Exception e) {
            // Update metrics
            long duration = System.currentTimeMillis() - startTime;
            failedOperations.incrementAndGet();
            lastFailureAt = LocalDateTime.now();
            
            log.error("Failed to store log event in MongoDB", e);
            
            return LogStorageResult.failure(1, duration, STRATEGY_NAME, e.getMessage());
        }
    }
    
    @Override
    public LogStorageResult storeBatch(List<LogEvent> logEvents) {
        if (logEvents == null || logEvents.isEmpty()) {
            return LogStorageResult.success(0, 0, STRATEGY_NAME, "No events to store");
        }
        
        long startTime = System.currentTimeMillis();
        totalOperations.incrementAndGet();
        totalLogEvents.addAndGet(logEvents.size());
        
        try {
            // Group events by collection for optimized batch operations
            var eventsByCollection = groupEventsByCollection(logEvents);
            
            int totalStored = 0;
            List<LogStorageError> errors = new ArrayList<>();
            
            // Process each collection group
            for (var entry : eventsByCollection.entrySet()) {
                String collection = entry.getKey();
                List<LogEvent> collectionEvents = entry.getValue();
                
                try {
                    // Enrich all events
                    collectionEvents.forEach(this::enrichLogEvent);
                    
                    // Perform bulk insert
                    BulkOperations bulkOps = mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, collection);
                    collectionEvents.forEach(bulkOps::insert);
                    bulkOps.execute();
                    
                    totalStored += collectionEvents.size();
                    
                    log.debug("Successfully stored {} events in MongoDB collection: {}", 
                             collectionEvents.size(), collection);
                    
                } catch (Exception e) {
                    log.error("Failed to store {} events in collection: {}", 
                             collectionEvents.size(), collection, e);
                    
                    // Add errors for failed events
                    for (LogEvent event : collectionEvents) {
                        errors.add(LogStorageError.fromException(
                            event.getEventId(), e, STRATEGY_NAME, collection));
                    }
                }
            }
            
            // Update metrics
            long duration = System.currentTimeMillis() - startTime;
            storedLogEvents.addAndGet(totalStored);
            
            if (totalStored == logEvents.size()) {
                successfulOperations.incrementAndGet();
                lastSuccessAt = LocalDateTime.now();
                return LogStorageResult.success(totalStored, duration, STRATEGY_NAME, "Multiple collections");
            } else if (totalStored > 0) {
                return LogStorageResult.partial(totalStored, logEvents.size() - totalStored, 
                                              duration, STRATEGY_NAME, "Multiple collections", errors);
            } else {
                failedOperations.incrementAndGet();
                lastFailureAt = LocalDateTime.now();
                return LogStorageResult.failure(logEvents.size(), duration, STRATEGY_NAME, 
                                              "All batch operations failed");
            }
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            failedOperations.incrementAndGet();
            lastFailureAt = LocalDateTime.now();
            
            log.error("Failed to process log events batch", e);
            
            return LogStorageResult.failure(logEvents.size(), duration, STRATEGY_NAME, e.getMessage());
        }
    }
    
    @Override
    public boolean isAvailable() {
        try {
            // Simple health check - try to get collection names
            mongoTemplate.getCollectionNames();
            return true;
        } catch (Exception e) {
            log.warn("MongoDB storage strategy is not available: {}", e.getMessage());
            return false;
        }
    }
    
    @Override
    public int getMaxBatchSize() {
        return MAX_BATCH_SIZE;
    }
    
    @Override
    public void cleanup() {
        log.info("MongoDB storage strategy cleanup completed");
    }
    
    @Override
    public LogStorageMetrics getMetrics() {
        return LogStorageMetrics.builder()
                .totalOperations(totalOperations.get())
                .successfulOperations(successfulOperations.get())
                .failedOperations(failedOperations.get())
                .totalLogEvents(totalLogEvents.get())
                .storedLogEvents(storedLogEvents.get())
                .available(isAvailable())
                .lastSuccessAt(lastSuccessAt)
                .lastFailureAt(lastFailureAt)
                .metricsStartTime(metricsStartTime)
                .lastUpdated(LocalDateTime.now())
                .build();
    }
    
    /**
     * Enriches log event with storage metadata
     */
    private void enrichLogEvent(LogEvent logEvent) {
        Map<String, Object> metadata = logEvent.getMetadata();
        if (metadata == null) {
            metadata = new HashMap<>();
        } else {
            metadata = new HashMap<>(metadata);
        }
        metadata.put("storedAt", LocalDateTime.now());
        metadata.put("storageStrategy", STRATEGY_NAME);

        // Note: Since LogEvent is immutable, we can't modify it directly
        // This method is called before storage, so the enrichment should be done
        // by creating a new LogEvent instance if needed
    }
    
    /**
     * Determines the appropriate MongoDB collection for a log event
     */
    private String determineCollection(LogEvent logEvent) {
        if (logEvent.getLevel() != null) {
            switch (logEvent.getLevel().toUpperCase()) {
                case "ERROR":
                case "FATAL":
                    return ERROR_LOGS_COLLECTION;
                default:
                    break;
            }
        }
        
        if (logEvent.getLogger() != null) {
            String loggerName = logEvent.getLogger().toLowerCase();
            if (loggerName.contains("audit")) {
                return AUDIT_LOGS_COLLECTION;
            }
            if (loggerName.contains("performance") || loggerName.contains("metrics")) {
                return PERFORMANCE_LOGS_COLLECTION;
            }
            if (loggerName.contains("security")) {
                return SECURITY_LOGS_COLLECTION;
            }
        }
        
        return APPLICATION_LOGS_COLLECTION;
    }
    
    /**
     * Groups log events by their target collection for batch optimization
     */
    private java.util.Map<String, List<LogEvent>> groupEventsByCollection(List<LogEvent> logEvents) {
        java.util.Map<String, List<LogEvent>> grouped = new HashMap<>();
        
        for (LogEvent event : logEvents) {
            String collection = determineCollection(event);
            grouped.computeIfAbsent(collection, k -> new ArrayList<>()).add(event);
        }
        
        return grouped;
    }
}
