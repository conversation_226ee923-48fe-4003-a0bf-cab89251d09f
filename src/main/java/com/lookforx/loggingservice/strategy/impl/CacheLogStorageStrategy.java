package com.lookforx.loggingservice.strategy.impl;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.domain.LogStorageResult;
import com.lookforx.loggingservice.strategy.LogStorageMetrics;
import com.lookforx.loggingservice.strategy.LogStorageStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicLong;

/**
 * In-memory cache implementation of LogStorageStrategy.
 * 
 * Provides ultra-fast log storage using in-memory cache for
 * high-frequency, short-term log storage needs.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "lookforx.logging.cache.enabled", havingValue = "true", matchIfMissing = false)
public class CacheLogStorageStrategy implements LogStorageStrategy {
    
    private static final String STRATEGY_NAME = "Cache";
    private static final int PRIORITY = 0; // Highest priority
    private static final int MAX_BATCH_SIZE = 10000;
    private static final int DEFAULT_MAX_SIZE = 50000;
    
    private final ConcurrentLinkedQueue<LogEvent> logCache = new ConcurrentLinkedQueue<>();
    private final ConcurrentHashMap<String, LogEvent> logIndex = new ConcurrentHashMap<>();
    private final int maxCacheSize = DEFAULT_MAX_SIZE;
    
    // Metrics
    private final AtomicLong totalOperations = new AtomicLong(0);
    private final AtomicLong successfulOperations = new AtomicLong(0);
    private final AtomicLong failedOperations = new AtomicLong(0);
    private final AtomicLong totalLogEvents = new AtomicLong(0);
    private final AtomicLong storedLogEvents = new AtomicLong(0);
    private final AtomicLong evictedEvents = new AtomicLong(0);
    private volatile LocalDateTime lastSuccessAt;
    private volatile LocalDateTime lastFailureAt;
    private final LocalDateTime metricsStartTime = LocalDateTime.now();
    
    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public boolean canHandle(LogEvent logEvent) {
        return logEvent != null && isAvailable();
    }
    
    @Override
    public LogStorageResult store(LogEvent logEvent) {
        if (logEvent == null) {
            return LogStorageResult.failure(0, 0, STRATEGY_NAME, "Log event is null");
        }
        
        long startTime = System.currentTimeMillis();
        totalOperations.incrementAndGet();
        totalLogEvents.incrementAndGet();
        
        try {
            enrichLogEvent(logEvent);
            addToCache(logEvent);
            
            long duration = System.currentTimeMillis() - startTime;
            successfulOperations.incrementAndGet();
            storedLogEvents.incrementAndGet();
            lastSuccessAt = LocalDateTime.now();
            
            return LogStorageResult.success(1, duration, STRATEGY_NAME, "memory-cache");
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            failedOperations.incrementAndGet();
            lastFailureAt = LocalDateTime.now();
            
            log.error("Failed to store log event in cache", e);
            return LogStorageResult.failure(1, duration, STRATEGY_NAME, e.getMessage());
        }
    }
    
    @Override
    public LogStorageResult storeBatch(List<LogEvent> logEvents) {
        if (logEvents == null || logEvents.isEmpty()) {
            return LogStorageResult.success(0, 0, STRATEGY_NAME, "No events to store");
        }
        
        long startTime = System.currentTimeMillis();
        totalOperations.incrementAndGet();
        totalLogEvents.addAndGet(logEvents.size());
        
        try {
            int storedCount = 0;
            
            for (LogEvent event : logEvents) {
                try {
                    enrichLogEvent(event);
                    addToCache(event);
                    storedCount++;
                } catch (Exception e) {
                    log.warn("Failed to cache individual log event: {}", e.getMessage());
                }
            }
            
            long duration = System.currentTimeMillis() - startTime;
            storedLogEvents.addAndGet(storedCount);
            
            if (storedCount == logEvents.size()) {
                successfulOperations.incrementAndGet();
                lastSuccessAt = LocalDateTime.now();
                return LogStorageResult.success(storedCount, duration, STRATEGY_NAME, "memory-cache");
            } else {
                return LogStorageResult.partial(storedCount, logEvents.size() - storedCount,
                                              duration, STRATEGY_NAME, "memory-cache", new java.util.ArrayList<>());
            }
            
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            failedOperations.incrementAndGet();
            lastFailureAt = LocalDateTime.now();
            
            return LogStorageResult.failure(logEvents.size(), duration, STRATEGY_NAME, e.getMessage());
        }
    }
    
    @Override
    public boolean isAvailable() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long freeMemory = runtime.freeMemory();
            long totalMemory = runtime.totalMemory();
            double memoryUsage = (double) (totalMemory - freeMemory) / totalMemory;
            return memoryUsage < 0.9;
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public int getMaxBatchSize() {
        return MAX_BATCH_SIZE;
    }
    
    @Override
    public void cleanup() {
        logCache.clear();
        logIndex.clear();
        log.info("Cache storage strategy cleanup completed");
    }
    
    @Override
    public LogStorageMetrics getMetrics() {
        return LogStorageMetrics.builder()
                .totalOperations(totalOperations.get())
                .successfulOperations(successfulOperations.get())
                .failedOperations(failedOperations.get())
                .totalLogEvents(totalLogEvents.get())
                .storedLogEvents(storedLogEvents.get())
                .available(isAvailable())
                .lastSuccessAt(lastSuccessAt)
                .lastFailureAt(lastFailureAt)
                .metricsStartTime(metricsStartTime)
                .lastUpdated(LocalDateTime.now())
                .build();
    }
    
    public int getCacheSize() {
        return logCache.size();
    }
    
    public List<LogEvent> getRecentEvents(int limit) {
        return logCache.stream().limit(limit).toList();
    }
    
    private void enrichLogEvent(LogEvent logEvent) {
        Map<String, Object> metadata = logEvent.getMetadata();
        if (metadata == null) {
            metadata = new java.util.HashMap<>();
        } else {
            metadata = new java.util.HashMap<>(metadata);
        }
        metadata.put("cachedAt", LocalDateTime.now());
        metadata.put("storageStrategy", STRATEGY_NAME);

        // Note: Since LogEvent is immutable, we can't modify it directly
        // This method is called before storage, so the enrichment should be done
        // by creating a new LogEvent instance if needed
    }
    
    private void addToCache(LogEvent logEvent) {
        logCache.offer(logEvent);
        if (logEvent.getEventId() != null) {
            logIndex.put(logEvent.getEventId(), logEvent);
        }
        
        // LRU eviction
        while (logCache.size() > maxCacheSize) {
            LogEvent evicted = logCache.poll();
            if (evicted != null) {
                if (evicted.getEventId() != null) {
                    logIndex.remove(evicted.getEventId());
                }
                evictedEvents.incrementAndGet();
            }
        }
    }
}
