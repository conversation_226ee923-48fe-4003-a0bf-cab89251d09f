package com.lookforx.loggingservice.strategy;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Performance metrics for log storage strategies.
 * 
 * Tracks performance and health metrics for monitoring
 * and optimization of storage strategies.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Data
@Builder(toBuilder = true)
public class LogStorageMetrics {
    
    /**
     * Total number of storage operations attempted
     */
    private final long totalOperations;
    
    /**
     * Number of successful storage operations
     */
    private final long successfulOperations;
    
    /**
     * Number of failed storage operations
     */
    private final long failedOperations;
    
    /**
     * Total number of log events processed
     */
    private final long totalLogEvents;
    
    /**
     * Number of log events successfully stored
     */
    private final long storedLogEvents;
    
    /**
     * Average operation duration in milliseconds
     */
    private final double averageDurationMs;
    
    /**
     * Minimum operation duration in milliseconds
     */
    private final long minDurationMs;
    
    /**
     * Maximum operation duration in milliseconds
     */
    private final long maxDurationMs;
    
    /**
     * Average throughput (events per second)
     */
    private final double averageThroughput;
    
    /**
     * Current availability status
     */
    private final boolean available;
    
    /**
     * Last successful operation timestamp
     */
    private final LocalDateTime lastSuccessAt;
    
    /**
     * Last failed operation timestamp
     */
    private final LocalDateTime lastFailureAt;
    
    /**
     * Metrics collection start time
     */
    private final LocalDateTime metricsStartTime;
    
    /**
     * Last metrics update time
     */
    private final LocalDateTime lastUpdated;
    
    /**
     * Gets the success rate as a percentage
     */
    public double getSuccessRate() {
        if (totalOperations == 0) {
            return 1.0;
        }
        return (double) successfulOperations / totalOperations;
    }
    
    /**
     * Gets the failure rate as a percentage
     */
    public double getFailureRate() {
        return 1.0 - getSuccessRate();
    }
    
    /**
     * Gets the storage efficiency (stored events / total events)
     */
    public double getStorageEfficiency() {
        if (totalLogEvents == 0) {
            return 1.0;
        }
        return (double) storedLogEvents / totalLogEvents;
    }
    
    /**
     * Creates empty metrics for a new strategy
     */
    public static LogStorageMetrics empty() {
        LocalDateTime now = LocalDateTime.now();
        return LogStorageMetrics.builder()
                .totalOperations(0)
                .successfulOperations(0)
                .failedOperations(0)
                .totalLogEvents(0)
                .storedLogEvents(0)
                .averageDurationMs(0.0)
                .minDurationMs(0)
                .maxDurationMs(0)
                .averageThroughput(0.0)
                .available(true)
                .metricsStartTime(now)
                .lastUpdated(now)
                .build();
    }
}
