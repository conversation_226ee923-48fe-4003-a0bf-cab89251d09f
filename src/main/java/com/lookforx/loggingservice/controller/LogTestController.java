package com.lookforx.loggingservice.controller;

import com.lookforx.loggingservice.service.LogStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/logs")
@RequiredArgsConstructor
@Slf4j
public class LogTestController {

    private final MongoTemplate mongoTemplate;
    private final LogStorageService logStorageService;

    @GetMapping("/test/count")
    public ResponseEntity<Map<String, Object>> getLogCounts() {
        try {
            Map<String, Object> response = new HashMap<>();
            
            // Count documents in each collection
            long applicationLogsCount = mongoTemplate.count(new Query(), "application_logs");
            long errorLogsCount = mongoTemplate.count(new Query(), "error_logs");
            long auditLogsCount = mongoTemplate.count(new Query(), "audit_logs");
            long performanceLogsCount = mongoTemplate.count(new Query(), "performance_logs");
            long securityLogsCount = mongoTemplate.count(new Query(), "security_logs");
            
            response.put("application_logs_count", applicationLogsCount);
            response.put("error_logs_count", errorLogsCount);
            response.put("audit_logs_count", auditLogsCount);
            response.put("performance_logs_count", performanceLogsCount);
            response.put("security_logs_count", securityLogsCount);
            response.put("total_logs", applicationLogsCount + errorLogsCount + auditLogsCount + performanceLogsCount + securityLogsCount);
            
            // Get collection names
            response.put("collections", mongoTemplate.getCollectionNames());
            
            log.info("Log counts retrieved: {}", response);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to get log counts", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to get log counts: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }

    @GetMapping("/test/recent")
    public ResponseEntity<Map<String, Object>> getRecentLogs() {
        try {
            Map<String, Object> response = new HashMap<>();
            
            // Get recent logs from each collection
            Query recentQuery = new Query().limit(5);
            
            response.put("recent_application_logs", mongoTemplate.find(recentQuery, Object.class, "application_logs"));
            response.put("recent_error_logs", mongoTemplate.find(recentQuery, Object.class, "error_logs"));
            
            log.info("Recent logs retrieved");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to get recent logs", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to get recent logs: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
