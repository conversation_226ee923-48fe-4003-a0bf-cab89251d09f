package com.lookforx.loggingservice.factory.impl;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.domain.LogEventType;
import com.lookforx.loggingservice.exception.LogEventCreationException;
import com.lookforx.loggingservice.factory.LogEventFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Factory implementation for creating Application log events.
 * 
 * Handles standard application logging events (INFO, DEBUG, WARN, ERROR)
 * with proper validation and enrichment.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@Slf4j
@Component
public class ApplicationLogEventFactory implements LogEventFactory {
    
    private static final LogEventType SUPPORTED_TYPE = LogEventType.APPLICATION;
    private static final int PRIORITY = 1;
    
    // Valid log levels for application logs
    private static final String[] VALID_LEVELS = {"TRACE", "DEBUG", "INFO", "WARN", "ERROR", "FATAL"};
    
    @Override
    public LogEventType getSupportedType() {
        return SUPPORTED_TYPE;
    }
    
    @Override
    public int getPriority() {
        return PRIORITY;
    }
    
    @Override
    public boolean canCreate(LogEventType type) {
        return SUPPORTED_TYPE == type;
    }
    
    @Override
    public boolean canCreateFromRawData(Map<String, Object> rawData) {
        if (rawData == null) {
            return false;
        }
        
        // Check if this looks like application log data
        Object loggerName = rawData.get("loggerName");
        Object level = rawData.get("level");
        Object message = rawData.get("message");
        
        // Must have basic required fields
        if (message == null) {
            return false;
        }
        
        // If logger name suggests a different type, let that factory handle it
        if (loggerName != null) {
            LogEventType detectedType = LogEventType.fromLoggerName(loggerName.toString());
            if (detectedType != LogEventType.APPLICATION) {
                return false;
            }
        }
        
        // If level suggests error type, let error factory handle it
        if (level != null) {
            LogEventType detectedType = LogEventType.fromLogLevel(level.toString());
            if (detectedType == LogEventType.ERROR) {
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    public LogEvent createLogEvent(String message, String level, String loggerName, 
                                  LocalDateTime timestamp, Map<String, Object> customFields) {
        
        // Validate required fields
        validateRequiredFields(message, level);
        
        // Create the log event using the builder pattern from common module
        LogEvent logEvent = LogEvent.builder()
                .eventId(generateEventId())
                .message(message)
                .level(normalizeLevel(level))
                .logger(loggerName != null ? loggerName : "application")
                .timestamp(timestamp != null ? timestamp : LocalDateTime.now())
                .serviceName("logging-service")
                .build();
        
        // Set custom fields by updating metadata
        Map<String, Object> allCustomFields = new HashMap<>(getDefaultCustomFields());
        if (customFields != null) {
            allCustomFields.putAll(customFields);
        }
        logEvent = LogEvent.builder()
                .eventId(logEvent.getEventId())
                .timestamp(logEvent.getTimestamp())
                .serviceName(logEvent.getServiceName())
                .version(logEvent.getVersion())
                .userId(logEvent.getUserId())
                .correlationId(logEvent.getCorrelationId())
                .metadata(allCustomFields)
                .logServiceName(logEvent.getLogServiceName())
                .logEnvironment(logEvent.getLogEnvironment())
                .logVersion(logEvent.getLogVersion())
                .level(logEvent.getLevel())
                .logger(logEvent.getLogger())
                .message(logEvent.getMessage())
                .thread(logEvent.getThread())
                .traceId(logEvent.getTraceId())
                .spanId(logEvent.getSpanId())
                .sessionId(logEvent.getSessionId())
                .requestId(logEvent.getRequestId())
                .className(logEvent.getClassName())
                .methodName(logEvent.getMethodName())
                .lineNumber(logEvent.getLineNumber())
                .exception(logEvent.getException())
                .stackTrace(logEvent.getStackTrace())
                .host(logEvent.getHost())
                .ip(logEvent.getIp())
                .service(logEvent.getService())
                .userAgent(logEvent.getUserAgent())
                .requestUri(logEvent.getRequestUri())
                .httpMethod(logEvent.getHttpMethod())
                .httpStatus(logEvent.getHttpStatus())
                .duration(logEvent.getDuration())
                .mdc(logEvent.getMdc())
                .build();
        
        // Enrich the log event
        logEvent = enrichLogEvent(logEvent);
        
        // Final validation
        if (!validateLogEvent(logEvent)) {
            throw new LogEventCreationException("Created log event failed validation", 
                                              SUPPORTED_TYPE.getCode(), getValidationRules());
        }
        
        return logEvent;
    }
    
    @Override
    public LogEvent createFromRawData(Map<String, Object> rawData) {
        if (!canCreateFromRawData(rawData)) {
            throw new LogEventCreationException("Cannot create application log event from provided raw data");
        }
        
        try {
            // Extract fields from raw data
            String message = extractString(rawData, "message");
            String level = extractString(rawData, "level");
            String loggerName = extractString(rawData, "loggerName");
            LocalDateTime timestamp = extractTimestamp(rawData, "timestamp");
            
            // Extract custom fields (everything else)
            Map<String, Object> customFields = new HashMap<>(rawData);
            customFields.remove("message");
            customFields.remove("level");
            customFields.remove("loggerName");
            customFields.remove("timestamp");
            
            return createLogEvent(message, level, loggerName, timestamp, customFields);
            
        } catch (Exception e) {
            throw new LogEventCreationException("Failed to create application log event from raw data", e);
        }
    }
    
    @Override
    public LogEvent createSimpleLogEvent(String message, String level) {
        return createLogEvent(message, level, null, null, null);
    }
    
    @Override
    public boolean validateLogEvent(LogEvent logEvent) {
        if (logEvent == null) {
            return false;
        }
        
        // Check required fields
        if (logEvent.getMessage() == null || logEvent.getMessage().trim().isEmpty()) {
            return false;
        }
        
        if (logEvent.getLevel() == null || !isValidLevel(logEvent.getLevel())) {
            return false;
        }
        
        if (logEvent.getTimestamp() == null) {
            return false;
        }
        
        if (logEvent.getEventId() == null || logEvent.getEventId().trim().isEmpty()) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public LogEvent enrichLogEvent(LogEvent logEvent) {
        if (logEvent == null) {
            return null;
        }
        
        // Add application-specific enrichment
        Map<String, Object> customFields = logEvent.getMetadata();
        if (customFields == null) {
            customFields = new HashMap<>();
        } else {
            customFields = new HashMap<>(customFields);
        }
        
        // Add enrichment metadata
        customFields.put("eventType", SUPPORTED_TYPE.getCode());
        customFields.put("enrichedAt", LocalDateTime.now());
        customFields.put("factory", this.getClass().getSimpleName());
        
        // Add thread information if available
        customFields.put("threadName", Thread.currentThread().getName());
        
        // Add hostname if available
        try {
            customFields.put("hostname", java.net.InetAddress.getLocalHost().getHostName());
        } catch (Exception e) {
            // Ignore hostname resolution failures
        }

        // Return new LogEvent with updated metadata
        return LogEvent.builder()
                .eventId(logEvent.getEventId())
                .timestamp(logEvent.getTimestamp())
                .serviceName(logEvent.getServiceName())
                .version(logEvent.getVersion())
                .userId(logEvent.getUserId())
                .correlationId(logEvent.getCorrelationId())
                .metadata(customFields)
                .logServiceName(logEvent.getLogServiceName())
                .logEnvironment(logEvent.getLogEnvironment())
                .logVersion(logEvent.getLogVersion())
                .level(logEvent.getLevel())
                .logger(logEvent.getLogger())
                .message(logEvent.getMessage())
                .thread(logEvent.getThread())
                .traceId(logEvent.getTraceId())
                .spanId(logEvent.getSpanId())
                .sessionId(logEvent.getSessionId())
                .requestId(logEvent.getRequestId())
                .className(logEvent.getClassName())
                .methodName(logEvent.getMethodName())
                .lineNumber(logEvent.getLineNumber())
                .exception(logEvent.getException())
                .stackTrace(logEvent.getStackTrace())
                .host(logEvent.getHost())
                .ip(logEvent.getIp())
                .service(logEvent.getService())
                .userAgent(logEvent.getUserAgent())
                .requestUri(logEvent.getRequestUri())
                .httpMethod(logEvent.getHttpMethod())
                .httpStatus(logEvent.getHttpStatus())
                .duration(logEvent.getDuration())
                .mdc(logEvent.getMdc())
                .build();
    }
    
    @Override
    public Map<String, Object> getDefaultCustomFields() {
        Map<String, Object> defaults = new HashMap<>();
        defaults.put("eventType", SUPPORTED_TYPE.getCode());
        defaults.put("createdAt", LocalDateTime.now());
        defaults.put("version", "1.0");
        return defaults;
    }
    
    @Override
    public String getValidationRules() {
        return "Application log events must have: non-empty message, valid level (TRACE/DEBUG/INFO/WARN/ERROR/FATAL), timestamp, and eventId";
    }
    
    /**
     * Generates a unique event ID
     */
    private String generateEventId() {
        return "app-" + UUID.randomUUID().toString();
    }
    
    /**
     * Validates required fields
     */
    private void validateRequiredFields(String message, String level) {
        if (message == null || message.trim().isEmpty()) {
            throw new LogEventCreationException("Message is required for application log events");
        }
        
        if (level == null || !isValidLevel(level)) {
            throw new LogEventCreationException("Valid level is required for application log events. Valid levels: " + 
                                              String.join(", ", VALID_LEVELS));
        }
    }
    
    /**
     * Normalizes log level to uppercase
     */
    private String normalizeLevel(String level) {
        return level != null ? level.toUpperCase() : "INFO";
    }
    
    /**
     * Checks if the level is valid for application logs
     */
    private boolean isValidLevel(String level) {
        if (level == null) {
            return false;
        }
        
        String upperLevel = level.toUpperCase();
        for (String validLevel : VALID_LEVELS) {
            if (validLevel.equals(upperLevel)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Extracts string value from raw data
     */
    private String extractString(Map<String, Object> rawData, String key) {
        Object value = rawData.get(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * Extracts timestamp from raw data
     */
    private LocalDateTime extractTimestamp(Map<String, Object> rawData, String key) {
        Object value = rawData.get(key);
        if (value == null) {
            return LocalDateTime.now();
        }
        
        if (value instanceof LocalDateTime) {
            return (LocalDateTime) value;
        }
        
        if (value instanceof String) {
            try {
                return LocalDateTime.parse((String) value);
            } catch (Exception e) {
                log.warn("Failed to parse timestamp: {}", value);
                return LocalDateTime.now();
            }
        }
        
        return LocalDateTime.now();
    }
}
