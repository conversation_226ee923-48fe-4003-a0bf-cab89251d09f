package com.lookforx.loggingservice.factory;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.domain.LogEventType;
import com.lookforx.loggingservice.exception.LogEventCreationException;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Factory interface for creating different types of log events.
 * 
 * Defines the contract for log event creation with proper validation,
 * enrichment, and type-specific processing.
 * 
 * Following the Factory Pattern from Gang of Four design patterns.
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
public interface LogEventFactory {
    
    /**
     * Gets the log event type this factory handles.
     * 
     * @return the log event type
     */
    LogEventType getSupportedType();
    
    /**
     * Gets the priority of this factory.
     * Lower numbers indicate higher priority.
     * 
     * @return the priority value (0 = highest priority)
     */
    int getPriority();
    
    /**
     * Checks if this factory can create the specified log event type.
     * 
     * @param type the log event type to check
     * @return true if this factory can handle the type, false otherwise
     */
    boolean canCreate(LogEventType type);
    
    /**
     * Checks if this factory can create a log event from the given raw data.
     * 
     * @param rawData the raw log data
     * @return true if this factory can process the data, false otherwise
     */
    boolean canCreateFromRawData(Map<String, Object> rawData);
    
    /**
     * Creates a log event of the supported type.
     * 
     * @param message the log message
     * @param level the log level
     * @param loggerName the logger name
     * @param timestamp the log timestamp
     * @param customFields additional custom fields
     * @return the created log event
     * @throws LogEventCreationException if creation fails
     */
    LogEvent createLogEvent(String message, String level, String loggerName, 
                           LocalDateTime timestamp, Map<String, Object> customFields);
    
    /**
     * Creates a log event from raw data (e.g., from Kafka message).
     * 
     * @param rawData the raw log data
     * @return the created log event
     * @throws LogEventCreationException if creation fails
     */
    LogEvent createFromRawData(Map<String, Object> rawData);
    
    /**
     * Creates a log event with minimal required information.
     * Other fields will be populated with defaults.
     * 
     * @param message the log message
     * @param level the log level
     * @return the created log event
     * @throws LogEventCreationException if creation fails
     */
    LogEvent createSimpleLogEvent(String message, String level);
    
    /**
     * Validates a log event created by this factory.
     * 
     * @param logEvent the log event to validate
     * @return true if valid, false otherwise
     */
    boolean validateLogEvent(LogEvent logEvent);
    
    /**
     * Enriches a log event with type-specific information.
     * 
     * @param logEvent the log event to enrich
     * @return the enriched log event
     */
    LogEvent enrichLogEvent(LogEvent logEvent);
    
    /**
     * Gets the default custom fields for this log event type.
     * 
     * @return map of default custom fields
     */
    Map<String, Object> getDefaultCustomFields();
    
    /**
     * Gets validation rules for this log event type.
     * 
     * @return validation rules description
     */
    String getValidationRules();
}
