package com.lookforx.eurekaserver;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.netflix.eureka.server.EnableEurekaServer;

/**
 * Eureka Server Application.
 *
 * <p>Bootstraps a Spring Boot application configured as a
 * Eureka service registry, enabling other microservices to
 * register and discover each other.</p>
 *
 */
@SpringBootApplication
@EnableEurekaServer
public class EurekaApplication {

    /**
     * Starts the Spring Boot application.
     *
     * @param args command-line arguments (ignored by default)
     */
    public static void main(String[] args) {
        SpringApplication.run(EurekaApplication.class, args);
    }

}