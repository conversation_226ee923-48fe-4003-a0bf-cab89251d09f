package com.lookforx.apigateway.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.reactive.config.CorsRegistry;
import org.springframework.web.reactive.config.WebFluxConfigurer;

import static org.springframework.http.HttpMethod.*;

/**
 * Global CORS configuration for development, test, and UAT environments.
 * <p>
 * This class allows any origin to invoke standard HTTP methods
 * (GET, POST, PUT, DELETE, OPTIONS) against all endpoints.
 * It is enabled only when the active Spring profile is one of
 * {@code dev}, {@code test}, or {@code uat}.
 * </p>
 */
@Configuration
@Profile({ "dev", "test", "uat" })
public class WebConfig implements WebFluxConfigurer {

    /**
     * Customize the CORS mappings for all paths.
     *
     * @param registry the registry to which CORS mappings are added
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                // allow all origins in non-prod
                .allowedOrigins("http://localhost:3000", "http://************:3000")
                // use enum names to avoid typos
                .allowedMethods(
                        GET.name(),
                        POST.name(),
                        PUT.name(),
                        DELETE.name(),
                        OPTIONS.name()
                )
                .allowedHeaders("*")
                .allowCredentials(true);
    }

}