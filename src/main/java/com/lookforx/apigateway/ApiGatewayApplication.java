package com.lookforx.apigateway;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 *   API Gateway application.
 *   Bootstraps the Spring context and starts the embedded server.
 */
@SpringBootApplication
public class ApiGatewayApplication {

    /**
     * Starts the Spring Boot application.
     *
     * @param args command-line arguments (ignored by default)
     */
    public static void main(String[] args) {
        SpringApplication.run(ApiGatewayApplication.class, args);
    }

}