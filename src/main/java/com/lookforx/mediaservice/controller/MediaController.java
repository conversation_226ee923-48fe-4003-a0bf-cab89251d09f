package com.lookforx.mediaservice.controller;

import com.lookforx.mediaservice.service.MediaService;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.file.Paths;

/**
 * REST controller for handling media operations (upload, download, update, delete)
 * in Cloudflare R2 storage.
 * <p>
 * Exposes endpoints under {@code /api/media} to manage files through the {@link MediaService}.
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/media")
public class MediaController {

    private final MediaService mediaService;

    /**
     * Upload a file to Cloudflare R2.
     *
     * @param file the multipart file to upload
     * @return 200 OK with the generated object key (e.g. "images/{uuid}-filename.jpg")
     */
    @PostMapping("/upload")
    public ResponseEntity<String> uploadImage(@RequestParam("file") MultipartFile file) {
        String key = mediaService.uploadFile(file);
        return ResponseEntity.ok(key);
    }

    /**
     * Download a file from Cloudflare R2.
     *
     * @param key the object key of the file to download
     * @return 200 OK with the file bytes and appropriate Content-Type and
     *         Content-Disposition headers for attachment download
     */
    @GetMapping("/download")
    public ResponseEntity<ByteArrayResource> download(@RequestParam("key") String key) {
        byte[] data = mediaService.downloadFile(key);
        String contentType = mediaService.getContentType(key);
        ByteArrayResource resource = new ByteArrayResource(data);

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=\"" + Paths.get(key).getFileName() + "\"")
                .body(resource);
    }

    /**
     * Update (replace) an existing file with a new one.
     * Deletes the old object and uploads the provided file.
     *
     * @param key  the object key of the existing file to replace
     * @param file the new multipart file to upload
     * @return 200 OK with the new generated object key
     */
    @PutMapping("/update")
    public ResponseEntity<String> update(@RequestParam("key") String key, @RequestParam("file") MultipartFile file) {
        String newKey = mediaService.updateFile(key, file);
        return ResponseEntity.ok(newKey);
    }

    /**
     * Delete a file from Cloudflare R2.
     *
     * @param key the object key of the file to delete
     * @return 204 No Content on successful deletion
     */
    @DeleteMapping("/delete")
    public ResponseEntity<Void> delete(@RequestParam("key") String key) {
        mediaService.deleteFile(key);
        return ResponseEntity.noContent().build();
    }

}
