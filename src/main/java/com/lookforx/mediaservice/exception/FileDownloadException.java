package com.lookforx.mediaservice.exception;

/**
 * Thrown when downloading a file from remote storage fails.
 */
public class FileDownloadException extends RuntimeException {

    /**
     * Constructs the exception with a detailed message and cause.
     *
     * @param message the detail message
     * @param cause the root cause
     */
    public FileDownloadException(String message, Throwable cause) {
        super(message, cause);
    }

}
