package com.lookforx.mediaservice.exception;

import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.common.dto.ErrorResponse;
import com.lookforx.common.dto.ExceptionMessageResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Stream;

import static com.lookforx.common.util.ExceptionMessageUtil.fetchAndFormat;
import static com.lookforx.common.util.ExceptionMessageUtil.resolveLanguage;

/**
 * Centralized exception handler for media operations.
 * <p>
 * Captures specific media-related exceptions and maps them to appropriate
 * HTTP status codes with a JSON error body.
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<ErrorResponse> handleUnsupportedMediaType(
            HttpMediaTypeNotSupportedException ex,
            HttpServletRequest request
    ) {
        String lang    = resolveLanguage(request);
        // you could pass ex.getContentType().toString() as an arg if your template uses it
        String message = fetchAndFormat("UNSUPPORTED_MEDIA_TYPE", lang);

        ErrorResponse error = ErrorResponse.builder()
                .status(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value())
                .error(HttpStatus.UNSUPPORTED_MEDIA_TYPE.getReasonPhrase())
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();

        return ResponseEntity
                .status(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
                .body(error);
    }

    @ExceptionHandler(FileUploadException.class)
    public ResponseEntity<ErrorResponse> handleFileUploadException(
            FileUploadException ex,
            HttpServletRequest request
    ) {
        String lang    = resolveLanguage(request);
        String message = fetchAndFormat("FILE_UPLOAD_FAILED", lang, ex.getMessage());

        ErrorResponse error = ErrorResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .error("File Upload Failed")
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();

        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(error);
    }

    @ExceptionHandler(FileDeletionException.class)
    public ResponseEntity<ErrorResponse> handleFileDeletionException(
            FileDeletionException ex,
            HttpServletRequest request
    ) {
        String lang    = resolveLanguage(request);
        String message = fetchAndFormat("FILE_DELETION_FAILED", lang, ex.getMessage());

        ErrorResponse error = ErrorResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .error("File Deletion Failed")
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();

        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(error);
    }

    @ExceptionHandler(FileDownloadException.class)
    public ResponseEntity<ErrorResponse> handleFileDownloadException(
            FileDownloadException ex,
            HttpServletRequest request
    ) {
        String lang    = resolveLanguage(request);
        String message = fetchAndFormat("FILE_DOWNLOAD_FAILED", lang, ex.getMessage());

        ErrorResponse error = ErrorResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .error("File Download Failed")
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();

        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(error);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleAllUncaught(
            Exception ex,
            HttpServletRequest request
    ) {
        log.error("Unhandled exception in Media Service", ex);

        String lang    = resolveLanguage(request);
        String message = fetchAndFormat("INTERNAL_SERVER_ERROR", lang);

        ErrorResponse error = ErrorResponse.builder()
                .status(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .error("Internal Server Error")
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();

        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(error);
    }

    /**
     * Handler for our custom UnsupportedMediaTypeException (thrown from MediaService).
     */
    @ExceptionHandler(UnsupportedMediaTypeException.class)
    public ResponseEntity<ErrorResponse> handleCustomUnsupportedMediaType(
            UnsupportedMediaTypeException ex,
            HttpServletRequest request
    ) {
        String lang    = resolveLanguage(request);
        // Use the same template code as the standard HTTP 415 handler
        String message = fetchAndFormat("UNSUPPORTED_MEDIA_TYPE", lang, ex.getMessage());

        ErrorResponse error = ErrorResponse.builder()
                .status(HttpStatus.UNSUPPORTED_MEDIA_TYPE.value())
                .error(HttpStatus.UNSUPPORTED_MEDIA_TYPE.getReasonPhrase())
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();

        return ResponseEntity
                .status(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
                .body(error);
    }

}
