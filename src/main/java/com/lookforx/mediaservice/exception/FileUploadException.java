package com.lookforx.mediaservice.exception;

/**
 * Thrown when a file fails to upload to remote storage.
 * <p>
 * Wraps lower-level I/O or service exceptions.
 */
public class FileUploadException extends RuntimeException {

    /**
     * Constructs the exception with a detailed message.
     *
     * @param message the detail message
     */
    public FileUploadException(String message) {
        super(message);
    }

    /**
     * Constructs the exception with a message and underlying cause.
     *
     * @param message the detail message
     * @param cause the root cause
     */
    public FileUploadException(String message, Throwable cause) {
        super(message, cause);
    }

}
