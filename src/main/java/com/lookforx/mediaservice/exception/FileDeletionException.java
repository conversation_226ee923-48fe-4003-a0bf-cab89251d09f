package com.lookforx.mediaservice.exception;

/**
 * Thrown when deletion of a file from remote storage fails.
 */
public class FileDeletionException extends RuntimeException {

    /**
     * Constructs the exception with a detailed message and cause.
     *
     * @param message the detail message
     * @param cause the root cause
     */
    public FileDeletionException(String message, Throwable cause) {
        super(message, cause);
    }

}
