package com.lookforx.mediaservice.service;

import com.lookforx.mediaservice.exception.FileDeletionException;
import com.lookforx.mediaservice.exception.FileDownloadException;
import com.lookforx.mediaservice.exception.FileUploadException;
import com.lookforx.mediaservice.exception.UnsupportedMediaTypeException;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.IOException;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for uploading, downloading, updating, and deleting files in Cloudflare R2 storage.
 * <p>
 * Encapsulates S3 client interactions and maps file extensions to storage folders.
 */
@Service
@RequiredArgsConstructor
public class MediaService {

    private final S3Client s3Client;

    @Value("${cloudflare.r2.bucket}")
    private String bucket;

    /**
     * Uploads the given multipart file to the configured R2 bucket.
     * <p>
     * Determines the target folder based on file extension, generates a unique key,
     * and stores the file bytes.
     *
     * @param file the multipart file to upload
     * @return the generated object key (e.g. "images/{uuid}-filename.png")
     * @throws UnsupportedMediaTypeException if filename is missing, content type is unknown,
     *         or the file extension is unsupported
     * @throws FileUploadException if an I/O error occurs during upload
     */
    public String uploadFile(MultipartFile file) {

        String originalFilename = Optional.ofNullable(file.getOriginalFilename())
                .orElseThrow(() -> new UnsupportedMediaTypeException("Filename is missing"))
                .toLowerCase();

        String contentType = Optional.ofNullable(file.getContentType())
                .orElseThrow(() -> new UnsupportedMediaTypeException("Content-Type is unknown"));

        String folder = switch (getFileExtension(originalFilename)) {
            case "jpg", "jpeg", "png", "gif"    -> "images";
            case "mp4", "mov"                   -> "videos";
            case "pdf", "doc", "docx", "txt"    -> "documents";
            default -> throw new UnsupportedMediaTypeException("Unsupported file type: " + contentType);
        };

        String key = folder + "/" + UUID.randomUUID() + "-" + originalFilename;

        PutObjectRequest putRequest = PutObjectRequest.builder()
                .bucket(bucket)
                .key(key)
                .contentType(contentType)
                .build();

        try {
            s3Client.putObject(putRequest, RequestBody.fromBytes(file.getBytes()));
        } catch (IOException e) {
            throw new FileUploadException("File upload to Cloudflare R2 failed", e);
        }

        return key;
    }

    /**
     * Replaces an existing file with a new one by deleting the old and uploading the new file.
     *
     * @param existingKey the object key of the file to delete
     * @param file        the new multipart file to upload
     * @return the new generated object key
     * @throws FileDeletionException if deletion of the old file fails
     * @throws UnsupportedMediaTypeException or FileUploadException propagated from uploadFile
     */
    public String updateFile(String existingKey, MultipartFile file) {
        deleteFile(existingKey);
        return uploadFile(file);
    }

    /**
     * Downloads the file bytes for the given object key.
     *
     * @param key the object key to download
     * @return the raw file bytes
     * @throws FileDownloadException if the object does not exist or download fails
     */
    public byte[] downloadFile(String key) {
        try {
            // first, ensure the object exists / fetch its metadata
            s3Client.headObject(HeadObjectRequest.builder()
                    .bucket(bucket)
                    .key(key)
                    .build());

            // then download
            ResponseBytes<GetObjectResponse> resp = s3Client.getObject(
                    GetObjectRequest.builder()
                            .bucket(bucket)
                            .key(key)
                            .build(),
                    ResponseTransformer.toBytes()
            );
            return resp.asByteArray();
        } catch (S3Exception e) {
            throw new FileDownloadException("Failed to download file: " + key, e);
        }
    }

    /**
     * Retrieves the Content-Type metadata of the specified object.
     *
     * @param key the object key whose metadata to fetch
     * @return the stored Content-Type header
     * @throws FileDownloadException if metadata lookup fails
     */
    public String getContentType(String key) {
        try {
            HeadObjectResponse head = s3Client.headObject(
                    HeadObjectRequest.builder()
                            .bucket(bucket)
                            .key(key)
                            .build()
            );
            return head.contentType();
        } catch (S3Exception e) {
            throw new FileDownloadException("Could not retrieve metadata for file: " + key, e);
        }
    }

    /**
     * Deletes the object at the specified key from the bucket.
     *
     * @param key the object key to delete
     * @throws FileDeletionException if deletion fails
     */
    public void deleteFile(String key) {
        try {
            s3Client.deleteObject(DeleteObjectRequest.builder()
                    .bucket(bucket)
                    .key(key)
                    .build()
            );
        } catch (S3Exception e) {
            throw new FileDeletionException("Failed to delete file: " + key, e);
        }
    }

    /**
     * Extracts the file extension from a filename.
     *
     * @param filename the name of the file
     * @return the extension without the dot
     * @throws IllegalArgumentException if no valid extension is found
     */
    private String getFileExtension(String filename) {
        int lastDot = filename.lastIndexOf('.');
        if (lastDot == -1 || lastDot == filename.length() - 1) {
            throw new IllegalArgumentException("Invalid file extension in filename: " + filename);
        }
        return filename.substring(lastDot + 1);
    }

}
