package com.lookforx.mediaservice.config;

import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;

import java.net.URI;

/**
 * Spring configuration for Cloudflare R2 integration.
 * <p>
 * Enables binding of {@link CloudflareProperties} and exposes an
 * {@link S3Client} bean pre-configured for R2 with path-style access,
 * disabled chunked encoding, and static credentials.
 */
@Configuration
@EnableConfigurationProperties(CloudflareProperties.class)
@RequiredArgsConstructor
public class CloudflareR2Config {

    private final CloudflareProperties cloudflareProperties;

    /**
     * Creates an {@link S3Client} configured to communicate with Cloudflare R2.
     * <ul>
     *   <li>Path-style access enabled (required by R2).</li>
     *   <li>Chunked encoding disabled for compatibility.</li>
     *   <li>Uses static AWS credentials from {@link CloudflareProperties}.</li>
     *   <li>Custom endpoint override based on configured R2 URL.</li>
     * </ul>
     *
     * @return a fully configured S3Client for interacting with Cloudflare R2 buckets
     */
    @Bean
    public S3Client s3Client() {

        S3Configuration serviceConfig = S3Configuration.builder()
                // path-style is required for R2
                .pathStyleAccessEnabled(true)
                // disable AWS4 chunked uploads
                .chunkedEncodingEnabled(false)
                .build();

        return S3Client.builder()
                .httpClientBuilder(ApacheHttpClient.builder())
                .region(Region.of("auto"))
                .endpointOverride(URI.create(cloudflareProperties.getEndpoint()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(
                                cloudflareProperties.getAccessKey(),
                                cloudflareProperties.getSecretKey())))
                .serviceConfiguration(serviceConfig)
                .build();

    }

}
