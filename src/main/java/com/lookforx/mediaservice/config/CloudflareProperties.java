package com.lookforx.mediaservice.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Configuration properties for Cloudflare R2 integration.
 * <p>
 * Binds properties with prefix "cloudflare.r2", including:
 * <ul>
 *   <li>{@code endpoint}: the R2 bucket endpoint URL.</li>
 *   <li>{@code accessKey}: the access key ID for authentication.</li>
 *   <li>{@code secretKey}: the secret key for authentication.</li>
 * </ul>
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "cloudflare.r2")
public class CloudflareProperties {
    private String endpoint;
    private String accessKey;
    private String secretKey;
}