package com.lookforx.referencedataservice.service;

import com.lookforx.referencedataservice.config.CacheConfig;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.referencedataservice.domain.ReferenceData;
import com.lookforx.referencedataservice.domain.ReferenceDataType;
import com.lookforx.referencedataservice.dto.ReferenceDataRequest;
import com.lookforx.referencedataservice.dto.ReferenceDataResponse;
import com.lookforx.common.exception.ResourceNotFoundException;
import com.lookforx.referencedataservice.repository.ReferenceDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReferenceDataService {
    
    private final ReferenceDataRepository referenceDataRepository;
    
    /**
     * Tüm referans verileri getirir.
     */
    public List<ReferenceDataResponse> getAllReferenceData() {
        // Fetch all reference data
        List<ReferenceData> entities = getAllReferenceDataEntities();
        return entities.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Tüm referans verileri pagination ve search ile getirir.
     */
    public Page<ReferenceDataResponse> getAllReferenceData(Pageable pageable, String search, ReferenceDataType type) {
        log.debug("Getting paginated reference data with search: {} and type: {}", search, type);

        Page<ReferenceData> entities;
        if (search != null && !search.trim().isEmpty()) {
            if (type != null) {
                entities = referenceDataRepository.findByTypeAndCodeContainingIgnoreCase(type, search.trim(), pageable);
            } else {
                entities = referenceDataRepository.findByCodeContainingIgnoreCase(search.trim(), pageable);
            }
        } else if (type != null) {
            entities = referenceDataRepository.findByType(type, pageable);
        } else {
            entities = referenceDataRepository.findAll(pageable);
        }

        return entities.map(this::mapToResponse);
    }

    /**
     * Tüm referans veri entity'lerini cache'leyerek getirir.
     */
    @Cacheable(value = CacheConfig.ALL_REFERENCE_DATA, key = "'all'")
    public List<ReferenceData> getAllReferenceDataEntities() {
        // Fetch from database
        return referenceDataRepository.findAll();
    }
    
    /**
     * Belirli bir tipteki tüm referans verileri getirir.
     */
    public List<ReferenceDataResponse> getReferenceDataByType(ReferenceDataType type) {
        log.debug("Fetching reference data by type: {}", type);
        List<ReferenceData> entities = getReferenceDataEntitiesByType(type);
        return entities.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Belirli bir tipteki tüm referans veri entity'lerini cache'leyerek getirir.
     */
    @Cacheable(value = CacheConfig.REFERENCE_DATA_BY_TYPE, key = "#type")
    public List<ReferenceData> getReferenceDataEntitiesByType(ReferenceDataType type) {
        log.debug("Fetching reference data entities by type from database: {}", type);
        return referenceDataRepository.findByType(type);
    }
    
    /**
     * Belirli bir tipteki aktif referans verileri getirir.
     */
    public List<ReferenceDataResponse> getActiveReferenceDataByType(ReferenceDataType type) {
        log.debug("Fetching active reference data by type: {}", type);
        List<ReferenceData> entities = getActiveReferenceDataEntitiesByType(type);
        return entities.stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Belirli bir tipteki aktif referans veri entity'lerini cache'leyerek getirir.
     */
    @Cacheable(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE, key = "#type")
    public List<ReferenceData> getActiveReferenceDataEntitiesByType(ReferenceDataType type) {
        log.debug("Fetching active reference data entities by type from database: {}", type);
        return referenceDataRepository.findByTypeAndActiveTrue(type);
    }
    
    /**
     * Belirli bir tipteki referans verileri sayfalı olarak getirir.
     */
    public Page<ReferenceDataResponse> getReferenceDataByType(ReferenceDataType type, Pageable pageable) {
        return referenceDataRepository.findByType(type, pageable)
                .map(this::mapToResponse);
    }
    
    /**
     * Belirli bir tipteki aktif referans verileri sayfalı olarak getirir.
     */
    public Page<ReferenceDataResponse> getActiveReferenceDataByType(ReferenceDataType type, Pageable pageable) {
        return referenceDataRepository.findByTypeAndActiveTrue(type, pageable)
                .map(this::mapToResponse);
    }
    
    /**
     * ID ile referans veri getirir.
     */
    public ReferenceDataResponse getReferenceDataById(Long id) {
        log.debug("Fetching reference data by id: {}", id);
        ReferenceData entity = getReferenceDataEntityById(id);
        return mapToResponse(entity);
    }

    /**
     * ID ile referans veri entity'sini cache'leyerek getirir.
     */
    @Cacheable(value = CacheConfig.REFERENCE_DATA_BY_ID, key = "#id")
    public ReferenceData getReferenceDataEntityById(Long id) {
        log.debug("Fetching reference data entity by id from database: {}", id);
        return referenceDataRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Reference data", id));
    }
    
    /**
     * Tip ve kod ile referans veri getirir.
     */
    @Cacheable(value = CacheConfig.REFERENCE_DATA_BY_TYPE_AND_CODE, key = "#type + '_' + #code")
    public ReferenceDataResponse getReferenceDataByTypeAndCode(ReferenceDataType type, String code) {
        log.debug("Fetching reference data by type: {} and code: {}", type, code);
        return referenceDataRepository.findByTypeAndCode(type, code)
                .map(this::mapToResponse)
                .orElseThrow(() -> new ResourceNotFoundException(
                    String.format("Reference data not found with type: %s and code: %s", type, code)));
    }
    
    /**
     * Tip ve kod ile aktif referans veri getirir.
     */
    public ReferenceDataResponse getActiveReferenceDataByTypeAndCode(ReferenceDataType type, String code) {
        return referenceDataRepository.findByTypeAndCodeAndActiveTrue(type, code)
                .map(this::mapToResponse)
                .orElseThrow(() -> new ResourceNotFoundException(
                    String.format("Active reference data not found with type: %s and code: %s", type, code)));
    }
    
    /**
     * Belirli bir tipteki referans verileri sıralı olarak getirir.
     */
    @Cacheable(value = CacheConfig.REFERENCE_DATA_BY_TYPE_ORDERED, key = "#type")
    public List<ReferenceDataResponse> getReferenceDataByTypeOrdered(ReferenceDataType type) {
        log.debug("Fetching ordered reference data by type: {}", type);
        return referenceDataRepository.findByTypeOrderByDisplayOrderAsc(type).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }
    
    /**
     * Belirli bir tipteki aktif referans verileri sıralı olarak getirir.
     */
    @Cacheable(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE_ORDERED, key = "#type")
    public List<ReferenceDataResponse> getActiveReferenceDataByTypeOrdered(ReferenceDataType type) {
        log.debug("Fetching ordered active reference data by type: {}", type);
        return referenceDataRepository.findByTypeAndActiveTrueOrderByDisplayOrderAsc(type).stream()
                .map(this::mapToResponse)
                .collect(Collectors.toList());
    }
    
    /**
     * Belirli bir dildeki referans veri adını getirir.
     */
    @Cacheable(value = CacheConfig.REFERENCE_DATA_NAME, key = "#type + '_' + #code + '_' + #languageCode")
    public String getReferenceDataName(ReferenceDataType type, String code, LanguageCode languageCode) {
        // Fetch reference data name
        ReferenceData referenceData = referenceDataRepository.findByTypeAndCode(type, code)
                .orElseThrow(() -> new ResourceNotFoundException(
                    String.format("Reference data not found with type: %s and code: %s", type, code)));

        return referenceData.getTranslations().getOrDefault(languageCode,
                referenceData.getTranslations().getOrDefault(LanguageCode.EN, "Unknown"));
    }
    
    /**
     * Yeni referans veri oluşturur.
     */
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = CacheConfig.ALL_REFERENCE_DATA, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_TYPE, key = "#request.type"),
        @CacheEvict(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE, key = "#request.type"),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_TYPE_ORDERED, key = "#request.type"),
        @CacheEvict(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE_ORDERED, key = "#request.type")
    })
    public ReferenceDataResponse createReferenceData(ReferenceDataRequest request) {
        log.info("Creating new reference data with type: {} and code: {}", request.getType(), request.getCode());
        ReferenceData referenceData = new ReferenceData();
        referenceData.setType(request.getType());
        referenceData.setCode(request.getCode());
        referenceData.setTranslations(request.getTranslations());
        referenceData.setPropertiesFromMap(request.getProperties());
        referenceData.setActive(request.isActive());
        referenceData.setDisplayOrder(request.getDisplayOrder());

        ReferenceData saved = referenceDataRepository.save(referenceData);
        return mapToResponse(saved);
    }
    
    /**
     * Mevcut referans veriyi günceller.
     */
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = CacheConfig.ALL_REFERENCE_DATA, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_ID, key = "#id"),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_TYPE, key = "#request.type"),
        @CacheEvict(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE, key = "#request.type"),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_TYPE_ORDERED, key = "#request.type"),
        @CacheEvict(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE_ORDERED, key = "#request.type"),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_TYPE_AND_CODE, key = "#request.type + '_' + #request.code"),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_NAME, allEntries = true)
    })
    public ReferenceDataResponse updateReferenceData(Long id, ReferenceDataRequest request) {
        log.info("Updating reference data with id: {}", id);
        ReferenceData referenceData = referenceDataRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Reference data", id));

        referenceData.setType(request.getType());
        referenceData.setCode(request.getCode());
        referenceData.setTranslations(request.getTranslations());
        referenceData.setPropertiesFromMap(request.getProperties());
        referenceData.setActive(request.isActive());
        referenceData.setDisplayOrder(request.getDisplayOrder());

        ReferenceData updated = referenceDataRepository.save(referenceData);
        return mapToResponse(updated);
    }
    
    /**
     * Referans veriyi aktif/pasif yapar.
     */
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = CacheConfig.ALL_REFERENCE_DATA, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_ID, key = "#id"),
        @CacheEvict(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE, allEntries = true),
        @CacheEvict(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE_ORDERED, allEntries = true)
    })
    public ReferenceDataResponse toggleReferenceDataStatus(Long id) {
        log.info("Toggling status for reference data with id: {}", id);
        ReferenceData referenceData = referenceDataRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Reference data", id));

        referenceData.setActive(!referenceData.isActive());

        ReferenceData updated = referenceDataRepository.save(referenceData);
        return mapToResponse(updated);
    }
    
    /**
     * Referans veriyi siler.
     */
    @Transactional
    @Caching(evict = {
        @CacheEvict(value = CacheConfig.ALL_REFERENCE_DATA, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_ID, key = "#id"),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_TYPE, allEntries = true),
        @CacheEvict(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_TYPE_ORDERED, allEntries = true),
        @CacheEvict(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE_ORDERED, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_TYPE_AND_CODE, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_NAME, allEntries = true)
    })
    public void deleteReferenceData(Long id) {
        log.info("Deleting reference data with id: {}", id);
        if (!referenceDataRepository.existsById(id)) {
            throw new ResourceNotFoundException("Reference data", id);
        }

        referenceDataRepository.deleteById(id);
    }
    
    /**
     * ReferenceData entity'sini ReferenceDataResponse DTO'suna dönüştürür.
     */
    private ReferenceDataResponse mapToResponse(ReferenceData referenceData) {
        return ReferenceDataResponse.builder()
                .id(referenceData.getId())
                .type(referenceData.getType())
                .code(referenceData.getCode())
                .translations(referenceData.getTranslations())
                .properties(referenceData.getPropertiesAsMap())
                .active(referenceData.isActive())
                .displayOrder(referenceData.getDisplayOrder())
                .createdAt(referenceData.getCreatedAt())
                .updatedAt(referenceData.getUpdatedAt())
                .build();
    }

    /**
     * Tüm cache'leri temizler.
     */
    @Caching(evict = {
        @CacheEvict(value = CacheConfig.ALL_REFERENCE_DATA, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_ID, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_TYPE, allEntries = true),
        @CacheEvict(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_TYPE_ORDERED, allEntries = true),
        @CacheEvict(value = CacheConfig.ACTIVE_REFERENCE_DATA_BY_TYPE_ORDERED, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_BY_TYPE_AND_CODE, allEntries = true),
        @CacheEvict(value = CacheConfig.REFERENCE_DATA_NAME, allEntries = true)
    })
    public void clearCache() {
        log.info("All reference data caches cleared");
    }
}