package com.lookforx.referencedataservice.repository;

import com.lookforx.referencedataservice.domain.ReferenceData;
import com.lookforx.referencedataservice.domain.ReferenceDataType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ReferenceDataRepository extends JpaRepository<ReferenceData, Long> {
    
    /**
     * Belirli bir tipteki tüm referans verileri getirir.
     */
    List<ReferenceData> findByType(ReferenceDataType type);
    
    /**
     * Belirli bir tipteki aktif referans verileri getirir.
     */
    List<ReferenceData> findByTypeAndActiveTrue(ReferenceDataType type);

    /**
     * Belirli bir tipteki referans verileri aktif durumuna göre getirir.
     */
    List<ReferenceData> findByTypeAndActive(ReferenceDataType type, boolean active);
    
    /**
     * <PERSON>irli bir tipteki referans verileri sayfalı olarak getirir.
     */
    Page<ReferenceData> findByType(ReferenceDataType type, Pageable pageable);
    
    /**
     * Belirli bir tipteki aktif referans verileri sayfalı olarak getirir.
     */
    Page<ReferenceData> findByTypeAndActiveTrue(ReferenceDataType type, Pageable pageable);
    
    /**
     * Tip ve kod ile referans veri arar.
     */
    Optional<ReferenceData> findByTypeAndCode(ReferenceDataType type, String code);
    
    /**
     * Tip ve kod ile aktif referans veri arar.
     */
    Optional<ReferenceData> findByTypeAndCodeAndActiveTrue(ReferenceDataType type, String code);
    
    /**
     * Belirli bir tipteki referans verileri display_order'a göre sıralı getirir.
     */
    List<ReferenceData> findByTypeOrderByDisplayOrderAsc(ReferenceDataType type);

    /**
     * Belirli bir tipteki referans verileri display_order'a göre sıralı getirir.
     */
    List<ReferenceData> findByTypeOrderByDisplayOrder(ReferenceDataType type);
    
    /**
     * Belirli bir tipteki aktif referans verileri display_order'a göre sıralı getirir.
     */
    List<ReferenceData> findByTypeAndActiveTrueOrderByDisplayOrderAsc(ReferenceDataType type);
    
    /**
     * Belirli bir anahtar-değer çiftine sahip referans verileri getirir.
     * JSON properties alanında arama yapar.
     */
    @Query("SELECT r FROM ReferenceData r WHERE r.properties LIKE CONCAT('%\"', :key, '\":\"', :value, '\"%')")
    List<ReferenceData> findByProperty(@Param("key") String key, @Param("value") String value);

    /**
     * Belirli bir anahtar-değer çiftine sahip aktif referans verileri getirir.
     * JSON properties alanında arama yapar.
     */
    @Query("SELECT r FROM ReferenceData r WHERE r.properties LIKE CONCAT('%\"', :key, '\":\"', :value, '\"%') AND r.active = true")
    List<ReferenceData> findByPropertyAndActiveTrue(@Param("key") String key, @Param("value") String value);

    /**
     * Properties alanında belirli bir anahtarın varlığını kontrol eder.
     */
    @Query("SELECT r FROM ReferenceData r WHERE r.properties LIKE CONCAT('%\"', :key, '\":%')")
    List<ReferenceData> findByPropertyKey(@Param("key") String key);

    /**
     * Properties alanında belirli bir anahtarın varlığını kontrol eder (sadece aktif).
     */
    @Query("SELECT r FROM ReferenceData r WHERE r.properties LIKE CONCAT('%\"', :key, '\":%') AND r.active = true")
    List<ReferenceData> findByPropertyKeyAndActiveTrue(@Param("key") String key);

    /**
     * PostgreSQL JSON fonksiyonları kullanarak property değeri arar.
     * Daha hassas JSON arama için kullanılır.
     */
    @Query(value = "SELECT * FROM reference_data WHERE properties::jsonb ->> :key = :value", nativeQuery = true)
    List<ReferenceData> findByPropertyJsonb(@Param("key") String key, @Param("value") String value);

    /**
     * PostgreSQL JSON fonksiyonları kullanarak property değeri arar (sadece aktif).
     */
    @Query(value = "SELECT * FROM reference_data WHERE properties::jsonb ->> :key = :value AND active = true", nativeQuery = true)
    List<ReferenceData> findByPropertyJsonbAndActiveTrue(@Param("key") String key, @Param("value") String value);

    /**
     * PostgreSQL JSON fonksiyonları kullanarak property anahtarının varlığını kontrol eder.
     */
    @Query(value = "SELECT * FROM reference_data WHERE jsonb_exists(properties::jsonb, ?1)", nativeQuery = true)
    List<ReferenceData> findByPropertyKeyJsonb(String key);

    /**
     * Code'da arama yapar (case insensitive)
     */
    Page<ReferenceData> findByCodeContainingIgnoreCase(String code, Pageable pageable);

    /**
     * Tip ve code'da arama yapar (case insensitive)
     */
    Page<ReferenceData> findByTypeAndCodeContainingIgnoreCase(ReferenceDataType type, String code, Pageable pageable);
}