package com.lookforx.referencedataservice.domain;

/**
 * Referans veri tiplerini tanımlayan genişletilmiş enum.
 */
public enum ReferenceDataType {

    // Coğrafi ve Yerelleştirme
    COUNTRY,        // Ülkeler
    REGION,         // <PERSON><PERSON><PERSON><PERSON> (ülke alt bölgeleri)
    CITY,           // Şehirler
    DISTRICT,       // İlçeler
    ZIP_CODE,       // Posta kodları
    LANGUAGE,       // Diller
    LOCALE,         // <PERSON><PERSON> ayar (dil + ülke kodu)
    CURRENCY,       // Para birimleri
    TIMEZONE,       // Zaman dilimleri

    // Kişisel Demografik Bilgiler
    GENDER,         // Cinsiyet
    MARITAL_STATUS, // Medeni durum
    EDUCATION_LEVEL,// Eğitim seviyesi
    OCCUPATION,     // Meslek
    AGE_GROUP,      // Ya<PERSON> grubu
    INCOME_LEVEL,   // Gelir seviyesi
    BLOOD_TYPE,     // Kan grubu

    // Kullanıcı ve Hesap
    USER_ROLE,          // Kullanıcı rolleri
    ACCOUNT_STATUS,     // Hesap durumu
    ADDRESS_TYPE,       // Adres tipi
    SECURITY_QUESTION,  // Güvenlik soruları
    SUBSCRIPTION_LEVEL, // Üyelik seviyeleri
    VERIFICATION_STATUS,// Doğrulama durumu

    // E-Ticaret ve Ürün
    INDUSTRY,           // Sektör
    PRODUCT_CATEGORY,   // Ürün kategorisi
    PRODUCT_CONDITION,  // Ürün durumu
    UNIT,               // Birim (ölçü birimleri)
    COLOR,              // Renkler
    SIZE,               // Boyutlar
    MATERIAL,           // Malzemeler
    BRAND,              // Marka
    PRODUCT_TAG,        // Ürün etiketleri
    SEASON,             // Sezon (koleksiyon)
    PRODUCT_ORIGIN,     // Üretim yeri
    PRODUCT_USAGE_TYPE, // Kullanım durumu (yeni, ikinci el, yenilenmiş vs.)

    // Sipariş ve Teslimat
    SHIPPING_METHOD,    // Kargo yöntemi
    DELIVERY_STATUS,    // Teslimat durumu
    ORDER_STATUS,       // Sipariş durumu
    RETURN_REASON,      // İade nedeni
    DELIVERY_PRIORITY,  // Teslimat önceliği
    WAREHOUSE_LOCATION, // Depo lokasyonu

    // Ödeme ve Finans
    PAYMENT_METHOD,     // Ödeme yöntemi
    PAYMENT_STATUS,     // Ödeme durumu
    TRANSACTION_TYPE,   // İşlem tipi
    INVOICE_STATUS,     // Fatura durumu
    DISCOUNT_TYPE,      // İndirim tipi
    TAX_TYPE,           // Vergi tipi
    REFUND_STATUS,      // Geri ödeme durumu

    // Sistem ve İletişim
    DOCUMENT_TYPE,      // Belge tipi
    NOTIFICATION_TYPE,  // Bildirim tipi
    LOG_LEVEL,          // Log seviyesi
    JOB_STATUS,         // Görev durumu
    ERROR_CODE,         // Hata kodları
    EMAIL_TEMPLATE_TYPE,// E-posta şablon tipleri
    SMS_TEMPLATE_TYPE,  // SMS şablon tipleri
    TASK_TYPE,          // Arka plan görev tipleri
    API_EVENT_TYPE,     // API olay tipleri

    // İş ve Raporlama
    REPORT_TYPE,        // Rapor türleri
    ANALYTICS_EVENT,    // Analitik olaylar
    KPI_TYPE,           // KPI göstergeleri

    // Lisanslama ve Konfigürasyon
    LICENSE_TYPE,       // Lisans türleri
    FEATURE_FLAG,       // Özellik bayrakları
    ENVIRONMENT,        // Ortam tipleri (dev, staging, prod)

    // Özel Tip
    CUSTOM              // Özel tip (genel amaçlı genişletme)
}
