package com.lookforx.referencedataservice.domain;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.lookforx.common.entity.BaseEntity;
import com.lookforx.common.enums.LanguageCode;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Referans veri entity sınıfı.
 * Çeşitli referans veri tiplerini (ülkeler, diller, para birimleri vb.) temsil eder.
 */
@Entity
@Table(name = "reference_data")
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReferenceData extends BaseEntity {
    
    /**
     * Referans veri tipi (örn. COUNTRY, LANGUAGE, CURRENCY)
     */
    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    private ReferenceDataType type;
    
    /**
     * Referans verinin kodu (örn. "TR", "EN", "USD")
     */
    @Column(name = "code", nullable = false)
    private String code;
    
    /**
     * Referans verinin farklı dillerdeki adları
     */
    @ElementCollection
    @CollectionTable(
            name = "reference_data_translations",
            joinColumns = @JoinColumn(name = "reference_data_id")
    )
    @MapKeyColumn(name = "language_code")
    @MapKeyEnumerated(EnumType.STRING)
    @Column(name = "name")
    @Builder.Default
    private Map<LanguageCode, String> translations = new HashMap<>();
    
    /**
     * Ek özellikler (JSON olarak saklanır)
     */
    @Column(name = "properties", columnDefinition = "TEXT")
    private String properties;
    
    /**
     * Aktif/pasif durumu
     */
    @Column(name = "active")
    @Builder.Default
    private boolean active = true;
    
    /**
     * Sıralama için kullanılabilir
     */
    @Column(name = "display_order")
    private Integer displayOrder;

    // Utility methods for properties JSON handling
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Properties'i Map olarak döndürür
     */
    public Map<String, Object> getPropertiesAsMap() {
        if (properties == null || properties.trim().isEmpty()) {
            return new HashMap<>();
        }
        try {
            return objectMapper.readValue(properties, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    /**
     * Map'i properties JSON string'ine çevirir
     */
    public void setPropertiesFromMap(Map<String, Object> propertiesMap) {
        if (propertiesMap == null || propertiesMap.isEmpty()) {
            this.properties = "{}";
            return;
        }
        try {
            this.properties = objectMapper.writeValueAsString(propertiesMap);
        } catch (Exception e) {
            this.properties = "{}";
        }
    }
}