package com.lookforx.referencedataservice.dto;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.referencedataservice.domain.ReferenceDataType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Referans veri bilgilerini istemciye döndürmek için kullanılan DTO.
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class ReferenceDataResponse {
    
    /**
     * Referans verinin benzersiz tanımlayıcısı
     */
    private Long id;
    
    /**
     * Referans veri tipi (örn. COUNTRY, LANGUAGE, CURRENCY)
     */
    private ReferenceDataType type;
    
    /**
     * Referans verinin kodu (örn. "TR", "EN", "USD")
     */
    private String code;
    
    /**
     * Referans verinin farklı dillerdeki adları
     */
    private Map<LanguageCode, String> translations;
    
    /**
     * Ek özellikler
     */
    private Map<String, Object> properties;
    
    /**
     * Aktif/pasif durumu
     */
    private boolean active;
    
    /**
     * Sıralama için kullanılabilir
     */
    private Integer displayOrder;
    
    /**
     * Oluşturulma tarihi
     */
    private LocalDateTime createdAt;
    
    /**
     * Son güncelleme tarihi
     */
    private LocalDateTime updatedAt;
}