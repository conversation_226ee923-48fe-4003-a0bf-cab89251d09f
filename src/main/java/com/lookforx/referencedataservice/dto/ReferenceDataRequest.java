package com.lookforx.referencedataservice.dto;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.referencedataservice.domain.ReferenceDataType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Referans veri oluşturma veya güncelleme için kullanılan DTO.
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class ReferenceDataRequest {
    
    /**
     * Referans veri tipi (örn. COUNTRY, LANGUAGE, CURRENCY)
     */
    @NotNull(message = "Referans veri tipi belirtilmelidir")
    private ReferenceDataType type;
    
    /**
     * Referans verinin kodu (örn. "TR", "EN", "USD")
     */
    @NotBlank(message = "Referans veri kodu belirtilmelidir")
    private String code;
    
    /**
     * Referans verinin farklı dillerdeki adları
     */
    @NotEmpty(message = "En az bir dil için ad belirtilmelidir")
    @Builder.Default
    private Map<LanguageCode, String> translations = new HashMap<>();

    /**
     * Ek özellikler
     */
    @Builder.Default
    private Map<String, Object> properties = new HashMap<>();

    /**
     * Aktif/pasif durumu
     */
    @Builder.Default
    private boolean active = true;
    
    /**
     * Sıralama için kullanılabilir
     */
    private Integer displayOrder;
}