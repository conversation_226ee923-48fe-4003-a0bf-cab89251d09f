package com.lookforx.referencedataservice.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * Cache management controller for monitoring and clearing caches
 */
@RestController
@RequestMapping("/api/v1/cache")
@RequiredArgsConstructor
@Slf4j
public class CacheController {

    private final CacheManager cacheManager;

    /**
     * Get all cache names and their statistics
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getCacheInfo() {
        Map<String, Object> cacheInfo = new HashMap<>();
        
        Collection<String> cacheNames = cacheManager.getCacheNames();
        cacheInfo.put("cacheNames", cacheNames);
        cacheInfo.put("totalCaches", cacheNames.size());
        
        Map<String, Object> cacheDetails = new HashMap<>();
        for (String cacheName : cacheNames) {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                Map<String, Object> details = new HashMap<>();
                details.put("name", cacheName);
                details.put("nativeCache", cache.getNativeCache().getClass().getSimpleName());
                cacheDetails.put(cacheName, details);
            }
        }
        cacheInfo.put("cacheDetails", cacheDetails);
        
        return ResponseEntity.ok(cacheInfo);
    }

    /**
     * Clear all caches
     */
    @DeleteMapping("/clear-all")
    public ResponseEntity<Map<String, Object>> clearAllCaches() {
        log.info("Clearing all caches");
        
        Collection<String> cacheNames = cacheManager.getCacheNames();
        int clearedCount = 0;
        
        for (String cacheName : cacheNames) {
            org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                clearedCount++;
                log.debug("Cleared cache: {}", cacheName);
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "All caches cleared successfully");
        result.put("clearedCaches", clearedCount);
        result.put("cacheNames", cacheNames);
        
        return ResponseEntity.ok(result);
    }

    /**
     * Clear specific cache
     */
    @DeleteMapping("/clear/{cacheName}")
    public ResponseEntity<Map<String, Object>> clearCache(@PathVariable String cacheName) {
        log.info("Clearing cache: {}", cacheName);
        
        org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
        if (cache == null) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Cache not found: " + cacheName);
            error.put("availableCaches", cacheManager.getCacheNames());
            return ResponseEntity.notFound().build();
        }
        
        cache.clear();
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "Cache cleared successfully");
        result.put("cacheName", cacheName);
        
        return ResponseEntity.ok(result);
    }

    /**
     * Evict specific cache entry
     */
    @DeleteMapping("/evict/{cacheName}/{key}")
    public ResponseEntity<Map<String, Object>> evictCacheEntry(
            @PathVariable String cacheName, 
            @PathVariable String key) {
        log.info("Evicting cache entry - cache: {}, key: {}", cacheName, key);
        
        org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
        if (cache == null) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Cache not found: " + cacheName);
            return ResponseEntity.notFound().build();
        }
        
        cache.evict(key);
        
        Map<String, Object> result = new HashMap<>();
        result.put("message", "Cache entry evicted successfully");
        result.put("cacheName", cacheName);
        result.put("key", key);
        
        return ResponseEntity.ok(result);
    }

    /**
     * Get cache entry
     */
    @GetMapping("/get/{cacheName}/{key}")
    public ResponseEntity<Map<String, Object>> getCacheEntry(
            @PathVariable String cacheName, 
            @PathVariable String key) {
        
        org.springframework.cache.Cache cache = cacheManager.getCache(cacheName);
        if (cache == null) {
            Map<String, Object> error = new HashMap<>();
            error.put("error", "Cache not found: " + cacheName);
            return ResponseEntity.notFound().build();
        }
        
        org.springframework.cache.Cache.ValueWrapper valueWrapper = cache.get(key);
        
        Map<String, Object> result = new HashMap<>();
        result.put("cacheName", cacheName);
        result.put("key", key);
        result.put("exists", valueWrapper != null);
        
        if (valueWrapper != null) {
            result.put("value", valueWrapper.get());
        }
        
        return ResponseEntity.ok(result);
    }
}
