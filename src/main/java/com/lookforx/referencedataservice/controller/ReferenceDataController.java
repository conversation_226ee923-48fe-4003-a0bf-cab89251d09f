package com.lookforx.referencedataservice.controller;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.referencedataservice.domain.ReferenceDataType;
import com.lookforx.referencedataservice.dto.ReferenceDataRequest;
import com.lookforx.referencedataservice.dto.ReferenceDataResponse;
import com.lookforx.referencedataservice.service.ReferenceDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/reference-data")
@RequiredArgsConstructor
@Tag(name = "Reference Data", description = "Reference Data API")
public class ReferenceDataController {
    
    private final ReferenceDataService referenceDataService;
    
    @GetMapping
    @Operation(summary = "Get all reference data with pagination and search")
    public ResponseEntity<?> getAllReferenceData(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) ReferenceDataType type,
            @RequestParam(defaultValue = "false") boolean unpaged) {

        if (unpaged) {
            return ResponseEntity.ok(referenceDataService.getAllReferenceData());
        }

        // Create pageable
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        // Get paginated results
        Page<ReferenceDataResponse> result = referenceDataService.getAllReferenceData(pageable, search, type);
        return ResponseEntity.ok(result);
    }
    
    @GetMapping("/type/{type}")
    @Operation(summary = "Get reference data by type")
    public ResponseEntity<List<ReferenceDataResponse>> getReferenceDataByType(
            @PathVariable @Parameter(description = "Reference data type") ReferenceDataType type) {
        return ResponseEntity.ok(referenceDataService.getReferenceDataByType(type));
    }
    
    @GetMapping("/type/{type}/active")
    @Operation(summary = "Get active reference data by type")
    public ResponseEntity<List<ReferenceDataResponse>> getActiveReferenceDataByType(
            @PathVariable @Parameter(description = "Reference data type") ReferenceDataType type) {
        return ResponseEntity.ok(referenceDataService.getActiveReferenceDataByType(type));
    }
    
    @GetMapping("/type/{type}/paged")
    @Operation(summary = "Get reference data by type (paginated)")
    public ResponseEntity<Page<ReferenceDataResponse>> getReferenceDataByTypePaged(
            @PathVariable @Parameter(description = "Reference data type") ReferenceDataType type,
            Pageable pageable) {
        return ResponseEntity.ok(referenceDataService.getReferenceDataByType(type, pageable));
    }
    
    @GetMapping("/type/{type}/active/paged")
    @Operation(summary = "Get active reference data by type (paginated)")
    public ResponseEntity<Page<ReferenceDataResponse>> getActiveReferenceDataByTypePaged(
            @PathVariable @Parameter(description = "Reference data type") ReferenceDataType type,
            Pageable pageable) {
        return ResponseEntity.ok(referenceDataService.getActiveReferenceDataByType(type, pageable));
    }
    
    @GetMapping("/type/{type}/ordered")
    @Operation(summary = "Get reference data by type (ordered by display order)")
    public ResponseEntity<List<ReferenceDataResponse>> getReferenceDataByTypeOrdered(
            @PathVariable @Parameter(description = "Reference data type") ReferenceDataType type) {
        return ResponseEntity.ok(referenceDataService.getReferenceDataByTypeOrdered(type));
    }
    
    @GetMapping("/type/{type}/active/ordered")
    @Operation(summary = "Get active reference data by type (ordered by display order)")
    public ResponseEntity<List<ReferenceDataResponse>> getActiveReferenceDataByTypeOrdered(
            @PathVariable @Parameter(description = "Reference data type") ReferenceDataType type) {
        return ResponseEntity.ok(referenceDataService.getActiveReferenceDataByTypeOrdered(type));
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "Get reference data by ID")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Reference data found"),
            @ApiResponse(responseCode = "404", description = "Reference data not found")
    })
    public ResponseEntity<ReferenceDataResponse> getReferenceDataById(
            @PathVariable @Parameter(description = "Reference data ID") Long id) {
        return ResponseEntity.ok(referenceDataService.getReferenceDataById(id));
    }
    
    @GetMapping("/type/{type}/code/{code}")
    @Operation(summary = "Get reference data by type and code")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Reference data found"),
            @ApiResponse(responseCode = "404", description = "Reference data not found")
    })
    public ResponseEntity<ReferenceDataResponse> getReferenceDataByTypeAndCode(
            @PathVariable @Parameter(description = "Reference data type") ReferenceDataType type,
            @PathVariable @Parameter(description = "Reference data code") String code) {
        return ResponseEntity.ok(referenceDataService.getReferenceDataByTypeAndCode(type, code));
    }
    
    @GetMapping("/type/{type}/code/{code}/active")
    @Operation(summary = "Get active reference data by type and code")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Reference data found"),
            @ApiResponse(responseCode = "404", description = "Reference data not found")
    })
    public ResponseEntity<ReferenceDataResponse> getActiveReferenceDataByTypeAndCode(
            @PathVariable @Parameter(description = "Reference data type") ReferenceDataType type,
            @PathVariable @Parameter(description = "Reference data code") String code) {
        return ResponseEntity.ok(referenceDataService.getActiveReferenceDataByTypeAndCode(type, code));
    }
    
    @GetMapping("/type/{type}/code/{code}/name")
    @Operation(summary = "Get reference data name by type, code and language")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Reference data name found"),
            @ApiResponse(responseCode = "404", description = "Reference data not found")
    })
    public ResponseEntity<String> getReferenceDataName(
            @PathVariable @Parameter(description = "Reference data type") ReferenceDataType type,
            @PathVariable @Parameter(description = "Reference data code") String code,
            @RequestParam @Parameter(description = "Language code") LanguageCode languageCode) {
        return ResponseEntity.ok(referenceDataService.getReferenceDataName(type, code, languageCode));
    }
    
    @PostMapping
    @Operation(summary = "Create new reference data")
    @ApiResponses({
            @ApiResponse(
                    responseCode = "201", 
                    description = "Reference data created",
                    content = @Content(schema = @Schema(implementation = ReferenceDataResponse.class))
            ),
            @ApiResponse(responseCode = "400", description = "Invalid request")
    })
    public ResponseEntity<ReferenceDataResponse> createReferenceData(
            @Valid @RequestBody ReferenceDataRequest request) {
        ReferenceDataResponse response = referenceDataService.createReferenceData(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "Update existing reference data")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Reference data updated"),
            @ApiResponse(responseCode = "400", description = "Invalid request"),
            @ApiResponse(responseCode = "404", description = "Reference data not found")
    })
    public ResponseEntity<ReferenceDataResponse> updateReferenceData(
            @PathVariable @Parameter(description = "Reference data ID") Long id,
            @Valid @RequestBody ReferenceDataRequest request) {
        return ResponseEntity.ok(referenceDataService.updateReferenceData(id, request));
    }
    
    @PatchMapping("/{id}/toggle-status")
    @Operation(summary = "Toggle reference data active status")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Reference data status toggled"),
            @ApiResponse(responseCode = "404", description = "Reference data not found")
    })
    public ResponseEntity<ReferenceDataResponse> toggleReferenceDataStatus(
            @PathVariable @Parameter(description = "Reference data ID") Long id) {
        return ResponseEntity.ok(referenceDataService.toggleReferenceDataStatus(id));
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete reference data")
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "Reference data deleted"),
            @ApiResponse(responseCode = "404", description = "Reference data not found")
    })
    public ResponseEntity<Void> deleteReferenceData(
            @PathVariable @Parameter(description = "Reference data ID") Long id) {
        referenceDataService.deleteReferenceData(id);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/cache/clear")
    @Operation(summary = "Clear reference data cache")
    @ApiResponse(responseCode = "200", description = "Cache cleared successfully")
    public ResponseEntity<String> clearCache() {
        referenceDataService.clearCache();
        return ResponseEntity.ok("Cache cleared successfully");
    }
}