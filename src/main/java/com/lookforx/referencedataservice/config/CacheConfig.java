package com.lookforx.referencedataservice.config;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis Cache Configuration for Reference Data Service
 */
@Configuration
@EnableCaching
public class CacheConfig {

    /**
     * Cache names used in the application
     */
    public static final String REFERENCE_DATA_BY_ID = "referenceDataById";
    public static final String REFERENCE_DATA_BY_TYPE = "referenceDataByType";
    public static final String REFERENCE_DATA_BY_TYPE_AND_CODE = "referenceDataByTypeAndCode";
    public static final String ACTIVE_REFERENCE_DATA_BY_TYPE = "activeReferenceDataByType";
    public static final String REFERENCE_DATA_BY_TYPE_ORDERED = "referenceDataByTypeOrdered";
    public static final String ACTIVE_REFERENCE_DATA_BY_TYPE_ORDERED = "activeReferenceDataByTypeOrdered";
    public static final String REFERENCE_DATA_NAME = "referenceDataName";
    public static final String ALL_REFERENCE_DATA = "allReferenceData";

    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        // Configure ObjectMapper for proper LocalDateTime serialization
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.activateDefaultTyping(
                objectMapper.getPolymorphicTypeValidator(),
                ObjectMapper.DefaultTyping.NON_FINAL,
                JsonTypeInfo.As.PROPERTY
        );

        // Create custom JSON serializer with configured ObjectMapper
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer(objectMapper);

        // Default cache configuration
        RedisCacheConfiguration defaultCacheConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1)) // Default TTL: 1 hour
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(jsonSerializer))
                .disableCachingNullValues();

        // Specific cache configurations
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // Reference data by ID - cache for 2 hours (rarely changes)
        cacheConfigurations.put(REFERENCE_DATA_BY_ID, defaultCacheConfig.entryTtl(Duration.ofHours(2)));
        
        // Reference data by type - cache for 1 hour
        cacheConfigurations.put(REFERENCE_DATA_BY_TYPE, defaultCacheConfig.entryTtl(Duration.ofHours(1)));
        
        // Reference data by type and code - cache for 2 hours (rarely changes)
        cacheConfigurations.put(REFERENCE_DATA_BY_TYPE_AND_CODE, defaultCacheConfig.entryTtl(Duration.ofHours(2)));
        
        // Active reference data by type - cache for 30 minutes (might change more frequently)
        cacheConfigurations.put(ACTIVE_REFERENCE_DATA_BY_TYPE, defaultCacheConfig.entryTtl(Duration.ofMinutes(30)));
        
        // Ordered reference data - cache for 1 hour
        cacheConfigurations.put(REFERENCE_DATA_BY_TYPE_ORDERED, defaultCacheConfig.entryTtl(Duration.ofHours(1)));
        cacheConfigurations.put(ACTIVE_REFERENCE_DATA_BY_TYPE_ORDERED, defaultCacheConfig.entryTtl(Duration.ofMinutes(30)));
        
        // Reference data names - cache for 4 hours (very rarely changes)
        cacheConfigurations.put(REFERENCE_DATA_NAME, defaultCacheConfig.entryTtl(Duration.ofHours(4)));
        
        // All reference data - cache for 15 minutes (used for admin operations)
        cacheConfigurations.put(ALL_REFERENCE_DATA, defaultCacheConfig.entryTtl(Duration.ofMinutes(15)));

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultCacheConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
