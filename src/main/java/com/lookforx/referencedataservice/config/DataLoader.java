package com.lookforx.referencedataservice.config;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.referencedataservice.domain.ReferenceData;
import com.lookforx.referencedataservice.domain.ReferenceDataType;
import com.lookforx.referencedataservice.repository.ReferenceDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Sample data loader for reference data service.
 * Only runs in dev and test profiles.
 */
@Component
@RequiredArgsConstructor
@Slf4j
@Profile({"disabled"}) // Disabled - using data.sql instead
public class DataLoader implements CommandLineRunner {

    private final ReferenceDataRepository referenceDataRepository;

    @Override
    public void run(String... args) throws Exception {
        if (referenceDataRepository.count() == 0) {
            log.info("Loading sample reference data...");
            loadCountries();
            loadLanguages();
            loadCurrencies();
            log.info("Sample reference data loaded successfully!");
        } else {
            log.info("Reference data already exists, skipping data loading.");
        }
    }

    private void loadCountries() {
        log.info("Loading countries...");

        // Turkey
        ReferenceData turkey = ReferenceData.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("TR")
                .translations(Map.of(
                        LanguageCode.EN, "Turkey",
                        LanguageCode.TR, "Türkiye",
                        LanguageCode.DE, "Türkei",
                        LanguageCode.FR, "Turquie",
                        LanguageCode.ES, "Turquía"
                ))
                .active(true)
                .displayOrder(1)
                .build();
        
        Map<String, Object> turkeyProps = new HashMap<>();
        turkeyProps.put("continent", "Europe");
        turkeyProps.put("capital", "Ankara");
        turkeyProps.put("currency", "TRY");
        turkeyProps.put("phoneCode", "+90");
        turkey.setPropertiesFromMap(turkeyProps);
        
        referenceDataRepository.save(turkey);

        // United States
        ReferenceData usa = ReferenceData.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("US")
                .translations(Map.of(
                        LanguageCode.EN, "United States",
                        LanguageCode.TR, "Amerika Birleşik Devletleri",
                        LanguageCode.DE, "Vereinigte Staaten",
                        LanguageCode.FR, "États-Unis",
                        LanguageCode.ES, "Estados Unidos"
                ))
                .active(true)
                .displayOrder(2)
                .build();
        
        Map<String, Object> usaProps = new HashMap<>();
        usaProps.put("continent", "North America");
        usaProps.put("capital", "Washington D.C.");
        usaProps.put("currency", "USD");
        usaProps.put("phoneCode", "+1");
        usa.setPropertiesFromMap(usaProps);
        
        referenceDataRepository.save(usa);

        // Germany
        ReferenceData germany = ReferenceData.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("DE")
                .translations(Map.of(
                        LanguageCode.EN, "Germany",
                        LanguageCode.TR, "Almanya",
                        LanguageCode.DE, "Deutschland",
                        LanguageCode.FR, "Allemagne",
                        LanguageCode.ES, "Alemania"
                ))
                .active(true)
                .displayOrder(3)
                .build();
        
        Map<String, Object> germanyProps = new HashMap<>();
        germanyProps.put("continent", "Europe");
        germanyProps.put("capital", "Berlin");
        germanyProps.put("currency", "EUR");
        germanyProps.put("phoneCode", "+49");
        germany.setPropertiesFromMap(germanyProps);
        
        referenceDataRepository.save(germany);

        // France
        ReferenceData france = ReferenceData.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("FR")
                .translations(Map.of(
                        LanguageCode.EN, "France",
                        LanguageCode.TR, "Fransa",
                        LanguageCode.DE, "Frankreich",
                        LanguageCode.FR, "France",
                        LanguageCode.ES, "Francia"
                ))
                .active(true)
                .displayOrder(4)
                .build();
        
        Map<String, Object> franceProps = new HashMap<>();
        franceProps.put("continent", "Europe");
        franceProps.put("capital", "Paris");
        franceProps.put("currency", "EUR");
        franceProps.put("phoneCode", "+33");
        france.setPropertiesFromMap(franceProps);
        
        referenceDataRepository.save(france);

        log.info("Countries loaded successfully!");
    }

    private void loadLanguages() {
        log.info("Loading languages...");

        // English
        ReferenceData english = ReferenceData.builder()
                .type(ReferenceDataType.LANGUAGE)
                .code("EN")
                .translations(Map.of(
                        LanguageCode.EN, "English",
                        LanguageCode.TR, "İngilizce",
                        LanguageCode.DE, "Englisch",
                        LanguageCode.FR, "Anglais",
                        LanguageCode.ES, "Inglés"
                ))
                .active(true)
                .displayOrder(1)
                .build();
        
        Map<String, Object> englishProps = new HashMap<>();
        englishProps.put("nativeName", "English");
        englishProps.put("iso639_1", "en");
        englishProps.put("iso639_2", "eng");
        english.setPropertiesFromMap(englishProps);
        
        referenceDataRepository.save(english);

        // Turkish
        ReferenceData turkish = ReferenceData.builder()
                .type(ReferenceDataType.LANGUAGE)
                .code("TR")
                .translations(Map.of(
                        LanguageCode.EN, "Turkish",
                        LanguageCode.TR, "Türkçe",
                        LanguageCode.DE, "Türkisch",
                        LanguageCode.FR, "Turc",
                        LanguageCode.ES, "Turco"
                ))
                .active(true)
                .displayOrder(2)
                .build();
        
        Map<String, Object> turkishProps = new HashMap<>();
        turkishProps.put("nativeName", "Türkçe");
        turkishProps.put("iso639_1", "tr");
        turkishProps.put("iso639_2", "tur");
        turkish.setPropertiesFromMap(turkishProps);
        
        referenceDataRepository.save(turkish);

        // German
        ReferenceData german = ReferenceData.builder()
                .type(ReferenceDataType.LANGUAGE)
                .code("DE")
                .translations(Map.of(
                        LanguageCode.EN, "German",
                        LanguageCode.TR, "Almanca",
                        LanguageCode.DE, "Deutsch",
                        LanguageCode.FR, "Allemand",
                        LanguageCode.ES, "Alemán"
                ))
                .active(true)
                .displayOrder(3)
                .build();
        
        Map<String, Object> germanProps = new HashMap<>();
        germanProps.put("nativeName", "Deutsch");
        germanProps.put("iso639_1", "de");
        germanProps.put("iso639_2", "deu");
        german.setPropertiesFromMap(germanProps);
        
        referenceDataRepository.save(german);

        log.info("Languages loaded successfully!");
    }

    private void loadCurrencies() {
        log.info("Loading currencies...");

        // Turkish Lira
        ReferenceData tryLira = ReferenceData.builder()
                .type(ReferenceDataType.CURRENCY)
                .code("TRY")
                .translations(Map.of(
                        LanguageCode.EN, "Turkish Lira",
                        LanguageCode.TR, "Türk Lirası",
                        LanguageCode.DE, "Türkische Lira",
                        LanguageCode.FR, "Livre turque",
                        LanguageCode.ES, "Lira turca"
                ))
                .active(true)
                .displayOrder(1)
                .build();
        
        Map<String, Object> tryProps = new HashMap<>();
        tryProps.put("symbol", "₺");
        tryProps.put("decimals", 2);
        tryProps.put("country", "TR");
        tryLira.setPropertiesFromMap(tryProps);
        
        referenceDataRepository.save(tryLira);

        // US Dollar
        ReferenceData usd = ReferenceData.builder()
                .type(ReferenceDataType.CURRENCY)
                .code("USD")
                .translations(Map.of(
                        LanguageCode.EN, "US Dollar",
                        LanguageCode.TR, "ABD Doları",
                        LanguageCode.DE, "US-Dollar",
                        LanguageCode.FR, "Dollar américain",
                        LanguageCode.ES, "Dólar estadounidense"
                ))
                .active(true)
                .displayOrder(2)
                .build();
        
        Map<String, Object> usdProps = new HashMap<>();
        usdProps.put("symbol", "$");
        usdProps.put("decimals", 2);
        usdProps.put("country", "US");
        usd.setPropertiesFromMap(usdProps);
        
        referenceDataRepository.save(usd);

        // Euro
        ReferenceData eur = ReferenceData.builder()
                .type(ReferenceDataType.CURRENCY)
                .code("EUR")
                .translations(Map.of(
                        LanguageCode.EN, "Euro",
                        LanguageCode.TR, "Euro",
                        LanguageCode.DE, "Euro",
                        LanguageCode.FR, "Euro",
                        LanguageCode.ES, "Euro"
                ))
                .active(true)
                .displayOrder(3)
                .build();
        
        Map<String, Object> eurProps = new HashMap<>();
        eurProps.put("symbol", "€");
        eurProps.put("decimals", 2);
        eurProps.put("countries", "DE,FR,IT,ES");
        eur.setPropertiesFromMap(eurProps);
        
        referenceDataRepository.save(eur);

        log.info("Currencies loaded successfully!");
    }
}
