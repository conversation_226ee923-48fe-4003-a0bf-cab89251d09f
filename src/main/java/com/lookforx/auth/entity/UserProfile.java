package com.lookforx.auth.entity;

import com.lookforx.auth.enums.UserType;
import com.lookforx.auth.enums.ProfileVisibility;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * User profile entity containing detailed user information
 */
@Document(collection = "user_profiles")
@Getter
@Setter
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class UserProfile {

    @Id
    private String id;

    @Indexed(unique = true)
    @Field("user_id")
    private String userId;  // Reference to User entity

    @Indexed(unique = true)
    @Field("username")
    private String username;

    // Basic User Information (from User entity)
    @Field("first_name")
    private String firstName;

    @Field("last_name")
    private String lastName;

    @Field("email")
    private String email;

    // Basic Profile Information
    @Field("user_type")
    @Builder.Default
    private UserType userType = UserType.REGULAR;

    @Field("birth_date")
    private LocalDate birthDate;

    @Field("nationality")
    private String nationality;

    @Field("preferred_language")
    @Builder.Default
    private String preferredLanguage = "tr";

    @Field("profile_photo_url")
    private String profilePhotoUrl;

    // Contact Information
    @Field("mobile_phone")
    private String mobilePhone;

    @Field("landline_phone")
    private String landlinePhone;

    // Address Information
    @Field("country")
    private String country;

    @Field("city")
    private String city;

    @Field("district")
    private String district;

    @Field("neighborhood")
    private String neighborhood;

    @Field("street")
    private String street;

    @Field("door_number")
    private String doorNumber;

    @Field("postal_code")
    private String postalCode;

    @Field("full_address")
    private String fullAddress;

    // Corporate User Information
    @Field("company_name")
    private String companyName;

    @Field("tax_number")
    private String taxNumber;

    @Field("company_type")
    private String companyType;

    @Field("website")
    private String website;

    @Field("company_address")
    private String companyAddress;

    @Field("company_logo_url")
    private String companyLogoUrl;

    // Privacy Settings
    @Field("profile_visibility")
    @Builder.Default
    private ProfileVisibility profileVisibility = ProfileVisibility.PUBLIC;

    // Profile Completion
    @Field("profile_completed")
    @Builder.Default
    private Boolean profileCompleted = false;

    @Field("completion_percentage")
    @Builder.Default
    private Integer completionPercentage = 0;

    // Audit Fields
    @CreatedDate
    @Field("created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Field("updated_at")
    private LocalDateTime updatedAt;

    /**
     * Calculate profile completion percentage
     */
    public void calculateCompletionPercentage() {
        int totalFields = 0;
        int completedFields = 0;

        // Basic fields (always counted)
        totalFields += 6; // username, userType, birthDate, nationality, preferredLanguage, mobilePhone
        if (username != null && !username.trim().isEmpty()) completedFields++;
        if (userType != null) completedFields++;
        if (birthDate != null) completedFields++;
        if (nationality != null && !nationality.trim().isEmpty()) completedFields++;
        if (preferredLanguage != null && !preferredLanguage.trim().isEmpty()) completedFields++;
        if (mobilePhone != null && !mobilePhone.trim().isEmpty()) completedFields++;

        // Address fields
        totalFields += 4; // country, city, district, fullAddress
        if (country != null && !country.trim().isEmpty()) completedFields++;
        if (city != null && !city.trim().isEmpty()) completedFields++;
        if (district != null && !district.trim().isEmpty()) completedFields++;
        if (fullAddress != null && !fullAddress.trim().isEmpty()) completedFields++;

        // Corporate fields (only if user is corporate)
        if (userType == UserType.COMPANY) {
            totalFields += 3; // companyName, taxNumber, companyAddress
            if (companyName != null && !companyName.trim().isEmpty()) completedFields++;
            if (taxNumber != null && !taxNumber.trim().isEmpty()) completedFields++;
            if (companyAddress != null && !companyAddress.trim().isEmpty()) completedFields++;
        }

        // Optional fields (counted but not required for 100%)
        if (profilePhotoUrl != null && !profilePhotoUrl.trim().isEmpty()) completedFields++;
        if (landlinePhone != null && !landlinePhone.trim().isEmpty()) completedFields++;
        if (website != null && !website.trim().isEmpty()) completedFields++;

        this.completionPercentage = totalFields > 0 ? (completedFields * 100) / totalFields : 0;
        this.profileCompleted = this.completionPercentage >= 80; // 80% completion threshold
    }

    /**
     * Get display name based on user type
     */
    public String getDisplayName() {
        if (userType == UserType.COMPANY && companyName != null && !companyName.trim().isEmpty()) {
            return companyName;
        }

        // Try to build full name from firstName and lastName
        if (firstName != null && lastName != null) {
            return firstName + " " + lastName;
        } else if (firstName != null) {
            return firstName;
        } else if (username != null) {
            return username;
        }

        return email; // Fallback to email
    }

    /**
     * Get profile image URL based on user type
     */
    public String getProfileImageUrl() {
        if (userType == UserType.COMPANY && companyLogoUrl != null && !companyLogoUrl.trim().isEmpty()) {
            return companyLogoUrl;
        }
        return profilePhotoUrl;
    }
}
