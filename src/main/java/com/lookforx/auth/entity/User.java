package com.lookforx.auth.entity;

import com.lookforx.auth.dto.UserResponse;
import com.lookforx.auth.enums.AuthProvider;
import com.lookforx.auth.enums.Role;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Entity representing a user in the authentication system.
 * <p>
 * Implements {@link UserDetails} for Spring Security integration and provides
 * role-based authorities.
 */
@Document(collection = "users")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class User implements UserDetails {

    @Id
    private String id;

    @Field("name")
    private String name;

    @Indexed(unique = true)
    @Field("email")
    private String email;

    @Field("password")
    private String password;

    @Field("image_url")
    private String imageUrl;

    @Field("email_verified")
    @Builder.Default
    private Boolean emailVerified = false;

    @Field("provider")
    private AuthProvider provider;

    @Field("roles")
    private Set<Role> roles;

    // String representation of roles for admin operations
    @Field("roles_string")
    private String rolesString;

    @Field("active")
    @Builder.Default
    private Boolean active = true;

    @CreatedDate
    @Field("created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Field("updated_at")
    private LocalDateTime updatedAt;

    @Field("last_login_at")
    private LocalDateTime lastLoginAt;

    // MongoDB auditing will handle createdAt and updatedAt automatically
    // via @CreatedDate and @LastModifiedDate annotations

    // Ensure rolesString is set when roles are present
    public void setRoles(Set<Role> roles) {
        this.roles = roles;
        if (roles != null && !roles.isEmpty()) {
            this.rolesString = roles.stream()
                .map(Role::name)
                .collect(java.util.stream.Collectors.joining(","));
        }
    }

    // Custom getter for active field
    public boolean isActive() {
        return active != null ? active : true;
    }

    // Custom setter for active field
    public void setActive(boolean active) {
        this.active = active;
    }

    /**
     * Converts user roles to Spring Security authorities.
     *
     * @return a collection of granted authorities with "ROLE_" prefix
     */
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return roles.stream()
                .map(role -> new SimpleGrantedAuthority("ROLE_" + role.name()))
                .collect(Collectors.toList());
    }

    /**
     * Returns the username used to authenticate the user, which is the email.
     *
     * @return user's email
     */
    @Override
    public String getUsername() {
        return email;
    }

    /**
     * Indicates whether the user's account has expired.
     * Always returns {@code true} in this implementation.
     *
     * @return {@code true}
     */
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    /**
     * Indicates whether the user's account is locked.
     * Always returns {@code true} in this implementation.
     *
     * @return {@code true}
     */
    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    /**
     * Indicates whether the user's credentials (password) have expired.
     * Always returns {@code true} in this implementation.
     *
     * @return {@code true}
     */
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    /**
     * Indicates whether the user is enabled.
     * Returns the value of the active field.
     *
     * @return {@code true} if user is active, {@code false} otherwise
     */
    @Override
    public boolean isEnabled() {
        return active != null ? active : true;
    }


    public boolean isEmailVerified() {
        return emailVerified != null ? emailVerified : false;
    }

    /**
     * Converts this {@code User} entity into a {@link UserResponse} DTO for API responses.
     *
     * @return a {@link UserResponse} with selected user information
     */
    public UserResponse toUserResponse() {
        Set<String> roleNames = new HashSet<>();

        // Önce roles field'ından al (enum set)
        if (this.roles != null && !this.roles.isEmpty()) {
            roleNames = this.roles.stream()
                    .map(Role::name)
                    .collect(Collectors.toSet());
        }
        // Fallback olarak rolesString'den al
        else if (this.rolesString != null && !this.rolesString.trim().isEmpty()) {
            roleNames = Arrays.stream(this.rolesString.split(","))
                    .map(String::trim)
                    .filter(role -> !role.isEmpty())
                    .collect(Collectors.toSet());
        }

        // Default to USER if no roles found
        if (roleNames.isEmpty()) {
            roleNames.add("USER");
        }

        // Convert user to response DTO

        // Format dates to strings
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

        return UserResponse.builder()
                .id(this.id)
                .name(this.name)
                .email(this.email)
                .imageUrl(this.imageUrl)
                .roles(roleNames)
                .emailVerified(this.emailVerified)
                .active(this.active != null ? this.active : true)
                .createdAt(this.createdAt != null ? this.createdAt.format(formatter) : null)
                .updatedAt(this.updatedAt != null ? this.updatedAt.format(formatter) : null)
                .lastLoginAt(this.lastLoginAt != null ? this.lastLoginAt.format(formatter) : null)
                .build();
    }
} 