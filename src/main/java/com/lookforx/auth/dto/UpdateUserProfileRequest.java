package com.lookforx.auth.dto;

import com.lookforx.auth.enums.UserType;
import com.lookforx.auth.enums.ProfileVisibility;
import jakarta.validation.constraints.*;
import lombok.*;

import java.time.LocalDate;

/**
 * DTO for updating user profile
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateUserProfileRequest {
    
    private String username;

    // Basic User Information
    private String firstName;
    private String lastName;

    private UserType userType;

    // Basic Information
    private LocalDate birthDate;

    private String nationality;

    private String preferredLanguage;

    // Contact Information
    private String mobilePhone;

    private String landlinePhone;
    
    // Address Information
    private String country;
    private String city;
    private String district;
    private String neighborhood;
    private String street;
    private String doorNumber;
    
    @Pattern(regexp = "^$|^\\d{5}$", message = "Postal code must be 5 digits")
    private String postalCode;

    @Size(max = 500, message = "Full address cannot exceed 500 characters")
    private String fullAddress;

    // Corporate Information
    private String companyName;
    private String taxNumber;
    private String companyType;

    @Pattern(regexp = "^$|^(https?://)?(www\\.)?[a-zA-Z0-9-]+\\.[a-zA-Z]{2,}(/.*)?$",
             message = "Invalid website URL format")
    private String website;

    @Size(max = 500, message = "Company address cannot exceed 500 characters")
    private String companyAddress;
    
    // Privacy Settings
    private ProfileVisibility profileVisibility;
}
