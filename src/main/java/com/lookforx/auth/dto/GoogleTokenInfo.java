package com.lookforx.auth.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

/**
 * DTO representing parsed token information returned by Google OAuth2.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GoogleTokenInfo {
    private String email;
    private String name;
    private String picture;
    private String sub;
    
    @JsonProperty("email_verified")
    private boolean emailVerified;
    
    private String locale;
    private String givenName;
    private String familyName;
} 