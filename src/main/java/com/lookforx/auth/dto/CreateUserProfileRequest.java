package com.lookforx.auth.dto;

import com.lookforx.auth.enums.UserType;
import com.lookforx.auth.enums.ProfileVisibility;
import jakarta.validation.constraints.*;
import lombok.*;

import java.time.LocalDate;

/**
 * DTO for creating user profile
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateUserProfileRequest {
    
    @Pattern(regexp = "^$|^[a-zA-Z0-9_]{3,30}$", message = "Username must be 3-30 characters with letters, numbers, and underscores only")
    private String username;

    // Basic User Information
    private String firstName;
    private String lastName;

    private UserType userType;

    // Basic Information
    @Past(message = "Birth date must be in the past")
    private LocalDate birthDate;

    private String nationality;

    @Pattern(regexp = "^$|^(tr|en|de|fr|es)$", message = "Preferred language must be one of: tr, en, de, fr, es")
    private String preferredLanguage;

    // Contact Information
    @Pattern(regexp = "^$|^\\+?[1-9]\\d{1,14}$", message = "Invalid mobile phone format")
    private String mobilePhone;

    @Pattern(regexp = "^$|^\\+?[1-9]\\d{1,14}$", message = "Invalid landline phone format")
    private String landlinePhone;
    
    // Address Information
    private String country;
    private String city;
    private String district;
    private String neighborhood;
    private String street;
    private String doorNumber;
    
    @Pattern(regexp = "^$|^\\d{5}$", message = "Postal code must be 5 digits")
    private String postalCode;

    @Size(max = 500, message = "Full address cannot exceed 500 characters")
    private String fullAddress;

    // Corporate Information
    private String companyName;
    private String taxNumber;
    private String companyType;

    @Pattern(regexp = "^$|^(https?://)?(www\\.)?[a-zA-Z0-9-]+\\.[a-zA-Z]{2,}(/.*)?$",
             message = "Invalid website URL format")
    private String website;

    @Size(max = 500, message = "Company address cannot exceed 500 characters")
    private String companyAddress;
    
    // Privacy Settings
    private ProfileVisibility profileVisibility;
}
