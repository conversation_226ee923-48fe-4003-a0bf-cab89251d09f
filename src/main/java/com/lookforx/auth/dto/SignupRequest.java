package com.lookforx.auth.dto;

import com.lookforx.common.annotations.PasswordValidation;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

/**
 * DTO representing the signup request with basic user registration information.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SignupRequest {
    @NotBlank(message = "Email boş olamaz")
    @Email(message = "Geçerli bir email adresi giriniz")
    private String email;

    @NotBlank(message = "<PERSON><PERSON><PERSON> boş olamaz")
    @PasswordValidation
    private String password;

    @NotBlank(message = "<PERSON>sim boş olamaz")
    private String name;
} 