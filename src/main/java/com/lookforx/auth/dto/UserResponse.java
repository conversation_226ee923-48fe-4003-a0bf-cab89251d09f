package com.lookforx.auth.dto;

import lombok.*;

import java.util.Set;

/**
 * DTO representing a user in API responses.
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserResponse {
    private String id;
    private String name;
    private String email;
    private String imageUrl;
    private Set<String> roles;
    private boolean emailVerified;
    private boolean active;
    private String createdAt;
    private String updatedAt;
    private String lastLoginAt;
}