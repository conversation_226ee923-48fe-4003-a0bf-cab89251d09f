package com.lookforx.auth.dto;

import com.lookforx.auth.enums.UserType;
import com.lookforx.auth.enums.ProfileVisibility;
import lombok.*;

import java.time.LocalDate;

/**
 * DTO for user profile responses
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserProfileResponse {
    
    private String id;
    private String userId;
    private String username;

    // Basic User Information
    private String firstName;
    private String lastName;
    private String email;

    // Basic Profile Information
    private UserType userType;
    private LocalDate birthDate;
    private String nationality;
    private String preferredLanguage;
    private String profilePhotoUrl;
    
    // Contact Information
    private String mobilePhone;
    private String landlinePhone;
    
    // Address Information
    private String country;
    private String city;
    private String district;
    private String neighborhood;
    private String street;
    private String doorNumber;
    private String postalCode;
    private String fullAddress;
    
    // Corporate Information
    private String companyName;
    private String taxNumber;
    private String companyType;
    private String website;
    private String companyAddress;
    private String companyLogoUrl;
    
    // Privacy Settings
    private ProfileVisibility profileVisibility;
    
    // Profile Status
    private Boolean profileCompleted;
    private Integer completionPercentage;
    
    // Computed Fields
    private String displayName;
    private String profileImageUrl;
    
    // Timestamps
    private String createdAt;
    private String updatedAt;
}
