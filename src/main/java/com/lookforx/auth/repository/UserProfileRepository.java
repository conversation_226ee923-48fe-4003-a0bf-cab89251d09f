package com.lookforx.auth.repository;

import com.lookforx.auth.entity.UserProfile;
import com.lookforx.auth.enums.UserType;
import com.lookforx.auth.enums.ProfileVisibility;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for UserProfile entity operations
 */
public interface UserProfileRepository extends MongoRepository<UserProfile, String> {

    /**
     * Find user profile by user ID
     */
    Optional<UserProfile> findByUserId(String userId);

    /**
     * Find user profile by username
     */
    Optional<UserProfile> findByUsername(String username);

    /**
     * Check if username exists
     */
    boolean existsByUsername(String username);

    /**
     * Check if username exists for different user
     */
    @Query("{ 'username': ?0, 'userId': { $ne: ?1 } }")
    boolean existsByUsernameAndUserIdNot(String username, String userId);

    /**
     * Find profiles by user type
     */
    List<UserProfile> findByUserType(UserType userType);

    /**
     * Find profiles by visibility
     */
    List<UserProfile> findByProfileVisibility(ProfileVisibility visibility);

    /**
     * Find completed profiles
     */
    List<UserProfile> findByProfileCompletedTrue();

    /**
     * Find profiles by completion percentage range
     */
    @Query("{ 'completionPercentage': { $gte: ?0, $lte: ?1 } }")
    List<UserProfile> findByCompletionPercentageBetween(int minPercentage, int maxPercentage);

    /**
     * Find profiles by country
     */
    List<UserProfile> findByCountry(String country);

    /**
     * Find profiles by city
     */
    List<UserProfile> findByCity(String city);

    /**
     * Find company profiles by company name (case insensitive)
     */
    @Query("{ 'userType': 'COMPANY', 'companyName': { $regex: ?0, $options: 'i' } }")
    List<UserProfile> findCompanyProfilesByCompanyNameContainingIgnoreCase(String companyName);

    /**
     * Search profiles by username or company name (case insensitive)
     */
    @Query("{ $or: [ " +
           "{ 'username': { $regex: ?0, $options: 'i' } }, " +
           "{ 'companyName': { $regex: ?0, $options: 'i' } } " +
           "] }")
    List<UserProfile> searchByUsernameOrCompanyName(String searchTerm);

    /**
     * Find public profiles for discovery
     */
    @Query("{ 'profileVisibility': 'PUBLIC', 'profileCompleted': true }")
    List<UserProfile> findPublicCompletedProfiles();

    /**
     * Count profiles by user type
     */
    long countByUserType(UserType userType);

    /**
     * Count completed profiles
     */
    long countByProfileCompletedTrue();
}
