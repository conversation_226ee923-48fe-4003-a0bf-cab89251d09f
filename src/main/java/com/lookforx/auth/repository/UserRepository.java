package com.lookforx.auth.repository;

import com.lookforx.auth.entity.User;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.Optional;

/**
 * Repository interface for performing CRUD operations and custom queries
 * on the {@link User} entity.
 * <p>
 * Extends {@link JpaRepository} to inherit common persistence methods.
 */
public interface UserRepository extends MongoRepository<User, String> {

    /**
     * Finds a user by their email address.
     *
     * @param email the email to search for
     * @return an {@link Optional} containing the user if found, or empty if not found
     */
    Optional<User> findByEmail(String email);

    /**
     * Checks whether a user exists with the given email address.
     *
     * @param email the email to check for existence
     * @return {@code true} if a user with the email exists, {@code false} otherwise
     */
    boolean existsByEmail(String email);

    /**
     * Count users by active status
     */
    long countByActive(boolean active);

    /**
     * Count users by roles containing specific role
     */
    long countByRolesStringContaining(String role);

    /**
     * Find users by name or email containing search term (case insensitive)
     */
    @Query("{ $or: [ { 'name': { $regex: ?0, $options: 'i' } }, { 'email': { $regex: ?0, $options: 'i' } } ] }")
    java.util.List<User> findByNameOrEmailContainingIgnoreCase(String searchTerm);

    /**
     * Find users by roles string containing specific role
     */
    java.util.List<User> findByRolesStringContaining(String role);

    /**
     * Find users by active status
     */
    java.util.List<User> findByActive(boolean active);

}