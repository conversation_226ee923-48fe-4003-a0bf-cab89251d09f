package com.lookforx.auth.config;

import com.lookforx.auth.security.JwtAuthenticationFilter;
import com.lookforx.auth.security.OAuth2LoginSuccessHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

/**
 * Configuration class for setting up Spring Security in the application.
 * <p>
 * This includes:
 * <ul>
 *     <li>CSRF and CORS settings</li>
 *     <li>Stateless session management</li>
 *     <li>Custom JWT authentication filter</li>
 *     <li>Permitting public routes and securing others</li>
 * </ul>
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthenticationFilter;

    /**
     * Configures the security filter chain for HTTP requests.
     * <p>
     * - Disables CSRF since the app uses tokens<br>
     * - Configures CORS using a custom {@link CorsConfigurationSource}<br>
     * - Sets session management to stateless for REST APIs<br>
     * - Adds a custom JWT filter before the username/password filter<br>
     * - Allows unauthenticated access to selected endpoints
     *
     * @param http the {@link HttpSecurity} to configure
     * @return the configured {@link SecurityFilterChain}
     * @throws Exception if an error occurs while configuring security
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(AbstractHttpConfigurer::disable)
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers(
                                "/",
                                "/error",
                                "/favicon.ico",
                                "/*/*.png",
                                "/*/*.gif",
                                "/*/*.svg",
                                "/*/*.jpg",
                                "/*/*.html",
                                "/*/*.css",
                                "/*/*.js",
                                "/oauth2/**",
                                "/login/**",
                                "/auth/**",
                                "/v1/auth/**",
                                "/v1/oauth2/**",
                                "/v1/test/**",
                                "/api/v1/auth/**",
                                "/api/v1/oauth2/**",
                                "/api/v1/test/**",
                                "/api/oauth2/**"
                        ).permitAll()
                        .anyRequest().authenticated());

        return http.build();
    }

    /**
     * Defines the CORS configuration for the application.
     * <p>
     * - Allows origins like localhost and a specific LAN IP<br>
     * - Permits common HTTP methods<br>
     * - Accepts specific headers and allows credentials<br>
     * - Exposes custom headers for tokens
     *
     * @return a configured {@link CorsConfigurationSource} for CORS handling
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(List.of("http://localhost:3000", "http://************:3000"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Arrays.asList("Authorization", "Content-Type", "X-Requested-With", "Accept", "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"));
        configuration.setAllowCredentials(true);
        configuration.setExposedHeaders(Arrays.asList("Access-Token", "Refresh-Token"));

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

}