package com.lookforx.auth.config;

import com.lookforx.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Configuration class for application-level beans related to authentication and security.
 * <p>
 * This configuration includes beans for:
 * <ul>
 *   <li>{@link UserDetailsService} - for loading user-specific data</li>
 *   <li>{@link AuthenticationProvider} - for authenticating users using a DAO-based provider</li>
 *   <li>{@link AuthenticationManager} - for processing authentication requests</li>
 *   <li>{@link PasswordEncoder} - for encoding and verifying user passwords</li>
 * </ul>
 */
@Configuration
@RequiredArgsConstructor
public class ApplicationConfig {

    private final UserRepository userRepository;

    /**
     * Defines the {@link UserDetailsService} bean used by Spring Security to retrieve user information.
     *
     * @return an implementation of {@link UserDetailsService} that loads users by their email (used as username)
     * @throws UsernameNotFoundException if no user is found with the given email
     */
    @Bean
    public UserDetailsService userDetailsService() {
        return username -> userRepository.findByEmail(username)
                .orElseThrow(() -> new UsernameNotFoundException("User not found"));
    }

    /**
     * Configures and returns an {@link AuthenticationProvider} that uses a {@link DaoAuthenticationProvider}
     * with the custom {@link UserDetailsService} and password encoder.
     *
     * @return an authentication provider for DAO-based authentication
     */
    @Bean
    public AuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService());
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    /**
     * Provides the {@link AuthenticationManager} bean used by Spring Security to handle authentication.
     *
     * @param config the {@link AuthenticationConfiguration} injected by Spring
     * @return an authentication manager instance
     * @throws Exception if an error occurs while obtaining the authentication manager
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();
    }

    /**
     * Provides a {@link PasswordEncoder} bean that uses BCrypt hashing algorithm.
     *
     * @return a {@link BCryptPasswordEncoder} instance for secure password encoding
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

}