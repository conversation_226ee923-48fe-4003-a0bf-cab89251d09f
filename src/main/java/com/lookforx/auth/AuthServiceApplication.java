package com.lookforx.auth;

import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.common.client.SpamDetectionClient;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.core.env.Environment;

/**
 * Entry point for the Auth Service Spring Boot application.
 * <p>
 * This application registers itself with a discovery server (e.g., Eureka)
 * using Spring Cloud's {@link EnableDiscoveryClient}.
 */
@SpringBootApplication(
    exclude = {
        DataSourceAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class,
        JpaRepositoriesAutoConfiguration.class
    },
    scanBasePackages = {
        "com.lookforx.auth",
        "com.lookforx.common"
    }
)
@EnableDiscoveryClient
@EnableFeignClients(basePackageClasses = {ExceptionServiceClient.class, SpamDetectionClient.class})
public class AuthServiceApplication {

    private final Environment environment;

    public AuthServiceApplication(Environment environment) {
        this.environment = environment;
    }

    /**
     * Main method that bootstraps the Spring Boot application.
     *
     * @param args the command-line arguments passed to the application
     */
    public static void main(String[] args) {
        SpringApplication.run(AuthServiceApplication.class, args);
    }

}
