package com.lookforx.auth.exception;

import com.lookforx.common.exception.BaseException;
import com.lookforx.common.exception.BusinessException;
import org.springframework.http.HttpStatus;

/**
 * Custom exception thrown when authentication fails.
 */
public class AuthenticationException extends BusinessException {

    private static final String ERROR_CODE = "AUTHENTICATION_FAILED";

    /**
     * Constructs a new {@code AuthenticationException} with the specified detail message.
     *
     * @param message the detail message explaining the reason for the exception
     */
    public AuthenticationException(String message) {
        super(ERROR_CODE, message, HttpStatus.UNAUTHORIZED);
    }

}