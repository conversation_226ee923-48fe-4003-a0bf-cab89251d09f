package com.lookforx.auth.exception;

import com.lookforx.common.exception.BaseException;
import com.lookforx.common.exception.ResourceNotFoundException;
import org.springframework.http.HttpStatus;

/**
 * Exception thrown when a user is not found.
 */
public class UserNotFoundException extends ResourceNotFoundException {

    private final String identifierName;
    private final String identifierValue;

    /**
     * Private ctor – use one of the static factories below.
     */
    private UserNotFoundException(String identifierName, String identifierValue) {
        super("User", identifierName, identifierValue);
        this.identifierName = identifierName;
        this.identifierValue = identifierValue;
    }

    /** Thrown when lookup by user-ID (as a String) fails. */
    public static UserNotFoundException forId(String userId) {
        return new UserNotFoundException("id", userId);
    }

    /** Thrown when lookup by email fails. */
    public static UserNotFoundException forEmail(String email) {
        return new UserNotFoundException("email", email);
    }

    /** Thrown when lookup by username fails. */
    public static UserNotFoundException forUsername(String username) {
        return new UserNotFoundException("username", username);
    }

    /** The kind of identifier used (id, email, or username). */
    public String getIdentifierName() {
        return identifierName;
    }

    /** The actual value that wasn’t found. */
    public String getIdentifierValue() {
        return identifierValue;
    }

}