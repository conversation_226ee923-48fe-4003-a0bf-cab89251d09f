package com.lookforx.auth.security;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * Handles failures during the OAuth2 login process.
 * Redirects the user back to the frontend login page with an error message.
 */
@Slf4j
@Component
public class OAuth2LoginFailureHandler extends SimpleUrlAuthenticationFailureHandler {

    /**
     * Invoked when OAuth2 login fails.
     *
     * @param request   the incoming {@link HttpServletRequest}
     * @param response  the outgoing {@link HttpServletResponse}
     * @param exception the exception that caused the failure
     * @throws IOException      if an I/O error occurs
     * @throws ServletException if a servlet error occurs
     */
    @Override
    public void onAuthenticationFailure(HttpServletRequest request, 
                                       HttpServletResponse response, 
                                       AuthenticationException exception) throws IOException, ServletException {
        log.error("OAuth2 authentication failed: {}", exception.getMessage(), exception);
        
        // Hata mesajını URL encode edin
        String errorMessage = URLEncoder.encode(exception.getMessage(), StandardCharsets.UTF_8.toString());
        
        // Frontend uygulamanıza hata ile yönlendirme yapın
        String redirectUrl = "http://192.168.1.35:3000/login?error=" + errorMessage;
        getRedirectStrategy().sendRedirect(request, response, redirectUrl);

    }

}