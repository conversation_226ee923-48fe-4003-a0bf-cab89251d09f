package com.lookforx.auth.security;

import com.lookforx.auth.service.JwtService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT authentication filter that intercepts HTTP requests and authenticates users
 * based on JWT tokens found in the Authorization header or cookies.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtService jwtService;
    private final UserDetailsService userDetailsService;

    /**
     * Filters every incoming HTTP request to validate JWT tokens.
     *
     * @param request     the incoming {@link HttpServletRequest}
     * @param response    the outgoing {@link HttpServletResponse}
     * @param filterChain the {@link FilterChain} for continuing the request
     * @throws ServletException if a servlet error occurs
     * @throws IOException      if an I/O error occurs
     */
    @Override
    protected void doFilterInternal(
            HttpServletRequest request,
            HttpServletResponse response,
            FilterChain filterChain) throws ServletException, IOException {
        
        // Login, signup ve OAuth endpoint'leri için filtreyi atla
        String servletPath = request.getServletPath();
        if (servletPath.contains("/v1/auth/login") ||
            servletPath.contains("/v1/auth/signup") ||
            servletPath.contains("/v1/oauth2/") ||
            servletPath.contains("/oauth2/") ||
            servletPath.contains("/v1/test/") ||
            servletPath.contains("/api/v1/auth/") ||
            servletPath.contains("/api/v1/oauth2/") ||
            servletPath.contains("/api/v1/test/") ||
            servletPath.contains("/api/oauth2/")) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // JWT token'ı al
        final String authHeader = request.getHeader("Authorization");
        final String jwt;
        final String userEmail;
        
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            // Cookie'den token'ı kontrol et
            Cookie[] cookies = request.getCookies();
            String tokenFromCookie = null;
            
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    if ("accessToken".equals(cookie.getName())) {
                        tokenFromCookie = cookie.getValue();
                        break;
                    }
                }
            }
            
            if (tokenFromCookie == null) {
                filterChain.doFilter(request, response);
                return;
            }
            
            jwt = tokenFromCookie;
        } else {
            jwt = authHeader.substring(7);
        }
        
        userEmail = jwtService.extractUsername(jwt);
        
        if (userEmail != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            UserDetails userDetails = this.userDetailsService.loadUserByUsername(userEmail);
            
            if (jwtService.isTokenValid(jwt, userDetails)) {
                UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                        userDetails,
                        null,
                        userDetails.getAuthorities()
                );
                
                authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authToken);
            }
        }
        
        filterChain.doFilter(request, response);
    }
} 