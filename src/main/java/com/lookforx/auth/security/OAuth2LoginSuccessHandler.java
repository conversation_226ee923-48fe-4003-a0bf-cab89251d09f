package com.lookforx.auth.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.auth.service.JwtService;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Collections;

/**
 * Handles successful OAuth2 login attempts.
 * Generates JWT tokens and adds them to the response headers.
 */
@Component
@RequiredArgsConstructor
public class OAuth2LoginSuccessHandler extends SimpleUrlAuthenticationSuccessHandler {

    private final JwtService jwtService;
    private final ObjectMapper objectMapper;

    /**
     * Invoked when OAuth2 login is successful.
     *
     * @param request        the incoming {@link HttpServletRequest}
     * @param response       the outgoing {@link HttpServletResponse}
     * @param authentication the successfully authenticated user
     * @throws IOException      if an I/O error occurs
     * @throws ServletException if a servlet error occurs
     */
    @Override
    public void onAuthenticationSuccess(HttpServletRequest request,
                                      HttpServletResponse response,
                                      Authentication authentication) throws IOException, ServletException {
        OAuth2User oAuth2User = (OAuth2User) authentication.getPrincipal();
        
        CustomUserDetails userDetails = new CustomUserDetails();
        userDetails.setEmail(oAuth2User.getAttribute("email"));
        userDetails.setAuthorities(Collections.singletonList(new org.springframework.security.core.authority.SimpleGrantedAuthority("ROLE_USER")));
        
        String accessToken = jwtService.generateAccessToken(userDetails);
        String refreshToken = jwtService.generateRefreshToken(userDetails);

        // Token'ları response header'larına ekle
        response.addHeader("Access-Token", accessToken);
        response.addHeader("Refresh-Token", refreshToken);

        // Başarılı login sonrası yönlendirme
        getRedirectStrategy().sendRedirect(request, response, "/auth/success");
    }
} 