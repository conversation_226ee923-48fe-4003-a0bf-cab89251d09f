package com.lookforx.auth.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.auth.dto.AuthResponse;
import com.lookforx.auth.dto.GoogleLoginRequest;
import com.lookforx.auth.service.OAuth2Service;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Controller for OAuth2-based login flows (e.g. Google).
 */
@RestController
@RequestMapping("/v1/oauth2")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000","http://myapp.local:3000/",  "http://************:3000"},
             allowCredentials = "true", maxAge = 3600)
@Tag(name = "OAuth2", description = "Endpoints for external OAuth2 login flows")
public class OAuth2Controller {

    private final OAuth2Service oAuth2Service;

    @Value("${app.oauth2.frontendUrl:http://localhost:3000}")
    private String frontendUrl;

    private final Map<String, AuthResponse> authCodeCache = new ConcurrentHashMap<>();

    /**
     * Returns the Google OAuth2 authorization URL to which the frontend can redirect.
     *
     * @return a URL string
     */
    @Operation(
            summary = "Get Google Auth URL",
            description = "Retrieves the Google OAuth2 consent screen URL.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "URL retrieved successfully",
                            content = @Content(mediaType = "text/plain",
                                    schema = @Schema(type = "string"))),
                    @ApiResponse(responseCode = "500", description = "Error generating URL",
                            content = @Content)
            }
    )
    @GetMapping("/google/url")
    public ResponseEntity<String> getGoogleAuthUrl() {
        try {
            log.info("Getting Google auth URL");
            String authUrl = oAuth2Service.getGoogleAuthUrl();
            log.info("Returning Google auth URL: {}", authUrl);
            return ResponseEntity.ok(authUrl);
        } catch (Exception e) {
            log.error("Error getting Google auth URL", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error: " + e.getMessage());
        }
    }

    /**
     * Verifies the Google ID token (front-end flow) and returns AuthResponse with tokens and user info.
     *
     * @param request contains the Google ID token from the client
     * @return AuthResponse with accessToken, refreshToken, and user
     */
    @Operation(
            summary = "Handle Google Login",
            description = "Exchanges a Google ID token for JWT access & refresh tokens.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Login successful",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = AuthResponse.class))),
                    @ApiResponse(responseCode = "500", description = "Login failed",
                            content = @Content)
            }
    )
    @PostMapping("/google/login")
    public ResponseEntity<AuthResponse> handleGoogleLogin(@RequestBody GoogleLoginRequest request) {
        try {
            log.info("Handling Google login with idToken: {}", request.getIdToken().substring(0, Math.min(20, request.getIdToken().length())) + "...");
            AuthResponse response = oAuth2Service.handleGoogleLogin(request.getIdToken());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error handling Google login", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(null);
        }
    }

    /**
     * Callback endpoint for Google OAuth2 code flow. Exchanges code for tokens,
     * sets them as cookies, and redirects back to your frontend.
     *
     * @param code  the authorization code returned by Google
     * @param error any error returned by Google
     * @param response servlet response to add cookies and redirect
     */
    @Operation(
            summary = "Handle Google Callback",
            description = "Processes OAuth2 code from Google, issues JWTs in cookies, and redirects to frontend.",
            responses = {
                    @ApiResponse(responseCode = "302", description = "Redirect to frontend", content = @Content),
                    @ApiResponse(responseCode = "400", description = "OAuth error returned", content = @Content),
                    @ApiResponse(responseCode = "500", description = "Callback processing failed", content = @Content)
            }
    )
    @GetMapping("/callback/google")
    public ResponseEntity<?> handleGoogleCallback(@RequestParam(value = "code", required = false) String code,
                                                @RequestParam(value = "error", required = false) String error,
                                                @RequestParam(value = "state", required = false) String state,
                                                HttpServletResponse response) {
        log.info("Google OAuth callback received - code: {}, error: {}, state: {}",
                 code != null ? "present" : "null", error, state);

        if (error != null) {
            log.error("Google OAuth error: {}", error);
            String redirectUrl = frontendUrl + "/auth/callback/google?error=" + error;
            return ResponseEntity.status(HttpStatus.FOUND)
                    .header("Location", redirectUrl)
                    .build();
        }

        if (code == null) {
            log.error("No authorization code received from Google");
            String redirectUrl = frontendUrl + "/auth/callback/google?error=no_code";
            return ResponseEntity.status(HttpStatus.FOUND)
                    .header("Location", redirectUrl)
                    .build();
        }

        try {
            log.info("Processing Google callback with code: {}", code.substring(0, Math.min(20, code.length())) + "...");
            AuthResponse authResponse = oAuth2Service.handleGoogleCallback(code);
            
            log.info("Setting authentication cookies for user: {}", authResponse.getUser().getEmail());

            // Access token'ı çerez olarak ayarla (JavaScript erişimi için HttpOnly=false)
            Cookie accessTokenCookie = new Cookie("accessToken", authResponse.getAccessToken());
            accessTokenCookie.setHttpOnly(false); // JavaScript erişimi için false
            accessTokenCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
            accessTokenCookie.setPath("/");
            accessTokenCookie.setMaxAge(86400); // 1 gün (JWT expiration ile uyumlu)
            response.addCookie(accessTokenCookie);
            log.info("Set accessToken cookie");

            // Refresh token'ı çerez olarak ayarla (JavaScript erişimi için HttpOnly=false)
            Cookie refreshTokenCookie = new Cookie("refreshToken", authResponse.getRefreshToken());
            refreshTokenCookie.setHttpOnly(false); // JavaScript erişimi için false
            refreshTokenCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
            refreshTokenCookie.setPath("/");
            refreshTokenCookie.setMaxAge(86400 * 7); // 7 gün
            response.addCookie(refreshTokenCookie);
            log.info("Set refreshToken cookie");

            // Kullanıcı bilgilerini de çerez olarak ayarla
            String userJson = new ObjectMapper().writeValueAsString(authResponse.getUser());
            String encodedUserJson = URLEncoder.encode(userJson, StandardCharsets.UTF_8.toString());
            log.info("User JSON to encode: {}", userJson);
            log.info("Encoded user JSON length: {}", encodedUserJson.length());

            Cookie userCookie = new Cookie("user", encodedUserJson);
            userCookie.setHttpOnly(false); // JavaScript erişimine izin ver
            userCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
            userCookie.setPath("/");
            userCookie.setMaxAge(86400); // 1 gün
            response.addCookie(userCookie);
            log.info("Set user cookie");
            
            // Frontend'e yönlendirme URL'i oluştur - success parametresi ile
            String redirectUrl = frontendUrl + "/auth/callback/google?success=true&user_id=" + authResponse.getUser().getId();

            log.info("Redirecting to frontend with success: {}", redirectUrl);
            return ResponseEntity.status(HttpStatus.FOUND)
                    .header("Location", redirectUrl)
                    .build();
        } catch (Exception e) {
            log.error("Error handling Google callback", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error: " + e.getMessage());
        }
    }


    /**
     * For single-page apps: frontend calls this to exchange the one-time code (cached above)
     * for the AuthResponse containing tokens & user info.
     *
     * @param request a map containing the `"code"` key
     * @return AuthResponse if code is valid
     */
    @Operation(
            summary = "Exchange One-Time Code",
            description = "Returns the AuthResponse stored in cache for a given code.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Code exchanged successfully",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = AuthResponse.class))),
                    @ApiResponse(responseCode = "400", description = "Code not provided", content = @Content),
                    @ApiResponse(responseCode = "401", description = "Invalid or expired code", content = @Content)
            }
    )
    @PostMapping("/exchange-code")
    public ResponseEntity<AuthResponse> exchangeCode(@RequestBody Map<String, String> request) {
        String code = request.get("code");
        if (code == null) {
            return ResponseEntity.badRequest().build();
        }

        AuthResponse authResponse = authCodeCache.remove(code);
        if (authResponse == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        return ResponseEntity.ok(authResponse);
    }

    /**
     * Test endpoint to simulate Google OAuth registration for testing welcome events
     */
    @Operation(
            summary = "Test Google Registration",
            description = "Test endpoint to simulate Google OAuth registration for testing welcome events.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Test registration successful",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = AuthResponse.class))),
                    @ApiResponse(responseCode = "500", description = "Test registration failed", content = @Content)
            }
    )
    @PostMapping("/google/test-register")
    public ResponseEntity<AuthResponse> testGoogleRegistration(@RequestBody Map<String, String> request) {
        try {
            String email = request.get("email");
            String name = request.get("name");
            String googleId = request.get("googleId");

            if (email == null || name == null || googleId == null) {
                return ResponseEntity.badRequest().build();
            }

            log.info("Test Google registration for email: {}", email);
            AuthResponse response = oAuth2Service.testGoogleRegistration(email, name, googleId);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error in test Google registration", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
