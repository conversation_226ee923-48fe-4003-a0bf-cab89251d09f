package com.lookforx.auth.controller;

import com.lookforx.auth.dto.*;
import com.lookforx.auth.service.UserProfileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controller for user profile management
 */
@RestController
@RequestMapping("/v1/profile")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "User Profile", description = "User profile management endpoints")
@CrossOrigin(origins = {"http://localhost:3000", "http://myapp.local:3000", "http://************:3000"}, 
             allowCredentials = "true", maxAge = 3600)
public class UserProfileController {

    private final UserProfileService userProfileService;

    /**
     * Create user profile
     */
    @PostMapping
    @Operation(summary = "Create user profile", description = "Create a new user profile")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<UserProfileResponse> createProfile(
            @Valid @RequestBody CreateUserProfileRequest request,
            Authentication authentication) {
        
        log.info("Creating profile for user: {}", authentication.getName());
        
        String userEmail = authentication.getName();
        UserProfileResponse response = userProfileService.createProfile(userEmail, request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    /**
     * Get current user's profile
     */
    @GetMapping("/me")
    @Operation(summary = "Get current user profile", description = "Get the authenticated user's profile")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<UserProfileResponse> getMyProfile(Authentication authentication) {
        log.info("Getting profile for user: {}", authentication.getName());
        
        String userEmail = authentication.getName();
        UserProfileResponse response = userProfileService.getProfileByUserId(userEmail);
        return ResponseEntity.ok(response);
    }

    /**
     * Update current user's profile
     */
    @PutMapping("/me")
    @Operation(summary = "Update current user profile", description = "Update the authenticated user's profile")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<UserProfileResponse> updateMyProfile(
            @Valid @RequestBody UpdateUserProfileRequest request,
            Authentication authentication) {
        
        log.info("Updating profile for user: {}", authentication.getName());
        
        String userEmail = authentication.getName();
        UserProfileResponse response = userProfileService.updateProfile(userEmail, request);
        return ResponseEntity.ok(response);
    }

    /**
     * Delete current user's profile
     */
    @DeleteMapping("/me")
    @Operation(summary = "Delete current user profile", description = "Delete the authenticated user's profile")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<Void> deleteMyProfile(Authentication authentication) {
        log.info("Deleting profile for user: {}", authentication.getName());
        
        String userEmail = authentication.getName();
        userProfileService.deleteProfile(userEmail);
        return ResponseEntity.noContent().build();
    }

    /**
     * Get profile by username (public endpoint)
     */
    @GetMapping("/username/{username}")
    @Operation(summary = "Get profile by username", description = "Get user profile by username (public)")
    public ResponseEntity<UserProfileResponse> getProfileByUsername(
            @Parameter(description = "Username to search for")
            @PathVariable String username) {
        
        log.info("Getting profile for username: {}", username);
        
        UserProfileResponse response = userProfileService.getProfileByUsername(username);
        return ResponseEntity.ok(response);
    }

    /**
     * Check username availability
     */
    @GetMapping("/username/{username}/available")
    @Operation(summary = "Check username availability", description = "Check if username is available")
    public ResponseEntity<Map<String, Boolean>> checkUsernameAvailability(
            @Parameter(description = "Username to check")
            @PathVariable String username) {
        
        log.info("Checking username availability: {}", username);
        
        boolean available = userProfileService.isUsernameAvailable(username);
        return ResponseEntity.ok(Map.of("available", available));
    }

    /**
     * Search profiles
     */
    @GetMapping("/search")
    @Operation(summary = "Search profiles", description = "Search user profiles by username or company name")
    public ResponseEntity<List<UserProfileResponse>> searchProfiles(
            @Parameter(description = "Search term")
            @RequestParam String q) {
        
        log.info("Searching profiles with term: {}", q);
        
        List<UserProfileResponse> responses = userProfileService.searchProfiles(q);
        return ResponseEntity.ok(responses);
    }

    /**
     * Get public profiles for discovery
     */
    @GetMapping("/public")
    @Operation(summary = "Get public profiles", description = "Get public completed profiles for discovery")
    public ResponseEntity<List<UserProfileResponse>> getPublicProfiles() {
        log.info("Getting public profiles");
        
        List<UserProfileResponse> responses = userProfileService.getPublicProfiles();
        return ResponseEntity.ok(responses);
    }

    /**
     * Admin: Get profile by user ID
     */
    @GetMapping("/admin/user/{userId}")
    @Operation(summary = "Admin: Get profile by user ID", description = "Get user profile by user ID (admin only)")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<UserProfileResponse> getProfileByUserId(
            @Parameter(description = "User ID")
            @PathVariable String userId) {

        log.info("Admin getting profile for user: {}", userId);

        try {
            UserProfileResponse response = userProfileService.getProfileByUserId(userId);
            log.info("Profile found for user: {}", userId);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error getting profile for user: {}, error: {}", userId, e.getMessage());
            throw e;
        }
    }

    /**
     * Admin: Update profile by user ID
     */
    @PutMapping("/admin/user/{userId}")
    @Operation(summary = "Admin: Update profile by user ID", description = "Update user profile by user ID (admin only)")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<UserProfileResponse> updateProfileByUserId(
            @Parameter(description = "User ID")
            @PathVariable String userId,
            @Valid @RequestBody UpdateUserProfileRequest request) {

        log.info("Admin updating profile for user: {}", userId);
        log.info("Request data: firstName={}, lastName={}, mobilePhone={}",
                request.getFirstName(), request.getLastName(), request.getMobilePhone());

        try {
            UserProfileResponse response = userProfileService.updateProfile(userId, request);
            log.info("Profile updated successfully for user: {}", userId);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error updating profile for user: {}, error: {}", userId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Admin: Delete profile by user ID
     */
    @DeleteMapping("/admin/user/{userId}")
    @Operation(summary = "Admin: Delete profile by user ID", description = "Delete user profile by user ID (admin only)")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteProfileByUserId(
            @Parameter(description = "User ID")
            @PathVariable String userId) {

        log.info("Admin deleting profile for user: {}", userId);

        userProfileService.deleteProfile(userId);
        return ResponseEntity.noContent().build();
    }
}
