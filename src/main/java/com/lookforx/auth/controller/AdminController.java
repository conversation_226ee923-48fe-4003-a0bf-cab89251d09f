package com.lookforx.auth.controller;

import com.lookforx.auth.dto.UserListResponse;
import com.lookforx.auth.dto.UserResponse;
import com.lookforx.auth.dto.UpdateUserRoleRequest;
import com.lookforx.auth.dto.UpdateUserStatusRequest;
import com.lookforx.auth.service.AdminService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * Controller for admin-only user management operations.
 */
@RestController
@RequestMapping("/v1/admin/users")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000", "http://myapp.local:3000", "http://************:3000"}, 
             allowCredentials = "true", maxAge = 3600)
@Tag(name = "Admin - User Management", description = "Admin endpoints for managing users")
@SecurityRequirement(name = "bearerAuth")
public class AdminController {

    private final AdminService adminService;

    /**
     * Get all users with pagination and filtering
     */
    @Operation(
            summary = "List Users",
            description = "Get paginated list of users with optional filtering and sorting",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Users retrieved successfully",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = UserListResponse.class))),
                    @ApiResponse(responseCode = "403", description = "Access denied", content = @Content)
            }
    )
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<UserListResponse> getUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) List<String> roles,
            @RequestParam(required = false) Boolean active,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDirection) {
        
        try {
            log.info("Admin request to list users - page: {}, size: {}, search: {}, role: {}, roles: {}, active: {}",
                     page, size, search, role, roles, active);

            // Create sort object
            Sort.Direction direction = sortDirection.equalsIgnoreCase("asc") ?
                Sort.Direction.ASC : Sort.Direction.DESC;
            Sort sort = Sort.by(direction, sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);

            UserListResponse response = adminService.getUsers(pageable, search, role, roles, active);
            
            log.info("Retrieved {} users out of {} total", response.getUsers().size(), response.getTotalCount());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error retrieving users", e);
            throw e;
        }
    }

    /**
     * Get user by ID
     */
    @Operation(
            summary = "Get User by ID",
            description = "Retrieve detailed information about a specific user",
            responses = {
                    @ApiResponse(responseCode = "200", description = "User found",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = UserResponse.class))),
                    @ApiResponse(responseCode = "404", description = "User not found", content = @Content),
                    @ApiResponse(responseCode = "403", description = "Access denied", content = @Content)
            }
    )
    @GetMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<UserResponse> getUserById(@PathVariable String userId) {
        try {
            log.info("Admin request to get user by ID: {}", userId);
            UserResponse user = adminService.getUserById(userId);
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            log.error("Error retrieving user by ID: {}", userId, e);
            throw e;
        }
    }

    /**
     * Update user roles
     */
    @Operation(
            summary = "Update User Roles",
            description = "Update the roles assigned to a user",
            responses = {
                    @ApiResponse(responseCode = "200", description = "User roles updated successfully",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = UserResponse.class))),
                    @ApiResponse(responseCode = "404", description = "User not found", content = @Content),
                    @ApiResponse(responseCode = "403", description = "Access denied", content = @Content),
                    @ApiResponse(responseCode = "400", description = "Invalid roles", content = @Content)
            }
    )
    @PutMapping("/{userId}/roles")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<UserResponse> updateUserRoles(
            @PathVariable String userId,
            @Valid @RequestBody UpdateUserRoleRequest request) {
        try {
            log.info("Admin request to update roles for user {}: {}", userId, request.getRoles());
            UserResponse updatedUser = adminService.updateUserRoles(userId, request.getRoles());
            log.info("Successfully updated roles for user: {}", userId);
            return ResponseEntity.ok(updatedUser);
        } catch (Exception e) {
            log.error("Error updating roles for user: {}", userId, e);
            throw e;
        }
    }

    /**
     * Update user status (activate/deactivate)
     */
    @Operation(
            summary = "Update User Status",
            description = "Activate or deactivate a user account",
            responses = {
                    @ApiResponse(responseCode = "200", description = "User status updated successfully",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = UserResponse.class))),
                    @ApiResponse(responseCode = "404", description = "User not found", content = @Content),
                    @ApiResponse(responseCode = "403", description = "Access denied", content = @Content)
            }
    )
    @PutMapping("/{userId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<UserResponse> updateUserStatus(
            @PathVariable String userId,
            @Valid @RequestBody UpdateUserStatusRequest request) {
        try {
            log.info("Admin request to update status for user {}: active={}", userId, request.isActive());
            UserResponse updatedUser = adminService.updateUserStatus(userId, request.isActive());
            log.info("Successfully updated status for user: {}", userId);
            return ResponseEntity.ok(updatedUser);
        } catch (Exception e) {
            log.error("Error updating status for user: {}", userId, e);
            throw e;
        }
    }

    /**
     * Delete user (soft delete)
     */
    @Operation(
            summary = "Delete User",
            description = "Soft delete a user account",
            responses = {
                    @ApiResponse(responseCode = "204", description = "User deleted successfully"),
                    @ApiResponse(responseCode = "404", description = "User not found", content = @Content),
                    @ApiResponse(responseCode = "403", description = "Access denied", content = @Content)
            }
    )
    @DeleteMapping("/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteUser(@PathVariable String userId) {
        try {
            log.info("Admin request to delete user: {}", userId);
            adminService.deleteUser(userId);
            log.info("Successfully deleted user: {}", userId);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            log.error("Error deleting user: {}", userId, e);
            throw e;
        }
    }

    /**
     * Get available roles
     */
    @Operation(
            summary = "Get Available Roles",
            description = "Retrieve list of all available user roles",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Roles retrieved successfully",
                            content = @Content(mediaType = "application/json")),
                    @ApiResponse(responseCode = "403", description = "Access denied", content = @Content)
            }
    )
    @GetMapping("/roles")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<List<String>> getAvailableRoles() {
        try {
            log.info("Admin request to get available roles");
            List<String> roles = adminService.getAvailableRoles();
            return ResponseEntity.ok(roles);
        } catch (Exception e) {
            log.error("Error retrieving available roles", e);
            throw e;
        }
    }

    /**
     * Get user statistics
     */
    @Operation(
            summary = "Get User Statistics",
            description = "Retrieve user statistics for admin dashboard",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully",
                            content = @Content(mediaType = "application/json")),
                    @ApiResponse(responseCode = "403", description = "Access denied", content = @Content)
            }
    )
    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getUserStats() {
        try {
            log.info("Admin request to get user statistics");
            Map<String, Object> stats = adminService.getUserStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("Error retrieving user statistics", e);
            throw e;
        }
    }
}
