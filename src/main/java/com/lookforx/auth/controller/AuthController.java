package com.lookforx.auth.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.auth.dto.AuthResponse;
import com.lookforx.auth.dto.LoginRequest;
import com.lookforx.auth.dto.SignupRequest;
import com.lookforx.auth.dto.UserResponse;
import com.lookforx.auth.entity.User;
import com.lookforx.auth.repository.UserRepository;
import com.lookforx.auth.security.CustomUserDetails;
import com.lookforx.auth.service.AuthService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * Controller for authentication-related endpoints: signup, login, token refresh, and user info retrieval.
 */
@RestController
@RequestMapping("/v1/auth")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = {"http://localhost:3000","http://myapp.local:3000",  "http://************:3000"}, maxAge = 3600)
@Validated
@Tag(name = "Authentication", description = "Endpoints for user signup, login, token refresh, and getting current user")
public class AuthController {

    private final AuthService authService;
    private final UserRepository userRepository;

    /**
     * Registers a new user and issues access & refresh tokens in cookies.
     *
     * @param request  the signup request containing email, password, etc.
     * @param response the HTTP response to which cookies will be added
     * @return the authentication response with tokens and user info
     */
    @Operation(
            summary = "User Signup",
            description = "Creates a new user account and returns JWT access & refresh tokens.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Signup successful",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = AuthResponse.class))),
                    @ApiResponse(responseCode = "400", description = "Signup failed",
                            content = @Content)
            }
    )
    @PostMapping("/signup")
    public ResponseEntity<AuthResponse> signup(@Valid @RequestBody SignupRequest request, HttpServletResponse response) {
        try {
            log.info("Signup request received for email: {}", request.getEmail());
            AuthResponse authResponse = authService.signup(request);

            // Access token'ı çerez olarak ayarla (JavaScript erişimi için HttpOnly=false)
            Cookie accessTokenCookie = new Cookie("accessToken", authResponse.getAccessToken());
            accessTokenCookie.setHttpOnly(false); // JavaScript erişimi için false
            accessTokenCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
            accessTokenCookie.setPath("/");
            accessTokenCookie.setMaxAge(86400); // 1 gün (JWT expiration ile uyumlu)
            response.addCookie(accessTokenCookie);

            // Refresh token'ı çerez olarak ayarla (JavaScript erişimi için HttpOnly=false)
            Cookie refreshTokenCookie = new Cookie("refreshToken", authResponse.getRefreshToken());
            refreshTokenCookie.setHttpOnly(false); // JavaScript erişimi için false
            refreshTokenCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
            refreshTokenCookie.setPath("/");
            refreshTokenCookie.setMaxAge(86400 * 7); // 7 gün
            response.addCookie(refreshTokenCookie);

            try {
                // Kullanıcı bilgilerini de çerez olarak ayarla
                String userJson = new ObjectMapper().writeValueAsString(authResponse.getUser());
                String encodedUserJson = URLEncoder.encode(userJson, StandardCharsets.UTF_8.toString());

                Cookie userCookie = new Cookie("user", encodedUserJson);
                userCookie.setHttpOnly(false); // JavaScript erişimine izin ver
                userCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
                userCookie.setPath("/");
                userCookie.setMaxAge(86400); // 1 gün
                response.addCookie(userCookie);
            } catch (Exception e) {
                log.error("Error setting user cookie", e);
            }

            log.info("Signup successful for email: {}", request.getEmail());
            return ResponseEntity.ok(authResponse);
        } catch (Exception e) {
            log.error("Signup failed for email: {}", request.getEmail(), e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(AuthResponse.builder()
                            .message(e.getMessage())
                            .build());
        }
    }

    /**
     * Authenticates a user and issues new access & refresh tokens in cookies.
     *
     * @param request  the login request with credentials
     * @param response the HTTP response to which cookies will be added
     * @return the authentication response with tokens and user info
     */
    @Operation(
            summary = "User Login",
            description = "Authenticates user credentials and returns JWT access & refresh tokens.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Login successful",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = AuthResponse.class))),
                    @ApiResponse(responseCode = "401", description = "Invalid credentials",
                            content = @Content)
            }
    )
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@Valid @RequestBody LoginRequest request, HttpServletResponse response) {
        try {
            log.info("Login request received for email: {}", request.getEmail());
            AuthResponse authResponse = authService.login(request);

            // Access token'ı çerez olarak ayarla (JavaScript erişimi için HttpOnly=false)
            Cookie accessTokenCookie = new Cookie("accessToken", authResponse.getAccessToken());
            accessTokenCookie.setHttpOnly(false); // JavaScript erişimi için false
            accessTokenCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
            accessTokenCookie.setPath("/");
            accessTokenCookie.setMaxAge(86400); // 1 gün (JWT expiration ile uyumlu)
            response.addCookie(accessTokenCookie);

            // Refresh token'ı çerez olarak ayarla (JavaScript erişimi için HttpOnly=false)
            Cookie refreshTokenCookie = new Cookie("refreshToken", authResponse.getRefreshToken());
            refreshTokenCookie.setHttpOnly(false); // JavaScript erişimi için false
            refreshTokenCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
            refreshTokenCookie.setPath("/");
            refreshTokenCookie.setMaxAge(86400 * 7); // 7 gün
            response.addCookie(refreshTokenCookie);

            try {
                // Kullanıcı bilgilerini de çerez olarak ayarla
                String userJson = new ObjectMapper().writeValueAsString(authResponse.getUser());
                String encodedUserJson = URLEncoder.encode(userJson, StandardCharsets.UTF_8.toString());

                Cookie userCookie = new Cookie("user", encodedUserJson);
                userCookie.setHttpOnly(false); // JavaScript erişimine izin ver
                userCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
                userCookie.setPath("/");
                userCookie.setMaxAge(86400); // 1 gün
                response.addCookie(userCookie);
            } catch (Exception e) {
                log.error("Error setting user cookie", e);
            }

            log.info("Login successful for email: {}", request.getEmail());
            return ResponseEntity.ok(authResponse);
        } catch (Exception e) {
            log.error("Login failed for email: {}", request.getEmail(), e);
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(AuthResponse.builder()
                            .message(e.getMessage())
                            .build());
        }
    }

    /**
     * Refreshes the access token using a valid refresh token, and sets new cookies.
     *
     * @param refreshToken the raw refresh token string
     * @param response     the HTTP response to which new cookies will be added
     * @return the new authentication response with refreshed tokens
     */
    @Operation(
            summary = "Refresh Token",
            description = "Issues a new access token given a valid refresh token.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Token refreshed",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = AuthResponse.class))),
                    @ApiResponse(responseCode = "401", description = "Invalid refresh token",
                            content = @Content)
            }
    )
    @PostMapping("/refresh-token")
    public ResponseEntity<AuthResponse> refreshToken(@RequestBody String refreshToken, HttpServletResponse response) {
        AuthResponse authResponse = authService.refreshToken(refreshToken);

        // Access token'ı çerez olarak ayarla (JavaScript erişimi için HttpOnly=false)
        Cookie accessTokenCookie = new Cookie("accessToken", authResponse.getAccessToken());
        accessTokenCookie.setHttpOnly(false); // JavaScript erişimi için false
        accessTokenCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
        accessTokenCookie.setPath("/");
        accessTokenCookie.setMaxAge(86400); // 1 gün (JWT expiration ile uyumlu)
        response.addCookie(accessTokenCookie);

        // Refresh token'ı çerez olarak ayarla (JavaScript erişimi için HttpOnly=false)
        Cookie refreshTokenCookie = new Cookie("refreshToken", authResponse.getRefreshToken());
        refreshTokenCookie.setHttpOnly(false); // JavaScript erişimi için false
        refreshTokenCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
        refreshTokenCookie.setPath("/");
        refreshTokenCookie.setMaxAge(86400 * 7); // 7 gün
        response.addCookie(refreshTokenCookie);

        try {
            // Kullanıcı bilgilerini de çerez olarak ayarla
            String userJson = new ObjectMapper().writeValueAsString(authResponse.getUser());
            String encodedUserJson = URLEncoder.encode(userJson, StandardCharsets.UTF_8.toString());

            Cookie userCookie = new Cookie("user", encodedUserJson);
            userCookie.setHttpOnly(false); // JavaScript erişimine izin ver
            userCookie.setSecure(false); // Geliştirme ortamında false, production'da true olmalı
            userCookie.setPath("/");
            userCookie.setMaxAge(86400); // 1 gün
            response.addCookie(userCookie);
        } catch (Exception e) {
            log.error("Error setting user cookie", e);
        }

        return ResponseEntity.ok(authResponse);
    }

    /**
     * Retrieves the currently authenticated user's details.
     *
     * @param authentication the Spring Security authentication object
     * @return the user's profile information
     */
    @Operation(
            summary = "Get Current User",
            description = "Returns the profile of the currently authenticated user.",
            responses = {
                    @ApiResponse(responseCode = "200", description = "User profile retrieved",
                            content = @Content(mediaType = "application/json",
                                    schema = @Schema(implementation = UserResponse.class))),
                    @ApiResponse(responseCode = "500", description = "Internal server error",
                            content = @Content)
            }
    )
    @GetMapping("/me")
    public ResponseEntity<UserResponse> getCurrentUser(Authentication authentication) {
        try {
            log.info("Getting current user info for: {}", authentication.getName());
            
            if (authentication.getPrincipal() instanceof User) {
                User user = (User) authentication.getPrincipal();
                log.info("User found: {}", user.getEmail());
                return ResponseEntity.ok(user.toUserResponse());
            } else if (authentication.getPrincipal() instanceof CustomUserDetails) {
                CustomUserDetails userDetails = (CustomUserDetails) authentication.getPrincipal();
                log.info("UserDetails found: {}", userDetails.getUsername());
                
                User user = userRepository.findByEmail(userDetails.getUsername())
                        .orElseThrow(() -> new UsernameNotFoundException("User not found with email: " + userDetails.getUsername()));

                log.info("User retrieved from repository: {} with roles: {}", user.getEmail(), user.getRoles());
                return ResponseEntity.ok(user.toUserResponse());
            } else {
                log.warn("Unknown principal type: {}", authentication.getPrincipal().getClass().getName());
                throw new UsernameNotFoundException("User details not found");
            }
        } catch (Exception e) {
            log.error("Error getting current user", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

}
