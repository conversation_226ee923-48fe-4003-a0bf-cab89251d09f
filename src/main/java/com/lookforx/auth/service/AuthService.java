package com.lookforx.auth.service;

import com.lookforx.auth.dto.AuthResponse;
import com.lookforx.auth.dto.LoginRequest;
import com.lookforx.auth.dto.SignupRequest;
import com.lookforx.auth.dto.UserResponse;
import com.lookforx.auth.entity.User;
import com.lookforx.auth.enums.AuthProvider;
import com.lookforx.auth.enums.Role;
import com.lookforx.auth.exception.AuthenticationException;
import com.lookforx.auth.repository.UserRepository;
import com.lookforx.common.events.UserWelcomeEvent;
import com.lookforx.common.kafka.EventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Lazy;

import java.util.Collections;

/**
 * Service class responsible for handling authentication-related operations,
 * including signup, login, and refresh token logic.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuthService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtService jwtService;
    private final AuthenticationManager authenticationManager;
    private final OAuth2Service oAuth2Service;
    @Lazy
    private final UserProfileService userProfileService;
    private final EventPublisher eventPublisher;

    /**
     * Registers a new user account with the provided signup data.
     *
     * @param request the signup request containing email, password, and name
     * @return an {@link AuthResponse} containing tokens and user details
     * @throws AuthenticationException if the email already exists or the password is invalid
     */
    public AuthResponse signup(SignupRequest request) {
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new AuthenticationException("Email already registered");
        }

        // Password validation is now handled by @PasswordValidation annotation

        User user = User.builder()
                .email(request.getEmail())
                .password(passwordEncoder.encode(request.getPassword()))
                .name(request.getName())
                .provider(AuthProvider.LOCAL)
                .roles(Collections.singleton(Role.USER))
                .emailVerified(false)
                .build();

        log.info("Creating new user: {}", user.getEmail());
        User savedUser = userRepository.save(user);
        log.info("User created successfully: {}", savedUser.getEmail());

        // Create initial user profile
        try {
            userProfileService.createInitialProfile(savedUser.getId(), savedUser.getEmail(), savedUser.getName());
            log.info("Initial profile created for user: {}", savedUser.getEmail());
        } catch (Exception e) {
            log.warn("Failed to create initial profile for user: {}, error: {}", savedUser.getEmail(), e.getMessage());
        }

        // Publish welcome event for notification service
        try {
            UserWelcomeEvent welcomeEvent = UserWelcomeEvent.builder()
                    .userId(String.valueOf(savedUser.getId().hashCode())) // Convert ObjectId to Long
                    .userEmail(savedUser.getEmail())
                    .userName(savedUser.getName())
                    .registrationMethod("EMAIL")
                    .preferredLanguage("EN") // Default to English for now
                    .isEmailVerified(savedUser.getEmailVerified())
                    .serviceName("auth-service")
                    .build();

            eventPublisher.publishUserEvent(welcomeEvent);
            log.info("Welcome event published for user: {}", savedUser.getEmail());
        } catch (Exception e) {
            log.warn("Failed to publish welcome event for user: {}, error: {}", savedUser.getEmail(), e.getMessage());
        }

        String accessToken = jwtService.generateAccessToken(user);
        String refreshToken = jwtService.generateRefreshToken(user);

        return AuthResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .user(mapToUserResponse(user))
                .message("Account created successfully")
                .build();
    }

    /**
     * Authenticates a user with the provided login credentials.
     *
     * @param request the login request containing email and password
     * @return an {@link AuthResponse} containing tokens and user details
     * @throws AuthenticationException if authentication fails or credentials are invalid
     */
    public AuthResponse login(LoginRequest request) {
        try {
            log.info("Attempting to authenticate user: {}", request.getEmail());
            
            // Kullanıcıyı veritabanında ara
            User user = userRepository.findByEmail(request.getEmail())
                    .orElseThrow(() -> new AuthenticationException("User not found"));
            
            // Şifre kontrolü
            if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
                log.warn("Invalid password for user: {}", request.getEmail());
                throw new AuthenticationException("Invalid email or password");
            }
            
            // Authentication manager ile kimlik doğrulama
            try {
                authenticationManager.authenticate(
                        new UsernamePasswordAuthenticationToken(
                                request.getEmail(),
                                request.getPassword()
                        )
                );
            } catch (Exception e) {
                log.error("Authentication failed for user: {}", request.getEmail(), e);
                throw new AuthenticationException("Authentication failed: " + e.getMessage());
            }

            log.info("User authenticated successfully: {}", request.getEmail());
            
            String accessToken = jwtService.generateAccessToken(user);
            String refreshToken = jwtService.generateRefreshToken(user);

            return AuthResponse.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .user(mapToUserResponse(user))
                    .build();
        } catch (AuthenticationException e) {
            log.error("Login failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during login", e);
            throw new AuthenticationException("Invalid email or password");
        }
    }

    /**
     * Refreshes the access and refresh tokens using a valid refresh token.
     *
     * @param refreshToken the refresh token to validate
     * @return a new {@link AuthResponse} containing refreshed tokens and user info
     * @throws AuthenticationException if the token is invalid or user not found
     */
    public AuthResponse refreshToken(String refreshToken) {
        String userEmail = jwtService.extractUsername(refreshToken);
        User user = userRepository.findByEmail(userEmail)
                .orElseThrow(() -> new AuthenticationException("User not found"));

        if (!jwtService.isTokenValid(refreshToken, user)) {
            throw new AuthenticationException("Invalid refresh token");
        }

        String accessToken = jwtService.generateAccessToken(user);
        String newRefreshToken = jwtService.generateRefreshToken(user);

        return AuthResponse.builder()
                .accessToken(accessToken)
                .refreshToken(newRefreshToken)
                .user(mapToUserResponse(user))
                .build();
    }

    /**
     * Converts a {@link User} entity to a {@link UserResponse} DTO.
     *
     * @param user the user entity
     * @return the user response DTO
     */
    private UserResponse mapToUserResponse(User user) {
        return UserResponse.builder()
                .id(user.getId().toString())
                .email(user.getEmail())
                .name(user.getName())
                .imageUrl(user.getImageUrl())
                .build();
    }



}
