package com.lookforx.auth.service;

import com.lookforx.auth.dto.UserListResponse;
import com.lookforx.auth.dto.UserResponse;
import com.lookforx.auth.entity.User;
import com.lookforx.auth.enums.Role;
import com.lookforx.auth.exception.UserNotFoundException;
import com.lookforx.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminService {

    private final UserRepository userRepository;
    private final MongoTemplate mongoTemplate;

    /**
     * Get paginated list of users with filtering
     */
    public UserListResponse getUsers(Pageable pageable, String search, String role, List<String> roles, Boolean active) {
        log.info("Getting users with filters - search: {}, role: {}, roles: {}, active: {}", search, role, roles, active);

        Query query = new Query();

        // Add search filter
        if (search != null && !search.trim().isEmpty()) {
            Criteria searchCriteria = new Criteria().orOperator(
                Criteria.where("name").regex(search, "i"),
                Criteria.where("email").regex(search, "i")
            );
            query.addCriteria(searchCriteria);
        }

        // Add role filter (single role - for backward compatibility)
        if (role != null && !role.trim().isEmpty()) {
            // Use exact match for role filtering
            String roleUpper = role.toUpperCase();
            Criteria roleCriteria = new Criteria().orOperator(
                Criteria.where("roles").in(roleUpper),
                Criteria.where("rolesString").regex("(^|,)" + roleUpper + "(,|$)")
            );
            query.addCriteria(roleCriteria);
        }

        // Add roles filter (multiple roles)
        if (roles != null && !roles.isEmpty()) {
            List<String> rolesUpper = roles.stream()
                .map(String::toUpperCase)
                .collect(Collectors.toList());

            List<Criteria> rolesCriteriaList = new ArrayList<>();
            for (String roleFilter : rolesUpper) {
                rolesCriteriaList.add(Criteria.where("roles").in(roleFilter));
                rolesCriteriaList.add(Criteria.where("rolesString").regex("(^|,)" + roleFilter + "(,|$)"));
            }

            Criteria rolesCriteria = new Criteria().orOperator(rolesCriteriaList.toArray(new Criteria[0]));
            query.addCriteria(rolesCriteria);
        }

        // Add active filter
        if (active != null) {
            query.addCriteria(Criteria.where("active").is(active));
        }

        // Get total count
        long totalCount = mongoTemplate.count(query, User.class);

        // Apply pagination
        query.with(pageable);

        // Execute query
        List<User> users = mongoTemplate.find(query, User.class);

        List<UserResponse> userResponses = users.stream()
                .map(this::convertToUserResponse)
                .collect(Collectors.toList());

        int totalPages = (int) Math.ceil((double) totalCount / pageable.getPageSize());

        return UserListResponse.builder()
                .users(userResponses)
                .totalCount(totalCount)
                .page(pageable.getPageNumber())
                .size(pageable.getPageSize())
                .totalPages(totalPages)
                .build();
    }

    /**
     * Get user by ID
     */
    public UserResponse getUserById(String userId) {
        log.info("Getting user by ID: {}", userId);
        User user = userRepository.findById(userId)
                .orElseThrow(() -> UserNotFoundException.forId(userId));
        return convertToUserResponse(user);
    }

    /**
     * Update user roles
     */
    public UserResponse updateUserRoles(String userId, List<String> newRoles) {
        log.info("Updating roles for user {}: {}", userId, newRoles);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> UserNotFoundException.forId(userId));

        // Validate roles
        Set<String> validRoles = getValidRoles();
        for (String role : newRoles) {
            if (!validRoles.contains(role)) {
                throw new IllegalArgumentException("Invalid role: " + role);
            }
        }

        // Convert string roles to Role enum set
        Set<Role> roleEnums = newRoles.stream()
                .map(roleStr -> Role.valueOf(roleStr.toUpperCase()))
                .collect(Collectors.toSet());

        log.info("Converting roles for user {}: {} -> {}", userId, newRoles, roleEnums);

        // Update both roles and rolesString
        user.setRoles(roleEnums);

        log.info("Updated user roles: roles={}, rolesString={}",
                user.getRoles(), user.getRolesString());

        // Update timestamp if field exists
        if (user.getUpdatedAt() != null || user.getCreatedAt() != null) {
            user.setUpdatedAt(LocalDateTime.now());
        }

        User savedUser = userRepository.save(user);
        log.info("Successfully updated roles for user: {}", userId);

        return convertToUserResponse(savedUser);
    }

    /**
     * Update user status (active/inactive)
     */
    public UserResponse updateUserStatus(String userId, boolean active) {
        log.info("Updating status for user {}: active={}", userId, active);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> UserNotFoundException.forId(userId));

        user.setActive(active);

        // Update timestamp if field exists
        if (user.getUpdatedAt() != null || user.getCreatedAt() != null) {
            user.setUpdatedAt(LocalDateTime.now());
        }

        User savedUser = userRepository.save(user);
        log.info("Successfully updated status for user: {}", userId);

        return convertToUserResponse(savedUser);
    }

    /**
     * Soft delete user
     */
    public void deleteUser(String userId) {
        log.info("Soft deleting user: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> UserNotFoundException.forId(userId));

        user.setActive(false);

        // Update timestamp if field exists
        if (user.getUpdatedAt() != null || user.getCreatedAt() != null) {
            user.setUpdatedAt(LocalDateTime.now());
        }

        userRepository.save(user);
        log.info("Successfully soft deleted user: {}", userId);
    }

    /**
     * Get available roles
     */
    public List<String> getAvailableRoles() {
        return new ArrayList<>(getValidRoles());
    }

    /**
     * Get user statistics
     */
    public Map<String, Object> getUserStats() {
        log.info("Getting user statistics");
        
        long totalUsers = userRepository.count();
        long activeUsers = userRepository.countByActive(true);
        long inactiveUsers = userRepository.countByActive(false);
        
        // Count users by role
        long adminUsers = userRepository.countByRolesStringContaining("ADMIN");
        long regularUsers = totalUsers - adminUsers;

        Map<String, Object> stats = new HashMap<>();
        stats.put("totalUsers", totalUsers);
        stats.put("activeUsers", activeUsers);
        stats.put("inactiveUsers", inactiveUsers);
        stats.put("adminUsers", adminUsers);
        stats.put("regularUsers", regularUsers);
        
        log.info("User statistics: {}", stats);
        return stats;
    }

    /**
     * Convert User entity to UserResponse DTO
     */
    private UserResponse convertToUserResponse(User user) {
        // Handle roles - try rolesString first, then fall back to roles enum
        Set<String> roles = new HashSet<>();

        if (user.getRolesString() != null && !user.getRolesString().trim().isEmpty()) {
            roles = Arrays.stream(user.getRolesString().split(","))
                          .map(String::trim)
                          .filter(role -> !role.isEmpty())
                          .collect(Collectors.toSet());
        } else if (user.getRoles() != null && !user.getRoles().isEmpty()) {
            roles = user.getRoles().stream()
                       .map(role -> role.name())
                       .collect(Collectors.toSet());
        }

        // Default to USER role if no roles found
        if (roles.isEmpty()) {
            roles.add("USER");
        }

        // Format dates to strings
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

        return UserResponse.builder()
                .id(user.getId())
                .name(user.getName())
                .email(user.getEmail())
                .imageUrl(user.getImageUrl())
                .roles(roles)
                .emailVerified(user.isEmailVerified())
                .active(user.isActive())
                .createdAt(user.getCreatedAt() != null ? user.getCreatedAt().format(formatter) : null)
                .updatedAt(user.getUpdatedAt() != null ? user.getUpdatedAt().format(formatter) : null)
                .lastLoginAt(user.getLastLoginAt() != null ? user.getLastLoginAt().format(formatter) : null)
                .build();
    }

    /**
     * Get valid roles
     */
    private Set<String> getValidRoles() {
        return Set.of("USER", "ADMIN", "MODERATOR");
    }
}
