package com.lookforx.auth.service;

import com.lookforx.auth.entity.User;
import com.lookforx.auth.enums.AuthProvider;
import com.lookforx.auth.enums.Role;
import com.lookforx.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

/**
 * Custom service that handles loading and saving OAuth2 users (e.g., from Google)
 * during the authentication process.
 * <p>
 * If the user already exists in the system, it updates basic profile info.
 * Otherwise, it creates a new user record in the database.
 */
@Service
@RequiredArgsConstructor
public class CustomOAuth2UserService extends DefaultOAuth2UserService {

    private final UserRepository userRepository;

    /**
     * Loads the OAuth2 user from the authentication provider, and either updates
     * or creates a corresponding user record in the database.
     *
     * @param userRequest the OAuth2 user request
     * @return a {@link DefaultOAuth2User} object representing the authenticated user
     * @throws OAuth2AuthenticationException if the OAuth2 authentication fails
     */
    @Override
    public OAuth2User loadUser(OAuth2UserRequest userRequest) throws OAuth2AuthenticationException {
        OAuth2User oAuth2User = super.loadUser(userRequest);
        Map<String, Object> attributes = oAuth2User.getAttributes();

        String email = (String) attributes.get("email");
        String name = (String) attributes.get("name");
        String picture = (String) attributes.get("picture");

        Optional<User> existingUser = userRepository.findByEmail(email);
        User user;

        if (existingUser.isPresent()) {
            user = existingUser.get();
            user.setName(name);
            user.setImageUrl(picture);
        } else {
            user = User.builder()
                    .email(email)
                    .name(name)
                    .imageUrl(picture)
                    .provider(AuthProvider.GOOGLE)
                    .roles(Collections.singleton(Role.USER))
                    .emailVerified(true)
                    .build();
        }

        user = userRepository.save(user);

        return new DefaultOAuth2User(
                user.getAuthorities(),
                attributes,
                "email"
        );
    }

} 