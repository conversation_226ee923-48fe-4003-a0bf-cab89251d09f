package com.lookforx.auth.service;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.lookforx.auth.dto.AuthResponse;
import com.lookforx.auth.entity.User;
import com.lookforx.auth.enums.AuthProvider;
import com.lookforx.auth.enums.Role;
import com.lookforx.auth.repository.UserRepository;
import com.lookforx.common.events.UserWelcomeEvent;
import com.lookforx.common.kafka.EventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.HashSet;

/**
 * Service responsible for handling OAuth2 authentication and integration,
 * particularly with Google as an identity provider.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OAuth2Service {

    private final UserRepository userRepository;
    private final JwtService jwtService;
    private final GoogleTokenVerifier googleTokenVerifier;
    @Lazy
    private final UserProfileService userProfileService;
    private final ClientRegistrationRepository clientRegistrationRepository;
    private final EventPublisher eventPublisher;

    @Value("${app.oauth2.redirectUri}")
    private String redirectUri;

    /**
     * Builds the Google OAuth2 authorization URL based on client registration settings.
     *
     * @return the Google OAuth2 login URL
     */
    public String getGoogleAuthUrl() {
        ClientRegistration registration = clientRegistrationRepository.findByRegistrationId("google");
        log.info("Using redirect URI: {}", redirectUri);
        
        // Spring Security OAuth2 yapılandırmasındaki redirect URI'yi kullan
        String actualRedirectUri = registration.getRedirectUri();
        log.info("Actual redirect URI from registration: {}", actualRedirectUri);
        
        return String.format("%s?client_id=%s&redirect_uri=%s&response_type=code&scope=%s",
                registration.getProviderDetails().getAuthorizationUri(),
                registration.getClientId(),
                actualRedirectUri,
                String.join(" ", registration.getScopes()));
    }

    /**
     * Handles the callback from Google OAuth2 flow, exchanging the authorization code
     * for an ID token and user information, then generates and returns authentication tokens.
     *
     * @param code the authorization code received from Google
     * @return {@link AuthResponse} containing access and refresh tokens along with user info
     */
    @Transactional
    public AuthResponse handleGoogleCallback(String code) {
        try {
            log.info("Handling Google callback with code: {}", code);
            
            // 1. Google'dan token al
            ClientRegistration registration = clientRegistrationRepository.findByRegistrationId("google");
            if (registration == null) {
                throw new IllegalStateException("Google client registration not found");
            }
            
            // Token endpoint URL'ini al
            String tokenUri = registration.getProviderDetails().getTokenUri();
            
            // Callback URL'i - Spring Security OAuth2 yapılandırmasındaki ile aynı olmalı
            String redirectUri = registration.getRedirectUri();
            log.info("Using redirect URI for token exchange: {}", redirectUri);
            
            // Token almak için POST isteği gönder
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
            map.add("code", code);
            map.add("client_id", registration.getClientId());
            map.add("client_secret", registration.getClientSecret());
            map.add("redirect_uri", redirectUri);
            map.add("grant_type", "authorization_code");
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(
                    tokenUri, request, Map.class);
            
            // 2. ID token'ı al ve doğrula
            String idToken = (String) response.getBody().get("id_token");
            if (idToken == null) {
                throw new IllegalStateException("ID token not found in Google response");
            }
            
            // 3. ID token'ı doğrula ve kullanıcı bilgilerini al
            GoogleIdToken.Payload payload = googleTokenVerifier.verify(idToken);
            String email = payload.getEmail();
            String name = (String) payload.get("name");
            String picture = (String) payload.get("picture");
            
            // 4. Kullanıcıyı veritabanında bul veya oluştur
            Optional<User> existingUser = userRepository.findByEmail(email);
            User user;

            // Kullanıcı bilgilerini ayarla
            user = existingUser.orElseGet(() -> {
                User newUser = User.builder()
                        .email(email)
                        .name(name)
                        .imageUrl(picture)
                        .provider(AuthProvider.GOOGLE)
                        .roles(Collections.singleton(Role.USER))
                        .emailVerified(true)
                        .build();
                User savedUser = userRepository.save(newUser);

                // Create initial profile for new user with Google profile image
                try {
                    userProfileService.createInitialProfileWithImage(savedUser.getId(), savedUser.getEmail(), savedUser.getName(), savedUser.getImageUrl());
                    log.info("Initial profile created for Google user: {} with profile image", savedUser.getEmail());
                } catch (Exception e) {
                    log.warn("Failed to create initial profile for Google user: {}, error: {}", savedUser.getEmail(), e.getMessage());
                }

                // Publish welcome event for new Google user
                try {
                    UserWelcomeEvent welcomeEvent = UserWelcomeEvent.builder()
                            .userId(String.valueOf(savedUser.getId().hashCode())) // Convert ObjectId to Long
                            .userEmail(savedUser.getEmail())
                            .userName(savedUser.getName())
                            .registrationMethod("GOOGLE")
                            .preferredLanguage("EN") // Default to English for now
                            .isEmailVerified(savedUser.getEmailVerified())
                            .serviceName("auth-service")
                            .build();

                    eventPublisher.publishUserEvent(welcomeEvent);
                    log.info("Welcome event published for new Google user: {}", savedUser.getEmail());
                } catch (Exception e) {
                    log.warn("Failed to publish welcome event for Google user: {}, error: {}", savedUser.getEmail(), e.getMessage());
                }

                return savedUser;
            });

            // Mevcut kullanıcıyı güncelle
            if (existingUser.isPresent()) {
                User updatedUser = existingUser.get();
                updatedUser.setName(name);
                updatedUser.setImageUrl(picture);
                updatedUser.setLastLoginAt(LocalDateTime.now());

                // DB'deki mevcut roles'ü koru
                log.info("Existing user {} has roles: {}", updatedUser.getEmail(), updatedUser.getRoles());

                user = userRepository.save(updatedUser);

                // Update existing user's profile with new Google image
                try {
                    userProfileService.updateProfileImage(updatedUser.getId(), picture);
                    log.info("Profile image updated for existing Google user: {}", updatedUser.getEmail());
                } catch (Exception e) {
                    log.warn("Failed to update profile image for existing Google user: {}, error: {}", updatedUser.getEmail(), e.getMessage());
                }
            }
            
            // JWT token oluştur
            String accessToken = jwtService.generateAccessToken(user);
            String refreshToken = jwtService.generateRefreshToken(user);
            
            // Kullanıcı bilgilerini log'a yaz
            log.info("User info for token: id={}, name={}, email={}, imageUrl={}", 
                    user.getId(), user.getName(), user.getEmail(), user.getImageUrl());
            
            // AuthResponse döndür
            return AuthResponse.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType("Bearer")
                    .expiresIn(jwtService.getAccessTokenExpirationTime())
                    .user(user.toUserResponse())
                    .build();
            
        } catch (Exception e) {
            log.error("Error handling Google callback", e);
            throw new RuntimeException("Error handling Google callback: " + e.getMessage(), e);
        }
    }

    /**
     * Handles login flow using a Google-issued ID token, verifies the token,
     * creates or updates the user, and returns access and refresh tokens.
     *
     * @param idToken the ID token received from Google client
     * @return {@link AuthResponse} with JWT tokens and user information
     */
    @Transactional
    public AuthResponse handleGoogleLogin(String idToken) {
        try {
            GoogleIdToken.Payload payload = googleTokenVerifier.verify(idToken);
            String email = payload.getEmail();
            String name = (String) payload.get("name");
            String picture = (String) payload.get("picture");

            Optional<User> existingUser = userRepository.findByEmail(email);
            User user;

            if (existingUser.isPresent()) {
                user = existingUser.get();
                // Mevcut kullanıcının bilgilerini güncelle
                user.setName(name);
                user.setImageUrl(picture);
                user.setLastLoginAt(LocalDateTime.now());

                // DB'deki mevcut roles'ü koru
                log.info("Existing user {} has roles: {}", user.getEmail(), user.getRoles());
            } else {
                // Yeni kullanıcı oluştur
                user = User.builder()
                        .email(email)
                        .name(name)
                        .imageUrl(picture)
                        .provider(AuthProvider.GOOGLE)
                        .roles(Collections.singleton(Role.USER))
                        .emailVerified(true)
                        .build();
            }

            user = userRepository.save(user);

            Authentication authentication = new UsernamePasswordAuthenticationToken(
                    user,
                    null,
                    user.getAuthorities()
            );
            SecurityContextHolder.getContext().setAuthentication(authentication);

            String accessToken = jwtService.generateAccessToken(user);
            String refreshToken = jwtService.generateRefreshToken(user);

            return AuthResponse.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType("Bearer")
                    .expiresIn(jwtService.getAccessTokenExpirationTime())
                    .user(user.toUserResponse())
                    .build();

        } catch (GeneralSecurityException | IOException e) {
            throw new IllegalArgumentException("Google token verification failed", e);
        }
    }

    /**
     * Test method to simulate Google OAuth registration for testing welcome events
     */
    @Transactional
    public AuthResponse testGoogleRegistration(String email, String name, String googleId) {
        log.info("Test Google registration for email: {}", email);

        // Check if user already exists
        Optional<User> existingUser = userRepository.findByEmail(email);

        User user;
        boolean isNewUser = false;

        if (existingUser.isPresent()) {
            user = existingUser.get();
            log.info("Existing user found for test registration: {}", email);
        } else {
            // Create new user - Note: googleId is stored in provider field as GOOGLE
            Set<Role> userRoles = new HashSet<>();
            userRoles.add(Role.USER);

            user = User.builder()
                    .email(email)
                    .name(name)
                    .provider(AuthProvider.GOOGLE)
                    .roles(userRoles)
                    .emailVerified(true)
                    .active(true)
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();

            user = userRepository.save(user);
            isNewUser = true;
            log.info("New test user created: {}", email);
        }

        // Only publish event for NEW users
        if (isNewUser) {
            try {
                UserWelcomeEvent welcomeEvent = UserWelcomeEvent.builder()
                        .userId(String.valueOf(user.getId().hashCode()))
                        .userEmail(user.getEmail())
                        .userName(user.getName())
                        .registrationMethod("GOOGLE")
                        .preferredLanguage("EN")
                        .isEmailVerified(user.isEmailVerified())
                        .serviceName("auth-service")
                        .build();

                eventPublisher.publishUserEvent(welcomeEvent);
                log.info("Welcome event published for test Google user: {}", user.getEmail());
            } catch (Exception e) {
                log.warn("Failed to publish welcome event for test Google user: {}, error: {}", user.getEmail(), e.getMessage());
            }
        }

        // Generate tokens - JwtService expects UserDetails
        String accessToken = jwtService.generateAccessToken(user);
        String refreshToken = jwtService.generateRefreshToken(user);

        // Set authentication context
        Authentication authentication = new UsernamePasswordAuthenticationToken(
                user.getEmail(), null, user.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);

        return AuthResponse.builder()
                .accessToken(accessToken)
                .refreshToken(refreshToken)
                .user(user.toUserResponse())
                .build();
    }

}
