package com.lookforx.auth.service;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Collections;

/**
 * A component responsible for verifying Google ID tokens issued during OAuth2 authentication.
 * <p>
 * This class uses Google's {@link GoogleIdTokenVerifier} to validate the token signature
 * and ensure the token was issued for the configured client ID.
 */
@Component
public class GoogleTokenVerifier {

    private final GoogleIdTokenVerifier verifier;

    /**
     * Constructs a {@code GoogleTokenVerifier} using the provided Google client ID.
     * The verifier will only accept tokens issued for this audience.
     *
     * @param clientId the Google OAuth2 client ID configured in application properties
     */
    public GoogleTokenVerifier(@Value("${spring.security.oauth2.client.registration.google.client-id}") String clientId) {
        this.verifier = new GoogleIdTokenVerifier.Builder(new NetHttpTransport(), new GsonFactory())
                .setAudience(Collections.singletonList(clientId))
                .build();
    }

    /**
     * Verifies the provided Google ID token string.
     *
     * @param idTokenString the ID token returned from the Google Sign-In client
     * @return the {@link GoogleIdToken.Payload} if the token is valid
     * @throws GeneralSecurityException if there is a cryptographic error during verification
     * @throws IOException              if the token cannot be retrieved or parsed
     * @throws IllegalArgumentException if the token is invalid or verification fails
     */
    public GoogleIdToken.Payload verify(String idTokenString) throws GeneralSecurityException, IOException {
        GoogleIdToken idToken = verifier.verify(idTokenString);
        if (idToken == null) {
            throw new IllegalArgumentException("Invalid ID token");
        }
        return idToken.getPayload();
    }

}