package com.lookforx.auth.service;

import com.lookforx.auth.dto.*;
import com.lookforx.auth.entity.UserProfile;
import com.lookforx.auth.enums.UserType;
import com.lookforx.auth.exception.UserNotFoundException;
import com.lookforx.auth.repository.UserProfileRepository;
import com.lookforx.auth.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.context.annotation.Lazy;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for managing user profiles
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserProfileService {

    private final UserProfileRepository userProfileRepository;
    private final UserRepository userRepository;
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    /**
     * Create initial user profile automatically after registration/login
     */
    @Transactional
    public UserProfileResponse createInitialProfile(String userId, String email, String fullName) {
        log.info("Creating initial profile for user: {} with email: {}", userId, email);

        // Check if profile already exists
        if (userProfileRepository.findByUserId(userId).isPresent()) {
            log.info("Profile already exists for user: {}", userId);
            return getProfileByUserId(userId);
        }

        // Parse full name into first and last name
        String firstName = null;
        String lastName = null;
        if (fullName != null && !fullName.trim().isEmpty()) {
            String[] nameParts = fullName.trim().split("\\s+");
            if (nameParts.length == 1) {
                // Sadece bir kelime varsa firstName olarak kullan
                firstName = nameParts[0];
            } else if (nameParts.length >= 2) {
                // Son kelime lastName, geri kalanı firstName
                lastName = nameParts[nameParts.length - 1];
                firstName = String.join(" ", java.util.Arrays.copyOfRange(nameParts, 0, nameParts.length - 1));
            }
        }

        // Generate initial username from email
        String initialUsername = generateUsernameFromEmail(email);

        UserProfile profile = UserProfile.builder()
                .userId(userId)
                .email(email)
                .firstName(firstName)
                .lastName(lastName)
                .username(initialUsername)
                .userType(UserType.REGULAR)
                .preferredLanguage("tr")
                .profileVisibility(com.lookforx.auth.enums.ProfileVisibility.PUBLIC)
                .build();

        // Calculate initial completion percentage
        profile.calculateCompletionPercentage();

        UserProfile savedProfile = userProfileRepository.save(profile);
        log.info("Initial profile created successfully for user: {} with completion: {}%",
                userId, savedProfile.getCompletionPercentage());

        return convertToResponse(savedProfile);
    }

    /**
     * Create initial user profile with profile image (for OAuth users)
     */
    @Transactional
    public UserProfileResponse createInitialProfileWithImage(String userId, String email, String fullName, String profileImageUrl) {
        log.info("Creating initial profile with image for user: {} with email: {}", userId, email);

        // Check if profile already exists
        if (userProfileRepository.findByUserId(userId).isPresent()) {
            log.info("Profile already exists for user: {}", userId);
            return getProfileByUserId(userId);
        }

        // Parse full name into first and last name
        String firstName = null;
        String lastName = null;
        if (fullName != null && !fullName.trim().isEmpty()) {
            String[] nameParts = fullName.trim().split("\\s+");
            if (nameParts.length == 1) {
                // Sadece bir kelime varsa firstName olarak kullan
                firstName = nameParts[0];
            } else if (nameParts.length >= 2) {
                // Son kelime lastName, geri kalanı firstName
                lastName = nameParts[nameParts.length - 1];
                firstName = String.join(" ", java.util.Arrays.copyOfRange(nameParts, 0, nameParts.length - 1));
            }
        }

        // Generate initial username from email
        String initialUsername = generateUsernameFromEmail(email);

        UserProfile profile = UserProfile.builder()
                .userId(userId)
                .email(email)
                .firstName(firstName)
                .lastName(lastName)
                .username(initialUsername)
                .userType(UserType.REGULAR)
                .preferredLanguage("tr")
                .profileVisibility(com.lookforx.auth.enums.ProfileVisibility.PUBLIC)
                .profilePhotoUrl(profileImageUrl) // Set Google profile image
                .build();

        // Calculate initial completion percentage
        profile.calculateCompletionPercentage();

        UserProfile savedProfile = userProfileRepository.save(profile);
        log.info("Initial profile with image created successfully for user: {} with completion: {}%",
                userId, savedProfile.getCompletionPercentage());

        return convertToResponse(savedProfile);
    }

    /**
     * Create user profile
     */
    @Transactional
    public UserProfileResponse createProfile(String userEmail, CreateUserProfileRequest request) {
        log.info("Creating profile for user: {}", userEmail);

        // Get user by email
        var user = userRepository.findByEmail(userEmail)
                .orElseThrow(() -> UserNotFoundException.forEmail(userEmail));

        String userId = user.getId();
        
        // Check if profile already exists
        if (userProfileRepository.findByUserId(userId).isPresent()) {
            throw new IllegalStateException("Profile already exists for user: " + userId);
        }
        
        // Check username uniqueness
        if (userProfileRepository.existsByUsername(request.getUsername())) {
            throw new IllegalArgumentException("Username already exists: " + request.getUsername());
        }
        
        // No validation required - all fields are optional
        
        UserProfile profile = UserProfile.builder()
                .userId(userId)
                .username(request.getUsername())
                .userType(request.getUserType())
                .birthDate(request.getBirthDate())
                .nationality(request.getNationality())
                .preferredLanguage(request.getPreferredLanguage() != null ? request.getPreferredLanguage() : "tr")
                .mobilePhone(request.getMobilePhone())
                .landlinePhone(request.getLandlinePhone())
                .country(request.getCountry())
                .city(request.getCity())
                .district(request.getDistrict())
                .neighborhood(request.getNeighborhood())
                .street(request.getStreet())
                .doorNumber(request.getDoorNumber())
                .postalCode(request.getPostalCode())
                .fullAddress(request.getFullAddress())
                .companyName(request.getCompanyName())
                .taxNumber(request.getTaxNumber())
                .companyType(request.getCompanyType())
                .website(request.getWebsite())
                .companyAddress(request.getCompanyAddress())
                .profileVisibility(request.getProfileVisibility() != null ? 
                    request.getProfileVisibility() : com.lookforx.auth.enums.ProfileVisibility.PUBLIC)
                .build();
        
        // Calculate completion percentage
        profile.calculateCompletionPercentage();
        
        UserProfile savedProfile = userProfileRepository.save(profile);
        log.info("Profile created successfully for user: {} with completion: {}%", 
                userId, savedProfile.getCompletionPercentage());
        
        return convertToResponse(savedProfile);
    }

    /**
     * Get user profile by user ID
     */
    public UserProfileResponse getProfileByUserId(String userId) {
        log.info("Getting profile for user: {}", userId);

        // Check if any profiles exist
        long totalProfiles = userProfileRepository.count();
        log.info("Total profiles in database: {}", totalProfiles);

        // Try to find profile
        var profileOpt = userProfileRepository.findByUserId(userId);
        if (profileOpt.isEmpty()) {
            log.warn("Profile not found for user: {}", userId);
            // List all profiles for debugging
            var allProfiles = userProfileRepository.findAll();
            log.info("Available profiles: {}", allProfiles.stream()
                .map(p -> "userId=" + p.getUserId() + ", username=" + p.getUsername())
                .toList());
            throw UserNotFoundException.forId(userId);
        }

        UserProfile profile = profileOpt.get();
        log.info("Profile found for user: {}, username: {}", userId, profile.getUsername());

        return convertToResponse(profile);
    }

    /**
     * Get user profile by username
     */
    public UserProfileResponse getProfileByUsername(String username) {
        log.info("Getting profile for username: {}", username);
        
        UserProfile profile = userProfileRepository.findByUsername(username)
                .orElseThrow(() -> UserNotFoundException.forUsername(username));
        
        return convertToResponse(profile);
    }

    /**
     * Update user profile
     */
    @Transactional
    public UserProfileResponse updateProfile(String userId, UpdateUserProfileRequest request) {
        log.info("Updating profile for user: {}", userId);
        log.info("Update request: firstName={}, lastName={}, mobilePhone={}",
                request.getFirstName(), request.getLastName(), request.getMobilePhone());

        UserProfile profile = userProfileRepository.findByUserId(userId)
                .orElseThrow(() -> UserNotFoundException.forId(userId));

        log.info("Found profile: firstName={}, lastName={}, mobilePhone={}",
                profile.getFirstName(), profile.getLastName(), profile.getMobilePhone());
        
        // Check username uniqueness if changed
        if (request.getUsername() != null && !request.getUsername().equals(profile.getUsername())) {
            if (userProfileRepository.existsByUsernameAndUserIdNot(request.getUsername(), userId)) {
                throw new IllegalArgumentException("Username already exists: " + request.getUsername());
            }
            profile.setUsername(request.getUsername());
        }
        
        // Update fields if provided
        if (request.getUserType() != null) {
            profile.setUserType(request.getUserType());
            // No validation required - all fields are optional
        }
        
        updateProfileFields(profile, request);
        
        // Recalculate completion percentage
        profile.calculateCompletionPercentage();
        
        UserProfile savedProfile = userProfileRepository.save(profile);
        log.info("Profile updated successfully for user: {} with completion: {}%", 
                userId, savedProfile.getCompletionPercentage());
        
        return convertToResponse(savedProfile);
    }

    /**
     * Update profile image (for OAuth users)
     */
    @Transactional
    public void updateProfileImage(String userId, String imageUrl) {
        log.info("Updating profile image for user: {}", userId);

        var profileOpt = userProfileRepository.findByUserId(userId);
        if (profileOpt.isPresent()) {
            UserProfile profile = profileOpt.get();
            profile.setProfilePhotoUrl(imageUrl);
            profile.calculateCompletionPercentage();
            userProfileRepository.save(profile);
            log.info("Profile image updated successfully for user: {}", userId);
        } else {
            log.warn("Profile not found for user: {}, cannot update image", userId);
        }
    }

    /**
     * Delete user profile
     */
    @Transactional
    public void deleteProfile(String userId) {
        log.info("Deleting profile for user: {}", userId);
        
        UserProfile profile = userProfileRepository.findByUserId(userId)
                .orElseThrow(() -> UserNotFoundException.forId(userId));
        
        userProfileRepository.delete(profile);
        log.info("Profile deleted successfully for user: {}", userId);
    }

    /**
     * Check username availability
     */
    public boolean isUsernameAvailable(String username) {
        return !userProfileRepository.existsByUsername(username);
    }

    /**
     * Search profiles
     */
    public List<UserProfileResponse> searchProfiles(String searchTerm) {
        log.info("Searching profiles with term: {}", searchTerm);
        
        List<UserProfile> profiles = userProfileRepository.searchByUsernameOrCompanyName(searchTerm);
        
        return profiles.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    /**
     * Get public profiles for discovery
     */
    public List<UserProfileResponse> getPublicProfiles() {
        log.info("Getting public completed profiles");
        
        List<UserProfile> profiles = userProfileRepository.findPublicCompletedProfiles();
        
        return profiles.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    // Validation removed - all fields are optional

    /**
     * Update profile fields from request
     */
    private void updateProfileFields(UserProfile profile, UpdateUserProfileRequest request) {
        if (request.getFirstName() != null) {
            log.info("Updating firstName from '{}' to '{}'", profile.getFirstName(), request.getFirstName());
            profile.setFirstName(request.getFirstName());
        }
        if (request.getLastName() != null) {
            log.info("Updating lastName from '{}' to '{}'", profile.getLastName(), request.getLastName());
            profile.setLastName(request.getLastName());
        }
        if (request.getBirthDate() != null) profile.setBirthDate(request.getBirthDate());
        if (request.getNationality() != null) profile.setNationality(request.getNationality());
        if (request.getPreferredLanguage() != null) profile.setPreferredLanguage(request.getPreferredLanguage());
        if (request.getMobilePhone() != null) profile.setMobilePhone(request.getMobilePhone());
        if (request.getLandlinePhone() != null) profile.setLandlinePhone(request.getLandlinePhone());
        if (request.getCountry() != null) profile.setCountry(request.getCountry());
        if (request.getCity() != null) profile.setCity(request.getCity());
        if (request.getDistrict() != null) profile.setDistrict(request.getDistrict());
        if (request.getNeighborhood() != null) profile.setNeighborhood(request.getNeighborhood());
        if (request.getStreet() != null) profile.setStreet(request.getStreet());
        if (request.getDoorNumber() != null) profile.setDoorNumber(request.getDoorNumber());
        if (request.getPostalCode() != null) profile.setPostalCode(request.getPostalCode());
        if (request.getFullAddress() != null) profile.setFullAddress(request.getFullAddress());
        if (request.getCompanyName() != null) profile.setCompanyName(request.getCompanyName());
        if (request.getTaxNumber() != null) profile.setTaxNumber(request.getTaxNumber());
        if (request.getCompanyType() != null) profile.setCompanyType(request.getCompanyType());
        if (request.getWebsite() != null) profile.setWebsite(request.getWebsite());
        if (request.getCompanyAddress() != null) profile.setCompanyAddress(request.getCompanyAddress());
        if (request.getProfileVisibility() != null) profile.setProfileVisibility(request.getProfileVisibility());
    }

    /**
     * Generate unique username from email
     */
    private String generateUsernameFromEmail(String email) {
        String baseUsername = email.substring(0, email.indexOf("@"))
                .replaceAll("[^a-zA-Z0-9]", "")
                .toLowerCase();

        // Ensure minimum length
        if (baseUsername.length() < 3) {
            baseUsername = "user" + baseUsername;
        }

        // Check uniqueness and add suffix if needed
        String username = baseUsername;
        int suffix = 1;
        while (userProfileRepository.existsByUsername(username)) {
            username = baseUsername + suffix;
            suffix++;
        }

        return username;
    }

    /**
     * Convert UserProfile to UserProfileResponse
     */
    private UserProfileResponse convertToResponse(UserProfile profile) {
        return UserProfileResponse.builder()
                .id(profile.getId())
                .userId(profile.getUserId())
                .username(profile.getUsername())
                .firstName(profile.getFirstName())
                .lastName(profile.getLastName())
                .email(profile.getEmail())
                .userType(profile.getUserType())
                .birthDate(profile.getBirthDate())
                .nationality(profile.getNationality())
                .preferredLanguage(profile.getPreferredLanguage())
                .profilePhotoUrl(profile.getProfilePhotoUrl())
                .mobilePhone(profile.getMobilePhone())
                .landlinePhone(profile.getLandlinePhone())
                .country(profile.getCountry())
                .city(profile.getCity())
                .district(profile.getDistrict())
                .neighborhood(profile.getNeighborhood())
                .street(profile.getStreet())
                .doorNumber(profile.getDoorNumber())
                .postalCode(profile.getPostalCode())
                .fullAddress(profile.getFullAddress())
                .companyName(profile.getCompanyName())
                .taxNumber(profile.getTaxNumber())
                .companyType(profile.getCompanyType())
                .website(profile.getWebsite())
                .companyAddress(profile.getCompanyAddress())
                .companyLogoUrl(profile.getCompanyLogoUrl())
                .profileVisibility(profile.getProfileVisibility())
                .profileCompleted(profile.getProfileCompleted())
                .completionPercentage(profile.getCompletionPercentage())
                .displayName(profile.getDisplayName())
                .profileImageUrl(profile.getProfileImageUrl())
                .createdAt(profile.getCreatedAt() != null ? profile.getCreatedAt().format(DATE_TIME_FORMATTER) : null)
                .updatedAt(profile.getUpdatedAt() != null ? profile.getUpdatedAt().format(DATE_TIME_FORMATTER) : null)
                .build();
    }
}
