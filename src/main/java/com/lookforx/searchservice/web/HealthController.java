package com.lookforx.searchservice.web;

import com.lookforx.searchservice.service.SearchCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Health check controller for search service.
 * Provides endpoints to check service and Elasticsearch health.
 */
@RestController
@RequestMapping("/api/v1/health")
@RequiredArgsConstructor
@Slf4j
public class HealthController {

    private final SearchCategoryService searchCategoryService;

    /**
     * Basic health check endpoint.
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "search-microservice");
        health.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(health);
    }

    /**
     * Elasticsearch health check endpoint.
     */
    @GetMapping("/elasticsearch")
    public ResponseEntity<Map<String, Object>> elasticsearchHealth() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            boolean isHealthy = searchCategoryService.isIndexHealthy();
            
            if (isHealthy) {
                health.put("status", "UP");
                health.put("elasticsearch", "CONNECTED");
                health.put("index", "HEALTHY");
                log.debug("Elasticsearch health check: HEALTHY");
                return ResponseEntity.ok(health);
            } else {
                health.put("status", "DOWN");
                health.put("elasticsearch", "DISCONNECTED");
                health.put("index", "UNHEALTHY");
                log.warn("Elasticsearch health check: UNHEALTHY");
                return ResponseEntity.status(503).body(health);
            }
        } catch (Exception e) {
            health.put("status", "DOWN");
            health.put("elasticsearch", "ERROR");
            health.put("error", e.getMessage());
            log.error("Elasticsearch health check failed", e);
            return ResponseEntity.status(503).body(health);
        }
    }
}
