package com.lookforx.searchservice.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.LocalDate;
import java.util.Map;

/**
 * Elasticsearch document for category search functionality.
 * This represents the structure of category data stored in Elasticsearch.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
@Document(indexName = "categories")
public class SearchCategoryDocument {
    
    @Id
    private String id;
    
    @Field(type = FieldType.Long)
    private Long parentId;
    
    @Field(type = FieldType.Object)
    private Map<String, String> translations;
    
    @Field(type = FieldType.Keyword)
    private String type;
    
    @Field(type = FieldType.Integer)
    private Integer level;
    
    @Field(type = FieldType.Date)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate createdAt;

    @Field(type = FieldType.Date)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate updatedAt;
    
    @Field(type = FieldType.Keyword)
    private String createdBy;
}
