package com.lookforx.searchservice.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.IndexOperations;
import org.springframework.data.elasticsearch.core.document.Document;
import org.springframework.stereotype.Component;
import com.lookforx.searchservice.domain.SearchCategoryDocument;
import org.springframework.core.io.ClassPathResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.Map;

/**
 * Elasticsearch health check component that runs on application startup.
 * Verifies Elasticsearch connection and index health.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ElasticsearchHealthCheck {

    private final ElasticsearchOperations elasticsearchOperations;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Performs Elasticsearch health check when application is ready.
     * Creates index with proper shard configuration to prevent shard issues.
     */
    @EventListener(ApplicationReadyEvent.class)
    public void checkElasticsearchHealth() {
        try {
            log.info("Performing Elasticsearch health check...");

            IndexOperations indexOps = elasticsearchOperations.indexOps(SearchCategoryDocument.class);

            // Check if index exists
            boolean indexExists = indexOps.exists();
            log.info("Categories index exists: {}", indexExists);

            if (!indexExists) {
                log.warn("Categories index does not exist. Creating index with proper shard configuration...");

                // Create index with custom settings to prevent shard issues
                createIndexWithSettings(indexOps);

                // Put mapping
                indexOps.putMapping();
                log.info("Categories index mapping applied");
            } else {
                // Check index health
                checkIndexHealth(indexOps);
            }

            // Test connection with a simple operation
            elasticsearchOperations.indexOps(SearchCategoryDocument.class).refresh();
            log.info("Elasticsearch connection test successful");

        } catch (Exception e) {
            log.error("Elasticsearch health check failed: {}", e.getMessage(), e);
            log.error("Search service may not function properly without Elasticsearch connection");

            // Try to recover from shard issues
            tryRecoverFromShardIssues();
        }
    }

    /**
     * Creates index with custom settings to prevent shard issues
     */
    private void createIndexWithSettings(IndexOperations indexOps) {
        try {
            // Load settings from JSON file
            Document settings = loadSettingsFromFile();

            boolean created = indexOps.create(settings);
            log.info("Categories index created with custom settings: {}", created);

        } catch (Exception e) {
            log.warn("Failed to create index with custom settings, trying with default settings: {}", e.getMessage());

            // Fallback to simple creation with basic settings
            Document basicSettings = Document.create()
                .append("number_of_shards", 1)
                .append("number_of_replicas", 0)
                .append("auto_expand_replicas", "0-1");

            boolean created = indexOps.create(basicSettings);
            log.info("Categories index created with basic settings: {}", created);
        }
    }

    /**
     * Loads Elasticsearch settings from JSON file
     */
    private Document loadSettingsFromFile() throws IOException {
        ClassPathResource resource = new ClassPathResource("elasticsearch-settings.json");
        Map<String, Object> settingsMap = objectMapper.readValue(resource.getInputStream(), Map.class);
        return Document.from(settingsMap);
    }

    /**
     * Checks index health and tries to fix shard issues
     */
    private void checkIndexHealth(IndexOperations indexOps) {
        try {
            // Try to refresh the index to check if it's healthy
            indexOps.refresh();
            log.info("Categories index is healthy");
        } catch (Exception e) {
            log.warn("Categories index health check failed: {}", e.getMessage());

            // Try to recreate index if it has shard issues
            if (e.getMessage().contains("unavailable_shards_exception") ||
                e.getMessage().contains("primary shard is not active")) {

                log.warn("Detected shard issues, attempting to recreate index...");
                recreateIndex(indexOps);
            }
        }
    }

    /**
     * Recreates the index to fix shard issues
     */
    private void recreateIndex(IndexOperations indexOps) {
        try {
            log.warn("Deleting problematic index...");
            indexOps.delete();

            log.info("Recreating index with proper shard configuration...");
            createIndexWithSettings(indexOps);

            // Put mapping
            indexOps.putMapping();
            log.info("Index recreated successfully");

        } catch (Exception e) {
            log.error("Failed to recreate index: {}", e.getMessage(), e);
        }
    }

    /**
     * Tries to recover from shard issues
     */
    private void tryRecoverFromShardIssues() {
        try {
            log.info("Attempting to recover from shard issues...");

            IndexOperations indexOps = elasticsearchOperations.indexOps(SearchCategoryDocument.class);

            // Force recreate the index
            recreateIndex(indexOps);

        } catch (Exception e) {
            log.error("Failed to recover from shard issues: {}", e.getMessage(), e);
        }
    }
}
