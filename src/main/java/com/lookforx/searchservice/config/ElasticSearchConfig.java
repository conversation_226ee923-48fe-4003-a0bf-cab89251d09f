package com.lookforx.searchservice.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchConfiguration;
import org.springframework.data.elasticsearch.repository.config.EnableElasticsearchRepositories;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.context.annotation.Bean;

import java.time.Duration;

@Configuration
@EnableElasticsearchRepositories(basePackages = "com.lookforx.searchservice.repository")
@ComponentScan
@Slf4j
public class ElasticSearchConfig extends ElasticsearchConfiguration {

    @Value("${elasticsearch.url}")
    private String elasticsearchUrl;

    @Override
    public ClientConfiguration clientConfiguration() {
        log.info("Configuring Elasticsearch client with URL: {}", elasticsearchUrl);

        return ClientConfiguration.builder()
                .connectedTo(elasticsearchUrl)
                .withConnectTimeout(Duration.ofSeconds(2))   // Reduced to 2s for faster failure
                .withSocketTimeout(Duration.ofSeconds(3))    // Reduced to 3s for faster failure
                .build();
    }

    /**
     * Custom RestClient bean with aggressive timeout settings
     */
    @Bean
    public RestClient restClient() {
        String[] hostAndPort = elasticsearchUrl.split(":");
        String host = hostAndPort[0];
        int port = Integer.parseInt(hostAndPort[1]);

        return RestClient.builder(new HttpHost(host, port, "http"))
                .setRequestConfigCallback(requestConfigBuilder ->
                    requestConfigBuilder
                        .setConnectTimeout(2000)        // 2 seconds connect timeout
                        .setSocketTimeout(3000)         // 3 seconds socket timeout
                        .setConnectionRequestTimeout(1000) // 1 second connection request timeout
                )
                .setHttpClientConfigCallback(httpClientBuilder ->
                    httpClientBuilder
                        .setMaxConnTotal(10)            // Max 10 connections
                        .setMaxConnPerRoute(5)          // Max 5 connections per route
                )
                .build();
    }
}