package com.lookforx.searchservice.controller;

import com.lookforx.searchservice.domain.SearchCategoryDocument;
import com.lookforx.searchservice.service.SearchCategoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/categories")
@RequiredArgsConstructor
public class SearchCategoryController {

    private final SearchCategoryService searchCategoryService;

    /**
     * Simple search endpoint for client applications.
     * Returns a list instead of paginated results.
     *
     * @param query the text to search
     * @param mode  FULL_TEXT | FUZZY | PHRASE (default: FULL_TEXT)
     * @param limit maximum number of results (default: 50)
     * @param language specific language to search in (optional, searches all languages if not specified)
     */
    @GetMapping("/search")
    public ResponseEntity<java.util.List<SearchCategoryDocument>> searchSimple(
            @RequestParam String query,
            @RequestParam(defaultValue = "FULL_TEXT") String mode,
            @RequestParam(defaultValue = "50") int limit,
            @RequestParam(required = false) String language
    ) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by("translations.EN.keyword").ascending());
        Page<SearchCategoryDocument> results = searchCategoryService.search(query, mode, language, pageable);

        if (results == null) {
            return ResponseEntity.ok(java.util.Collections.emptyList());
        }

        return ResponseEntity.ok(results.getContent());
    }

    /**
     * Index (create) a new category document.
     */
    @PostMapping
    public ResponseEntity<Void> indexCategory(
            @RequestBody SearchCategoryDocument document
    ) {
        searchCategoryService.indexCategory(document);
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

    /**
     * Update an existing category document by ID.
     */
    @PutMapping("/{id}")
    public ResponseEntity<Void> updateCategory(
            @PathVariable String id,
            @RequestBody SearchCategoryDocument document
    ) {
        document.setId(id);
        searchCategoryService.updateCategory(id, document);
        return ResponseEntity.noContent().build();
    }

    /**
     * Delete a category document by ID.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCategory(
            @PathVariable String id
    ) {
        searchCategoryService.deleteCategory(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Clear all category documents from the index.
     */
    @DeleteMapping("/clear-all")
    public ResponseEntity<Void> clearAllCategories() {
        searchCategoryService.clearAllCategories();
        return ResponseEntity.noContent().build();
    }

    /**
     * Bulk index multiple category documents.
     */
    @PostMapping("/bulk-index")
    public ResponseEntity<Void> bulkIndexCategories(
            @RequestBody java.util.List<SearchCategoryDocument> documents
    ) {
        searchCategoryService.bulkIndexCategories(documents);
        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

}
