package com.lookforx.searchservice.repository;

import com.lookforx.searchservice.domain.SearchCategoryDocument;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;

import java.util.List;

public interface SearchCategoryRepository
        extends ElasticsearchRepository<SearchCategoryDocument, String> {

    // Simple search for testing - improved with wildcard and fuzzy matching
    @Query("""
        {
          "bool": {
            "should": [
              {
                "multi_match": {
                  "query": "?0",
                  "fields": [
                    "translations.EN", "translations.TR", "translations.DE", "translations.FR",
                    "translations.ES", "translations.ZH", "translations.HI", "translations.RU",
                    "translations.AR", "translations.PT", "translations.IT", "translations.JA",
                    "translations.KO", "translations.NL", "translations.PL", "translations.DA",
                    "translations.SV", "translations.NO", "translations.FI", "translations.CS",
                    "translations.HU", "translations.RO", "translations.EL", "translations.TH",
                    "translations.VI", "translations.ID", "translations.MS", "translations.HE",
                    "translations.UR", "translations.FA", "translations.BN", "translations.PA"
                  ],
                  "type": "best_fields",
                  "fuzziness": "AUTO",
                  "boost": 3.0
                }
              },
              {
                "query_string": {
                  "query": "*?0*",
                  "fields": [
                    "translations.EN", "translations.TR", "translations.DE", "translations.FR",
                    "translations.ES", "translations.ZH", "translations.HI", "translations.RU",
                    "translations.AR", "translations.PT", "translations.IT", "translations.JA",
                    "translations.KO", "translations.NL", "translations.PL", "translations.DA",
                    "translations.SV", "translations.NO", "translations.FI", "translations.CS",
                    "translations.HU", "translations.RO", "translations.EL", "translations.TH",
                    "translations.VI", "translations.ID", "translations.MS", "translations.HE",
                    "translations.UR", "translations.FA", "translations.BN", "translations.PA"
                  ],
                  "default_operator": "OR",
                  "boost": 1.0
                }
              }
            ],
            "minimum_should_match": 1
          }
        }
        """)
    Page<SearchCategoryDocument> searchSimple(String query, Pageable pageable);

    // Simple full text search
    @Query("""
        {
          "multi_match": {
            "query": "?0",
            "fields": [
              "translations.EN", "translations.TR", "translations.DE", "translations.FR",
              "translations.ES", "translations.ZH", "translations.HI", "translations.RU"
            ]
          }
        }
        """)
    Page<SearchCategoryDocument> searchFullText(String query, Pageable pageable);

    // Simple fuzzy search
    @Query("""
        {
          "multi_match": {
            "query": "?0",
            "fields": [
              "translations.EN", "translations.TR", "translations.DE", "translations.FR",
              "translations.ES", "translations.ZH", "translations.HI", "translations.RU"
            ],
            "fuzziness": "AUTO"
          }
        }
        """)
    Page<SearchCategoryDocument> searchFuzzy(String query, Pageable pageable);

    // Simple phrase search
    @Query("""
        {
          "multi_match": {
            "query": "?0",
            "fields": [
              "translations.EN", "translations.TR", "translations.DE", "translations.FR",
              "translations.ES", "translations.ZH", "translations.HI", "translations.RU"
            ],
            "type": "phrase"
          }
        }
        """)
    Page<SearchCategoryDocument> searchPhrase(String query, Pageable pageable);

    // Search by type
    @Query("""
        {
          "bool": {
            "must": [
              {
                "term": {
                  "type": "?1"
                }
              },
              {
                "multi_match": {
                  "query": "?0",
                  "fields": ["translations.*^2", "translations.EN^3"],
                  "type": "best_fields",
                  "fuzziness": "AUTO"
                }
              }
            ]
          }
        }
        """)
    Page<SearchCategoryDocument> searchByType(String query, String type, Pageable pageable);

    // Search by level
    @Query("""
        {
          "bool": {
            "must": [
              {
                "term": {
                  "level": ?1
                }
              },
              {
                "multi_match": {
                  "query": "?0",
                  "fields": ["translations.*"],
                  "type": "best_fields",
                  "fuzziness": "AUTO"
                }
              }
            ]
          }
        }
        """)
    Page<SearchCategoryDocument> searchByLevel(String query, Integer level, Pageable pageable);

    // Find by type
    List<SearchCategoryDocument> findByType(String type);

    // Find by level
    List<SearchCategoryDocument> findByLevel(Integer level);

    // Find by parent ID
    List<SearchCategoryDocument> findByParentId(Long parentId);

    // Autocomplete search
    @Query("""
        {
          "bool": {
            "should": [
              {
                "match": {
                  "translations.*": {
                    "query": "?0",
                    "analyzer": "autocomplete_analyzer",
                    "boost": 2.0
                  }
                }
              },
              {
                "prefix": {
                  "translations.EN": {
                    "value": "?0",
                    "boost": 1.5
                  }
                }
              }
            ],
            "minimum_should_match": 1
          }
        }
        """)
    Page<SearchCategoryDocument> autocomplete(String query, Pageable pageable);

    // Language-specific search methods
    @Query("""
        {
          "bool": {
            "should": [
              {
                "match": {
                  "translations.?1": {
                    "query": "?0",
                    "boost": 3.0
                  }
                }
              },
              {
                "wildcard": {
                  "translations.?1": {
                    "value": "*?0*",
                    "boost": 1.0
                  }
                }
              }
            ],
            "minimum_should_match": 1
          }
        }
        """)
    Page<SearchCategoryDocument> searchSimpleByLanguage(String query, String language, Pageable pageable);

    @Query("""
        {
          "bool": {
            "should": [
              {
                "multi_match": {
                  "query": "?0",
                  "fields": ["translations.?1"],
                  "fuzziness": "AUTO",
                  "prefix_length": 1,
                  "max_expansions": 50,
                  "boost": 2.0
                }
              },
              {
                "wildcard": {
                  "translations.?1": {
                    "value": "*?0*",
                    "boost": 1.0
                  }
                }
              }
            ],
            "minimum_should_match": 1
          }
        }
        """)
    Page<SearchCategoryDocument> searchFuzzyByLanguage(String query, String language, Pageable pageable);

    @Query("""
        {
          "bool": {
            "should": [
              {
                "multi_match": {
                  "query": "?0",
                  "fields": ["translations.?1"],
                  "type": "phrase",
                  "boost": 3.0
                }
              },
              {
                "multi_match": {
                  "query": "?0",
                  "fields": ["translations.?1"],
                  "type": "phrase_prefix",
                  "boost": 2.0
                }
              },
              {
                "prefix": {
                  "translations.?1": {
                    "value": "?0",
                    "boost": 1.5
                  }
                }
              }
            ],
            "minimum_should_match": 1
          }
        }
        """)
    Page<SearchCategoryDocument> searchPhraseByLanguage(String query, String language, Pageable pageable);
}