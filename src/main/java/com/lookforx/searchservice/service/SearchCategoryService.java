package com.lookforx.searchservice.service;

import com.lookforx.searchservice.domain.SearchCategoryDocument;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface SearchCategoryService {

    /**
     * Advanced search with multiple modes and filters
     */
    Page<SearchCategoryDocument> search(String query, String mode, Pageable pageable);

    /**
     * Advanced search with language filter
     */
    Page<SearchCategoryDocument> search(String query, String mode, String language, Pageable pageable);

    /**
     * Search with type filter
     */
    Page<SearchCategoryDocument> searchByType(String query, String type, String mode, Pageable pageable);

    /**
     * Search with level filter
     */
    Page<SearchCategoryDocument> searchByLevel(String query, Integer level, String mode, Pageable pageable);

    /**
     * Autocomplete search for quick suggestions
     */
    Page<SearchCategoryDocument> autocomplete(String query, Pageable pageable);

    /**
     * Get categories by type
     */
    List<SearchCategoryDocument> getCategoriesByType(String type);

    /**
     * Get categories by level
     */
    List<SearchCategoryDocument> getCategoriesByLevel(Integer level);

    /**
     * Get categories by parent ID
     */
    List<SearchCategoryDocument> getCategoriesByParentId(Long parentId);

    /** Index (create) a new SearchCategoryDocument in Elasticsearch */
    void indexCategory(SearchCategoryDocument document);

    /** Update an existing SearchCategoryDocument (upsert) */
    void updateCategory(String id, SearchCategoryDocument document);

    /** Delete a SearchCategoryDocument by its ID */
    void deleteCategory(String id);

    /** Clear all SearchCategoryDocuments from the index */
    void clearAllCategories();

    /** Bulk index multiple categories */
    void bulkIndexCategories(List<SearchCategoryDocument> documents);

    /** Check if index exists and is healthy */
    boolean isIndexHealthy();

    /** Refresh index to make recent changes searchable */
    void refreshIndex();

}
