package com.lookforx.customersupportservice.dto.request;

import com.lookforx.customersupportservice.domain.enums.TicketPriority;
import com.lookforx.customersupportservice.domain.enums.TicketType;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Request DTO for creating a new ticket
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateTicketRequest {

    /**
     * Ticket type - required
     */
    @NotNull(message = "Ticket type is required")
    private TicketType type;

    /**
     * Ticket subject/title - required
     */
    @NotBlank(message = "Subject is required")
    @Size(min = 5, max = 200, message = "Subject must be between 5 and 200 characters")
    private String subject;

    /**
     * Detailed description - required
     */
    @NotBlank(message = "Description is required")
    @Size(min = 10, max = 5000, message = "Description must be between 10 and 5000 characters")
    private String description;

    /**
     * Priority level - optional, will be set based on ticket type if not provided
     */
    private TicketPriority priority;

    /**
     * Reference ID for complaints (user ID, request ID, bid ID)
     * Required for USER_COMPLAINT, REQUEST_COMPLAINT, BID_COMPLAINT
     */
    private String referenceId;

    /**
     * Reference type for complaints (USER, REQUEST, BID)
     */
    private String referenceType;

    /**
     * User email for communication - auto-filled from authentication
     */
    @Email(message = "Valid email is required")
    private String userEmail;

    /**
     * User name for personalization - auto-filled from authentication
     */
    @Size(min = 2, max = 100, message = "User name must be between 2 and 100 characters")
    private String userName;

    /**
     * Attachments (file URLs) - optional
     */
    @Size(max = 10, message = "Maximum 10 attachments allowed")
    private List<String> attachments;

    /**
     * Tags for categorization - optional
     */
    @Size(max = 20, message = "Maximum 20 tags allowed")
    private List<String> tags;

    /**
     * Additional metadata - optional
     */
    private Map<String, Object> metadata;

    /**
     * Contact form specific fields
     */
    private ContactFormData contactForm;

    /**
     * Complaint specific fields
     */
    private ComplaintData complaint;

    /**
     * Suggestion specific fields
     */
    private SuggestionData suggestion;

    /**
     * Contact form specific data
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactFormData {
        
        @Size(max = 100, message = "Company name cannot exceed 100 characters")
        private String company;
        
        @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Invalid phone number format")
        private String phone;
        
        @Size(max = 50, message = "Department cannot exceed 50 characters")
        private String department;
        
        private boolean requestCallback;
        
        private String preferredContactTime;
    }

    /**
     * Complaint specific data
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComplaintData {
        
        @NotBlank(message = "Incident date is required for complaints")
        private String incidentDate;
        
        @Size(max = 1000, message = "Evidence description cannot exceed 1000 characters")
        private String evidenceDescription;
        
        private List<String> evidenceAttachments;
        
        @Size(max = 500, message = "Desired outcome cannot exceed 500 characters")
        private String desiredOutcome;
        
        private boolean previouslyReported;
        
        private String previousReportReference;
    }

    /**
     * Suggestion specific data
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SuggestionData {
        
        @NotBlank(message = "Category is required for suggestions")
        @Size(max = 50, message = "Category cannot exceed 50 characters")
        private String category;
        
        @Size(max = 1000, message = "Current situation cannot exceed 1000 characters")
        private String currentSituation;
        
        @Size(max = 1000, message = "Proposed solution cannot exceed 1000 characters")
        private String proposedSolution;
        
        @Size(max = 1000, message = "Expected benefits cannot exceed 1000 characters")
        private String expectedBenefits;
        
        private Integer implementationPriority;
        
        private boolean willingToTestBeta;
    }

    // Validation methods

    /**
     * Validate reference ID requirement based on ticket type
     */
    public boolean isReferenceIdValid() {
        if (type != null && type.requiresReferenceId()) {
            return referenceId != null && !referenceId.trim().isEmpty();
        }
        return true;
    }

    /**
     * Validate type-specific data
     */
    public boolean isTypeSpecificDataValid() {
        if (type == null) {
            return false;
        }

        return switch (type) {
            case CONTACT_FORM -> true; // Allow contact form without specific data
            case USER_COMPLAINT, REQUEST_COMPLAINT, BID_COMPLAINT ->
                isReferenceIdValid(); // Only require reference ID, not complaint data
            case SUGGESTION -> true; // Allow suggestion without specific data
            default -> true;
        };
    }

    /**
     * Get effective priority (use provided or default based on type)
     */
    public TicketPriority getEffectivePriority() {
        if (priority != null) {
            return priority;
        }
        return type != null ? type.getDefaultPriority() : TicketPriority.LOW;
    }
}
