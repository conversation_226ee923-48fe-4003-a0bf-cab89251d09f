package com.lookforx.customersupportservice.dto.request;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Request DTO for updating an existing ticket by user
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateTicketRequest {

    /**
     * Additional description or update to the ticket
     */
    @Size(max = 2000, message = "Additional description cannot exceed 2000 characters")
    private String additionalDescription;

    /**
     * New attachments to add
     */
    @Size(max = 5, message = "Maximum 5 additional attachments allowed")
    private List<String> newAttachments;

    /**
     * Tags to add
     */
    @Size(max = 10, message = "Maximum 10 additional tags allowed")
    private List<String> newTags;

    /**
     * User comment/update
     */
    @Size(min = 1, max = 1000, message = "Comment must be between 1 and 1000 characters")
    private String comment;

    /**
     * Whether user wants to close the ticket
     */
    private boolean requestClose;

    /**
     * Reason for closing (if requestClose is true)
     */
    @Size(max = 500, message = "Close reason cannot exceed 500 characters")
    private String closeReason;

    /**
     * Updated contact information
     */
    private ContactUpdate contactUpdate;

    /**
     * Contact information update
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContactUpdate {

        @Size(max = 100, message = "Email cannot exceed 100 characters")
        private String email;

        @Size(max = 100, message = "Name cannot exceed 100 characters")
        private String name;

        @Size(max = 20, message = "Phone cannot exceed 20 characters")
        private String phone;

        /**
         * Check if this contact update has any changes
         */
        public boolean hasUpdates() {
            return (email != null && !email.trim().isEmpty()) ||
                   (name != null && !name.trim().isEmpty()) ||
                   (phone != null && !phone.trim().isEmpty());
        }
    }

    /**
     * Check if the update request has any meaningful content
     */
    public boolean hasContent() {
        return (additionalDescription != null && !additionalDescription.trim().isEmpty()) ||
               (comment != null && !comment.trim().isEmpty()) ||
               (newAttachments != null && !newAttachments.isEmpty()) ||
               (newTags != null && !newTags.isEmpty()) ||
               requestClose ||
               (contactUpdate != null && contactUpdate.hasUpdates());
    }
}
