package com.lookforx.customersupportservice.dto.request;

import com.lookforx.customersupportservice.domain.enums.TicketPriority;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Request DTO for admin to update ticket
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminUpdateTicketRequest {

    /**
     * New status for the ticket
     */
    private TicketStatus status;

    /**
     * New priority for the ticket
     */
    private TicketPriority priority;

    /**
     * Assign ticket to admin user
     */
    @Size(max = 50, message = "Assigned user ID cannot exceed 50 characters")
    private String assignTo;

    /**
     * Admin comment (visible to user)
     */
    @Size(max = 2000, message = "Admin comment cannot exceed 2000 characters")
    private String adminComment;

    /**
     * Internal note (not visible to user)
     */
    @Size(max = 2000, message = "Internal note cannot exceed 2000 characters")
    private String internalNote;

    /**
     * Resolution details (when resolving ticket)
     */
    @Size(max = 2000, message = "Resolution cannot exceed 2000 characters")
    private String resolution;

    /**
     * Tags to add
     */
    @Size(max = 20, message = "Maximum 20 tags allowed")
    private List<String> addTags;

    /**
     * Tags to remove
     */
    @Size(max = 20, message = "Maximum 20 tags to remove")
    private List<String> removeTags;

    /**
     * Reason for status change (for audit trail)
     */
    @Size(max = 500, message = "Status change reason cannot exceed 500 characters")
    private String statusChangeReason;

    /**
     * Whether to send notification to user
     */
    @Builder.Default
    private boolean notifyUser = true;

    /**
     * Custom notification message (if different from default)
     */
    @Size(max = 1000, message = "Custom notification message cannot exceed 1000 characters")
    private String customNotificationMessage;

    /**
     * Escalation details
     */
    private EscalationDetails escalation;

    /**
     * Escalation specific data
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EscalationDetails {
        
        @Size(max = 100, message = "Escalation reason cannot exceed 100 characters")
        private String reason;
        
        @Size(max = 50, message = "Escalated to user ID cannot exceed 50 characters")
        private String escalatedTo;
        
        @Size(max = 50, message = "Department cannot exceed 50 characters")
        private String department;
        
        @Size(max = 500, message = "Escalation notes cannot exceed 500 characters")
        private String notes;
        
        private TicketPriority newPriority;
    }

    /**
     * Check if the request has any meaningful updates
     */
    public boolean hasUpdates() {
        return status != null ||
               priority != null ||
               (assignTo != null && !assignTo.trim().isEmpty()) ||
               (adminComment != null && !adminComment.trim().isEmpty()) ||
               (internalNote != null && !internalNote.trim().isEmpty()) ||
               (resolution != null && !resolution.trim().isEmpty()) ||
               (addTags != null && !addTags.isEmpty()) ||
               (removeTags != null && !removeTags.isEmpty()) ||
               escalation != null;
    }

    /**
     * Check if this is a status change request
     */
    public boolean isStatusChange() {
        return status != null;
    }

    /**
     * Check if this is an assignment request
     */
    public boolean isAssignment() {
        return assignTo != null && !assignTo.trim().isEmpty();
    }

    /**
     * Check if this is a resolution request
     */
    public boolean isResolution() {
        return status == TicketStatus.RESOLVED && 
               resolution != null && !resolution.trim().isEmpty();
    }

    /**
     * Check if this is an escalation request
     */
    public boolean isEscalation() {
        return status == TicketStatus.ESCALATED && escalation != null;
    }

    /**
     * Validate resolution requirement
     */
    public boolean isResolutionValid() {
        if (status == TicketStatus.RESOLVED) {
            return resolution != null && !resolution.trim().isEmpty();
        }
        return true;
    }

    /**
     * Validate escalation requirement
     */
    public boolean isEscalationValid() {
        if (status == TicketStatus.ESCALATED) {
            return escalation != null && 
                   escalation.getReason() != null && 
                   !escalation.getReason().trim().isEmpty();
        }
        return true;
    }
}
