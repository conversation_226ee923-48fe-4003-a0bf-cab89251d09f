package com.lookforx.customersupportservice.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.lookforx.customersupportservice.domain.enums.TicketPriority;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;
import com.lookforx.customersupportservice.domain.enums.TicketType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Response DTO for ticket information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketResponse {

    private String id;
    private String ticketNumber;
    private String userId;
    private String userEmail;
    private String userName;
    private TicketType type;
    private TicketStatus status;
    private TicketPriority priority;
    private String subject;
    private String description;
    private String referenceId;
    private String referenceType;
    private String assignedTo;
    private String assignedToName;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime assignedAt;

    private String resolution;
    private String resolvedBy;
    private String resolvedByName;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime resolvedAt;
    private List<TicketCommentResponse> comments;
    private List<String> attachments;
    private List<String> tags;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;
    
    // Additional computed fields
    private boolean canBeUpdatedByUser;
    private boolean isOverdue;
    private long ageInHours;
    private String statusDisplayName;
    private String priorityDisplayName;
    private String typeDisplayName;

    /**
     * Comment response DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TicketCommentResponse {
        private String id;
        private String content;
        private String authorId;
        private String authorName;
        private String authorType;
        private boolean visibleToUser;
        private boolean internal;
        private List<String> attachments;

        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime createdAt;

        @JsonSerialize(using = LocalDateTimeSerializer.class)
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
        private LocalDateTime updatedAt;
        private boolean edited;
    }

    /**
     * Create a user-safe response (hide internal information)
     */
    public static TicketResponse createUserResponse(TicketResponse response) {
        // Filter out internal comments
        List<TicketCommentResponse> userVisibleComments = response.getComments() != null ?
            response.getComments().stream()
                .filter(TicketCommentResponse::isVisibleToUser)
                .toList() : null;

        return TicketResponse.builder()
            .id(response.getId())
            .ticketNumber(response.getTicketNumber())
            .userId(response.getUserId())
            .userEmail(response.getUserEmail())
            .userName(response.getUserName())
            .type(response.getType())
            .status(response.getStatus())
            .priority(response.getPriority())
            .subject(response.getSubject())
            .description(response.getDescription())
            .referenceId(response.getReferenceId())
            .referenceType(response.getReferenceType())
            .resolution(response.getResolution())
            .resolvedAt(response.getResolvedAt())
            .comments(userVisibleComments)
            .attachments(response.getAttachments())
            .tags(response.getTags())
            .createdAt(response.getCreatedAt())
            .updatedAt(response.getUpdatedAt())
            .canBeUpdatedByUser(response.isCanBeUpdatedByUser())
            .isOverdue(response.isOverdue())
            .ageInHours(response.getAgeInHours())
            .statusDisplayName(response.getStatusDisplayName())
            .priorityDisplayName(response.getPriorityDisplayName())
            .typeDisplayName(response.getTypeDisplayName())
            .build();
    }
}
