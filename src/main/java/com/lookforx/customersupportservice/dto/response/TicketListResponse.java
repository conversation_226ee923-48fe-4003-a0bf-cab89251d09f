package com.lookforx.customersupportservice.dto.response;


import com.lookforx.customersupportservice.domain.enums.TicketPriority;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;
import com.lookforx.customersupportservice.domain.enums.TicketType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.util.List;

/**
 * Response DTO for ticket list (summary information)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketListResponse {

    private List<TicketSummary> tickets;
    private PaginationInfo pagination;
    private FilterInfo filters;
    private SummaryStats stats;

    /**
     * Ticket summary for list view
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TicketSummary {
        private String id;
        private String ticketNumber;
        private String userId;
        private String userName;
        private TicketType type;
        private TicketStatus status;
        private TicketPriority priority;
        private String subject;
        private String assignedTo;
        private String assignedToName;

        private String createdAt;
        private String updatedAt;
        private String resolvedAt;
        private boolean isOverdue;
        private long ageInHours;
        private int commentCount;
        private boolean hasAttachments;
        private List<String> tags;
        
        // Display names
        private String statusDisplayName;
        private String priorityDisplayName;
        private String typeDisplayName;
        private String priorityColorCode;
    }

    /**
     * Pagination information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PaginationInfo {
        private int page;
        private int size;
        private long totalElements;
        private int totalPages;
        private boolean hasNext;
        private boolean hasPrevious;
        private boolean isFirst;
        private boolean isLast;
    }

    /**
     * Applied filters information
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FilterInfo {
        private List<TicketStatus> statuses;
        private List<TicketType> types;
        private List<TicketPriority> priorities;
        private String assignedTo;
        private String userId;

        private String createdAfter;
        private String createdBefore;
        private String searchQuery;
        private List<String> tags;
        private boolean showOverdueOnly;
        private String sortBy;
        private String sortDirection;
    }

    /**
     * Summary statistics
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SummaryStats {
        private long totalTickets;
        private long openTickets;
        private long resolvedTickets;
        private long overdueTickets;
        private long highPriorityTickets;
        private long unassignedTickets;
        private double averageResolutionTimeHours;
        private StatusBreakdown statusBreakdown;
        private PriorityBreakdown priorityBreakdown;
        private TypeBreakdown typeBreakdown;
    }

    /**
     * Status breakdown
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusBreakdown {
        private long received;
        private long inProgress;
        private long waitingForUser;
        private long escalated;
        private long resolved;
        private long rejected;
        private long closed;
        private long cancelled;
    }

    /**
     * Priority breakdown
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PriorityBreakdown {
        private long low;
        private long medium;
        private long high;
        private long urgent;
    }

    /**
     * Type breakdown
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TypeBreakdown {
        private long contactForm;
        private long userComplaint;
        private long requestComplaint;
        private long bidComplaint;
        private long suggestion;
        private long technicalIssue;
        private long accountIssue;
        private long paymentIssue;
        private long other;
    }

    /**
     * Create empty response
     */
    public static TicketListResponse empty(int page, int size) {
        return TicketListResponse.builder()
            .tickets(List.of())
            .pagination(PaginationInfo.builder()
                .page(page)
                .size(size)
                .totalElements(0)
                .totalPages(0)
                .hasNext(false)
                .hasPrevious(false)
                .isFirst(true)
                .isLast(true)
                .build())
            .stats(SummaryStats.builder()
                .totalTickets(0)
                .openTickets(0)
                .resolvedTickets(0)
                .overdueTickets(0)
                .highPriorityTickets(0)
                .unassignedTickets(0)
                .averageResolutionTimeHours(0.0)
                .build())
            .build();
    }
}
