package com.lookforx.customersupportservice.domain.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Ticket status for customer support system
 */
@Getter
public enum TicketStatus {
    
    RECEIVED("Received", "Ticket has been received and is waiting for review", false, true),
    IN_PROGRESS("In Progress", "Ticket is being processed by support team", false, true),
    WAITING_FOR_USER("Waiting for User", "Waiting for additional information from user", false, true),
    ESCALATED("Escalated", "Ticket has been escalated to higher level support", false, true),
    RESOLVED("Resolved", "Ticket has been resolved successfully", true, false),
    REJECTED("Rejected", "Ticket has been rejected", true, false),
    CLOSED("Closed", "Ticket has been closed", true, false),
    CANCELLED("Cancelled", "Ticket has been cancelled by user", true, false);

    private final String displayName;
    private final String description;
    private final boolean isFinal;
    private final boolean canBeUpdated;

    TicketStatus(String displayName, String description, boolean isFinal, boolean canBeUpdated) {
        this.displayName = displayName;
        this.description = description;
        this.isFinal = isFinal;
        this.canBeUpdated = canBeUpdated;
    }

    /**
     * Get status by name (case insensitive)
     */
    public static TicketStatus fromString(String name) {
        if (name == null || name.trim().isEmpty()) {
            return RECEIVED;
        }
        
        try {
            return TicketStatus.valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return RECEIVED;
        }
    }

    /**
     * Check if status transition is valid
     */
    public boolean canTransitionTo(TicketStatus newStatus) {
        if (this.isFinal) {
            return false; // Final statuses cannot be changed
        }
        
        switch (this) {
            case RECEIVED:
                return newStatus == IN_PROGRESS ||
                       newStatus == REJECTED ||
                       newStatus == ESCALATED;

            case IN_PROGRESS:
                return newStatus == RESOLVED ||
                       newStatus == REJECTED ||
                       newStatus == WAITING_FOR_USER ||
                       newStatus == ESCALATED;

            case WAITING_FOR_USER:
                return newStatus == IN_PROGRESS ||
                       newStatus == RESOLVED ||
                       newStatus == REJECTED ||
                       newStatus == CLOSED;

            case ESCALATED:
                return newStatus == IN_PROGRESS ||
                       newStatus == RESOLVED ||
                       newStatus == REJECTED;

            default:
                return false;
        }
    }

    /**
     * Get all possible next statuses
     */
    public TicketStatus[] getPossibleNextStatuses() {
        switch (this) {
            case RECEIVED:
                return new TicketStatus[]{IN_PROGRESS, REJECTED, ESCALATED};
            case IN_PROGRESS:
                return new TicketStatus[]{RESOLVED, REJECTED, WAITING_FOR_USER, ESCALATED};
            case WAITING_FOR_USER:
                return new TicketStatus[]{IN_PROGRESS, RESOLVED, REJECTED, CLOSED};
            case ESCALATED:
                return new TicketStatus[]{IN_PROGRESS, RESOLVED, REJECTED};
            default:
                return new TicketStatus[]{};
        }
    }

    /**
     * Check if status requires notification to user
     */
    public boolean requiresUserNotification() {
        return this == RECEIVED || 
               this == RESOLVED || 
               this == REJECTED || 
               this == WAITING_FOR_USER;
    }
}
