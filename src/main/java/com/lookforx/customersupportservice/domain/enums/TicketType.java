package com.lookforx.customersupportservice.domain.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Ticket types for customer support system
 */
@Getter
public enum TicketType {
    
    CONTACT_FORM("Contact Form", "General contact form submission"),
    USER_COMPLAINT("User Complaint", "Complaint about a user"),
    REQUEST_COMPLAINT("Request Complaint", "Complaint about a request/talep"),
    BID_COMPLAINT("Bid Complaint", "Complaint about a bid/teklif"),
    SUGGESTION("Suggestion", "User suggestion for improvement"),
    TECHNICAL_ISSUE("Technical Issue", "Technical problem or bug report"),
    ACCOUNT_ISSUE("Account Issue", "Account related problems"),
    PAYMENT_ISSUE("Payment Issue", "Payment and billing related issues"),
    OTHER("Other", "Other types of support requests");

    private final String displayName;
    private final String description;

    TicketType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * Get ticket type by name (case insensitive)
     */
    public static TicketType fromString(String name) {
        if (name == null || name.trim().isEmpty()) {
            return OTHER;
        }
        
        try {
            return TicketType.valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return OTHER;
        }
    }

    /**
     * Check if ticket type requires additional reference ID
     */
    public boolean requiresReferenceId() {
        return this == USER_COMPLAINT || 
               this == REQUEST_COMPLAINT || 
               this == BID_COMPLAINT;
    }

    /**
     * Get default priority for ticket type
     */
    public TicketPriority getDefaultPriority() {
        switch (this) {
            case TECHNICAL_ISSUE:
            case PAYMENT_ISSUE:
            case ACCOUNT_ISSUE:
                return TicketPriority.HIGH;
            case USER_COMPLAINT:
            case REQUEST_COMPLAINT:
            case BID_COMPLAINT:
                return TicketPriority.MEDIUM;
            case CONTACT_FORM:
            case SUGGESTION:
            case OTHER:
            default:
                return TicketPriority.LOW;
        }
    }
}
