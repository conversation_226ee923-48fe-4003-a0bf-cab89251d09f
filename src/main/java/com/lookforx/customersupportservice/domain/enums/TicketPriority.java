package com.lookforx.customersupportservice.domain.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * Ticket priority levels for customer support system
 */
@Getter
public enum TicketPriority {
    
    LOW("Low", "Low priority - can be handled in normal queue", 1, 72),
    MEDIUM("Medium", "Medium priority - should be handled within 24 hours", 2, 24),
    HIGH("High", "High priority - should be handled within 8 hours", 3, 8),
    URGENT("Urgent", "Urgent priority - should be handled immediately", 4, 2);

    private final String displayName;
    private final String description;
    private final int level;
    private final int expectedResponseTimeHours;

    TicketPriority(String displayName, String description, int level, int expectedResponseTimeHours) {
        this.displayName = displayName;
        this.description = description;
        this.level = level;
        this.expectedResponseTimeHours = expectedResponseTimeHours;
    }

    /**
     * Get priority by name (case insensitive)
     */
    public static TicketPriority fromString(String name) {
        if (name == null || name.trim().isEmpty()) {
            return LOW;
        }
        
        try {
            return TicketPriority.valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return LOW;
        }
    }

    /**
     * Get priority by level
     */
    public static TicketPriority fromLevel(int level) {
        switch (level) {
            case 1: return LOW;
            case 2: return MEDIUM;
            case 3: return HIGH;
            case 4: return URGENT;
            default: return LOW;
        }
    }

    /**
     * Check if this priority is higher than another
     */
    public boolean isHigherThan(TicketPriority other) {
        return this.level > other.level;
    }

    /**
     * Check if this priority is lower than another
     */
    public boolean isLowerThan(TicketPriority other) {
        return this.level < other.level;
    }

    /**
     * Get CSS class for UI styling
     */
    public String getCssClass() {
        switch (this) {
            case LOW: return "priority-low";
            case MEDIUM: return "priority-medium";
            case HIGH: return "priority-high";
            case URGENT: return "priority-urgent";
            default: return "priority-low";
        }
    }

    /**
     * Get color code for UI
     */
    public String getColorCode() {
        switch (this) {
            case LOW: return "#28a745";      // Green
            case MEDIUM: return "#ffc107";   // Yellow
            case HIGH: return "#fd7e14";     // Orange
            case URGENT: return "#dc3545";   // Red
            default: return "#28a745";
        }
    }
}
