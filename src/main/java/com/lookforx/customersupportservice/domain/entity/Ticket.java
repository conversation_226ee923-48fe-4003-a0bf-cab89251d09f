package com.lookforx.customersupportservice.domain.entity;

import com.lookforx.customersupportservice.domain.enums.TicketPriority;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;
import com.lookforx.customersupportservice.domain.enums.TicketType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.index.TextIndexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Ticket entity for customer support system
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "tickets")
@CompoundIndexes({
    @CompoundIndex(name = "user_status_idx", def = "{'userId': 1, 'status': 1}"),
    @CompoundIndex(name = "type_status_idx", def = "{'type': 1, 'status': 1}"),
    @CompoundIndex(name = "priority_created_idx", def = "{'priority': -1, 'createdAt': -1}"),
    @CompoundIndex(name = "assigned_status_idx", def = "{'assignedTo': 1, 'status': 1}")
})
public class Ticket {

    @Id
    private String id;

    /**
     * Unique ticket number for user reference
     */
    @Indexed(unique = true)
    private String ticketNumber;

    /**
     * User who created the ticket
     */
    @Indexed
    private String userId;

    /**
     * User email for communication
     */
    private String userEmail;

    /**
     * User name for personalization
     */
    private String userName;

    /**
     * Ticket type
     */
    @Indexed
    private TicketType type;

    /**
     * Current status of the ticket
     */
    @Indexed
    private TicketStatus status;

    /**
     * Priority level
     */
    @Indexed
    private TicketPriority priority;

    /**
     * Ticket subject/title
     */
    @TextIndexed(weight = 2)
    private String subject;

    /**
     * Detailed description of the issue
     */
    @TextIndexed
    private String description;

    /**
     * Reference ID for complaints (user ID, request ID, bid ID)
     */
    private String referenceId;

    /**
     * Reference type for complaints
     */
    private String referenceType;

    /**
     * Admin user assigned to handle this ticket
     */
    @Indexed
    private String assignedTo;

    /**
     * Admin user who assigned the ticket
     */
    private String assignedBy;

    /**
     * When the ticket was assigned
     */
    private LocalDateTime assignedAt;

    /**
     * Resolution details when ticket is resolved
     */
    private String resolution;

    /**
     * Admin who resolved the ticket
     */
    private String resolvedBy;

    /**
     * When the ticket was resolved
     */
    private LocalDateTime resolvedAt;

    /**
     * Comments and updates on the ticket
     */
    @Builder.Default
    private List<TicketComment> comments = new ArrayList<>();

    /**
     * Attachments (file URLs)
     */
    @Builder.Default
    private List<String> attachments = new ArrayList<>();

    /**
     * Tags for categorization
     */
    @Builder.Default
    private List<String> tags = new ArrayList<>();

    /**
     * Internal notes (visible only to admins)
     */
    @Builder.Default
    private List<String> internalNotes = new ArrayList<>();

    /**
     * Metadata for additional information
     */
    @Builder.Default
    private TicketMetadata metadata = new TicketMetadata();

    /**
     * Creation timestamp
     */
    @CreatedDate
    @Indexed
    private LocalDateTime createdAt;

    /**
     * Last modification timestamp
     */
    @LastModifiedDate
    private LocalDateTime updatedAt;

    /**
     * Soft delete flag
     */
    @Builder.Default
    private boolean deleted = false;

    /**
     * Version for optimistic locking
     */
    @Builder.Default
    private Long version = 0L;

    // Helper methods

    /**
     * Add a comment to the ticket
     */
    public void addComment(TicketComment comment) {
        if (this.comments == null) {
            this.comments = new ArrayList<>();
        }
        this.comments.add(comment);
    }

    /**
     * Add an internal note
     */
    public void addInternalNote(String note) {
        if (this.internalNotes == null) {
            this.internalNotes = new ArrayList<>();
        }
        this.internalNotes.add(note);
    }

    /**
     * Add a tag
     */
    public void addTag(String tag) {
        if (this.tags == null) {
            this.tags = new ArrayList<>();
        }
        if (!this.tags.contains(tag)) {
            this.tags.add(tag);
        }
    }

    /**
     * Check if ticket can be updated by user
     */
    public boolean canBeUpdatedByUser() {
        return status != null && status.isCanBeUpdated() && !deleted;
    }

    /**
     * Check if ticket is overdue based on priority
     */
    public boolean isOverdue() {
        if (status == null || status.isFinal() || createdAt == null || priority == null) {
            return false;
        }
        
        LocalDateTime deadline = createdAt.plusHours(priority.getExpectedResponseTimeHours());
        return LocalDateTime.now().isAfter(deadline);
    }

    /**
     * Get ticket age in hours
     */
    public long getAgeInHours() {
        if (createdAt == null) {
            return 0;
        }
        return java.time.Duration.between(createdAt, LocalDateTime.now()).toHours();
    }
}
