package com.lookforx.customersupportservice.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Comment entity for tickets
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketComment {

    /**
     * Unique comment ID
     */
    private String id;

    /**
     * Comment content
     */
    private String content;

    /**
     * User who made the comment (user ID or admin ID)
     */
    private String authorId;

    /**
     * Author name for display
     */
    private String authorName;

    /**
     * Author type (USER, ADMIN, SYSTEM)
     */
    @Builder.Default
    private CommentAuthorType authorType = CommentAuthorType.USER;

    /**
     * Whether this comment is visible to the user
     */
    @Builder.Default
    private boolean visibleToUser = true;

    /**
     * Whether this comment is internal (admin only)
     */
    @Builder.Default
    private boolean internal = false;

    /**
     * Attachments in this comment
     */
    @Builder.Default
    private List<String> attachments = new ArrayList<>();

    /**
     * When the comment was created
     */
    private LocalDateTime createdAt;

    /**
     * When the comment was last updated
     */
    private LocalDateTime updatedAt;

    /**
     * Whether the comment has been edited
     */
    @Builder.Default
    private boolean edited = false;

    /**
     * Comment author type enum
     */
    public enum CommentAuthorType {
        USER("User"),
        ADMIN("Admin"),
        SYSTEM("System");

        private final String displayName;

        CommentAuthorType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * Create a user comment
     */
    public static TicketComment createUserComment(String authorId, String authorName, String content) {
        return TicketComment.builder()
                .id(java.util.UUID.randomUUID().toString())
                .content(content)
                .authorId(authorId)
                .authorName(authorName)
                .authorType(CommentAuthorType.USER)
                .visibleToUser(true)
                .internal(false)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * Create an admin comment
     */
    public static TicketComment createAdminComment(String authorId, String authorName, String content, boolean internal) {
        return TicketComment.builder()
                .id(java.util.UUID.randomUUID().toString())
                .content(content)
                .authorId(authorId)
                .authorName(authorName)
                .authorType(CommentAuthorType.ADMIN)
                .visibleToUser(!internal)
                .internal(internal)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * Create a system comment
     */
    public static TicketComment createSystemComment(String content) {
        return TicketComment.builder()
                .id(java.util.UUID.randomUUID().toString())
                .content(content)
                .authorId("SYSTEM")
                .authorName("System")
                .authorType(CommentAuthorType.SYSTEM)
                .visibleToUser(true)
                .internal(false)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    /**
     * Add attachment to comment
     */
    public void addAttachment(String attachmentUrl) {
        if (this.attachments == null) {
            this.attachments = new ArrayList<>();
        }
        this.attachments.add(attachmentUrl);
    }

    /**
     * Mark comment as edited
     */
    public void markAsEdited() {
        this.edited = true;
        this.updatedAt = LocalDateTime.now();
    }
}
