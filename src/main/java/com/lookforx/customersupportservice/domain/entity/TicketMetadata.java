package com.lookforx.customersupportservice.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Metadata entity for tickets to store additional information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TicketMetadata {

    /**
     * User's IP address when creating the ticket
     */
    private String ipAddress;

    /**
     * User agent information
     */
    private String userAgent;

    /**
     * Browser information
     */
    private String browser;

    /**
     * Operating system information
     */
    private String operatingSystem;

    /**
     * Device type (mobile, desktop, tablet)
     */
    private String deviceType;

    /**
     * Language preference
     */
    private String language;

    /**
     * Timezone
     */
    private String timezone;

    /**
     * Referrer URL
     */
    private String referrerUrl;

    /**
     * Page URL where the ticket was created
     */
    private String pageUrl;

    /**
     * Session ID
     */
    private String sessionId;

    /**
     * Application version
     */
    private String appVersion;

    /**
     * Platform (web, mobile, api)
     */
    private String platform;

    /**
     * Source of the ticket (contact_form, user_profile, admin_panel)
     */
    private String source;

    /**
     * Campaign or marketing source
     */
    private String campaign;

    /**
     * UTM parameters
     */
    @Builder.Default
    private Map<String, String> utmParameters = new HashMap<>();

    /**
     * Custom fields for additional data
     */
    @Builder.Default
    private Map<String, Object> customFields = new HashMap<>();

    /**
     * System information
     */
    @Builder.Default
    private Map<String, String> systemInfo = new HashMap<>();

    /**
     * Performance metrics
     */
    @Builder.Default
    private Map<String, Object> performanceMetrics = new HashMap<>();

    // Helper methods

    /**
     * Add custom field
     */
    public void addCustomField(String key, Object value) {
        if (this.customFields == null) {
            this.customFields = new HashMap<>();
        }
        this.customFields.put(key, value);
    }

    /**
     * Get custom field
     */
    public Object getCustomField(String key) {
        if (this.customFields == null) {
            return null;
        }
        return this.customFields.get(key);
    }

    /**
     * Add UTM parameter
     */
    public void addUtmParameter(String key, String value) {
        if (this.utmParameters == null) {
            this.utmParameters = new HashMap<>();
        }
        this.utmParameters.put(key, value);
    }

    /**
     * Add system info
     */
    public void addSystemInfo(String key, String value) {
        if (this.systemInfo == null) {
            this.systemInfo = new HashMap<>();
        }
        this.systemInfo.put(key, value);
    }

    /**
     * Add performance metric
     */
    public void addPerformanceMetric(String key, Object value) {
        if (this.performanceMetrics == null) {
            this.performanceMetrics = new HashMap<>();
        }
        this.performanceMetrics.put(key, value);
    }

    /**
     * Check if metadata contains specific information
     */
    public boolean hasCustomField(String key) {
        return this.customFields != null && this.customFields.containsKey(key);
    }

    /**
     * Get device information summary
     */
    public String getDeviceSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (deviceType != null) {
            summary.append(deviceType);
        }
        
        if (operatingSystem != null) {
            if (summary.length() > 0) summary.append(" - ");
            summary.append(operatingSystem);
        }
        
        if (browser != null) {
            if (summary.length() > 0) summary.append(" - ");
            summary.append(browser);
        }
        
        return summary.toString();
    }

    /**
     * Get location information summary
     */
    public String getLocationSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (language != null) {
            summary.append("Lang: ").append(language);
        }
        
        if (timezone != null) {
            if (summary.length() > 0) summary.append(", ");
            summary.append("TZ: ").append(timezone);
        }
        
        return summary.toString();
    }
}
