package com.lookforx.customersupportservice.controller;

import com.lookforx.common.dto.ApiResponse;
import com.lookforx.customersupportservice.domain.entity.Ticket;
import com.lookforx.customersupportservice.domain.enums.TicketPriority;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;
import com.lookforx.customersupportservice.domain.enums.TicketType;
import com.lookforx.customersupportservice.service.NotificationEventService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Test controller for event publishing
 */
@RestController
@RequestMapping("/v1/test")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Test", description = "Test endpoints for event publishing")
public class TestController {

    private final NotificationEventService notificationEventService;

    @PostMapping("/publish-ticket-created")
    @Operation(summary = "Test ticket created event publishing")
    public ResponseEntity<ApiResponse<String>> testTicketCreatedEvent() {
        log.info("Testing ticket created event publishing");
        
        try {
            // Create a test ticket
            Ticket testTicket = createTestTicket();
            
            // Publish event
            notificationEventService.publishTicketCreatedEvent(testTicket);
            
            return ResponseEntity.ok(ApiResponse.success(
                "Ticket created event published successfully", 
                "Event published for ticket: " + testTicket.getTicketNumber()
            ));
            
        } catch (Exception e) {
            log.error("Failed to publish ticket created event", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("Failed to publish event: " + e.getMessage()));
        }
    }

    @PostMapping("/publish-ticket-status-changed")
    @Operation(summary = "Test ticket status changed event publishing")
    public ResponseEntity<ApiResponse<String>> testTicketStatusChangedEvent() {
        log.info("Testing ticket status changed event publishing");
        
        try {
            // Create a test ticket
            Ticket testTicket = createTestTicket();
            testTicket.setStatus(TicketStatus.IN_PROGRESS);
            
            // Publish event
            notificationEventService.publishTicketStatusChangedEvent(testTicket, TicketStatus.RESOLVED, "admin123");
            
            return ResponseEntity.ok(ApiResponse.success(
                "Ticket status changed event published successfully", 
                "Event published for ticket: " + testTicket.getTicketNumber()
            ));
            
        } catch (Exception e) {
            log.error("Failed to publish ticket status changed event", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("Failed to publish event: " + e.getMessage()));
        }
    }

    @PostMapping("/publish-ticket-assigned")
    @Operation(summary = "Test ticket assigned event publishing")
    public ResponseEntity<ApiResponse<String>> testTicketAssignedEvent() {
        log.info("Testing ticket assigned event publishing");
        
        try {
            // Create a test ticket
            Ticket testTicket = createTestTicket();
            testTicket.setAssignedTo("support123");
            testTicket.setAssignedBy("admin123");
            testTicket.setAssignedAt(LocalDateTime.now());
            
            // Publish event
            notificationEventService.publishTicketAssignedEvent(testTicket, "support123", "admin123");
            
            return ResponseEntity.ok(ApiResponse.success(
                "Ticket assigned event published successfully", 
                "Event published for ticket: " + testTicket.getTicketNumber()
            ));
            
        } catch (Exception e) {
            log.error("Failed to publish ticket assigned event", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("Failed to publish event: " + e.getMessage()));
        }
    }

    @PostMapping("/publish-ticket-resolved")
    @Operation(summary = "Test ticket resolved event publishing")
    public ResponseEntity<ApiResponse<String>> testTicketResolvedEvent() {
        log.info("Testing ticket resolved event publishing");
        
        try {
            // Create a test ticket
            Ticket testTicket = createTestTicket();
            testTicket.setStatus(TicketStatus.RESOLVED);
            testTicket.setResolution("Issue resolved successfully");
            testTicket.setResolvedAt(LocalDateTime.now());
            testTicket.setResolvedBy("support123");
            
            // Publish event
            notificationEventService.publishTicketResolvedEvent(testTicket, "support123", "Issue resolved successfully");
            
            return ResponseEntity.ok(ApiResponse.success(
                "Ticket resolved event published successfully", 
                "Event published for ticket: " + testTicket.getTicketNumber()
            ));
            
        } catch (Exception e) {
            log.error("Failed to publish ticket resolved event", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("Failed to publish event: " + e.getMessage()));
        }
    }

    @PostMapping("/publish-ticket-comment-added")
    @Operation(summary = "Test ticket comment added event publishing")
    public ResponseEntity<ApiResponse<String>> testTicketCommentAddedEvent() {
        log.info("Testing ticket comment added event publishing");
        
        try {
            // Create a test ticket
            Ticket testTicket = createTestTicket();
            
            // Publish event
            notificationEventService.publishTicketCommentAddedEvent(testTicket, "support123", "This is a test comment");
            
            return ResponseEntity.ok(ApiResponse.success(
                "Ticket comment added event published successfully", 
                "Event published for ticket: " + testTicket.getTicketNumber()
            ));
            
        } catch (Exception e) {
            log.error("Failed to publish ticket comment added event", e);
            return ResponseEntity.internalServerError()
                .body(ApiResponse.error("Failed to publish event: " + e.getMessage()));
        }
    }

    private Ticket createTestTicket() {
        Ticket ticket = new Ticket();
        ticket.setId(UUID.randomUUID().toString());
        ticket.setTicketNumber("TKT-TEST-" + System.currentTimeMillis());
        ticket.setUserId("12345");
        ticket.setUserEmail("<EMAIL>");
        ticket.setUserName("Test User");
        ticket.setType(TicketType.TECHNICAL_ISSUE);
        ticket.setStatus(TicketStatus.RECEIVED);
        ticket.setPriority(TicketPriority.MEDIUM);
        ticket.setSubject("Test Ticket Subject");
        ticket.setDescription("This is a test ticket description for event publishing");
        ticket.setReferenceType("REQUEST");
        ticket.setReferenceId("REQ-123");
        ticket.setCreatedAt(LocalDateTime.now());
        ticket.setUpdatedAt(LocalDateTime.now());
        
        return ticket;
    }
}
