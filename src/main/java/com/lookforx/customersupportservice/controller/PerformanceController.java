package com.lookforx.customersupportservice.controller;

import com.lookforx.customersupportservice.service.PerformanceMonitoringService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Performance monitoring controller for admin operations
 * Provides performance metrics and health monitoring
 */
@RestController
@RequestMapping("/v1/admin/performance")
@RequiredArgsConstructor
@Slf4j
public class PerformanceController {

    private final PerformanceMonitoringService performanceMonitoringService;

    /**
     * Get performance metrics
     */
    @GetMapping("/metrics")
    public ResponseEntity<Map<String, Object>> getPerformanceMetrics(
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.info("Admin {} requesting performance metrics", adminUserId);
        
        Map<String, Object> metrics = performanceMonitoringService.getPerformanceMetrics();
        
        return ResponseEntity.ok(metrics);
    }

    /**
     * Get system health status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getHealthStatus(
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.debug("Admin {} requesting health status", adminUserId);
        
        Map<String, Object> health = performanceMonitoringService.getHealthStatus();
        
        return ResponseEntity.ok(health);
    }

    /**
     * Clear all performance metrics
     */
    @DeleteMapping("/metrics/clear")
    public ResponseEntity<Map<String, String>> clearMetrics(
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.warn("Admin {} clearing all performance metrics", adminUserId);
        
        performanceMonitoringService.clearMetrics();
        
        return ResponseEntity.ok(Map.of(
            "message", "All performance metrics cleared successfully",
            "clearedBy", adminUserId
        ));
    }

    /**
     * Record test metrics (for testing purposes)
     */
    @PostMapping("/test/record")
    public ResponseEntity<Map<String, String>> recordTestMetrics(
            @RequestParam String endpoint,
            @RequestParam(defaultValue = "100") long responseTime,
            @RequestParam(defaultValue = "false") boolean isError,
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.info("Admin {} recording test metrics for endpoint: {}", adminUserId, endpoint);
        
        performanceMonitoringService.recordApiRequest(endpoint, responseTime, isError);
        
        return ResponseEntity.ok(Map.of(
            "message", "Test metrics recorded successfully",
            "endpoint", endpoint,
            "responseTime", String.valueOf(responseTime),
            "isError", String.valueOf(isError),
            "recordedBy", adminUserId
        ));
    }
}
