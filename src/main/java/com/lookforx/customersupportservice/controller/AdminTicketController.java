package com.lookforx.customersupportservice.controller;

import com.lookforx.common.dto.ApiResponse;
import com.lookforx.customersupportservice.dto.request.AdminUpdateTicketRequest;
import com.lookforx.customersupportservice.dto.response.TicketListResponse;
import com.lookforx.customersupportservice.dto.response.TicketResponse;
import com.lookforx.customersupportservice.domain.enums.TicketPriority;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;
import com.lookforx.customersupportservice.domain.enums.TicketType;
import com.lookforx.customersupportservice.service.TicketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * REST Controller for admin ticket operations
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/admin/tickets")
@RequiredArgsConstructor
@Tag(name = "Admin Tickets", description = "Admin ticket management operations")
@SecurityRequirement(name = "bearerAuth")
@PreAuthorize("hasRole('ADMIN') or hasRole('SUPPORT')")
public class AdminTicketController {

    private final TicketService ticketService;

    @GetMapping
    @Operation(summary = "Get all tickets with filters", description = "Get paginated list of all tickets with filtering options")
    public ResponseEntity<ApiResponse<TicketListResponse>> getAllTickets(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @Parameter(description = "Filter by statuses") @RequestParam(required = false) List<TicketStatus> statuses,
            @Parameter(description = "Filter by types") @RequestParam(required = false) List<TicketType> types,
            @Parameter(description = "Filter by priorities") @RequestParam(required = false) List<TicketPriority> priorities,
            @Parameter(description = "Filter by assigned admin") @RequestParam(required = false) String assignedTo,
            @Parameter(description = "Filter by user ID") @RequestParam(required = false) String userId,
            @Parameter(description = "Filter by created after date") @RequestParam(required = false) 
                @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime createdAfter,
            @Parameter(description = "Filter by created before date") @RequestParam(required = false) 
                @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime createdBefore,
            @Parameter(description = "Search query") @RequestParam(required = false) String searchQuery,
            @Parameter(description = "Filter by tags") @RequestParam(required = false) List<String> tags,
            @Parameter(description = "Show only overdue tickets") @RequestParam(defaultValue = "false") boolean showOverdueOnly,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        log.debug("Admin {} getting all tickets with filters", adminUserId);
        
        try {
            Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            TicketListResponse response = ticketService.getAllTickets(
                statuses, types, priorities, assignedTo, userId, 
                createdAfter, createdBefore, searchQuery, tags, showOverdueOnly, pageable);
            
            return ResponseEntity.ok(ApiResponse.success(response, "Tickets retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting all tickets for admin: {}", adminUserId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve tickets: " + e.getMessage()));
        }
    }

    @GetMapping("/{ticketId}")
    @Operation(summary = "Get ticket by ID (admin)", description = "Get detailed ticket information (admin access)")
    public ResponseEntity<ApiResponse<TicketResponse>> getTicketById(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @PathVariable String ticketId) {
        
        log.debug("Admin {} getting ticket: {}", adminUserId, ticketId);
        
        try {
            TicketResponse response = ticketService.getTicketByIdAdmin(ticketId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting ticket: {} for admin: {}", ticketId, adminUserId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("Ticket not found"));
        }
    }

    @PutMapping("/{ticketId}")
    @Operation(summary = "Update ticket (admin)", description = "Update ticket with admin privileges")
    public ResponseEntity<ApiResponse<TicketResponse>> updateTicket(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @PathVariable String ticketId,
            @Valid @RequestBody AdminUpdateTicketRequest request) {
        
        log.info("Admin {} updating ticket: {}", adminUserId, ticketId);
        
        try {
            TicketResponse response = ticketService.updateTicketAdmin(ticketId, request, adminUserId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket updated successfully"));
        } catch (Exception e) {
            log.error("Error updating ticket: {} by admin: {}", ticketId, adminUserId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to update ticket: " + e.getMessage()));
        }
    }

    @PostMapping("/{ticketId}/assign")
    @Operation(summary = "Assign ticket", description = "Assign ticket to an admin user")
    public ResponseEntity<ApiResponse<TicketResponse>> assignTicket(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @PathVariable String ticketId,
            @RequestParam String assignToUserId) {
        
        log.info("Admin {} assigning ticket: {} to: {}", adminUserId, ticketId, assignToUserId);
        
        try {
            TicketResponse response = ticketService.assignTicket(ticketId, assignToUserId, adminUserId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket assigned successfully"));
        } catch (Exception e) {
            log.error("Error assigning ticket: {} by admin: {}", ticketId, adminUserId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to assign ticket: " + e.getMessage()));
        }
    }

    @PostMapping("/{ticketId}/comments")
    @Operation(summary = "Add admin comment", description = "Add admin comment to ticket")
    public ResponseEntity<ApiResponse<TicketResponse>> addAdminComment(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @PathVariable String ticketId,
            @RequestParam String comment,
            @RequestParam(defaultValue = "false") boolean internal) {
        
        log.info("Admin {} adding comment to ticket: {}", adminUserId, ticketId);
        
        try {
            TicketResponse response = ticketService.addAdminComment(ticketId, comment, internal, adminUserId);
            return ResponseEntity.ok(ApiResponse.success(response, "Comment added successfully"));
        } catch (Exception e) {
            log.error("Error adding comment to ticket: {} by admin: {}", ticketId, adminUserId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to add comment: " + e.getMessage()));
        }
    }

    @PostMapping("/{ticketId}/resolve")
    @Operation(summary = "Resolve ticket", description = "Mark ticket as resolved")
    public ResponseEntity<ApiResponse<TicketResponse>> resolveTicket(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @PathVariable String ticketId,
            @RequestParam String resolution) {
        
        log.info("Admin {} resolving ticket: {}", adminUserId, ticketId);
        
        try {
            TicketResponse response = ticketService.resolveTicket(ticketId, resolution, adminUserId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket resolved successfully"));
        } catch (Exception e) {
            log.error("Error resolving ticket: {} by admin: {}", ticketId, adminUserId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to resolve ticket: " + e.getMessage()));
        }
    }

    @PostMapping("/{ticketId}/reject")
    @Operation(summary = "Reject ticket", description = "Mark ticket as rejected")
    public ResponseEntity<ApiResponse<TicketResponse>> rejectTicket(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @PathVariable String ticketId,
            @RequestParam String reason) {
        
        log.info("Admin {} rejecting ticket: {}", adminUserId, ticketId);
        
        try {
            TicketResponse response = ticketService.rejectTicket(ticketId, reason, adminUserId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket rejected successfully"));
        } catch (Exception e) {
            log.error("Error rejecting ticket: {} by admin: {}", ticketId, adminUserId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to reject ticket: " + e.getMessage()));
        }
    }

    @PostMapping("/{ticketId}/escalate")
    @Operation(summary = "Escalate ticket", description = "Escalate ticket to higher level")
    public ResponseEntity<ApiResponse<TicketResponse>> escalateTicket(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @PathVariable String ticketId,
            @RequestBody AdminUpdateTicketRequest.EscalationDetails escalation) {
        
        log.info("Admin {} escalating ticket: {}", adminUserId, ticketId);
        
        try {
            TicketResponse response = ticketService.escalateTicket(ticketId, escalation, adminUserId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket escalated successfully"));
        } catch (Exception e) {
            log.error("Error escalating ticket: {} by admin: {}", ticketId, adminUserId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to escalate ticket: " + e.getMessage()));
        }
    }

    @PostMapping("/{ticketId}/close")
    @Operation(summary = "Close ticket", description = "Close ticket")
    public ResponseEntity<ApiResponse<TicketResponse>> closeTicket(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @PathVariable String ticketId,
            @RequestParam String reason) {
        
        log.info("Admin {} closing ticket: {}", adminUserId, ticketId);
        
        try {
            TicketResponse response = ticketService.closeTicket(ticketId, reason, adminUserId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket closed successfully"));
        } catch (Exception e) {
            log.error("Error closing ticket: {} by admin: {}", ticketId, adminUserId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to close ticket: " + e.getMessage()));
        }
    }

    @PostMapping("/{ticketId}/reopen")
    @Operation(summary = "Reopen ticket", description = "Reopen closed ticket")
    public ResponseEntity<ApiResponse<TicketResponse>> reopenTicket(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @PathVariable String ticketId,
            @RequestParam String reason) {
        
        log.info("Admin {} reopening ticket: {}", adminUserId, ticketId);
        
        try {
            TicketResponse response = ticketService.reopenTicket(ticketId, reason, adminUserId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket reopened successfully"));
        } catch (Exception e) {
            log.error("Error reopening ticket: {} by admin: {}", ticketId, adminUserId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to reopen ticket: " + e.getMessage()));
        }
    }

    @GetMapping("/assigned")
    @Operation(summary = "Get assigned tickets", description = "Get tickets assigned to the admin")
    public ResponseEntity<ApiResponse<TicketListResponse>> getAssignedTickets(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.debug("Getting assigned tickets for admin: {}", adminUserId);
        
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            TicketListResponse response = ticketService.getAssignedTickets(adminUserId, pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Assigned tickets retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting assigned tickets for admin: {}", adminUserId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve assigned tickets: " + e.getMessage()));
        }
    }

    @GetMapping("/unassigned")
    @Operation(summary = "Get unassigned tickets", description = "Get tickets that are not assigned to any admin")
    public ResponseEntity<ApiResponse<TicketListResponse>> getUnassignedTickets(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.debug("Admin {} getting unassigned tickets", adminUserId);
        
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            TicketListResponse response = ticketService.getUnassignedTickets(pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Unassigned tickets retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting unassigned tickets for admin: {}", adminUserId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve unassigned tickets: " + e.getMessage()));
        }
    }

    @GetMapping("/overdue")
    @Operation(summary = "Get overdue tickets", description = "Get tickets that are overdue")
    public ResponseEntity<ApiResponse<TicketListResponse>> getOverdueTickets(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        log.debug("Admin {} getting overdue tickets", adminUserId);
        
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            TicketListResponse response = ticketService.getOverdueTickets(pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Overdue tickets retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting overdue tickets for admin: {}", adminUserId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve overdue tickets: " + e.getMessage()));
        }
    }

    @GetMapping("/statistics")
    @Operation(summary = "Get ticket statistics", description = "Get overall ticket statistics")
    public ResponseEntity<ApiResponse<TicketListResponse.SummaryStats>> getStatistics(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId) {
        
        log.debug("Admin {} getting ticket statistics", adminUserId);
        
        try {
            TicketListResponse.SummaryStats stats = ticketService.getTicketStatistics();
            return ResponseEntity.ok(ApiResponse.success(stats, "Statistics retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting statistics for admin: {}", adminUserId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve statistics: " + e.getMessage()));
        }
    }

    @GetMapping("/statistics/admin")
    @Operation(summary = "Get admin statistics", description = "Get statistics for the admin's performance")
    public ResponseEntity<ApiResponse<TicketListResponse.SummaryStats>> getAdminStatistics(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId) {
        
        log.debug("Getting admin statistics for: {}", adminUserId);
        
        try {
            TicketListResponse.SummaryStats stats = ticketService.getAdminStatistics(adminUserId);
            return ResponseEntity.ok(ApiResponse.success(stats, "Admin statistics retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting admin statistics for: {}", adminUserId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve admin statistics: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{ticketId}")
    @Operation(summary = "Delete ticket", description = "Soft delete ticket")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<Void>> deleteTicket(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @PathVariable String ticketId) {
        
        log.warn("Admin {} deleting ticket: {}", adminUserId, ticketId);
        
        try {
            ticketService.deleteTicket(ticketId, adminUserId);
            return ResponseEntity.ok(ApiResponse.success(null, "Ticket deleted successfully"));
        } catch (Exception e) {
            log.error("Error deleting ticket: {} by admin: {}", ticketId, adminUserId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to delete ticket: " + e.getMessage()));
        }
    }

    @PostMapping("/{ticketId}/restore")
    @Operation(summary = "Restore ticket", description = "Restore soft deleted ticket")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ApiResponse<TicketResponse>> restoreTicket(
            @RequestHeader(value = "X-User-ID", required = false) String adminUserId,
            @PathVariable String ticketId) {
        
        log.info("Admin {} restoring ticket: {}", adminUserId, ticketId);
        
        try {
            TicketResponse response = ticketService.restoreTicket(ticketId, adminUserId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket restored successfully"));
        } catch (Exception e) {
            log.error("Error restoring ticket: {} by admin: {}", ticketId, adminUserId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to restore ticket: " + e.getMessage()));
        }
    }
}
