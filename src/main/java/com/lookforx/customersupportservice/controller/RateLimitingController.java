package com.lookforx.customersupportservice.controller;

import com.lookforx.customersupportservice.service.CacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.Map;

/**
 * Rate limiting controller for API endpoints
 * Provides rate limiting functionality using Redis
 */
@RestController
@RequestMapping("/v1/rate-limit")
@RequiredArgsConstructor
@Slf4j
public class RateLimitingController {

    private final CacheService cacheService;

    // Rate limiting configurations
    private static final int DEFAULT_MAX_REQUESTS = 100;
    private static final Duration DEFAULT_WINDOW = Duration.ofHours(1);
    private static final int TICKET_CREATION_MAX_REQUESTS = 10;
    private static final Duration TICKET_CREATION_WINDOW = Duration.ofMinutes(15);

    /**
     * Check rate limit for general API usage
     */
    @GetMapping("/check")
    public ResponseEntity<Map<String, Object>> checkRateLimit(HttpServletRequest request) {
        String clientIp = getClientIp(request);
        String identifier = "api_general:" + clientIp;
        
        boolean isLimited = cacheService.isRateLimited(identifier, DEFAULT_MAX_REQUESTS, DEFAULT_WINDOW);
        
        return ResponseEntity.ok(Map.of(
            "rateLimited", isLimited,
            "identifier", identifier,
            "maxRequests", DEFAULT_MAX_REQUESTS,
            "windowMinutes", DEFAULT_WINDOW.toMinutes()
        ));
    }

    /**
     * Check rate limit for ticket creation
     */
    @GetMapping("/check/ticket-creation")
    public ResponseEntity<Map<String, Object>> checkTicketCreationRateLimit(
            @RequestParam String userId,
            HttpServletRequest request) {
        
        String clientIp = getClientIp(request);
        String identifier = "ticket_creation:" + userId + ":" + clientIp;
        
        boolean isLimited = cacheService.isRateLimited(identifier, TICKET_CREATION_MAX_REQUESTS, TICKET_CREATION_WINDOW);
        
        if (isLimited) {
            log.warn("Ticket creation rate limit exceeded for user: {} from IP: {}", userId, clientIp);
            return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).body(Map.of(
                "rateLimited", true,
                "message", "Too many ticket creation requests. Please try again later.",
                "identifier", identifier,
                "maxRequests", TICKET_CREATION_MAX_REQUESTS,
                "windowMinutes", TICKET_CREATION_WINDOW.toMinutes()
            ));
        }
        
        return ResponseEntity.ok(Map.of(
            "rateLimited", false,
            "identifier", identifier,
            "maxRequests", TICKET_CREATION_MAX_REQUESTS,
            "windowMinutes", TICKET_CREATION_WINDOW.toMinutes()
        ));
    }

    /**
     * Check rate limit for user-specific operations
     */
    @GetMapping("/check/user/{userId}")
    public ResponseEntity<Map<String, Object>> checkUserRateLimit(
            @PathVariable String userId,
            @RequestParam(defaultValue = "50") int maxRequests,
            @RequestParam(defaultValue = "60") int windowMinutes,
            HttpServletRequest request) {
        
        String clientIp = getClientIp(request);
        String identifier = "user_operations:" + userId + ":" + clientIp;
        Duration window = Duration.ofMinutes(windowMinutes);
        
        boolean isLimited = cacheService.isRateLimited(identifier, maxRequests, window);
        
        return ResponseEntity.ok(Map.of(
            "rateLimited", isLimited,
            "identifier", identifier,
            "maxRequests", maxRequests,
            "windowMinutes", windowMinutes
        ));
    }

    /**
     * Reset rate limit for specific identifier (admin only)
     */
    @DeleteMapping("/reset")
    public ResponseEntity<Map<String, String>> resetRateLimit(
            @RequestParam String identifier,
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.info("Admin {} resetting rate limit for identifier: {}", adminUserId, identifier);
        
        String key = "rate_limit:" + identifier;
        cacheService.delete(key);
        
        return ResponseEntity.ok(Map.of(
            "message", "Rate limit reset successfully",
            "identifier", identifier,
            "resetBy", adminUserId
        ));
    }

    /**
     * Get rate limit status for identifier
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getRateLimitStatus(@RequestParam String identifier) {
        String key = "rate_limit:" + identifier;
        Object currentCount = cacheService.get(key);
        
        return ResponseEntity.ok(Map.of(
            "identifier", identifier,
            "currentCount", currentCount != null ? currentCount : 0,
            "exists", cacheService.exists(key)
        ));
    }

    /**
     * Get all rate limit keys (admin only)
     */
    @GetMapping("/all")
    public ResponseEntity<Map<String, Object>> getAllRateLimits(
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.debug("Admin {} requesting all rate limits", adminUserId);
        
        var keys = cacheService.getKeys("rate_limit:*");
        
        return ResponseEntity.ok(Map.of(
            "rateLimitKeys", keys,
            "count", keys.size(),
            "requestedBy", adminUserId
        ));
    }

    /**
     * Extract client IP address from request
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
