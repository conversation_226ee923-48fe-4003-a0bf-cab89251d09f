package com.lookforx.customersupportservice.controller;

import com.lookforx.customersupportservice.service.CacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * Cache management controller for admin operations
 * Provides cache monitoring and management capabilities
 */
@RestController
@RequestMapping("/v1/admin/cache")
@RequiredArgsConstructor
@Slf4j
public class CacheManagementController {

    private final CacheService cacheService;

    /**
     * Get cache statistics
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getCacheStatistics(
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.info("Admin {} requesting cache statistics", adminUserId);
        
        String stats = cacheService.getCacheStatistics();
        
        return ResponseEntity.ok(Map.of(
            "statistics", stats,
            "requestedBy", adminUserId,
            "timestamp", System.currentTimeMillis()
        ));
    }

    /**
     * Clear all caches
     */
    @DeleteMapping("/clear-all")
    public ResponseEntity<Map<String, String>> clearAllCaches(
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.warn("Admin {} clearing all caches", adminUserId);
        
        cacheService.clearAllCaches();
        
        return ResponseEntity.ok(Map.of(
            "message", "All caches cleared successfully",
            "clearedBy", adminUserId
        ));
    }

    /**
     * Clear specific cache
     */
    @DeleteMapping("/clear/{cacheName}")
    public ResponseEntity<Map<String, String>> clearCache(
            @PathVariable String cacheName,
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.warn("Admin {} clearing cache: {}", adminUserId, cacheName);
        
        cacheService.clearCache(cacheName);
        
        return ResponseEntity.ok(Map.of(
            "message", "Cache cleared successfully",
            "cacheName", cacheName,
            "clearedBy", adminUserId
        ));
    }

    /**
     * Evict specific cache entry
     */
    @DeleteMapping("/evict/{cacheName}/{key}")
    public ResponseEntity<Map<String, String>> evictCacheEntry(
            @PathVariable String cacheName,
            @PathVariable String key,
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.info("Admin {} evicting cache entry - Cache: {}, Key: {}", adminUserId, cacheName, key);
        
        cacheService.evictCacheEntry(cacheName, key);
        
        return ResponseEntity.ok(Map.of(
            "message", "Cache entry evicted successfully",
            "cacheName", cacheName,
            "key", key,
            "evictedBy", adminUserId
        ));
    }

    /**
     * Check if cache entry exists
     */
    @GetMapping("/exists/{cacheName}/{key}")
    public ResponseEntity<Map<String, Object>> checkCacheEntry(
            @PathVariable String cacheName,
            @PathVariable String key,
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        boolean exists = cacheService.cacheEntryExists(cacheName, key);
        
        return ResponseEntity.ok(Map.of(
            "exists", exists,
            "cacheName", cacheName,
            "key", key,
            "checkedBy", adminUserId
        ));
    }

    /**
     * Get cache entry value
     */
    @GetMapping("/get/{cacheName}/{key}")
    public ResponseEntity<Map<String, Object>> getCacheEntry(
            @PathVariable String cacheName,
            @PathVariable String key,
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        Object value = cacheService.getCacheEntry(cacheName, key);
        
        return ResponseEntity.ok(Map.of(
            "value", value,
            "cacheName", cacheName,
            "key", key,
            "exists", value != null,
            "retrievedBy", adminUserId
        ));
    }

    /**
     * Put entry in cache
     */
    @PostMapping("/put/{cacheName}/{key}")
    public ResponseEntity<Map<String, String>> putCacheEntry(
            @PathVariable String cacheName,
            @PathVariable String key,
            @RequestBody Object value,
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.info("Admin {} putting cache entry - Cache: {}, Key: {}", adminUserId, cacheName, key);
        
        cacheService.putCacheEntry(cacheName, key, value);
        
        return ResponseEntity.ok(Map.of(
            "message", "Cache entry added successfully",
            "cacheName", cacheName,
            "key", key,
            "addedBy", adminUserId
        ));
    }

    /**
     * Get all Redis keys matching pattern
     */
    @GetMapping("/keys")
    public ResponseEntity<Map<String, Object>> getKeys(
            @RequestParam(defaultValue = "*") String pattern,
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.debug("Admin {} requesting keys with pattern: {}", adminUserId, pattern);
        
        var keys = cacheService.getKeys(pattern);
        
        return ResponseEntity.ok(Map.of(
            "keys", keys,
            "pattern", pattern,
            "count", keys.size(),
            "requestedBy", adminUserId
        ));
    }

    /**
     * Delete Redis key
     */
    @DeleteMapping("/key/{key}")
    public ResponseEntity<Map<String, String>> deleteKey(
            @PathVariable String key,
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        log.warn("Admin {} deleting Redis key: {}", adminUserId, key);
        
        cacheService.delete(key);
        
        return ResponseEntity.ok(Map.of(
            "message", "Redis key deleted successfully",
            "key", key,
            "deletedBy", adminUserId
        ));
    }

    /**
     * Check if Redis key exists
     */
    @GetMapping("/key-exists/{key}")
    public ResponseEntity<Map<String, Object>> checkKeyExists(
            @PathVariable String key,
            @RequestHeader("X-Admin-User-Id") String adminUserId) {
        
        boolean exists = cacheService.exists(key);
        
        return ResponseEntity.ok(Map.of(
            "exists", exists,
            "key", key,
            "checkedBy", adminUserId
        ));
    }
}
