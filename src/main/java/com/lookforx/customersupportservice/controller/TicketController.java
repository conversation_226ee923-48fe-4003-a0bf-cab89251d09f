package com.lookforx.customersupportservice.controller;

import com.lookforx.common.dto.ApiResponse;
import com.lookforx.customersupportservice.dto.request.CreateTicketRequest;
import com.lookforx.customersupportservice.dto.request.UpdateTicketRequest;
import com.lookforx.customersupportservice.dto.response.TicketListResponse;
import com.lookforx.customersupportservice.dto.response.TicketResponse;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;
import com.lookforx.customersupportservice.domain.enums.TicketType;
import com.lookforx.customersupportservice.service.TicketService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST Controller for user ticket operations
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/tickets")
@RequiredArgsConstructor
@Tag(name = "User Tickets", description = "User ticket management operations")
public class TicketController {

    private final TicketService ticketService;

    @PostMapping
    @Operation(summary = "Create a new ticket", description = "Create a new support ticket")
    public ResponseEntity<ApiResponse<TicketResponse>> createTicket(
            @RequestBody CreateTicketRequest request,
            @RequestHeader(value = "X-User-ID", required = false) String userId,
            @RequestHeader(value = "X-User-Email", required = false) String userEmail,
            @RequestHeader(value = "X-User-Name", required = false) String userName) {

        log.info("Creating ticket for user: {} with type: {}", userId, request.getType());

        try {
            // Auto-fill user information from headers if not provided in request
            if ((request.getUserEmail() == null || request.getUserEmail().trim().isEmpty()) && userEmail != null) {
                request.setUserEmail(userEmail);
            }
            if ((request.getUserName() == null || request.getUserName().trim().isEmpty()) && userName != null) {
                request.setUserName(userName);
            }

            // Validate required fields after auto-filling
            if (request.getUserEmail() == null || request.getUserEmail().trim().isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("User email is required"));
            }
            if (request.getUserName() == null || request.getUserName().trim().isEmpty()) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("User name is required"));
            }

            TicketResponse response = ticketService.createTicket(request, userId);
            return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(response, "Ticket created successfully"));
        } catch (Exception e) {
            log.error("Error creating ticket for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to create ticket: " + e.getMessage()));
        }
    }

    @GetMapping
    @Operation(summary = "Get user tickets", description = "Get paginated list of user's tickets")
    public ResponseEntity<ApiResponse<TicketListResponse>> getUserTickets(
            @RequestHeader(value = "X-User-ID", required = false) String userId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        log.debug("Getting tickets for user: {} page: {} size: {}", userId, page, size);
        
        try {
            Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
            
            TicketListResponse response = ticketService.getUserTickets(userId, pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Tickets retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting tickets for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve tickets: " + e.getMessage()));
        }
    }

    @GetMapping("/status/{status}")
    @Operation(summary = "Get user tickets by status", description = "Get user's tickets filtered by status")
    public ResponseEntity<ApiResponse<TicketListResponse>> getUserTicketsByStatus(
            @RequestHeader(value = "X-User-ID", required = false) String userId,
            @PathVariable TicketStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        log.debug("Getting tickets for user: {} with status: {}", userId, status);
        
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            TicketListResponse response = ticketService.getUserTicketsByStatus(userId, status, pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Tickets retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting tickets by status for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve tickets: " + e.getMessage()));
        }
    }

    @GetMapping("/type/{type}")
    @Operation(summary = "Get user tickets by type", description = "Get user's tickets filtered by type")
    public ResponseEntity<ApiResponse<TicketListResponse>> getUserTicketsByType(
            @RequestHeader(value = "X-User-ID", required = false) String userId,
            @PathVariable TicketType type,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        log.debug("Getting tickets for user: {} with type: {}", userId, type);
        
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
            TicketListResponse response = ticketService.getUserTicketsByType(userId, type, pageable);
            return ResponseEntity.ok(ApiResponse.success(response, "Tickets retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting tickets by type for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve tickets: " + e.getMessage()));
        }
    }

    @GetMapping("/{ticketId}")
    @Operation(summary = "Get ticket by ID", description = "Get detailed ticket information")
    public ResponseEntity<ApiResponse<TicketResponse>> getTicketById(
            @RequestHeader(value = "X-User-ID", required = false) String userId,
            @PathVariable String ticketId) {
        
        log.debug("Getting ticket: {} for user: {}", ticketId, userId);
        
        try {
            TicketResponse response = ticketService.getTicketById(ticketId, userId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting ticket: {} for user: {}", ticketId, userId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("Ticket not found or access denied"));
        }
    }

    @GetMapping("/number/{ticketNumber}")
    @Operation(summary = "Get ticket by number", description = "Get ticket by ticket number")
    public ResponseEntity<ApiResponse<TicketResponse>> getTicketByNumber(
            @RequestHeader(value = "X-User-ID", required = false) String userId,
            @PathVariable String ticketNumber) {
        
        log.debug("Getting ticket by number: {} for user: {}", ticketNumber, userId);
        
        try {
            TicketResponse response = ticketService.getTicketByNumber(ticketNumber, userId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting ticket by number: {} for user: {}", ticketNumber, userId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(ApiResponse.error("Ticket not found or access denied"));
        }
    }

    @PutMapping("/{ticketId}")
    @Operation(summary = "Update ticket", description = "Update ticket information")
    public ResponseEntity<ApiResponse<TicketResponse>> updateTicket(
            @RequestHeader(value = "X-User-ID", required = false) String userId,
            @PathVariable String ticketId,
            @Valid @RequestBody UpdateTicketRequest request) {
        
        log.info("Updating ticket: {} for user: {}", ticketId, userId);
        
        try {
            TicketResponse response = ticketService.updateTicket(ticketId, request, userId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket updated successfully"));
        } catch (Exception e) {
            log.error("Error updating ticket: {} for user: {}", ticketId, userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to update ticket: " + e.getMessage()));
        }
    }

    @PostMapping("/{ticketId}/comments")
    @Operation(summary = "Add comment to ticket", description = "Add a comment to the ticket")
    public ResponseEntity<ApiResponse<TicketResponse>> addComment(
            @RequestHeader(value = "X-User-ID", required = false) String userId,
            @PathVariable String ticketId,
            @RequestBody String comment) {
        
        log.info("Adding comment to ticket: {} for user: {}", ticketId, userId);
        
        try {
            TicketResponse response = ticketService.addUserComment(ticketId, comment, userId);
            return ResponseEntity.ok(ApiResponse.success(response, "Comment added successfully"));
        } catch (Exception e) {
            log.error("Error adding comment to ticket: {} for user: {}", ticketId, userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to add comment: " + e.getMessage()));
        }
    }

    @PostMapping("/{ticketId}/close")
    @Operation(summary = "Request ticket closure", description = "Request closure of the ticket")
    public ResponseEntity<ApiResponse<TicketResponse>> requestClosure(
            @RequestHeader(value = "X-User-ID", required = false) String userId,
            @PathVariable String ticketId,
            @RequestBody(required = false) String reason) {
        
        log.info("Requesting closure for ticket: {} by user: {}", ticketId, userId);
        
        try {
            TicketResponse response = ticketService.requestTicketClosure(ticketId, reason, userId);
            return ResponseEntity.ok(ApiResponse.success(response, "Ticket closure requested successfully"));
        } catch (Exception e) {
            log.error("Error requesting closure for ticket: {} by user: {}", ticketId, userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                .body(ApiResponse.error("Failed to request closure: " + e.getMessage()));
        }
    }

    @GetMapping("/statistics")
    @Operation(summary = "Get user ticket statistics", description = "Get statistics for user's tickets")
    public ResponseEntity<ApiResponse<TicketListResponse.SummaryStats>> getUserStatistics(
            @RequestHeader(value = "X-User-ID", required = false) String userId) {
        
        log.debug("Getting statistics for user: {}", userId);
        
        try {
            TicketListResponse.SummaryStats stats = ticketService.getUserTicketStatistics(userId);
            return ResponseEntity.ok(ApiResponse.success(stats, "Statistics retrieved successfully"));
        } catch (Exception e) {
            log.error("Error getting statistics for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("Failed to retrieve statistics: " + e.getMessage()));
        }
    }

    @GetMapping("/can-access/{ticketId}")
    @Operation(summary = "Check ticket access", description = "Check if user can access the ticket")
    public ResponseEntity<ApiResponse<Boolean>> canAccessTicket(
            @RequestHeader(value = "X-User-ID", required = false) String userId,
            @PathVariable String ticketId) {
        
        try {
            boolean canAccess = ticketService.canUserAccessTicket(ticketId, userId);
            return ResponseEntity.ok(ApiResponse.success(canAccess, "Access check completed"));
        } catch (Exception e) {
            log.error("Error checking access for ticket: {} by user: {}", ticketId, userId, e);
            return ResponseEntity.ok(ApiResponse.success(false, "Access denied"));
        }
    }

    @GetMapping("/can-update/{ticketId}")
    @Operation(summary = "Check ticket update permission", description = "Check if user can update the ticket")
    public ResponseEntity<ApiResponse<Boolean>> canUpdateTicket(
            @RequestHeader(value = "X-User-ID", required = false) String userId,
            @PathVariable String ticketId) {
        
        try {
            boolean canUpdate = ticketService.canUserUpdateTicket(ticketId, userId);
            return ResponseEntity.ok(ApiResponse.success(canUpdate, "Update permission check completed"));
        } catch (Exception e) {
            log.error("Error checking update permission for ticket: {} by user: {}", ticketId, userId, e);
            return ResponseEntity.ok(ApiResponse.success(false, "Update not allowed"));
        }
    }
}
