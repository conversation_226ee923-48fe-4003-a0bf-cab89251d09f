package com.lookforx.customersupportservice.repository;

import com.lookforx.customersupportservice.domain.entity.Ticket;
import com.lookforx.customersupportservice.domain.enums.TicketPriority;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;
import com.lookforx.customersupportservice.domain.enums.TicketType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Ticket entity
 */
@Repository
public interface TicketRepository extends MongoRepository<Ticket, String> {

    /**
     * Find ticket by ticket number
     */
    Optional<Ticket> findByTicketNumber(String ticketNumber);

    /**
     * Find tickets by user ID
     */
    Page<Ticket> findByUserIdAndDeletedFalseOrderByCreatedAtDesc(String userId, Pageable pageable);

    /**
     * Find tickets by user ID and status
     */
    Page<Ticket> findByUserIdAndStatusAndDeletedFalseOrderByCreatedAtDesc(
        String userId, TicketStatus status, Pageable pageable);

    /**
     * Find tickets by user ID and type
     */
    Page<Ticket> findByUserIdAndTypeAndDeletedFalseOrderByCreatedAtDesc(
        String userId, TicketType type, Pageable pageable);

    /**
     * Find tickets by status
     */
    Page<Ticket> findByStatusAndDeletedFalseOrderByCreatedAtDesc(TicketStatus status, Pageable pageable);

    /**
     * Find tickets by type
     */
    Page<Ticket> findByTypeAndDeletedFalseOrderByCreatedAtDesc(TicketType type, Pageable pageable);

    /**
     * Find tickets by priority
     */
    Page<Ticket> findByPriorityAndDeletedFalseOrderByCreatedAtDesc(TicketPriority priority, Pageable pageable);

    /**
     * Find tickets assigned to specific admin
     */
    Page<Ticket> findByAssignedToAndDeletedFalseOrderByCreatedAtDesc(String assignedTo, Pageable pageable);

    /**
     * Find unassigned tickets
     */
    Page<Ticket> findByAssignedToIsNullAndDeletedFalseOrderByPriorityDescCreatedAtAsc(Pageable pageable);

    /**
     * Find overdue tickets
     */
    @Query("{ 'status': { $in: ['RECEIVED', 'IN_PROGRESS', 'WAITING_FOR_USER', 'ESCALATED'] }, " +
           "'deleted': false, " +
           "$expr: { $lt: [{ $add: ['$createdAt', { $multiply: ['$priority.expectedResponseTimeHours', 3600000] }] }, new Date()] } }")
    Page<Ticket> findOverdueTickets(Pageable pageable);

    /**
     * Find tickets by multiple statuses
     */
    Page<Ticket> findByStatusInAndDeletedFalseOrderByCreatedAtDesc(List<TicketStatus> statuses, Pageable pageable);

    /**
     * Find tickets by multiple types
     */
    Page<Ticket> findByTypeInAndDeletedFalseOrderByCreatedAtDesc(List<TicketType> types, Pageable pageable);

    /**
     * Find tickets by multiple priorities
     */
    Page<Ticket> findByPriorityInAndDeletedFalseOrderByCreatedAtDesc(List<TicketPriority> priorities, Pageable pageable);

    /**
     * Find tickets created between dates
     */
    Page<Ticket> findByCreatedAtBetweenAndDeletedFalseOrderByCreatedAtDesc(
        LocalDateTime startDate, LocalDateTime endDate, Pageable pageable);

    /**
     * Find tickets by reference ID (for complaints)
     */
    List<Ticket> findByReferenceIdAndDeletedFalseOrderByCreatedAtDesc(String referenceId);

    /**
     * Find tickets by tags
     */
    Page<Ticket> findByTagsInAndDeletedFalseOrderByCreatedAtDesc(List<String> tags, Pageable pageable);

    /**
     * Text search in subject and description
     */
    @Query("{ $text: { $search: ?0 }, 'deleted': false }")
    Page<Ticket> findByTextSearch(String searchText, Pageable pageable);

    /**
     * Complex search with multiple criteria
     */
    @Query("{ " +
           "$and: [ " +
           "  { 'deleted': false }, " +
           "  { $or: [ " +
           "    { 'userId': { $regex: ?0, $options: 'i' } }, " +
           "    { 'subject': { $regex: ?0, $options: 'i' } }, " +
           "    { 'description': { $regex: ?0, $options: 'i' } }, " +
           "    { 'ticketNumber': { $regex: ?0, $options: 'i' } } " +
           "  ] } " +
           "] }")
    Page<Ticket> findBySearchQuery(String searchQuery, Pageable pageable);

    // Statistics queries

    /**
     * Count tickets by status
     */
    long countByStatusAndDeletedFalse(TicketStatus status);

    /**
     * Count tickets by type
     */
    long countByTypeAndDeletedFalse(TicketType type);

    /**
     * Count tickets by priority
     */
    long countByPriorityAndDeletedFalse(TicketPriority priority);

    /**
     * Count tickets by user
     */
    long countByUserIdAndDeletedFalse(String userId);

    /**
     * Count assigned tickets
     */
    long countByAssignedToIsNotNullAndDeletedFalse();

    /**
     * Count unassigned tickets
     */
    long countByAssignedToIsNullAndDeletedFalse();

    /**
     * Count tickets assigned to specific admin
     */
    long countByAssignedToAndDeletedFalse(String assignedTo);

    /**
     * Count tickets created today
     */
    @Query("{ 'createdAt': { $gte: ?0 }, 'deleted': false }")
    long countTicketsCreatedAfter(LocalDateTime date);

    /**
     * Count resolved tickets in date range
     */
    @Query("{ 'status': 'RESOLVED', 'resolvedAt': { $gte: ?0, $lte: ?1 }, 'deleted': false }")
    long countResolvedTicketsBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find tickets for user with status filter
     */
    @Query("{ 'userId': ?0, 'status': { $in: ?1 }, 'deleted': false }")
    Page<Ticket> findByUserIdAndStatusIn(String userId, List<TicketStatus> statuses, Pageable pageable);

    /**
     * Find tickets with complex admin filters
     */
    @Query("{ " +
           "$and: [ " +
           "  { 'deleted': false }, " +
           "  { $or: [ " +
           "    { 'status': { $in: ?0 } }, " +
           "    { 'type': { $in: ?1 } }, " +
           "    { 'priority': { $in: ?2 } }, " +
           "    { 'assignedTo': ?3 } " +
           "  ] } " +
           "] }")
    Page<Ticket> findWithAdminFilters(
        List<TicketStatus> statuses, 
        List<TicketType> types, 
        List<TicketPriority> priorities, 
        String assignedTo, 
        Pageable pageable);

    /**
     * Get next ticket number sequence
     */
    @Query(value = "{ 'ticketNumber': { $regex: '^TKT-' } }", 
           sort = "{ 'ticketNumber': -1 }", 
           fields = "{ 'ticketNumber': 1 }")
    Optional<Ticket> findTopByTicketNumberStartingWithOrderByTicketNumberDesc(String prefix);
}
