package com.lookforx.customersupportservice.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger/OpenAPI Configuration
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Customer Support Service API")
                        .version("1.0.0")
                        .description("API documentation for Customer Support Service")
                        .contact(new Contact()
                                .name("LookForX Team")
                                .email("<EMAIL>")))
                .servers(List.of(
                        new Server()
                                .url("http://localhost:8086/api")
                                .description("Development Server")
                ));
    }
}
