package com.lookforx.customersupportservice.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.cache.interceptor.SimpleCacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Enhanced cache configuration for customer support service
 * Provides optimized caching strategies for different data types
 */
@Configuration
@EnableCaching
public class CacheConfig implements CachingConfigurer {

    // Cache names
    public static final String TICKET_CACHE = "tickets";
    public static final String CATEGORY_CACHE = "categories";
    public static final String USER_SESSION_CACHE = "user-sessions";
    public static final String SEARCH_RESULT_CACHE = "search-results";
    public static final String RATE_LIMIT_CACHE = "rate-limits";
    public static final String TICKET_STATS_CACHE = "ticket-stats";

    @Value("${spring.cache.redis.time-to-live:3600000}")
    private long defaultTtl;

    private final RedisConnectionFactory redisConnectionFactory;

    public CacheConfig(RedisConnectionFactory redisConnectionFactory) {
        this.redisConnectionFactory = redisConnectionFactory;
    }

    /**
     * Redis cache manager with custom configurations for different cache types
     */
    @Bean
    @Override
    public CacheManager cacheManager() {
        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultCacheConfiguration())
                .withInitialCacheConfigurations(cacheConfigurations())
                .transactionAware()
                .build();
    }

    /**
     * Default cache configuration
     */
    private RedisCacheConfiguration defaultCacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMillis(defaultTtl))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();
    }

    /**
     * Custom cache configurations for different cache types
     */
    private Map<String, RedisCacheConfiguration> cacheConfigurations() {
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

        // Ticket cache - 1 hour TTL
        cacheConfigurations.put(TICKET_CACHE,
            RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues());

        // Category cache - 24 hours TTL (categories change rarely)
        cacheConfigurations.put(CATEGORY_CACHE,
            RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(24))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues());

        // User session cache - 2 hours TTL
        cacheConfigurations.put(USER_SESSION_CACHE,
            RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(2))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues());

        // Search result cache - 15 minutes TTL
        cacheConfigurations.put(SEARCH_RESULT_CACHE,
            RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(15))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues());

        // Rate limit cache - 1 hour TTL
        cacheConfigurations.put(RATE_LIMIT_CACHE,
            RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues());

        // Ticket stats cache - 30 minutes TTL
        cacheConfigurations.put(TICKET_STATS_CACHE,
            RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues());

        // Legacy cache names for backward compatibility
        cacheConfigurations.put("userTickets", defaultCacheConfiguration().entryTtl(Duration.ofMinutes(30)));
        cacheConfigurations.put("ticket", defaultCacheConfiguration().entryTtl(Duration.ofHours(1)));
        cacheConfigurations.put("statistics", defaultCacheConfiguration().entryTtl(Duration.ofMinutes(15)));
        cacheConfigurations.put("searchResults", defaultCacheConfiguration().entryTtl(Duration.ofMinutes(10)));
        cacheConfigurations.put("adminTickets", defaultCacheConfiguration().entryTtl(Duration.ofMinutes(5)));

        return cacheConfigurations;
    }

    /**
     * Custom key generator for cache keys
     */
    @Bean
    @Override
    public KeyGenerator keyGenerator() {
        return (target, method, params) -> {
            StringBuilder sb = new StringBuilder();
            sb.append(target.getClass().getSimpleName()).append(".");
            sb.append(method.getName()).append(":");
            for (Object param : params) {
                if (param != null) {
                    sb.append(param.toString()).append(",");
                }
            }
            return sb.toString();
        };
    }

    /**
     * Cache error handler - logs errors but doesn't fail the operation
     */
    @Bean
    @Override
    public CacheErrorHandler errorHandler() {
        return new SimpleCacheErrorHandler();
    }
}
