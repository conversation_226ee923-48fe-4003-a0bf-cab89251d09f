package com.lookforx.customersupportservice.mapper;

import com.lookforx.customersupportservice.domain.entity.Ticket;
import com.lookforx.customersupportservice.domain.entity.TicketComment;
import com.lookforx.customersupportservice.dto.response.TicketListResponse;
import com.lookforx.customersupportservice.dto.response.TicketResponse;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper class for converting between Ticket entities and DTOs
 */
@Component
public class TicketMapper {

    /**
     * Convert Ticket entity to TicketResponse DTO
     */
    public TicketResponse toResponse(Ticket ticket) {
        if (ticket == null) {
            return null;
        }

        return TicketResponse.builder()
            .id(ticket.getId())
            .ticketNumber(ticket.getTicketNumber())
            .userId(ticket.getUserId())
            .userEmail(ticket.getUserEmail())
            .userName(ticket.getUserName())
            .type(ticket.getType())
            .status(ticket.getStatus())
            .priority(ticket.getPriority())
            .subject(ticket.getSubject())
            .description(ticket.getDescription())
            .referenceId(ticket.getReferenceId())
            .referenceType(ticket.getReferenceType())
            .assignedTo(ticket.getAssignedTo())
            .assignedAt(ticket.getAssignedAt())
            .resolution(ticket.getResolution())
            .resolvedBy(ticket.getResolvedBy())
            .resolvedAt(ticket.getResolvedAt())
            .comments(toCommentResponses(ticket.getComments()))
            .attachments(ticket.getAttachments())
            .tags(ticket.getTags())
            .createdAt(ticket.getCreatedAt())
            .updatedAt(ticket.getUpdatedAt())
            .canBeUpdatedByUser(ticket.canBeUpdatedByUser())
            .isOverdue(ticket.isOverdue())
            .ageInHours(ticket.getAgeInHours())
            .statusDisplayName(ticket.getStatus() != null ? ticket.getStatus().getDisplayName() : null)
            .priorityDisplayName(ticket.getPriority() != null ? ticket.getPriority().getDisplayName() : null)
            .typeDisplayName(ticket.getType() != null ? ticket.getType().getDisplayName() : null)
            .build();
    }

    /**
     * Convert Ticket entity to TicketSummary for list view
     */
    public TicketListResponse.TicketSummary toSummary(Ticket ticket) {
        if (ticket == null) {
            return null;
        }

        return TicketListResponse.TicketSummary.builder()
            .id(ticket.getId())
            .ticketNumber(ticket.getTicketNumber())
            .userId(ticket.getUserId())
            .userName(ticket.getUserName())
            .type(ticket.getType())
            .status(ticket.getStatus())
            .priority(ticket.getPriority())
            .subject(ticket.getSubject())
            .assignedTo(ticket.getAssignedTo())
            .createdAt(ticket.getCreatedAt() != null ? ticket.getCreatedAt().toString() : null)
            .updatedAt(ticket.getUpdatedAt() != null ? ticket.getUpdatedAt().toString() : null)
            .resolvedAt(ticket.getResolvedAt() != null ? ticket.getResolvedAt().toString() : null)
            .isOverdue(ticket.isOverdue())
            .ageInHours(ticket.getAgeInHours())
            .commentCount(ticket.getComments() != null ? ticket.getComments().size() : 0)
            .hasAttachments(ticket.getAttachments() != null && !ticket.getAttachments().isEmpty())
            .tags(ticket.getTags())
            .statusDisplayName(ticket.getStatus() != null ? ticket.getStatus().getDisplayName() : null)
            .priorityDisplayName(ticket.getPriority() != null ? ticket.getPriority().getDisplayName() : null)
            .typeDisplayName(ticket.getType() != null ? ticket.getType().getDisplayName() : null)
            .priorityColorCode(ticket.getPriority() != null ? ticket.getPriority().getColorCode() : null)
            .build();
    }

    /**
     * Convert TicketComment entity to TicketCommentResponse DTO
     */
    public TicketResponse.TicketCommentResponse toCommentResponse(TicketComment comment) {
        if (comment == null) {
            return null;
        }

        return TicketResponse.TicketCommentResponse.builder()
            .id(comment.getId())
            .content(comment.getContent())
            .authorId(comment.getAuthorId())
            .authorName(comment.getAuthorName())
            .authorType(comment.getAuthorType() != null ? comment.getAuthorType().name() : null)
            .visibleToUser(comment.isVisibleToUser())
            .internal(comment.isInternal())
            .attachments(comment.getAttachments())
            .createdAt(comment.getCreatedAt())
            .updatedAt(comment.getUpdatedAt())
            .edited(comment.isEdited())
            .build();
    }

    /**
     * Convert list of TicketComment entities to list of TicketCommentResponse DTOs
     */
    public List<TicketResponse.TicketCommentResponse> toCommentResponses(List<TicketComment> comments) {
        if (comments == null) {
            return null;
        }

        return comments.stream()
            .map(this::toCommentResponse)
            .collect(Collectors.toList());
    }

    /**
     * Convert Page of Ticket entities to TicketListResponse
     */
    public TicketListResponse toListResponse(Page<Ticket> ticketPage, boolean isUserView) {
        if (ticketPage == null) {
            return TicketListResponse.empty(0, 10);
        }

        List<TicketListResponse.TicketSummary> ticketSummaries = ticketPage.getContent().stream()
            .map(this::toSummary)
            .collect(Collectors.toList());

        TicketListResponse.PaginationInfo pagination = TicketListResponse.PaginationInfo.builder()
            .page(ticketPage.getNumber())
            .size(ticketPage.getSize())
            .totalElements(ticketPage.getTotalElements())
            .totalPages(ticketPage.getTotalPages())
            .hasNext(ticketPage.hasNext())
            .hasPrevious(ticketPage.hasPrevious())
            .isFirst(ticketPage.isFirst())
            .isLast(ticketPage.isLast())
            .build();

        // Generate basic stats from current page data
        TicketListResponse.SummaryStats stats = generateStatsFromTickets(ticketPage.getContent());

        return TicketListResponse.builder()
            .tickets(ticketSummaries)
            .pagination(pagination)
            .stats(stats)
            .build();
    }

    /**
     * Generate summary statistics from a list of tickets
     */
    private TicketListResponse.SummaryStats generateStatsFromTickets(List<Ticket> tickets) {
        if (tickets == null || tickets.isEmpty()) {
            return TicketListResponse.SummaryStats.builder()
                .totalTickets(0)
                .openTickets(0)
                .resolvedTickets(0)
                .overdueTickets(0)
                .highPriorityTickets(0)
                .unassignedTickets(0)
                .averageResolutionTimeHours(0.0)
                .build();
        }

        long totalTickets = tickets.size();
        long openTickets = tickets.stream()
            .filter(t -> !t.getStatus().isFinal())
            .count();
        long resolvedTickets = tickets.stream()
            .filter(t -> t.getStatus().name().equals("RESOLVED"))
            .count();
        long overdueTickets = tickets.stream()
            .filter(Ticket::isOverdue)
            .count();
        long highPriorityTickets = tickets.stream()
            .filter(t -> t.getPriority().name().equals("HIGH") || t.getPriority().name().equals("URGENT"))
            .count();
        long unassignedTickets = tickets.stream()
            .filter(t -> t.getAssignedTo() == null)
            .count();

        // Calculate average resolution time for resolved tickets
        double averageResolutionTime = tickets.stream()
            .filter(t -> t.getResolvedAt() != null && t.getCreatedAt() != null)
            .mapToLong(t -> java.time.Duration.between(t.getCreatedAt(), t.getResolvedAt()).toHours())
            .average()
            .orElse(0.0);

        return TicketListResponse.SummaryStats.builder()
            .totalTickets(totalTickets)
            .openTickets(openTickets)
            .resolvedTickets(resolvedTickets)
            .overdueTickets(overdueTickets)
            .highPriorityTickets(highPriorityTickets)
            .unassignedTickets(unassignedTickets)
            .averageResolutionTimeHours(averageResolutionTime)
            .build();
    }

    /**
     * Convert list of Ticket entities to list of TicketResponse DTOs
     */
    public List<TicketResponse> toResponses(List<Ticket> tickets) {
        if (tickets == null) {
            return null;
        }

        return tickets.stream()
            .map(this::toResponse)
            .collect(Collectors.toList());
    }

    /**
     * Create user-safe responses (filter out internal information)
     */
    public List<TicketResponse> toUserResponses(List<Ticket> tickets) {
        if (tickets == null) {
            return null;
        }

        return tickets.stream()
            .map(this::toResponse)
            .map(TicketResponse::createUserResponse)
            .collect(Collectors.toList());
    }

    /**
     * Create user-safe list response
     */
    public TicketListResponse toUserListResponse(Page<Ticket> ticketPage) {
        TicketListResponse response = toListResponse(ticketPage, true);
        
        // Filter out any sensitive information from summaries if needed
        // For now, TicketSummary doesn't contain sensitive info
        
        return response;
    }
}
