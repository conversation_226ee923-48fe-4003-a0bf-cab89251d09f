package com.lookforx.customersupportservice.exception;

/**
 * Exception thrown when an invalid operation is attempted on a ticket
 */
public class InvalidTicketOperationException extends RuntimeException {
    
    public InvalidTicketOperationException(String message) {
        super(message);
    }
    
    public InvalidTicketOperationException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public static InvalidTicketOperationException cannotUpdate(String ticketId, String status) {
        return new InvalidTicketOperationException(
            String.format("Ticket %s cannot be updated in status: %s", ticketId, status));
    }
    
    public static InvalidTicketOperationException invalidStatusTransition(String from, String to) {
        return new InvalidTicketOperationException(
            String.format("Invalid status transition from %s to %s", from, to));
    }
    
    public static InvalidTicketOperationException missingRequiredField(String field) {
        return new InvalidTicketOperationException(
            String.format("Required field missing: %s", field));
    }
}
