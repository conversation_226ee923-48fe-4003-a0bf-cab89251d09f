package com.lookforx.customersupportservice.exception;

/**
 * Exception thrown when a ticket is not found
 */
public class TicketNotFoundException extends RuntimeException {
    
    public TicketNotFoundException(String message) {
        super(message);
    }
    
    public TicketNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public static TicketNotFoundException byId(String ticketId) {
        return new TicketNotFoundException("Ticket not found with ID: " + ticketId);
    }
    
    public static TicketNotFoundException byNumber(String ticketNumber) {
        return new TicketNotFoundException("Ticket not found with number: " + ticketNumber);
    }
}
