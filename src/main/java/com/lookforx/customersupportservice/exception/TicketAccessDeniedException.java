package com.lookforx.customersupportservice.exception;

/**
 * Exception thrown when user tries to access a ticket they don't own
 */
public class TicketAccessDeniedException extends RuntimeException {
    
    public TicketAccessDeniedException(String message) {
        super(message);
    }
    
    public TicketAccessDeniedException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public static TicketAccessDeniedException forUser(String ticketId, String userId) {
        return new TicketAccessDeniedException(
            String.format("User %s does not have access to ticket %s", userId, ticketId));
    }
}
