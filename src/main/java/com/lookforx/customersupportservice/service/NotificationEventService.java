package com.lookforx.customersupportservice.service;

import com.lookforx.customersupportservice.domain.entity.Ticket;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;

/**
 * Service for publishing notification events via Kafka
 */
public interface NotificationEventService {

    /**
     * Publish ticket created event
     */
    void publishTicketCreatedEvent(Ticket ticket);

    /**
     * Publish ticket updated event
     */
    void publishTicketUpdatedEvent(Ticket ticket, String updatedBy);

    /**
     * Publish ticket status changed event
     */
    void publishTicketStatusChangedEvent(Ticket ticket, TicketStatus newStatus, String changedBy);

    /**
     * Publish ticket assigned event
     */
    void publishTicketAssignedEvent(Ticket ticket, String assignedTo, String assignedBy);

    /**
     * Publish ticket resolved event
     */
    void publishTicketResolvedEvent(Ticket ticket, String resolvedBy, String resolution);

    /**
     * Publish ticket rejected event
     */
    void publishTicketRejectedEvent(Ticket ticket, String rejectedBy, String reason);

    /**
     * Publish ticket escalated event
     */
    void publishTicketEscalatedEvent(Ticket ticket, String escalatedBy, String reason);

    /**
     * Publish ticket closed event
     */
    void publishTicketClosedEvent(Ticket ticket, String closedBy, String reason);

    /**
     * Publish ticket reopened event
     */
    void publishTicketReopenedEvent(Ticket ticket, String reopenedBy, String reason);

    /**
     * Publish ticket comment added event
     */
    void publishTicketCommentAddedEvent(Ticket ticket, String commentBy, String comment);

    /**
     * Publish ticket overdue event
     */
    void publishTicketOverdueEvent(Ticket ticket);

    /**
     * Publish ticket reminder event
     */
    void publishTicketReminderEvent(Ticket ticket, String reminderType);
}
