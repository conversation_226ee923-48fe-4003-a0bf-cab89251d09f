package com.lookforx.customersupportservice.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Performance monitoring service for tracking application metrics
 * Provides performance monitoring and metrics collection
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PerformanceMonitoringService {

    private final CacheService cacheService;
    
    // In-memory metrics storage (for demonstration)
    private final Map<String, Long> requestCounts = new ConcurrentHashMap<>();
    private final Map<String, Long> responseTimes = new ConcurrentHashMap<>();
    private final Map<String, Long> errorCounts = new ConcurrentHashMap<>();

    /**
     * Record API request metrics
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> recordApiRequest(String endpoint, long responseTimeMs, boolean isError) {
        try {
            String timestamp = LocalDateTime.now().toString().substring(0, 13); // Hour precision
            String key = endpoint + ":" + timestamp;
            
            // Increment request count
            requestCounts.merge(key, 1L, Long::sum);
            
            // Record response time
            responseTimes.merge(key, responseTimeMs, (existing, newValue) -> (existing + newValue) / 2);
            
            // Record errors
            if (isError) {
                errorCounts.merge(key, 1L, Long::sum);
            }
            
            // Store in Redis for persistence
            cacheService.incrementWithTtl("metrics:requests:" + key, Duration.ofHours(24));
            if (isError) {
                cacheService.incrementWithTtl("metrics:errors:" + key, Duration.ofHours(24));
            }
            
            log.debug("Recorded metrics for endpoint: {}, responseTime: {}ms, error: {}", 
                     endpoint, responseTimeMs, isError);
            
        } catch (Exception e) {
            log.error("Error recording API metrics", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * Record cache hit/miss metrics
     */
    @Async("cacheExecutor")
    public CompletableFuture<Void> recordCacheMetrics(String cacheName, String operation, boolean hit) {
        try {
            String timestamp = LocalDateTime.now().toString().substring(0, 13); // Hour precision
            String key = cacheName + ":" + operation + ":" + timestamp;
            
            if (hit) {
                cacheService.incrementWithTtl("metrics:cache:hits:" + key, Duration.ofHours(24));
            } else {
                cacheService.incrementWithTtl("metrics:cache:misses:" + key, Duration.ofHours(24));
            }
            
            log.debug("Recorded cache metrics - Cache: {}, Operation: {}, Hit: {}", 
                     cacheName, operation, hit);
            
        } catch (Exception e) {
            log.error("Error recording cache metrics", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * Record database query metrics
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> recordDatabaseMetrics(String queryType, long executionTimeMs) {
        try {
            String timestamp = LocalDateTime.now().toString().substring(0, 13); // Hour precision
            String key = queryType + ":" + timestamp;
            
            cacheService.incrementWithTtl("metrics:db:queries:" + key, Duration.ofHours(24));
            cacheService.setWithTtl("metrics:db:time:" + key, executionTimeMs, Duration.ofHours(24));
            
            log.debug("Recorded database metrics - Query: {}, Time: {}ms", queryType, executionTimeMs);
            
        } catch (Exception e) {
            log.error("Error recording database metrics", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * Record Kafka metrics
     */
    @Async("taskExecutor")
    public CompletableFuture<Void> recordKafkaMetrics(String topic, String operation, boolean success) {
        try {
            String timestamp = LocalDateTime.now().toString().substring(0, 13); // Hour precision
            String key = topic + ":" + operation + ":" + timestamp;
            
            if (success) {
                cacheService.incrementWithTtl("metrics:kafka:success:" + key, Duration.ofHours(24));
            } else {
                cacheService.incrementWithTtl("metrics:kafka:errors:" + key, Duration.ofHours(24));
            }
            
            log.debug("Recorded Kafka metrics - Topic: {}, Operation: {}, Success: {}", 
                     topic, operation, success);
            
        } catch (Exception e) {
            log.error("Error recording Kafka metrics", e);
        }
        
        return CompletableFuture.completedFuture(null);
    }

    /**
     * Get performance metrics summary
     */
    public Map<String, Object> getPerformanceMetrics() {
        try {
            Map<String, Object> metrics = new ConcurrentHashMap<>();
            
            // API metrics
            long totalRequests = requestCounts.values().stream().mapToLong(Long::longValue).sum();
            long totalErrors = errorCounts.values().stream().mapToLong(Long::longValue).sum();
            double avgResponseTime = responseTimes.values().stream()
                    .mapToLong(Long::longValue)
                    .average()
                    .orElse(0.0);
            
            metrics.put("api", Map.of(
                "totalRequests", totalRequests,
                "totalErrors", totalErrors,
                "errorRate", totalRequests > 0 ? (double) totalErrors / totalRequests : 0.0,
                "avgResponseTime", avgResponseTime
            ));
            
            // Cache metrics from Redis
            var cacheHits = cacheService.getKeys("metrics:cache:hits:*");
            var cacheMisses = cacheService.getKeys("metrics:cache:misses:*");
            
            metrics.put("cache", Map.of(
                "hitKeys", cacheHits.size(),
                "missKeys", cacheMisses.size(),
                "hitRate", cacheHits.size() + cacheMisses.size() > 0 ? 
                    (double) cacheHits.size() / (cacheHits.size() + cacheMisses.size()) : 0.0
            ));
            
            // Database metrics
            var dbQueries = cacheService.getKeys("metrics:db:queries:*");
            metrics.put("database", Map.of(
                "totalQueries", dbQueries.size()
            ));
            
            // Kafka metrics
            var kafkaSuccess = cacheService.getKeys("metrics:kafka:success:*");
            var kafkaErrors = cacheService.getKeys("metrics:kafka:errors:*");
            
            metrics.put("kafka", Map.of(
                "successfulOperations", kafkaSuccess.size(),
                "failedOperations", kafkaErrors.size(),
                "successRate", kafkaSuccess.size() + kafkaErrors.size() > 0 ? 
                    (double) kafkaSuccess.size() / (kafkaSuccess.size() + kafkaErrors.size()) : 0.0
            ));
            
            metrics.put("timestamp", LocalDateTime.now());
            
            return metrics;
            
        } catch (Exception e) {
            log.error("Error getting performance metrics", e);
            return Map.of("error", "Failed to retrieve metrics");
        }
    }

    /**
     * Clear all performance metrics
     */
    public void clearMetrics() {
        try {
            requestCounts.clear();
            responseTimes.clear();
            errorCounts.clear();
            
            // Clear Redis metrics
            var metricsKeys = cacheService.getKeys("metrics:*");
            metricsKeys.forEach(cacheService::delete);
            
            log.info("All performance metrics cleared");
            
        } catch (Exception e) {
            log.error("Error clearing metrics", e);
        }
    }

    /**
     * Get system health status
     */
    public Map<String, Object> getHealthStatus() {
        try {
            Map<String, Object> health = new ConcurrentHashMap<>();
            
            // Check Redis connectivity
            boolean redisHealthy = false;
            try {
                cacheService.setWithTtl("health:check", "ok", Duration.ofMinutes(1));
                redisHealthy = "ok".equals(cacheService.get("health:check"));
            } catch (Exception e) {
                log.warn("Redis health check failed", e);
            }
            
            health.put("redis", Map.of(
                "status", redisHealthy ? "UP" : "DOWN",
                "timestamp", LocalDateTime.now()
            ));
            
            // Overall health
            health.put("overall", Map.of(
                "status", redisHealthy ? "UP" : "DEGRADED",
                "timestamp", LocalDateTime.now()
            ));
            
            return health;
            
        } catch (Exception e) {
            log.error("Error checking health status", e);
            return Map.of(
                "overall", Map.of("status", "DOWN", "error", e.getMessage()),
                "timestamp", LocalDateTime.now()
            );
        }
    }
}
