package com.lookforx.customersupportservice.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * Cache service for managing Redis cache operations
 * Provides utilities for cache management and monitoring
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CacheService {

    private final CacheManager cacheManager;
    private final RedisTemplate<String, Object> redisTemplate;
    private final RedisTemplate<String, String> stringRedisTemplate;

    /**
     * Clear all caches
     */
    public void clearAllCaches() {
        log.info("Clearing all caches");
        cacheManager.getCacheNames().forEach(cacheName -> {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
                log.debug("Cleared cache: {}", cacheName);
            }
        });
    }

    /**
     * Clear specific cache
     */
    public void clearCache(String cacheName) {
        log.info("Clearing cache: {}", cacheName);
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
            log.debug("Cache cleared: {}", cacheName);
        } else {
            log.warn("Cache not found: {}", cacheName);
        }
    }

    /**
     * Evict specific cache entry
     */
    public void evictCacheEntry(String cacheName, String key) {
        log.debug("Evicting cache entry - Cache: {}, Key: {}", cacheName, key);
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.evict(key);
            log.debug("Cache entry evicted - Cache: {}, Key: {}", cacheName, key);
        } else {
            log.warn("Cache not found: {}", cacheName);
        }
    }

    /**
     * Check if cache entry exists
     */
    public boolean cacheEntryExists(String cacheName, String key) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            Cache.ValueWrapper valueWrapper = cache.get(key);
            return valueWrapper != null;
        }
        return false;
    }

    /**
     * Get cache entry
     */
    public Object getCacheEntry(String cacheName, String key) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            Cache.ValueWrapper valueWrapper = cache.get(key);
            return valueWrapper != null ? valueWrapper.get() : null;
        }
        return null;
    }

    /**
     * Put entry in cache
     */
    public void putCacheEntry(String cacheName, String key, Object value) {
        log.debug("Putting cache entry - Cache: {}, Key: {}", cacheName, key);
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.put(key, value);
            log.debug("Cache entry added - Cache: {}, Key: {}", cacheName, key);
        } else {
            log.warn("Cache not found: {}", cacheName);
        }
    }

    /**
     * Set value with TTL using Redis template
     */
    public void setWithTtl(String key, Object value, Duration ttl) {
        log.debug("Setting value with TTL - Key: {}, TTL: {}", key, ttl);
        redisTemplate.opsForValue().set(key, value, ttl);
    }

    /**
     * Get value using Redis template
     */
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * Delete key using Redis template
     */
    public void delete(String key) {
        log.debug("Deleting key: {}", key);
        redisTemplate.delete(key);
    }

    /**
     * Check if key exists
     */
    public boolean exists(String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    /**
     * Get all keys matching pattern
     */
    public Set<String> getKeys(String pattern) {
        return redisTemplate.keys(pattern);
    }

    /**
     * Increment counter
     */
    public Long increment(String key) {
        return redisTemplate.opsForValue().increment(key);
    }

    /**
     * Increment counter with TTL
     */
    public Long incrementWithTtl(String key, Duration ttl) {
        Long value = redisTemplate.opsForValue().increment(key);
        redisTemplate.expire(key, ttl);
        return value;
    }

    /**
     * Async cache warming for frequently accessed data
     */
    @Async("cacheExecutor")
    public CompletableFuture<Void> warmCache(String cacheName, String key, Object value) {
        log.debug("Warming cache - Cache: {}, Key: {}", cacheName, key);
        putCacheEntry(cacheName, key, value);
        return CompletableFuture.completedFuture(null);
    }

    /**
     * Get cache statistics (if available)
     */
    public String getCacheStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("Available caches: ");
        cacheManager.getCacheNames().forEach(cacheName -> {
            stats.append(cacheName).append(", ");
        });
        return stats.toString();
    }

    /**
     * Rate limiting check
     */
    public boolean isRateLimited(String identifier, int maxRequests, Duration window) {
        String key = "rate_limit:" + identifier;
        Long currentCount = incrementWithTtl(key, window);
        
        if (currentCount == null) {
            return false;
        }
        
        boolean isLimited = currentCount > maxRequests;
        if (isLimited) {
            log.warn("Rate limit exceeded for identifier: {} (count: {}, max: {})", 
                    identifier, currentCount, maxRequests);
        }
        
        return isLimited;
    }

    /**
     * Session management
     */
    public void storeUserSession(String sessionId, Object sessionData, Duration ttl) {
        String key = "session:" + sessionId;
        setWithTtl(key, sessionData, ttl);
        log.debug("User session stored: {}", sessionId);
    }

    /**
     * Get user session
     */
    public Object getUserSession(String sessionId) {
        String key = "session:" + sessionId;
        return get(key);
    }

    /**
     * Remove user session
     */
    public void removeUserSession(String sessionId) {
        String key = "session:" + sessionId;
        delete(key);
        log.debug("User session removed: {}", sessionId);
    }
}
