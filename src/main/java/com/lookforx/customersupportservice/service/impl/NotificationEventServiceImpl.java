package com.lookforx.customersupportservice.service.impl;

import com.lookforx.common.events.*;
import com.lookforx.common.kafka.EventPublisher;
import com.lookforx.customersupportservice.domain.entity.Ticket;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;
import com.lookforx.customersupportservice.service.NotificationEventService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * Final simplified implementation of NotificationEventService using lookforx-common EventPublisher
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NotificationEventServiceImpl implements NotificationEventService {

    private final EventPublisher eventPublisher;

    @Override
    public void publishTicketCreatedEvent(Ticket ticket) {
        log.info("Publishing ticket created event for ticket: {}", ticket.getTicketNumber());
        publishEvent(ticket, "CREATED", null);
    }

    @Override
    public void publishTicketUpdatedEvent(Ticket ticket, String updatedBy) {
        log.info("Publishing ticket updated event for ticket: {} by: {}", ticket.getTicketNumber(), updatedBy);
        publishEvent(ticket, "UPDATED", updatedBy);
    }

    @Override
    public void publishTicketStatusChangedEvent(Ticket ticket, TicketStatus newStatus, String changedBy) {
        log.info("Publishing ticket status changed event for ticket: {} to status: {} by: {}", 
            ticket.getTicketNumber(), newStatus, changedBy);
        publishEvent(ticket, "STATUS_CHANGED_" + newStatus.name(), changedBy);
    }

    @Override
    public void publishTicketAssignedEvent(Ticket ticket, String assignedTo, String assignedBy) {
        log.info("Publishing ticket assigned event for ticket: {} assigned to: {} by: {}", 
            ticket.getTicketNumber(), assignedTo, assignedBy);
        publishEvent(ticket, "ASSIGNED", assignedBy);
    }

    @Override
    public void publishTicketResolvedEvent(Ticket ticket, String resolvedBy, String resolution) {
        log.info("Publishing ticket resolved event for ticket: {} by: {}", ticket.getTicketNumber(), resolvedBy);
        publishEvent(ticket, "RESOLVED", resolvedBy);
    }

    @Override
    public void publishTicketRejectedEvent(Ticket ticket, String rejectedBy, String reason) {
        log.info("Publishing ticket rejected event for ticket: {} by: {}", ticket.getTicketNumber(), rejectedBy);
        publishEvent(ticket, "REJECTED", rejectedBy);
    }

    @Override
    public void publishTicketEscalatedEvent(Ticket ticket, String escalatedBy, String reason) {
        log.info("Publishing ticket escalated event for ticket: {} by: {}", ticket.getTicketNumber(), escalatedBy);
        publishEvent(ticket, "ESCALATED", escalatedBy);
    }

    @Override
    public void publishTicketClosedEvent(Ticket ticket, String closedBy, String reason) {
        log.info("Publishing ticket closed event for ticket: {} by: {}", ticket.getTicketNumber(), closedBy);
        publishEvent(ticket, "CLOSED", closedBy);
    }

    @Override
    public void publishTicketReopenedEvent(Ticket ticket, String reopenedBy, String reason) {
        log.info("Publishing ticket reopened event for ticket: {} by: {}", ticket.getTicketNumber(), reopenedBy);
        publishEvent(ticket, "REOPENED", reopenedBy);
    }

    @Override
    public void publishTicketCommentAddedEvent(Ticket ticket, String commentBy, String comment) {
        log.debug("Publishing ticket comment added event for ticket: {} by: {}", ticket.getTicketNumber(), commentBy);
        publishEvent(ticket, "COMMENT_ADDED", commentBy);
    }

    @Override
    public void publishTicketOverdueEvent(Ticket ticket) {
        log.warn("Publishing ticket overdue event for ticket: {}", ticket.getTicketNumber());
        publishEvent(ticket, "OVERDUE", "SYSTEM");
    }

    @Override
    public void publishTicketReminderEvent(Ticket ticket, String reminderType) {
        log.info("Publishing ticket reminder event for ticket: {} type: {}", ticket.getTicketNumber(), reminderType);
        publishEvent(ticket, "REMINDER_" + reminderType, "SYSTEM");
    }

    private void publishEvent(Ticket ticket, String eventType, String actionBy) {
        try {
            // Use appropriate event class based on event type
            BaseEvent event = createEventByType(ticket, eventType, actionBy);

            // Use EventPublisher from lookforx-common
            eventPublisher.publishSupportTicketEvent(event);

            log.debug("Successfully published {} event for ticket: {}", eventType, ticket.getTicketNumber());
        } catch (Exception e) {
            log.error("Failed to publish {} event for ticket: {}", eventType, ticket.getTicketNumber(), e);
        }
    }

    private BaseEvent createEventByType(Ticket ticket, String eventType, String actionBy) {
        // Set common fields
        Long userId = parseUserIdAsLong(ticket.getUserId());
        String serviceName = "customer-support-service";
        String correlationId = ticket.getId();

        switch (eventType) {
            case "CREATED":
                return TicketCreatedEvent.builder()
                    .userId(userId)
                    .serviceName(serviceName)
                    .correlationId(correlationId)
                    .ticketId(parseTicketId(ticket.getId()))
                    .ticketType(ticket.getType().name())
                    .relatedEntityId(ticket.getReferenceId())
                    .title(ticket.getSubject())
                    .description(ticket.getDescription())
                    .priority(ticket.getPriority().name())
                    .targetType(ticket.getReferenceType())
                    .targetId(parseTargetId(ticket.getReferenceId()))
                    .build();

            case "ASSIGNED":
                return TicketAssignedEvent.builder()
                    .userId(userId)
                    .serviceName(serviceName)
                    .correlationId(correlationId)
                    .ticketId(parseTicketId(ticket.getId()))
                    .ticketType(ticket.getType().name())
                    .title(ticket.getSubject())
                    .priority(ticket.getPriority().name())
                    .assignedTo(parseTicketId(ticket.getAssignedTo()))
                    .assignedBy(parseTicketId(actionBy))
                    .assignmentNotes("Ticket assigned to " + ticket.getAssignedTo())
                    .build();

            case "RESOLVED":
                return TicketResolvedEvent.builder()
                    .userId(userId)
                    .serviceName(serviceName)
                    .correlationId(correlationId)
                    .ticketId(parseTicketId(ticket.getId()))
                    .ticketType(ticket.getType().name())
                    .title(ticket.getSubject())
                    .resolution(ticket.getResolution())
                    .resolvedBy(parseTicketId(actionBy))
                    .resolutionNotes(ticket.getResolution())
                    .build();

            case "COMMENT_ADDED":
                return TicketCommentAddedEvent.builder()
                    .userId(userId)
                    .serviceName(serviceName)
                    .correlationId(correlationId)
                    .ticketId(parseTicketId(ticket.getId()))
                    .ticketType(ticket.getType().name())
                    .title(ticket.getSubject())
                    .comment("Comment added to ticket")
                    .commentBy(parseTicketId(actionBy))
                    .commentByName(actionBy)
                    .isInternal(false)
                    .build();

            default:
                // For status changes and other events, use TicketStatusChangedEvent
                if (eventType.startsWith("STATUS_CHANGED_")) {
                    String newStatus = eventType.replace("STATUS_CHANGED_", "");
                    return TicketStatusChangedEvent.builder()
                        .userId(userId)
                        .serviceName(serviceName)
                        .correlationId(correlationId)
                        .ticketId(parseTicketId(ticket.getId()))
                        .ticketType(ticket.getType().name())
                        .oldStatus(ticket.getStatus().name())
                        .newStatus(newStatus)
                        .title(ticket.getSubject())
                        .resolutionNotes(buildDescription(ticket, eventType, actionBy))
                        .assignedTo(parseTicketId(ticket.getAssignedTo()))
                        .changedBy(parseTicketId(actionBy))
                        .build();
                } else {
                    // Default to TicketCreatedEvent for other events
                    return TicketCreatedEvent.builder()
                        .userId(userId)
                        .serviceName(serviceName)
                        .correlationId(correlationId)
                        .ticketId(parseTicketId(ticket.getId()))
                        .ticketType(ticket.getType().name() + "_" + eventType)
                        .relatedEntityId(ticket.getReferenceId())
                        .title(ticket.getSubject())
                        .description(buildDescription(ticket, eventType, actionBy))
                        .priority(ticket.getPriority().name())
                        .targetType(ticket.getReferenceType())
                        .targetId(parseTargetId(ticket.getReferenceId()))
                        .build();
                }
        }
    }

    private Long parseUserIdAsLong(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return null;
        }

        try {
            return Long.parseLong(userId);
        } catch (NumberFormatException e) {
            log.warn("Invalid userId format: {}, returning 0", userId);
            return 0L;
        }
    }

    private String buildDescription(Ticket ticket, String eventType, String actionBy) {
        StringBuilder desc = new StringBuilder();
        desc.append("Ticket ").append(eventType.toLowerCase().replace("_", " "));
        
        if (actionBy != null && !actionBy.equals("SYSTEM")) {
            desc.append(" by ").append(actionBy);
        }
        
        desc.append(". Status: ").append(ticket.getStatus().getDisplayName());
        
        if (ticket.getDescription() != null && ticket.getDescription().length() > 0) {
            desc.append(". Original: ").append(
                ticket.getDescription().length() > 100 ? 
                ticket.getDescription().substring(0, 100) + "..." : 
                ticket.getDescription()
            );
        }
        
        return desc.toString();
    }



    private UUID parseTicketId(String ticketId) {
        try {
            return UUID.fromString(ticketId);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid ticketId format: {}, generating random UUID", ticketId);
            return UUID.randomUUID();
        }
    }

    private UUID parseTargetId(String targetId) {
        if (targetId == null || targetId.trim().isEmpty()) {
            return null;
        }
        
        try {
            return UUID.fromString(targetId);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid targetId format: {}, returning null", targetId);
            return null;
        }
    }
}
