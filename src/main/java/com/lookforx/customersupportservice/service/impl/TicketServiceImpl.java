package com.lookforx.customersupportservice.service.impl;

import com.lookforx.customersupportservice.domain.entity.Ticket;
import com.lookforx.customersupportservice.domain.entity.TicketComment;
import com.lookforx.customersupportservice.domain.entity.TicketMetadata;
import com.lookforx.customersupportservice.domain.enums.TicketPriority;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;
import com.lookforx.customersupportservice.domain.enums.TicketType;
import com.lookforx.customersupportservice.dto.request.AdminUpdateTicketRequest;
import com.lookforx.customersupportservice.dto.request.CreateTicketRequest;
import com.lookforx.customersupportservice.dto.request.UpdateTicketRequest;
import com.lookforx.customersupportservice.dto.response.TicketListResponse;
import com.lookforx.customersupportservice.dto.response.TicketResponse;
import com.lookforx.customersupportservice.repository.TicketRepository;
import com.lookforx.customersupportservice.service.TicketService;
import com.lookforx.customersupportservice.service.NotificationEventService;
import com.lookforx.customersupportservice.mapper.TicketMapper;
import com.lookforx.customersupportservice.exception.TicketNotFoundException;
import com.lookforx.customersupportservice.exception.TicketAccessDeniedException;
import com.lookforx.customersupportservice.exception.InvalidTicketOperationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.lookforx.customersupportservice.config.CacheConfig;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Implementation of TicketService
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class TicketServiceImpl implements TicketService {

    private final TicketRepository ticketRepository;
    private final NotificationEventService notificationEventService;
    private final TicketMapper ticketMapper;
    
    private static final AtomicLong ticketCounter = new AtomicLong(1);

    @Override
    public TicketResponse createTicket(CreateTicketRequest request, String userId) {
        log.info("Creating ticket for user: {} with type: {}", userId, request.getType());
        
        // Validate request
        validateCreateTicketRequest(request);
        
        // Create ticket entity
        Ticket ticket = Ticket.builder()
            .ticketNumber(generateTicketNumber())
            .userId(userId)
            .userEmail(request.getUserEmail())
            .userName(request.getUserName())
            .type(request.getType())
            .status(TicketStatus.RECEIVED)
            .priority(request.getEffectivePriority())
            .subject(request.getSubject())
            .description(request.getDescription())
            .referenceId(request.getReferenceId())
            .referenceType(request.getReferenceType())
            .attachments(request.getAttachments() != null ? request.getAttachments() : List.of())
            .tags(request.getTags() != null ? request.getTags() : List.of())
            .metadata(createMetadataFromRequest(request))
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();

        // Add initial system comment
        ticket.addComment(TicketComment.createSystemComment(
            "Ticket created successfully. Our support team will review your request shortly."
        ));

        // Save ticket
        Ticket savedTicket = ticketRepository.save(ticket);
        
        // Publish event
        notificationEventService.publishTicketCreatedEvent(savedTicket);
        
        log.info("Ticket created successfully: {}", savedTicket.getTicketNumber());
        return ticketMapper.toResponse(savedTicket);
    }

    @Override
    @Cacheable(value = CacheConfig.TICKET_CACHE, key = "'user_' + #userId + '_' + #pageable.pageNumber + '_' + #pageable.pageSize")
    public TicketListResponse getUserTickets(String userId, Pageable pageable) {
        log.debug("Getting tickets for user: {}", userId);
        
        Page<Ticket> ticketPage = ticketRepository.findByUserIdAndDeletedFalseOrderByCreatedAtDesc(userId, pageable);
        return ticketMapper.toListResponse(ticketPage, true); // true for user view
    }

    @Override
    public TicketListResponse getUserTicketsByStatus(String userId, TicketStatus status, Pageable pageable) {
        log.debug("Getting tickets for user: {} with status: {}", userId, status);
        
        Page<Ticket> ticketPage = ticketRepository.findByUserIdAndStatusAndDeletedFalseOrderByCreatedAtDesc(
            userId, status, pageable);
        return ticketMapper.toListResponse(ticketPage, true);
    }

    @Override
    public TicketListResponse getUserTicketsByType(String userId, TicketType type, Pageable pageable) {
        log.debug("Getting tickets for user: {} with type: {}", userId, type);
        
        Page<Ticket> ticketPage = ticketRepository.findByUserIdAndTypeAndDeletedFalseOrderByCreatedAtDesc(
            userId, type, pageable);
        return ticketMapper.toListResponse(ticketPage, true);
    }

    @Override
    @Cacheable(value = CacheConfig.TICKET_CACHE, key = "'single_' + #ticketId", unless = "#result == null")
    public TicketResponse getTicketById(String ticketId, String userId) {
        log.debug("Getting ticket: {} for user: {}", ticketId, userId);
        
        Ticket ticket = findTicketById(ticketId);
        validateUserAccess(ticket, userId);
        
        return TicketResponse.createUserResponse(ticketMapper.toResponse(ticket));
    }

    @Override
    public TicketResponse getTicketByNumber(String ticketNumber, String userId) {
        log.debug("Getting ticket by number: {} for user: {}", ticketNumber, userId);
        
        Ticket ticket = ticketRepository.findByTicketNumber(ticketNumber)
            .orElseThrow(() -> TicketNotFoundException.byNumber(ticketNumber));
        
        validateUserAccess(ticket, userId);
        return TicketResponse.createUserResponse(ticketMapper.toResponse(ticket));
    }

    @Override
    @CacheEvict(value = {"ticket", "userTickets"}, allEntries = true)
    public TicketResponse updateTicket(String ticketId, UpdateTicketRequest request, String userId) {
        log.info("Updating ticket: {} by user: {}", ticketId, userId);
        
        Ticket ticket = findTicketById(ticketId);
        validateUserAccess(ticket, userId);
        validateUserCanUpdate(ticket);
        
        // Update ticket fields
        if (request.getAdditionalDescription() != null && !request.getAdditionalDescription().trim().isEmpty()) {
            ticket.setDescription(ticket.getDescription() + "\n\n--- User Update ---\n" + request.getAdditionalDescription());
        }
        
        if (request.getNewAttachments() != null && !request.getNewAttachments().isEmpty()) {
            ticket.getAttachments().addAll(request.getNewAttachments());
        }
        
        if (request.getNewTags() != null && !request.getNewTags().isEmpty()) {
            request.getNewTags().forEach(ticket::addTag);
        }
        
        if (request.getComment() != null && !request.getComment().trim().isEmpty()) {
            ticket.addComment(TicketComment.createUserComment(userId, ticket.getUserName(), request.getComment()));
        }
        
        if (request.isRequestClose()) {
            ticket.setStatus(TicketStatus.CANCELLED);
            ticket.addComment(TicketComment.createSystemComment(
                "Ticket closure requested by user. Reason: " + 
                (request.getCloseReason() != null ? request.getCloseReason() : "No reason provided")
            ));
        }
        
        // Update contact information if provided
        if (request.getContactUpdate() != null && request.getContactUpdate().hasUpdates()) {
            updateContactInformation(ticket, request.getContactUpdate());
        }
        
        ticket.setUpdatedAt(LocalDateTime.now());
        ticket.setVersion(ticket.getVersion() + 1);
        
        Ticket savedTicket = ticketRepository.save(ticket);
        
        // Publish event
        notificationEventService.publishTicketUpdatedEvent(savedTicket, userId);
        
        log.info("Ticket updated successfully: {}", ticketId);
        return TicketResponse.createUserResponse(ticketMapper.toResponse(savedTicket));
    }

    @Override
    @CacheEvict(value = {"ticket", "userTickets"}, allEntries = true)
    public TicketResponse addUserComment(String ticketId, String comment, String userId) {
        log.info("Adding user comment to ticket: {} by user: {}", ticketId, userId);
        
        Ticket ticket = findTicketById(ticketId);
        validateUserAccess(ticket, userId);
        validateUserCanUpdate(ticket);
        
        ticket.addComment(TicketComment.createUserComment(userId, ticket.getUserName(), comment));
        ticket.setUpdatedAt(LocalDateTime.now());
        
        Ticket savedTicket = ticketRepository.save(ticket);
        
        // Publish event
        notificationEventService.publishTicketCommentAddedEvent(savedTicket, userId, comment);
        
        return TicketResponse.createUserResponse(ticketMapper.toResponse(savedTicket));
    }

    @Override
    @CacheEvict(value = {"ticket", "userTickets"}, allEntries = true)
    public TicketResponse requestTicketClosure(String ticketId, String reason, String userId) {
        log.info("User requesting ticket closure: {} by user: {}", ticketId, userId);
        
        Ticket ticket = findTicketById(ticketId);
        validateUserAccess(ticket, userId);
        
        if (ticket.getStatus().isFinal()) {
            throw InvalidTicketOperationException.cannotUpdate(ticketId, ticket.getStatus().name());
        }
        
        ticket.setStatus(TicketStatus.CANCELLED);
        ticket.addComment(TicketComment.createSystemComment(
            "Ticket closure requested by user. Reason: " + (reason != null ? reason : "No reason provided")
        ));
        ticket.setUpdatedAt(LocalDateTime.now());
        
        Ticket savedTicket = ticketRepository.save(ticket);
        
        // Publish event
        notificationEventService.publishTicketStatusChangedEvent(savedTicket, TicketStatus.CANCELLED, userId);
        
        return TicketResponse.createUserResponse(ticketMapper.toResponse(savedTicket));
    }

    // Helper methods
    
    private void validateCreateTicketRequest(CreateTicketRequest request) {
        if (!request.isReferenceIdValid()) {
            throw new RuntimeException("Reference ID is required for complaint tickets");
        }
        
        if (!request.isTypeSpecificDataValid()) {
            throw new RuntimeException("Type-specific data is required for ticket type: " + request.getType());
        }
    }
    
    private TicketMetadata createMetadataFromRequest(CreateTicketRequest request) {
        TicketMetadata metadata = new TicketMetadata();
        
        if (request.getMetadata() != null) {
            request.getMetadata().forEach(metadata::addCustomField);
        }
        
        // Add timestamp and source
        metadata.addSystemInfo("createdAt", LocalDateTime.now().toString());
        metadata.addSystemInfo("source", "api");
        
        return metadata;
    }
    
    private Ticket findTicketById(String ticketId) {
        return ticketRepository.findById(ticketId)
            .orElseThrow(() -> TicketNotFoundException.byId(ticketId));
    }

    private void validateUserAccess(Ticket ticket, String userId) {
        if (!ticket.getUserId().equals(userId)) {
            throw TicketAccessDeniedException.forUser(ticket.getId(), userId);
        }
    }

    private void validateUserCanUpdate(Ticket ticket) {
        if (!ticket.canBeUpdatedByUser()) {
            throw InvalidTicketOperationException.cannotUpdate(ticket.getId(), ticket.getStatus().name());
        }
    }
    
    private void updateContactInformation(Ticket ticket, UpdateTicketRequest.ContactUpdate contactUpdate) {
        if (contactUpdate.getEmail() != null && !contactUpdate.getEmail().trim().isEmpty()) {
            ticket.setUserEmail(contactUpdate.getEmail());
        }
        if (contactUpdate.getName() != null && !contactUpdate.getName().trim().isEmpty()) {
            ticket.setUserName(contactUpdate.getName());
        }
        // Phone can be stored in metadata
        if (contactUpdate.getPhone() != null && !contactUpdate.getPhone().trim().isEmpty()) {
            ticket.getMetadata().addCustomField("phone", contactUpdate.getPhone());
        }
    }

    @Override
    public String generateTicketNumber() {
        String datePrefix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        long counter = ticketCounter.getAndIncrement();
        return String.format("TKT-%s-%06d", datePrefix, counter);
    }

    @Override
    public boolean canUserAccessTicket(String ticketId, String userId) {
        try {
            Ticket ticket = findTicketById(ticketId);
            return ticket.getUserId().equals(userId);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean canUserUpdateTicket(String ticketId, String userId) {
        try {
            Ticket ticket = findTicketById(ticketId);
            return ticket.getUserId().equals(userId) && ticket.canBeUpdatedByUser();
        } catch (Exception e) {
            return false;
        }
    }

    // Admin operations implementation

    @Override
    @Cacheable(value = CacheConfig.TICKET_CACHE, key = "'admin_all_' + #statuses + '_' + #types + '_' + #priorities + '_' + #assignedTo + '_' + #pageable.pageNumber")
    public TicketListResponse getAllTickets(List<TicketStatus> statuses, List<TicketType> types,
            List<TicketPriority> priorities, String assignedTo, String userId,
            LocalDateTime createdAfter, LocalDateTime createdBefore, String searchQuery,
            List<String> tags, boolean showOverdueOnly, Pageable pageable) {

        log.debug("Getting all tickets with filters - statuses: {}, types: {}, priorities: {}", statuses, types, priorities);

        try {
            Page<Ticket> ticketPage;

            if (showOverdueOnly) {
                ticketPage = ticketRepository.findOverdueTickets(pageable);
            } else if (searchQuery != null && !searchQuery.trim().isEmpty()) {
                ticketPage = ticketRepository.findBySearchQuery(searchQuery.trim(), pageable);
            } else {
                // Use complex filtering
                ticketPage = ticketRepository.findWithAdminFilters(statuses, types, priorities, assignedTo, pageable);
            }

            return ticketMapper.toListResponse(ticketPage, false); // false for admin view
        } catch (Exception e) {
            log.error("Error getting all tickets with filters", e);
            return TicketListResponse.empty(pageable.getPageNumber(), pageable.getPageSize());
        }
    }

    @Override
    // @Cacheable(value = CacheConfig.TICKET_CACHE, key = "'admin_single_' + #ticketId") // Temporarily disabled due to Jackson JSR310 issue
    public TicketResponse getTicketByIdAdmin(String ticketId) {
        log.debug("Admin getting ticket: {}", ticketId);

        Ticket ticket = findTicketById(ticketId);
        return ticketMapper.toResponse(ticket); // Full response for admin
    }

    @Override
    @CacheEvict(value = {"ticket", "adminTickets", "userTickets"}, allEntries = true)
    public TicketResponse updateTicketAdmin(String ticketId, AdminUpdateTicketRequest request, String adminUserId) {
        log.info("Admin {} updating ticket: {}", adminUserId, ticketId);

        Ticket ticket = findTicketById(ticketId);
        TicketStatus oldStatus = ticket.getStatus();

        // Update status if provided
        if (request.getStatus() != null) {
            if (!oldStatus.canTransitionTo(request.getStatus())) {
                throw new RuntimeException("Invalid status transition from " + oldStatus + " to " + request.getStatus());
            }
            ticket.setStatus(request.getStatus());
        }

        // Update priority if provided
        if (request.getPriority() != null) {
            ticket.setPriority(request.getPriority());
        }

        // Handle assignment
        if (request.getAssignTo() != null && !request.getAssignTo().trim().isEmpty()) {
            ticket.setAssignedTo(request.getAssignTo());
            ticket.setAssignedBy(adminUserId);
            ticket.setAssignedAt(LocalDateTime.now());
        }

        // Add admin comment if provided
        if (request.getAdminComment() != null && !request.getAdminComment().trim().isEmpty()) {
            ticket.addComment(TicketComment.createAdminComment(
                adminUserId, "Admin", request.getAdminComment(), false));
        }

        // Add internal note if provided
        if (request.getInternalNote() != null && !request.getInternalNote().trim().isEmpty()) {
            ticket.addInternalNote(request.getInternalNote());
            ticket.addComment(TicketComment.createAdminComment(
                adminUserId, "Admin", request.getInternalNote(), true));
        }

        // Handle resolution
        if (request.getResolution() != null && !request.getResolution().trim().isEmpty()) {
            ticket.setResolution(request.getResolution());
            ticket.setResolvedBy(adminUserId);
            ticket.setResolvedAt(LocalDateTime.now());
            if (ticket.getStatus() != TicketStatus.RESOLVED) {
                ticket.setStatus(TicketStatus.RESOLVED);
            }
        }

        // Handle tags
        if (request.getAddTags() != null && !request.getAddTags().isEmpty()) {
            request.getAddTags().forEach(ticket::addTag);
        }
        if (request.getRemoveTags() != null && !request.getRemoveTags().isEmpty()) {
            request.getRemoveTags().forEach(tag -> ticket.getTags().remove(tag));
        }

        // Handle escalation
        if (request.getEscalation() != null) {
            ticket.setStatus(TicketStatus.ESCALATED);
            if (request.getEscalation().getNewPriority() != null) {
                ticket.setPriority(request.getEscalation().getNewPriority());
            }
            ticket.addComment(TicketComment.createSystemComment(
                "Ticket escalated. Reason: " + request.getEscalation().getReason()));
        }

        ticket.setUpdatedAt(LocalDateTime.now());
        ticket.setVersion(ticket.getVersion() + 1);

        Ticket savedTicket = ticketRepository.save(ticket);

        // Publish events
        if (request.getStatus() != null && !request.getStatus().equals(oldStatus)) {
            notificationEventService.publishTicketStatusChangedEvent(savedTicket, request.getStatus(), adminUserId);
        }
        if (request.getAssignTo() != null && !request.getAssignTo().trim().isEmpty()) {
            notificationEventService.publishTicketAssignedEvent(savedTicket, request.getAssignTo(), adminUserId);
        }

        log.info("Admin ticket update completed: {}", ticketId);
        return ticketMapper.toResponse(savedTicket);
    }

    @Override
    @CacheEvict(value = {CacheConfig.TICKET_CACHE, CacheConfig.TICKET_STATS_CACHE}, allEntries = true)
    public TicketResponse assignTicket(String ticketId, String assignToUserId, String adminUserId) {
        log.info("Admin {} assigning ticket: {} to: {}", adminUserId, ticketId, assignToUserId);

        Ticket ticket = findTicketById(ticketId);
        ticket.setAssignedTo(assignToUserId);
        ticket.setAssignedBy(adminUserId);
        ticket.setAssignedAt(LocalDateTime.now());
        ticket.setUpdatedAt(LocalDateTime.now());

        // Add system comment
        ticket.addComment(TicketComment.createSystemComment(
            "Ticket assigned to " + assignToUserId + " by " + adminUserId));

        Ticket savedTicket = ticketRepository.save(ticket);

        // Publish event
        notificationEventService.publishTicketAssignedEvent(savedTicket, assignToUserId, adminUserId);

        return ticketMapper.toResponse(savedTicket);
    }

    @Override
    @CacheEvict(value = {"ticket", "adminTickets", "userTickets"}, allEntries = true)
    public TicketResponse addAdminComment(String ticketId, String comment, boolean internal, String adminUserId) {
        log.info("Admin {} adding comment to ticket: {} (internal: {})", adminUserId, ticketId, internal);

        Ticket ticket = findTicketById(ticketId);
        ticket.addComment(TicketComment.createAdminComment(adminUserId, "Admin", comment, internal));
        ticket.setUpdatedAt(LocalDateTime.now());

        Ticket savedTicket = ticketRepository.save(ticket);

        // Publish event only for non-internal comments
        if (!internal) {
            notificationEventService.publishTicketCommentAddedEvent(savedTicket, adminUserId, comment);
        }

        return ticketMapper.toResponse(savedTicket);
    }

    @Override
    @CacheEvict(value = {CacheConfig.TICKET_CACHE, CacheConfig.TICKET_STATS_CACHE}, allEntries = true)
    public TicketResponse resolveTicket(String ticketId, String resolution, String adminUserId) {
        log.info("Admin {} resolving ticket: {}", adminUserId, ticketId);

        Ticket ticket = findTicketById(ticketId);

        if (!ticket.getStatus().canTransitionTo(TicketStatus.RESOLVED)) {
            throw new RuntimeException("Cannot resolve ticket in current status: " + ticket.getStatus());
        }

        ticket.setStatus(TicketStatus.RESOLVED);
        ticket.setResolution(resolution);
        ticket.setResolvedBy(adminUserId);
        ticket.setResolvedAt(LocalDateTime.now());
        ticket.setUpdatedAt(LocalDateTime.now());

        // Add resolution comment
        ticket.addComment(TicketComment.createAdminComment(
            adminUserId, "Admin", "Ticket resolved: " + resolution, false));

        Ticket savedTicket = ticketRepository.save(ticket);

        // Publish event
        notificationEventService.publishTicketResolvedEvent(savedTicket, adminUserId, resolution);

        return ticketMapper.toResponse(savedTicket);
    }

    @Override
    @CacheEvict(value = {CacheConfig.TICKET_CACHE, CacheConfig.TICKET_STATS_CACHE}, allEntries = true)
    public TicketResponse rejectTicket(String ticketId, String reason, String adminUserId) {
        log.info("Admin {} rejecting ticket: {}", adminUserId, ticketId);

        Ticket ticket = findTicketById(ticketId);

        if (!ticket.getStatus().canTransitionTo(TicketStatus.REJECTED)) {
            throw new RuntimeException("Cannot reject ticket in current status: " + ticket.getStatus());
        }

        ticket.setStatus(TicketStatus.REJECTED);
        ticket.setUpdatedAt(LocalDateTime.now());

        // Add rejection comment
        ticket.addComment(TicketComment.createAdminComment(
            adminUserId, "Admin", "Ticket rejected: " + reason, false));

        Ticket savedTicket = ticketRepository.save(ticket);

        // Publish event
        notificationEventService.publishTicketRejectedEvent(savedTicket, adminUserId, reason);

        return ticketMapper.toResponse(savedTicket);
    }

    @Override
    @CacheEvict(value = {"ticket", "adminTickets", "userTickets"}, allEntries = true)
    public TicketResponse escalateTicket(String ticketId, AdminUpdateTicketRequest.EscalationDetails escalation, String adminUserId) {
        log.info("Admin {} escalating ticket: {}", adminUserId, ticketId);

        Ticket ticket = findTicketById(ticketId);

        if (!ticket.getStatus().canTransitionTo(TicketStatus.ESCALATED)) {
            throw new RuntimeException("Cannot escalate ticket in current status: " + ticket.getStatus());
        }

        ticket.setStatus(TicketStatus.ESCALATED);
        if (escalation.getNewPriority() != null) {
            ticket.setPriority(escalation.getNewPriority());
        }
        if (escalation.getEscalatedTo() != null) {
            ticket.setAssignedTo(escalation.getEscalatedTo());
            ticket.setAssignedBy(adminUserId);
            ticket.setAssignedAt(LocalDateTime.now());
        }
        ticket.setUpdatedAt(LocalDateTime.now());

        // Add escalation comment
        ticket.addComment(TicketComment.createAdminComment(
            adminUserId, "Admin", "Ticket escalated: " + escalation.getReason(), false));

        Ticket savedTicket = ticketRepository.save(ticket);

        // Publish event
        notificationEventService.publishTicketEscalatedEvent(savedTicket, adminUserId, escalation.getReason());

        return ticketMapper.toResponse(savedTicket);
    }

    @Override
    @CacheEvict(value = {"ticket", "adminTickets", "userTickets"}, allEntries = true)
    public TicketResponse closeTicket(String ticketId, String reason, String adminUserId) {
        log.info("Admin {} closing ticket: {}", adminUserId, ticketId);

        Ticket ticket = findTicketById(ticketId);

        if (!ticket.getStatus().canTransitionTo(TicketStatus.CLOSED)) {
            throw new RuntimeException("Cannot close ticket in current status: " + ticket.getStatus());
        }

        ticket.setStatus(TicketStatus.CLOSED);
        ticket.setUpdatedAt(LocalDateTime.now());

        // Add closure comment
        ticket.addComment(TicketComment.createAdminComment(
            adminUserId, "Admin", "Ticket closed: " + reason, false));

        Ticket savedTicket = ticketRepository.save(ticket);

        // Publish event
        notificationEventService.publishTicketClosedEvent(savedTicket, adminUserId, reason);

        return ticketMapper.toResponse(savedTicket);
    }

    @Override
    @CacheEvict(value = {CacheConfig.TICKET_CACHE, CacheConfig.TICKET_STATS_CACHE}, allEntries = true)
    public TicketResponse reopenTicket(String ticketId, String reason, String adminUserId) {
        log.info("Admin {} reopening ticket: {}", adminUserId, ticketId);

        Ticket ticket = findTicketById(ticketId);

        if (ticket.getStatus().isFinal()) {
            ticket.setStatus(TicketStatus.IN_PROGRESS);
            ticket.setUpdatedAt(LocalDateTime.now());

            // Add reopen comment
            ticket.addComment(TicketComment.createAdminComment(
                adminUserId, "Admin", "Ticket reopened: " + reason, false));

            Ticket savedTicket = ticketRepository.save(ticket);

            // Publish event
            notificationEventService.publishTicketReopenedEvent(savedTicket, adminUserId, reason);

            return ticketMapper.toResponse(savedTicket);
        } else {
            throw new RuntimeException("Cannot reopen ticket in current status: " + ticket.getStatus());
        }
    }

    @Override
    @Cacheable(value = CacheConfig.TICKET_CACHE, key = "'assigned_' + #adminUserId + '_' + #pageable.pageNumber")
    public TicketListResponse getAssignedTickets(String adminUserId, Pageable pageable) {
        log.debug("Getting assigned tickets for admin: {}", adminUserId);

        Page<Ticket> ticketPage = ticketRepository.findByAssignedToAndDeletedFalseOrderByCreatedAtDesc(adminUserId, pageable);
        return ticketMapper.toListResponse(ticketPage, false);
    }

    @Override
    // @Cacheable(value = CacheConfig.TICKET_CACHE, key = "'unassigned_' + #pageable.pageNumber") // Temporarily disabled due to Jackson JSR310 issue
    public TicketListResponse getUnassignedTickets(Pageable pageable) {
        log.debug("Getting unassigned tickets");

        Page<Ticket> ticketPage = ticketRepository.findByAssignedToIsNullAndDeletedFalseOrderByPriorityDescCreatedAtAsc(pageable);
        return ticketMapper.toListResponse(ticketPage, false);
    }

    @Override
    // @Cacheable(value = CacheConfig.TICKET_CACHE, key = "'overdue_' + #pageable.pageNumber") // Temporarily disabled due to Jackson JSR310 issue
    public TicketListResponse getOverdueTickets(Pageable pageable) {
        log.debug("Getting overdue tickets");

        Page<Ticket> ticketPage = ticketRepository.findOverdueTickets(pageable);
        return ticketMapper.toListResponse(ticketPage, false);
    }

    @Override
    @Cacheable(value = "statistics", key = "'global'")
    public TicketListResponse.SummaryStats getTicketStatistics() {
        log.debug("Getting global ticket statistics");

        try {
            long totalTickets = ticketRepository.count();
            long openTickets = ticketRepository.countByStatusAndDeletedFalse(TicketStatus.RECEIVED) +
                              ticketRepository.countByStatusAndDeletedFalse(TicketStatus.IN_PROGRESS) +
                              ticketRepository.countByStatusAndDeletedFalse(TicketStatus.WAITING_FOR_USER) +
                              ticketRepository.countByStatusAndDeletedFalse(TicketStatus.ESCALATED);
            long resolvedTickets = ticketRepository.countByStatusAndDeletedFalse(TicketStatus.RESOLVED);
            long highPriorityTickets = ticketRepository.countByPriorityAndDeletedFalse(TicketPriority.HIGH) +
                                      ticketRepository.countByPriorityAndDeletedFalse(TicketPriority.URGENT);
            long unassignedTickets = ticketRepository.countByAssignedToIsNullAndDeletedFalse();

            return TicketListResponse.SummaryStats.builder()
                .totalTickets(totalTickets)
                .openTickets(openTickets)
                .resolvedTickets(resolvedTickets)
                .overdueTickets(0) // TODO: Implement overdue count
                .highPriorityTickets(highPriorityTickets)
                .unassignedTickets(unassignedTickets)
                .averageResolutionTimeHours(0.0) // TODO: Calculate average resolution time
                .build();
        } catch (Exception e) {
            log.error("Error getting ticket statistics", e);
            return TicketListResponse.SummaryStats.builder().build();
        }
    }

    @Override
    @Cacheable(value = CacheConfig.TICKET_STATS_CACHE, key = "'user_' + #userId")
    public TicketListResponse.SummaryStats getUserTicketStatistics(String userId) {
        log.debug("Getting ticket statistics for user: {}", userId);

        try {
            long totalTickets = ticketRepository.countByUserIdAndDeletedFalse(userId);
            // For user stats, we can use simpler counts
            return TicketListResponse.SummaryStats.builder()
                .totalTickets(totalTickets)
                .openTickets(0) // TODO: Implement user-specific counts
                .resolvedTickets(0)
                .overdueTickets(0)
                .highPriorityTickets(0)
                .unassignedTickets(0)
                .averageResolutionTimeHours(0.0)
                .build();
        } catch (Exception e) {
            log.error("Error getting user ticket statistics for: {}", userId, e);
            return TicketListResponse.SummaryStats.builder().build();
        }
    }

    @Override
    @Cacheable(value = "statistics", key = "'admin_' + #adminUserId")
    public TicketListResponse.SummaryStats getAdminStatistics(String adminUserId) {
        log.debug("Getting admin statistics for: {}", adminUserId);

        try {
            long assignedTickets = ticketRepository.countByAssignedToAndDeletedFalse(adminUserId);
            // For admin stats, focus on assigned tickets
            return TicketListResponse.SummaryStats.builder()
                .totalTickets(assignedTickets)
                .openTickets(0) // TODO: Implement admin-specific counts
                .resolvedTickets(0)
                .overdueTickets(0)
                .highPriorityTickets(0)
                .unassignedTickets(0)
                .averageResolutionTimeHours(0.0)
                .build();
        } catch (Exception e) {
            log.error("Error getting admin statistics for: {}", adminUserId, e);
            return TicketListResponse.SummaryStats.builder().build();
        }
    }

    @Override
    @Cacheable(value = CacheConfig.SEARCH_RESULT_CACHE, key = "#searchQuery + '_' + #pageable.pageNumber")
    public TicketListResponse searchTickets(String searchQuery, Pageable pageable) {
        log.debug("Searching tickets with query: {}", searchQuery);

        try {
            Page<Ticket> ticketPage = ticketRepository.findBySearchQuery(searchQuery, pageable);
            return ticketMapper.toListResponse(ticketPage, false);
        } catch (Exception e) {
            log.error("Error searching tickets with query: {}", searchQuery, e);
            return TicketListResponse.empty(pageable.getPageNumber(), pageable.getPageSize());
        }
    }

    @Override
    public List<TicketResponse> getTicketsByReferenceId(String referenceId) {
        log.debug("Getting tickets by reference ID: {}", referenceId);

        try {
            List<Ticket> tickets = ticketRepository.findByReferenceIdAndDeletedFalseOrderByCreatedAtDesc(referenceId);
            return ticketMapper.toResponses(tickets);
        } catch (Exception e) {
            log.error("Error getting tickets by reference ID: {}", referenceId, e);
            return List.of();
        }
    }

    @Override
    @Cacheable(value = CacheConfig.SEARCH_RESULT_CACHE, key = "'tags_' + #tags.toString() + '_' + #pageable.pageNumber")
    public TicketListResponse getTicketsByTags(List<String> tags, Pageable pageable) {
        log.debug("Getting tickets by tags: {}", tags);

        try {
            Page<Ticket> ticketPage = ticketRepository.findByTagsInAndDeletedFalseOrderByCreatedAtDesc(tags, pageable);
            return ticketMapper.toListResponse(ticketPage, false);
        } catch (Exception e) {
            log.error("Error getting tickets by tags: {}", tags, e);
            return TicketListResponse.empty(pageable.getPageNumber(), pageable.getPageSize());
        }
    }

    @Override
    @CacheEvict(value = {"ticket", "adminTickets", "userTickets"}, allEntries = true)
    public List<TicketResponse> bulkUpdateStatus(List<String> ticketIds, TicketStatus newStatus, String adminUserId) {
        log.info("Admin {} bulk updating status to {} for {} tickets", adminUserId, newStatus, ticketIds.size());

        List<TicketResponse> updatedTickets = new ArrayList<>();

        for (String ticketId : ticketIds) {
            try {
                Ticket ticket = findTicketById(ticketId);
                if (ticket.getStatus().canTransitionTo(newStatus)) {
                    ticket.setStatus(newStatus);
                    ticket.setUpdatedAt(LocalDateTime.now());
                    ticket.addComment(TicketComment.createSystemComment(
                        "Status updated to " + newStatus.getDisplayName() + " by " + adminUserId));

                    Ticket savedTicket = ticketRepository.save(ticket);
                    updatedTickets.add(ticketMapper.toResponse(savedTicket));

                    // Publish event
                    notificationEventService.publishTicketStatusChangedEvent(savedTicket, newStatus, adminUserId);
                }
            } catch (Exception e) {
                log.error("Error updating ticket {} in bulk operation", ticketId, e);
            }
        }

        return updatedTickets;
    }

    @Override
    @CacheEvict(value = {"ticket", "adminTickets", "userTickets"}, allEntries = true)
    public List<TicketResponse> bulkAssignTickets(List<String> ticketIds, String assignToUserId, String adminUserId) {
        log.info("Admin {} bulk assigning {} tickets to {}", adminUserId, ticketIds.size(), assignToUserId);

        List<TicketResponse> assignedTickets = new ArrayList<>();

        for (String ticketId : ticketIds) {
            try {
                Ticket ticket = findTicketById(ticketId);
                ticket.setAssignedTo(assignToUserId);
                ticket.setAssignedBy(adminUserId);
                ticket.setAssignedAt(LocalDateTime.now());
                ticket.setUpdatedAt(LocalDateTime.now());
                ticket.addComment(TicketComment.createSystemComment(
                    "Ticket assigned to " + assignToUserId + " by " + adminUserId));

                Ticket savedTicket = ticketRepository.save(ticket);
                assignedTickets.add(ticketMapper.toResponse(savedTicket));

                // Publish event
                notificationEventService.publishTicketAssignedEvent(savedTicket, assignToUserId, adminUserId);
            } catch (Exception e) {
                log.error("Error assigning ticket {} in bulk operation", ticketId, e);
            }
        }

        return assignedTickets;
    }

    @Override
    @CacheEvict(value = {CacheConfig.TICKET_CACHE, CacheConfig.TICKET_STATS_CACHE}, allEntries = true)
    public void deleteTicket(String ticketId, String adminUserId) {
        log.warn("Admin {} soft deleting ticket: {}", adminUserId, ticketId);

        Ticket ticket = findTicketById(ticketId);
        ticket.setDeleted(true);
        ticket.setUpdatedAt(LocalDateTime.now());
        ticket.addInternalNote("Ticket deleted by admin: " + adminUserId);

        ticketRepository.save(ticket);
    }

    @Override
    @CacheEvict(value = {CacheConfig.TICKET_CACHE, CacheConfig.TICKET_STATS_CACHE}, allEntries = true)
    public TicketResponse restoreTicket(String ticketId, String adminUserId) {
        log.info("Admin {} restoring deleted ticket: {}", adminUserId, ticketId);

        Ticket ticket = ticketRepository.findById(ticketId)
            .orElseThrow(() -> new RuntimeException("Ticket not found: " + ticketId));

        if (!ticket.isDeleted()) {
            throw new RuntimeException("Ticket is not deleted: " + ticketId);
        }

        ticket.setDeleted(false);
        ticket.setUpdatedAt(LocalDateTime.now());
        ticket.addInternalNote("Ticket restored by admin: " + adminUserId);

        Ticket savedTicket = ticketRepository.save(ticket);
        return ticketMapper.toResponse(savedTicket);
    }
}
