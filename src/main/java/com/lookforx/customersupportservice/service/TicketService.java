package com.lookforx.customersupportservice.service;

import com.lookforx.customersupportservice.dto.request.AdminUpdateTicketRequest;
import com.lookforx.customersupportservice.dto.request.CreateTicketRequest;
import com.lookforx.customersupportservice.dto.request.UpdateTicketRequest;
import com.lookforx.customersupportservice.dto.response.TicketListResponse;
import com.lookforx.customersupportservice.dto.response.TicketResponse;
import com.lookforx.customersupportservice.domain.enums.TicketPriority;
import com.lookforx.customersupportservice.domain.enums.TicketStatus;
import com.lookforx.customersupportservice.domain.enums.TicketType;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service interface for ticket operations
 */
public interface TicketService {

    // User operations

    /**
     * Create a new ticket
     */
    TicketResponse createTicket(CreateTicketRequest request, String userId);

    /**
     * Get user's tickets with pagination
     */
    TicketListResponse getUserTickets(String userId, Pageable pageable);

    /**
     * Get user's tickets filtered by status
     */
    TicketListResponse getUserTicketsByStatus(String userId, TicketStatus status, Pageable pageable);

    /**
     * Get user's tickets filtered by type
     */
    TicketListResponse getUserTicketsByType(String userId, TicketType type, Pageable pageable);

    /**
     * Get ticket by ID (user can only access their own tickets)
     */
    TicketResponse getTicketById(String ticketId, String userId);

    /**
     * Get ticket by ticket number (user can only access their own tickets)
     */
    TicketResponse getTicketByNumber(String ticketNumber, String userId);

    /**
     * Update ticket by user
     */
    TicketResponse updateTicket(String ticketId, UpdateTicketRequest request, String userId);

    /**
     * Add comment to ticket by user
     */
    TicketResponse addUserComment(String ticketId, String comment, String userId);

    /**
     * Request ticket closure by user
     */
    TicketResponse requestTicketClosure(String ticketId, String reason, String userId);

    // Admin operations

    /**
     * Get all tickets with filters and pagination (admin only)
     */
    TicketListResponse getAllTickets(
        List<TicketStatus> statuses,
        List<TicketType> types,
        List<TicketPriority> priorities,
        String assignedTo,
        String userId,
        LocalDateTime createdAfter,
        LocalDateTime createdBefore,
        String searchQuery,
        List<String> tags,
        boolean showOverdueOnly,
        Pageable pageable
    );

    /**
     * Get ticket by ID (admin can access any ticket)
     */
    TicketResponse getTicketByIdAdmin(String ticketId);

    /**
     * Update ticket by admin
     */
    TicketResponse updateTicketAdmin(String ticketId, AdminUpdateTicketRequest request, String adminUserId);

    /**
     * Assign ticket to admin user
     */
    TicketResponse assignTicket(String ticketId, String assignToUserId, String adminUserId);

    /**
     * Add admin comment to ticket
     */
    TicketResponse addAdminComment(String ticketId, String comment, boolean internal, String adminUserId);

    /**
     * Resolve ticket
     */
    TicketResponse resolveTicket(String ticketId, String resolution, String adminUserId);

    /**
     * Reject ticket
     */
    TicketResponse rejectTicket(String ticketId, String reason, String adminUserId);

    /**
     * Escalate ticket
     */
    TicketResponse escalateTicket(String ticketId, AdminUpdateTicketRequest.EscalationDetails escalation, String adminUserId);

    /**
     * Close ticket
     */
    TicketResponse closeTicket(String ticketId, String reason, String adminUserId);

    /**
     * Reopen ticket
     */
    TicketResponse reopenTicket(String ticketId, String reason, String adminUserId);

    /**
     * Get tickets assigned to specific admin
     */
    TicketListResponse getAssignedTickets(String adminUserId, Pageable pageable);

    /**
     * Get unassigned tickets
     */
    TicketListResponse getUnassignedTickets(Pageable pageable);

    /**
     * Get overdue tickets
     */
    TicketListResponse getOverdueTickets(Pageable pageable);

    // Statistics and reporting

    /**
     * Get ticket statistics
     */
    TicketListResponse.SummaryStats getTicketStatistics();

    /**
     * Get user ticket statistics
     */
    TicketListResponse.SummaryStats getUserTicketStatistics(String userId);

    /**
     * Get admin performance statistics
     */
    TicketListResponse.SummaryStats getAdminStatistics(String adminUserId);

    // Utility operations

    /**
     * Search tickets by text
     */
    TicketListResponse searchTickets(String searchQuery, Pageable pageable);

    /**
     * Get tickets by reference ID (for complaints)
     */
    List<TicketResponse> getTicketsByReferenceId(String referenceId);

    /**
     * Get tickets by tags
     */
    TicketListResponse getTicketsByTags(List<String> tags, Pageable pageable);

    /**
     * Bulk update ticket status
     */
    List<TicketResponse> bulkUpdateStatus(List<String> ticketIds, TicketStatus newStatus, String adminUserId);

    /**
     * Bulk assign tickets
     */
    List<TicketResponse> bulkAssignTickets(List<String> ticketIds, String assignToUserId, String adminUserId);

    /**
     * Delete ticket (soft delete)
     */
    void deleteTicket(String ticketId, String adminUserId);

    /**
     * Restore deleted ticket
     */
    TicketResponse restoreTicket(String ticketId, String adminUserId);

    /**
     * Generate ticket number
     */
    String generateTicketNumber();

    /**
     * Validate ticket access for user
     */
    boolean canUserAccessTicket(String ticketId, String userId);

    /**
     * Check if ticket can be updated by user
     */
    boolean canUserUpdateTicket(String ticketId, String userId);
}
