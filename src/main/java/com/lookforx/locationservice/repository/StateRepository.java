package com.lookforx.locationservice.repository;

import com.lookforx.locationservice.dto.StateDto;
import com.lookforx.locationservice.entity.State;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * State Repository
 * 
 * High-performance repository for state/province data with country-based queries.
 * Optimized for location hierarchy navigation.
 */
@Repository
public interface StateRepository extends JpaRepository<State, Long> {

    /**
     * Find all states by country ID (most common query)
     * Uses projection for minimal data transfer
     */
    @Query("SELECT new com.lookforx.locationservice.dto.StateDto(s.id, s.name, s.countryId) " +
           "FROM State s WHERE s.countryId = :countryId AND s.flag = 1 ORDER BY s.name ASC")
    List<StateDto> findByCountryIdMinimal(@Param("countryId") Long countryId);

    /**
     * Find all states by country ID with country info
     */
    @Query("SELECT new com.lookforx.locationservice.dto.StateDto(s.id, s.name, s.countryId, s.countryCode, c.name) " +
           "FROM State s JOIN Country c ON s.countryId = c.id " +
           "WHERE s.countryId = :countryId AND s.flag = 1 AND c.flag = 1 ORDER BY s.name ASC")
    List<StateDto> findByCountryIdWithCountryInfo(@Param("countryId") Long countryId);

    /**
     * Find states by country ID with pagination (for large countries like USA)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.StateDto(s.id, s.name, s.countryId, s.countryCode, c.name) " +
           "FROM State s JOIN Country c ON s.countryId = c.id " +
           "WHERE s.countryId = :countryId AND s.flag = 1 AND c.flag = 1")
    Page<StateDto> findByCountryIdPaginated(@Param("countryId") Long countryId, Pageable pageable);

    /**
     * Search states by name within a country
     */
    @Query("SELECT new com.lookforx.locationservice.dto.StateDto(s.id, s.name, s.countryId, s.countryCode, c.name) " +
           "FROM State s JOIN Country c ON s.countryId = c.id " +
           "WHERE s.countryId = :countryId AND LOWER(s.name) LIKE LOWER(CONCAT('%', :query, '%')) " +
           "AND s.flag = 1 AND c.flag = 1 " +
           "ORDER BY " +
           "CASE WHEN LOWER(s.name) LIKE LOWER(CONCAT(:query, '%')) THEN 1 " +
           "     WHEN LOWER(s.name) LIKE LOWER(CONCAT('%', :query, '%')) THEN 2 " +
           "     ELSE 3 END, s.name ASC")
    List<StateDto> searchStatesByNameInCountry(@Param("countryId") Long countryId, @Param("query") String query);

    /**
     * Global state search (across all countries)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.StateDto(s.id, s.name, s.countryId, s.countryCode, c.name) " +
           "FROM State s JOIN Country c ON s.countryId = c.id " +
           "WHERE LOWER(s.name) LIKE LOWER(CONCAT('%', :query, '%')) " +
           "AND s.flag = 1 AND c.flag = 1 " +
           "ORDER BY " +
           "CASE WHEN LOWER(s.name) LIKE LOWER(CONCAT(:query, '%')) THEN 1 " +
           "     WHEN LOWER(s.name) LIKE LOWER(CONCAT('%', :query, '%')) THEN 2 " +
           "     ELSE 3 END, c.name ASC, s.name ASC")
    List<StateDto> searchStatesGlobally(@Param("query") String query);

    /**
     * Find state by country code and state code
     */
    @Query("SELECT s FROM State s WHERE s.countryCode = :countryCode AND s.iso2 = :stateCode AND s.flag = 1")
    Optional<State> findByCountryCodeAndStateCode(@Param("countryCode") String countryCode, 
                                                  @Param("stateCode") String stateCode);

    /**
     * Check if state exists by ID (for validation)
     */
    @Query("SELECT COUNT(s) > 0 FROM State s WHERE s.id = :id AND s.flag = 1")
    boolean existsByIdAndActive(@Param("id") Long id);

    /**
     * Check if state exists in country (for validation)
     */
    @Query("SELECT COUNT(s) > 0 FROM State s WHERE s.id = :stateId AND s.countryId = :countryId AND s.flag = 1")
    boolean existsByIdAndCountryId(@Param("stateId") Long stateId, @Param("countryId") Long countryId);

    /**
     * Get states count by country
     */
    @Query("SELECT COUNT(s) FROM State s WHERE s.countryId = :countryId AND s.flag = 1")
    long countByCountryId(@Param("countryId") Long countryId);

    /**
     * Find state by ID with projection (to avoid entity loading issues)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.StateDto(s.id, s.name, s.countryId, " +
           "s.countryCode, s.iso2, s.type, s.latitude, s.longitude) " +
           "FROM State s WHERE s.id = :id AND s.flag = 1")
    Optional<StateDto> findByIdProjection(@Param("id") Long id);

    /**
     * Find popular states (major states/provinces)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.StateDto(s.id, s.name, s.countryId, s.countryCode, c.name) " +
           "FROM State s JOIN Country c ON s.countryId = c.id " +
           "WHERE (s.countryCode = 'US' AND s.iso2 IN ('CA', 'NY', 'TX', 'FL', 'IL')) " +
           "OR (s.countryCode = 'CA' AND s.iso2 IN ('ON', 'QC', 'BC', 'AB')) " +
           "OR (s.countryCode = 'AU' AND s.iso2 IN ('NSW', 'VIC', 'QLD', 'WA')) " +
           "AND s.flag = 1 AND c.flag = 1 ORDER BY c.name ASC, s.name ASC")
    List<StateDto> findPopularStates();
}
