package com.lookforx.locationservice.repository;

import com.lookforx.locationservice.dto.CityDto;
import com.lookforx.locationservice.entity.City;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * City Repository
 * 
 * High-performance repository for city data with complex location-based queries.
 * This is the most performance-critical repository as cities are queried most frequently.
 */
@Repository
public interface CityRepository extends JpaRepository<City, Long> {

    /**
     * Find cities by state ID (most common query)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CityDto(c.id, c.name, c.stateId, c.countryId) " +
           "FROM City c WHERE c.stateId = :stateId AND c.flag = 1 ORDER BY c.name ASC")
    List<CityDto> findByStateIdMinimal(@Param("stateId") Long stateId);

    /**
     * Find cities by state ID with full location hierarchy
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CityDto(c.id, c.name, c.stateId, s.name, " +
           "c.countryId, co.name, co.iso2) " +
           "FROM City c " +
           "JOIN State s ON c.stateId = s.id " +
           "JOIN Country co ON c.countryId = co.id " +
           "WHERE c.stateId = :stateId AND c.flag = 1 AND s.flag = 1 AND co.flag = 1 " +
           "ORDER BY c.name ASC")
    List<CityDto> findByStateIdWithHierarchy(@Param("stateId") Long stateId);

    /**
     * Find cities by country ID with pagination (for large countries)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CityDto(c.id, c.name, c.stateId, s.name, " +
           "c.countryId, co.name, co.iso2) " +
           "FROM City c " +
           "JOIN State s ON c.stateId = s.id " +
           "JOIN Country co ON c.countryId = co.id " +
           "WHERE c.countryId = :countryId AND c.flag = 1 AND s.flag = 1 AND co.flag = 1")
    Page<CityDto> findByCountryIdPaginated(@Param("countryId") Long countryId, Pageable pageable);

    /**
     * Search cities by name within a state
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CityDto(c.id, c.name, c.stateId, s.name, " +
           "c.countryId, co.name, co.iso2) " +
           "FROM City c " +
           "JOIN State s ON c.stateId = s.id " +
           "JOIN Country co ON c.countryId = co.id " +
           "WHERE c.stateId = :stateId AND LOWER(c.name) LIKE LOWER(CONCAT('%', :query, '%')) " +
           "AND c.flag = 1 AND s.flag = 1 AND co.flag = 1 " +
           "ORDER BY " +
           "CASE WHEN LOWER(c.name) LIKE LOWER(CONCAT(:query, '%')) THEN 1 " +
           "     WHEN LOWER(c.name) LIKE LOWER(CONCAT('%', :query, '%')) THEN 2 " +
           "     ELSE 3 END, c.name ASC")
    List<CityDto> searchCitiesByNameInState(@Param("stateId") Long stateId, @Param("query") String query);

    /**
     * Search cities by name within a country
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CityDto(c.id, c.name, c.stateId, s.name, " +
           "c.countryId, co.name, co.iso2) " +
           "FROM City c " +
           "JOIN State s ON c.stateId = s.id " +
           "JOIN Country co ON c.countryId = co.id " +
           "WHERE c.countryId = :countryId AND LOWER(c.name) LIKE LOWER(CONCAT('%', :query, '%')) " +
           "AND c.flag = 1 AND s.flag = 1 AND co.flag = 1 " +
           "ORDER BY " +
           "CASE WHEN LOWER(c.name) LIKE LOWER(CONCAT(:query, '%')) THEN 1 " +
           "     WHEN LOWER(c.name) LIKE LOWER(CONCAT('%', :query, '%')) THEN 2 " +
           "     ELSE 3 END, s.name ASC, c.name ASC")
    Page<CityDto> searchCitiesByNameInCountry(@Param("countryId") Long countryId, 
                                              @Param("query") String query, Pageable pageable);

    /**
     * Global city search (across all countries) with pagination
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CityDto(c.id, c.name, c.stateId, s.name, " +
           "c.countryId, co.name, co.iso2) " +
           "FROM City c " +
           "JOIN State s ON c.stateId = s.id " +
           "JOIN Country co ON c.countryId = co.id " +
           "WHERE LOWER(c.name) LIKE LOWER(CONCAT('%', :query, '%')) " +
           "AND c.flag = 1 AND s.flag = 1 AND co.flag = 1 " +
           "ORDER BY " +
           "CASE WHEN LOWER(c.name) LIKE LOWER(CONCAT(:query, '%')) THEN 1 " +
           "     WHEN LOWER(c.name) LIKE LOWER(CONCAT('%', :query, '%')) THEN 2 " +
           "     ELSE 3 END, co.name ASC, s.name ASC, c.name ASC")
    Page<CityDto> searchCitiesGlobally(@Param("query") String query, Pageable pageable);

    /**
     * Find cities near coordinates (for location-based services)
     */
    @Query(value = "SELECT c.id, c.name, c.state_id, s.name as state_name, " +
                   "c.country_id, co.name as country_name, co.iso2, " +
                   "c.latitude, c.longitude, " +
                   "(6371 * acos(cos(radians(:latitude)) * cos(radians(c.latitude)) * " +
                   "cos(radians(c.longitude) - radians(:longitude)) + " +
                   "sin(radians(:latitude)) * sin(radians(c.latitude)))) AS distance " +
                   "FROM cities c " +
                   "JOIN states s ON c.state_id = s.id " +
                   "JOIN countries co ON c.country_id = co.id " +
                   "WHERE c.flag = 1 AND s.flag = 1 AND co.flag = 1 " +
                   "HAVING distance < :radiusKm " +
                   "ORDER BY distance ASC " +
                   "LIMIT :limit", nativeQuery = true)
    List<Object[]> findCitiesNearCoordinates(@Param("latitude") BigDecimal latitude,
                                           @Param("longitude") BigDecimal longitude,
                                           @Param("radiusKm") Double radiusKm,
                                           @Param("limit") Integer limit);

    /**
     * Check if city exists by ID (for validation)
     */
    @Query("SELECT COUNT(c) > 0 FROM City c WHERE c.id = :id AND c.flag = 1")
    boolean existsByIdAndActive(@Param("id") Long id);

    /**
     * Check if city exists in state and country (for validation)
     */
    @Query("SELECT COUNT(c) > 0 FROM City c WHERE c.id = :cityId AND c.stateId = :stateId " +
           "AND c.countryId = :countryId AND c.flag = 1")
    boolean existsByIdAndStateIdAndCountryId(@Param("cityId") Long cityId, 
                                           @Param("stateId") Long stateId, 
                                           @Param("countryId") Long countryId);

    /**
     * Get cities count by state
     */
    @Query("SELECT COUNT(c) FROM City c WHERE c.stateId = :stateId AND c.flag = 1")
    long countByStateId(@Param("stateId") Long stateId);

    /**
     * Find city by ID with projection (to avoid entity loading issues)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CityDto(c.id, c.name, c.stateId, " +
           "c.stateCode, c.countryId, c.countryCode, c.latitude, c.longitude) " +
           "FROM City c WHERE c.id = :id AND c.flag = 1")
    Optional<CityDto> findByIdProjection(@Param("id") Long id);

    /**
     * Get cities count by country
     */
    @Query("SELECT COUNT(c) FROM City c WHERE c.countryId = :countryId AND c.flag = 1")
    long countByCountryId(@Param("countryId") Long countryId);

    /**
     * Find major cities (capitals and large cities)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CityDto(c.id, c.name, c.stateId, s.name, " +
           "c.countryId, co.name, co.iso2, c.latitude, c.longitude) " +
           "FROM City c " +
           "JOIN State s ON c.stateId = s.id " +
           "JOIN Country co ON c.countryId = co.id " +
           "WHERE LOWER(c.name) IN ('istanbul', 'ankara', 'izmir', 'bursa', 'antalya', 'adana', " +
           "'new york', 'los angeles', 'chicago', 'houston', 'phoenix', 'philadelphia', " +
           "'london', 'manchester', 'birmingham', 'glasgow', 'liverpool', " +
           "'paris', 'marseille', 'lyon', 'toulouse', 'nice', " +
           "'berlin', 'hamburg', 'munich', 'cologne', 'frankfurt', " +
           "'tokyo', 'osaka', 'yokohama', 'nagoya', 'sapporo', " +
           "'sydney', 'melbourne', 'brisbane', 'perth', 'adelaide') " +
           "AND c.flag = 1 AND s.flag = 1 AND co.flag = 1 " +
           "ORDER BY co.name ASC, c.name ASC")
    List<CityDto> findMajorCities();
}
