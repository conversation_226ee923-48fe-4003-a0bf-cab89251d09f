package com.lookforx.locationservice.repository;

import com.lookforx.locationservice.dto.CountryDto;
import com.lookforx.locationservice.entity.Country;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Country Repository
 * 
 * High-performance repository with optimized queries for country data.
 * Uses projection queries to minimize data transfer and improve performance.
 */
@Repository
public interface CountryRepository extends JpaRepository<Country, Long> {

    /**
     * Find all active countries with minimal data for dropdown/selection
     * Uses projection to fetch only necessary fields
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CountryDto(c.id, c.name, c.iso2, c.emoji) " +
           "FROM Country c WHERE c.flag = 1 ORDER BY c.name ASC")
    List<CountryDto> findAllActiveCountriesMinimal();

    /**
     * Find all active countries with phone codes for registration forms
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CountryDto(c.id, c.name, c.iso2, c.phonecode, c.emoji) " +
           "FROM Country c WHERE c.flag = 1 ORDER BY c.name ASC")
    List<CountryDto> findAllActiveCountriesWithPhonecode();

    /**
     * Find country by ISO2 code (most common lookup)
     */
    @Query("SELECT c FROM Country c WHERE c.iso2 = :iso2 AND c.flag = 1")
    Optional<Country> findByIso2(@Param("iso2") String iso2);

    /**
     * Find country by ISO2 code with projection (to avoid entity loading issues)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CountryDto(c.id, c.name, c.iso2, c.iso3, " +
           "c.phonecode, c.capital, c.currency, c.currencySymbol, c.region, c.subregion, " +
           "c.nationality, c.latitude, c.longitude, c.emoji) " +
           "FROM Country c WHERE c.iso2 = :iso2 AND c.flag = 1")
    Optional<CountryDto> findByIso2Projection(@Param("iso2") String iso2);

    /**
     * Find country by ISO3 code
     */
    @Query("SELECT c FROM Country c WHERE c.iso3 = :iso3 AND c.flag = 1")
    Optional<Country> findByIso3(@Param("iso3") String iso3);

    /**
     * Find country by ISO3 code with projection (to avoid entity loading issues)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CountryDto(c.id, c.name, c.iso2, c.iso3, " +
           "c.phonecode, c.capital, c.currency, c.currencySymbol, c.region, c.subregion, " +
           "c.nationality, c.latitude, c.longitude, c.emoji) " +
           "FROM Country c WHERE c.iso3 = :iso3 AND c.flag = 1")
    Optional<CountryDto> findByIso3Projection(@Param("iso3") String iso3);

    /**
     * Search countries by name (case-insensitive)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CountryDto(c.id, c.name, c.iso2, c.emoji) " +
           "FROM Country c WHERE LOWER(c.name) LIKE LOWER(CONCAT('%', :query, '%')) AND c.flag = 1 " +
           "ORDER BY " +
           "CASE WHEN LOWER(c.name) LIKE LOWER(CONCAT(:query, '%')) THEN 1 " +
           "     WHEN LOWER(c.name) LIKE LOWER(CONCAT('%', :query, '%')) THEN 2 " +
           "     ELSE 3 END, c.name ASC")
    List<CountryDto> searchCountriesByName(@Param("query") String query);

    /**
     * Find countries by region
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CountryDto(c.id, c.name, c.iso2, c.emoji) " +
           "FROM Country c WHERE c.region = :region AND c.flag = 1 ORDER BY c.name ASC")
    List<CountryDto> findByRegion(@Param("region") String region);

    /**
     * Check if country exists by ID (for validation)
     */
    @Query("SELECT COUNT(c) > 0 FROM Country c WHERE c.id = :id AND c.flag = 1")
    boolean existsByIdAndActive(@Param("id") Long id);

    /**
     * Get popular countries (by usage or predefined list)
     * This could be based on user activity or manually curated
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CountryDto(c.id, c.name, c.iso2, c.emoji) " +
           "FROM Country c WHERE c.iso2 IN ('US', 'GB', 'CA', 'AU', 'DE', 'FR', 'TR', 'JP', 'CN', 'IN') " +
           "AND c.flag = 1 ORDER BY c.name ASC")
    List<CountryDto> findPopularCountries();

    /**
     * Find all active countries (for caching)
     */
    @Query("SELECT c FROM Country c WHERE c.flag = 1 ORDER BY c.name ASC")
    List<Country> findAllActive();

    /**
     * Debug: Find all countries without flag filter
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CountryDto(c.id, c.name, c.iso2, c.emoji) " +
           "FROM Country c ORDER BY c.name ASC")
    List<CountryDto> findAllCountriesDebug();

    /**
     * Debug: Count all countries
     */
    @Query("SELECT COUNT(c) FROM Country c")
    long countAllCountries();

    /**
     * Debug: Count countries by flag value
     */
    @Query("SELECT c.flag, COUNT(c) FROM Country c GROUP BY c.flag")
    List<Object[]> countByFlag();

    /**
     * Find country by ID with projection (to avoid entity loading issues)
     */
    @Query("SELECT new com.lookforx.locationservice.dto.CountryDto(c.id, c.name, c.iso2, c.iso3, " +
           "c.phonecode, c.capital, c.currency, c.currencySymbol, c.region, c.subregion, " +
           "c.nationality, c.latitude, c.longitude, c.emoji) " +
           "FROM Country c WHERE c.id = :id AND c.flag = 1")
    Optional<CountryDto> findByIdProjection(@Param("id") Long id);
}
