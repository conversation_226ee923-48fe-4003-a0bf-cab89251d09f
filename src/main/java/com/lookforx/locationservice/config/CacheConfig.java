package com.lookforx.locationservice.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Cache Configuration
 *
 * Simple cache configuration for location data.
 * Redis configuration disabled for now.
 */
@Configuration
@EnableCaching
public class CacheConfig {

    // Redis cache configuration with simplified serialization
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1)) // Default TTL
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new JdkSerializationRedisSerializer()))
                .disableCachingNullValues();

        // Custom cache configurations with different TTLs
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

        // Country caches - 24 hours (countries rarely change)
        cacheConfigurations.put("countries", defaultConfig.entryTtl(Duration.ofHours(24)));
        cacheConfigurations.put("country", defaultConfig.entryTtl(Duration.ofHours(24)));
        cacheConfigurations.put("countries_by_region", defaultConfig.entryTtl(Duration.ofHours(24)));

        // State caches - 12 hours
        cacheConfigurations.put("states", defaultConfig.entryTtl(Duration.ofHours(12)));
        cacheConfigurations.put("states_with_info", defaultConfig.entryTtl(Duration.ofHours(12)));
        cacheConfigurations.put("state", defaultConfig.entryTtl(Duration.ofHours(12)));
        cacheConfigurations.put("state_count", defaultConfig.entryTtl(Duration.ofHours(12)));

        // City caches - 6 hours
        cacheConfigurations.put("cities", defaultConfig.entryTtl(Duration.ofHours(6)));
        cacheConfigurations.put("cities_with_hierarchy", defaultConfig.entryTtl(Duration.ofHours(6)));
        cacheConfigurations.put("city", defaultConfig.entryTtl(Duration.ofHours(6)));
        cacheConfigurations.put("city_count", defaultConfig.entryTtl(Duration.ofHours(6)));
        cacheConfigurations.put("cities_near", defaultConfig.entryTtl(Duration.ofHours(6)));

        // Search caches - 1 hour (dynamic content)
        cacheConfigurations.put("country_search", defaultConfig.entryTtl(Duration.ofHours(1)));
        cacheConfigurations.put("state_search", defaultConfig.entryTtl(Duration.ofHours(1)));
        cacheConfigurations.put("state_search_global", defaultConfig.entryTtl(Duration.ofHours(1)));
        cacheConfigurations.put("city_search", defaultConfig.entryTtl(Duration.ofHours(1)));
        cacheConfigurations.put("location_search", defaultConfig.entryTtl(Duration.ofHours(1)));

        // Validation caches - 30 minutes (frequently accessed, need to be fresh)
        cacheConfigurations.put("validation", defaultConfig.entryTtl(Duration.ofMinutes(30)));

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
