package com.lookforx.locationservice.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Location Search DTO
 * 
 * Unified search result DTO that can represent any location type
 * (country, state, or city) in search results.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LocationSearchDto {

    private Long id;
    private String name;
    private String type; // "country", "state", "city"
    private String fullName; // e.g., "Istanbul, Turkey" or "California, USA"
    
    // Country info
    private Long countryId;
    private String countryName;
    private String countryCode;
    private String emoji;
    
    // State info (if applicable)
    private Long stateId;
    private String stateName;
    private String stateCode;
    
    // Coordinates
    private BigDecimal latitude;
    private BigDecimal longitude;
    
    // Search relevance score
    private Double relevanceScore;
    
    // Constructor for country search results
    public LocationSearchDto(Long id, String name, String countryCode, String emoji) {
        this.id = id;
        this.name = name;
        this.type = "country";
        this.fullName = name;
        this.countryId = id;
        this.countryName = name;
        this.countryCode = countryCode;
        this.emoji = emoji;
    }
    
    // Constructor for state search results
    public LocationSearchDto(Long id, String name, Long countryId, String countryName, 
                           String countryCode, String emoji) {
        this.id = id;
        this.name = name;
        this.type = "state";
        this.fullName = name + ", " + countryName;
        this.countryId = countryId;
        this.countryName = countryName;
        this.countryCode = countryCode;
        this.emoji = emoji;
        this.stateId = id;
        this.stateName = name;
    }
    
    // Constructor for city search results
    public LocationSearchDto(Long id, String name, Long stateId, String stateName,
                           Long countryId, String countryName, String countryCode, String emoji) {
        this.id = id;
        this.name = name;
        this.type = "city";
        this.fullName = name + ", " + stateName + ", " + countryName;
        this.countryId = countryId;
        this.countryName = countryName;
        this.countryCode = countryCode;
        this.emoji = emoji;
        this.stateId = stateId;
        this.stateName = stateName;
    }
}
