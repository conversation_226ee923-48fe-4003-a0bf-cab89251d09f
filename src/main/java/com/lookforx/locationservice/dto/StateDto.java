package com.lookforx.locationservice.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

import java.math.BigDecimal;

/**
 * State DTO
 * 
 * Lightweight representation of state/province data for API responses.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StateDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String name;
    private Long countryId;
    private String countryCode;
    private String countryName;
    private String iso2;
    private String type;
    private BigDecimal latitude;
    private BigDecimal longitude;
    
    // Minimal constructor for state selection
    public StateDto(Long id, String name, Long countryId) {
        this.id = id;
        this.name = name;
        this.countryId = countryId;
    }
    
    // Constructor with country info
    public StateDto(Long id, String name, Long countryId, String countryCode, String countryName) {
        this.id = id;
        this.name = name;
        this.countryId = countryId;
        this.countryCode = countryCode;
        this.countryName = countryName;
    }

    // Constructor for full state details (projection query)
    public StateDto(Long id, String name, Long countryId, String countryCode, String iso2,
                   String type, BigDecimal latitude, BigDecimal longitude) {
        this.id = id;
        this.name = name;
        this.countryId = countryId;
        this.countryCode = countryCode;
        this.iso2 = iso2;
        this.type = type;
        this.latitude = latitude;
        this.longitude = longitude;
    }
}
