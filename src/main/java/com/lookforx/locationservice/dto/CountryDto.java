package com.lookforx.locationservice.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Country DTO
 * 
 * Lightweight representation of country data for API responses.
 * Optimized for minimal data transfer and fast serialization.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CountryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String name;
    private String iso2;
    private String iso3;
    private String phonecode;
    private String capital;
    private String currency;
    private String currencySymbol;
    private String region;
    private String subregion;
    private String nationality;
    private BigDecimal latitude;
    private BigDecimal longitude;
    private String emoji;
    private String emojiUnicodeValue;
    
    // Minimal constructor for basic country info
    public CountryDto(Long id, String name, String iso2, String emoji) {
        this.id = id;
        this.name = name;
        this.iso2 = iso2;
        this.emoji = emoji;
    }
    
    // Constructor for country selection dropdown
    public CountryDto(Long id, String name, String iso2, String phonecode, String emoji) {
        this.id = id;
        this.name = name;
        this.iso2 = iso2;
        this.phonecode = phonecode;
        this.emoji = emoji;
    }

    // Constructor for full country details (projection query)
    public CountryDto(Long id, String name, String iso2, String iso3, String phonecode,
                     String capital, String currency, String currencySymbol, String region,
                     String subregion, String nationality, BigDecimal latitude,
                     BigDecimal longitude, String emoji) {
        this.id = id;
        this.name = name;
        this.iso2 = iso2;
        this.iso3 = iso3;
        this.phonecode = phonecode;
        this.capital = capital;
        this.currency = currency;
        this.currencySymbol = currencySymbol;
        this.region = region;
        this.subregion = subregion;
        this.nationality = nationality;
        this.latitude = latitude;
        this.longitude = longitude;
        this.emoji = emoji;
    }
}
