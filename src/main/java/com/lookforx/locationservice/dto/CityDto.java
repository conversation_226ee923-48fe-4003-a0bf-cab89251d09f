package com.lookforx.locationservice.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

import java.math.BigDecimal;

/**
 * City DTO
 * 
 * Lightweight representation of city data for API responses.
 * Most frequently used DTO, optimized for performance.
 */
@Data
@Builder
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CityDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    private String name;
    private Long stateId;
    private String stateName;
    private String stateCode;
    private Long countryId;
    private String countryName;
    private String countryCode;
    private BigDecimal latitude;
    private BigDecimal longitude;

    // Minimal constructor for city selection
    public CityDto(Long id, String name, Long stateId, Long countryId) {
        this.id = id;
        this.name = name;
        this.stateId = stateId;
        this.countryId = countryId;
    }

    // Constructor with location hierarchy
    public CityDto(Long id, String name, Long stateId, String stateName,
                   Long countryId, String countryName, String countryCode) {
        this.id = id;
        this.name = name;
        this.stateId = stateId;
        this.stateName = stateName;
        this.countryId = countryId;
        this.countryName = countryName;
        this.countryCode = countryCode;
    }

    // Constructor for major cities query (9 parameters)
    public CityDto(Long id, String name, Long stateId, String stateName,
                   Long countryId, String countryName, String countryCode,
                   BigDecimal latitude, BigDecimal longitude) {
        this.id = id;
        this.name = name;
        this.stateId = stateId;
        this.stateName = stateName;
        this.countryId = countryId;
        this.countryName = countryName;
        this.countryCode = countryCode;
        this.latitude = latitude;
        this.longitude = longitude;
    }

    // Full constructor with coordinates (10 parameters)
    public CityDto(Long id, String name, Long stateId, String stateName, String stateCode,
                   Long countryId, String countryName, String countryCode,
                   BigDecimal latitude, BigDecimal longitude) {
        this.id = id;
        this.name = name;
        this.stateId = stateId;
        this.stateName = stateName;
        this.stateCode = stateCode;
        this.countryId = countryId;
        this.countryName = countryName;
        this.countryCode = countryCode;
        this.latitude = latitude;
        this.longitude = longitude;
    }

    // Constructor for city details (projection query - 8 parameters)
    public CityDto(Long id, String name, Long stateId, String stateCode,
                   Long countryId, String countryCode, BigDecimal latitude, BigDecimal longitude) {
        this.id = id;
        this.name = name;
        this.stateId = stateId;
        this.stateCode = stateCode;
        this.countryId = countryId;
        this.countryCode = countryCode;
        this.latitude = latitude;
        this.longitude = longitude;
    }
}
