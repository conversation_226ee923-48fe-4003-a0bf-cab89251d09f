package com.lookforx.locationservice.controller;

import com.lookforx.locationservice.dto.CityDto;
import com.lookforx.locationservice.dto.CountryDto;
import com.lookforx.locationservice.dto.LocationSearchDto;
import com.lookforx.locationservice.dto.StateDto;
import com.lookforx.locationservice.service.LocationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * Location Controller
 * 
 * High-performance REST API for geographical location data.
 * Provides comprehensive location hierarchy management with caching.
 */
@RestController
@RequestMapping("/api/v1/locations")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Location Management", description = "APIs for managing geographical location data")
public class LocationController {

    private final LocationService locationService;

    // Country endpoints
    @GetMapping("/countries")
    @Operation(summary = "Get all countries", description = "Retrieve all active countries with minimal data")
    public ResponseEntity<List<CountryDto>> getAllCountries(
            @Parameter(description = "Include phone codes") @RequestParam(defaultValue = "false") boolean includePhonecode) {
        
        List<CountryDto> countries = includePhonecode 
                ? locationService.getAllCountriesWithPhonecode()
                : locationService.getAllCountries();
        
        return ResponseEntity.ok(countries);
    }

    @GetMapping("/countries/popular")
    @Operation(summary = "Get popular countries", description = "Retrieve most commonly used countries")
    public ResponseEntity<List<CountryDto>> getPopularCountries() {
        return ResponseEntity.ok(locationService.getPopularCountries());
    }

    @GetMapping("/countries/{id}")
    @Operation(summary = "Get country by ID", description = "Retrieve detailed country information by ID")
    public ResponseEntity<CountryDto> getCountryById(@PathVariable Long id) {
        return locationService.getCountryById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/countries/iso2/{iso2}")
    @Operation(summary = "Get country by ISO2 code", description = "Retrieve country information by ISO2 code")
    public ResponseEntity<CountryDto> getCountryByIso2(@PathVariable String iso2) {
        return locationService.getCountryByIso2(iso2.toUpperCase())
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/countries/search")
    @Operation(summary = "Search countries", description = "Search countries by name")
    public ResponseEntity<List<CountryDto>> searchCountries(
            @Parameter(description = "Search query") @RequestParam String q) {
        return ResponseEntity.ok(locationService.searchCountries(q));
    }

    @GetMapping("/countries/region/{region}")
    @Operation(summary = "Get countries by region", description = "Retrieve countries in a specific region")
    public ResponseEntity<List<CountryDto>> getCountriesByRegion(@PathVariable String region) {
        return ResponseEntity.ok(locationService.getCountriesByRegion(region));
    }

    // State endpoints
    @GetMapping("/countries/{countryId}/states")
    @Operation(summary = "Get states by country", description = "Retrieve all states/provinces in a country")
    public ResponseEntity<List<StateDto>> getStatesByCountry(
            @PathVariable Long countryId,
            @Parameter(description = "Include country information") @RequestParam(defaultValue = "false") boolean includeCountryInfo) {
        
        List<StateDto> states = includeCountryInfo
                ? locationService.getStatesByCountryIdWithInfo(countryId)
                : locationService.getStatesByCountryId(countryId);
        
        return ResponseEntity.ok(states);
    }

    @GetMapping("/countries/{countryId}/states/paginated")
    @Operation(summary = "Get states by country with pagination", description = "Retrieve states with pagination support")
    public ResponseEntity<Page<StateDto>> getStatesByCountryPaginated(
            @PathVariable Long countryId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "name") String sort,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "asc") String direction) {
        
        Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
        
        return ResponseEntity.ok(locationService.getStatesByCountryIdPaginated(countryId, pageable));
    }

    @GetMapping("/states/{id}")
    @Operation(summary = "Get state by ID", description = "Retrieve detailed state information by ID")
    public ResponseEntity<StateDto> getStateById(@PathVariable Long id) {
        return locationService.getStateById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/countries/{countryId}/states/search")
    @Operation(summary = "Search states in country", description = "Search states within a specific country")
    public ResponseEntity<List<StateDto>> searchStatesInCountry(
            @PathVariable Long countryId,
            @Parameter(description = "Search query") @RequestParam String q) {
        return ResponseEntity.ok(locationService.searchStatesInCountry(countryId, q));
    }

    @GetMapping("/states/search")
    @Operation(summary = "Search states globally", description = "Search states across all countries")
    public ResponseEntity<List<StateDto>> searchStatesGlobally(
            @Parameter(description = "Search query") @RequestParam String q) {
        return ResponseEntity.ok(locationService.searchStatesGlobally(q));
    }

    @GetMapping("/states/popular")
    @Operation(summary = "Get popular states", description = "Retrieve most commonly used states/provinces")
    public ResponseEntity<List<StateDto>> getPopularStates() {
        return ResponseEntity.ok(locationService.getPopularStates());
    }

    // City endpoints
    @GetMapping("/states/{stateId}/cities")
    @Operation(summary = "Get cities by state", description = "Retrieve all cities in a state")
    public ResponseEntity<List<CityDto>> getCitiesByState(
            @PathVariable Long stateId,
            @Parameter(description = "Include location hierarchy") @RequestParam(defaultValue = "false") boolean includeHierarchy) {
        
        List<CityDto> cities = includeHierarchy
                ? locationService.getCitiesByStateIdWithHierarchy(stateId)
                : locationService.getCitiesByStateId(stateId);
        
        return ResponseEntity.ok(cities);
    }

    @GetMapping("/countries/{countryId}/cities")
    @Operation(summary = "Get cities by country with pagination", description = "Retrieve cities in a country with pagination")
    public ResponseEntity<Page<CityDto>> getCitiesByCountryPaginated(
            @PathVariable Long countryId,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "50") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "name") String sort,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "asc") String direction) {
        
        Sort.Direction sortDirection = "desc".equalsIgnoreCase(direction) ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
        
        return ResponseEntity.ok(locationService.getCitiesByCountryIdPaginated(countryId, pageable));
    }

    @GetMapping("/cities/{id}")
    @Operation(summary = "Get city by ID", description = "Retrieve detailed city information by ID")
    public ResponseEntity<CityDto> getCityById(@PathVariable Long id) {
        return locationService.getCityById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/states/{stateId}/cities/search")
    @Operation(summary = "Search cities in state", description = "Search cities within a specific state")
    public ResponseEntity<List<CityDto>> searchCitiesInState(
            @PathVariable Long stateId,
            @Parameter(description = "Search query") @RequestParam String q) {
        return ResponseEntity.ok(locationService.searchCitiesInState(stateId, q));
    }

    @GetMapping("/countries/{countryId}/cities/search")
    @Operation(summary = "Search cities in country", description = "Search cities within a specific country")
    public ResponseEntity<Page<CityDto>> searchCitiesInCountry(
            @PathVariable Long countryId,
            @Parameter(description = "Search query") @RequestParam String q,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "50") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("name"));
        return ResponseEntity.ok(locationService.searchCitiesInCountry(countryId, q, pageable));
    }

    @GetMapping("/cities/search")
    @Operation(summary = "Search cities globally", description = "Search cities across all countries")
    public ResponseEntity<Page<CityDto>> searchCitiesGlobally(
            @Parameter(description = "Search query") @RequestParam String q,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "50") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("name"));
        return ResponseEntity.ok(locationService.searchCitiesGlobally(q, pageable));
    }

    @GetMapping("/cities/near")
    @Operation(summary = "Get cities near location", description = "Find cities near specified coordinates")
    public ResponseEntity<List<CityDto>> getCitiesNearLocation(
            @Parameter(description = "Latitude") @RequestParam BigDecimal latitude,
            @Parameter(description = "Longitude") @RequestParam BigDecimal longitude,
            @Parameter(description = "Radius in kilometers") @RequestParam(defaultValue = "50.0") Double radiusKm,
            @Parameter(description = "Maximum number of results") @RequestParam(defaultValue = "20") Integer limit) {
        
        return ResponseEntity.ok(locationService.getCitiesNearLocation(latitude, longitude, radiusKm, limit));
    }

    @GetMapping("/cities/major")
    @Operation(summary = "Get major cities", description = "Retrieve major cities worldwide")
    public ResponseEntity<List<CityDto>> getMajorCities() {
        return ResponseEntity.ok(locationService.getMajorCities());
    }

    // Unified search endpoint
    @GetMapping("/search")
    @Operation(summary = "Unified location search", description = "Search across countries, states, and cities")
    public ResponseEntity<List<LocationSearchDto>> searchLocations(
            @Parameter(description = "Search query") @RequestParam String q,
            @Parameter(description = "Location type filter (country, state, city)") @RequestParam(required = false) String type,
            @Parameter(description = "Filter by country ID") @RequestParam(required = false) Long countryId,
            @Parameter(description = "Filter by state ID") @RequestParam(required = false) Long stateId,
            @Parameter(description = "Maximum number of results") @RequestParam(defaultValue = "20") Integer limit) {
        
        return ResponseEntity.ok(locationService.searchLocations(q, type, countryId, stateId, limit));
    }

    // Statistics endpoints
    @GetMapping("/countries/{countryId}/states/count")
    @Operation(summary = "Get states count", description = "Get number of states in a country")
    public ResponseEntity<Long> getStatesCount(@PathVariable Long countryId) {
        return ResponseEntity.ok(locationService.getStatesCountByCountry(countryId));
    }

    @GetMapping("/states/{stateId}/cities/count")
    @Operation(summary = "Get cities count by state", description = "Get number of cities in a state")
    public ResponseEntity<Long> getCitiesCountByState(@PathVariable Long stateId) {
        return ResponseEntity.ok(locationService.getCitiesCountByState(stateId));
    }

    @GetMapping("/countries/{countryId}/cities/count")
    @Operation(summary = "Get cities count by country", description = "Get number of cities in a country")
    public ResponseEntity<Long> getCitiesCountByCountry(@PathVariable Long countryId) {
        return ResponseEntity.ok(locationService.getCitiesCountByCountry(countryId));
    }

    // Debug endpoints
    @GetMapping("/debug/countries/all")
    @Operation(summary = "Debug: Get all countries without flag filter")
    public ResponseEntity<List<CountryDto>> getAllCountriesDebug() {
        return ResponseEntity.ok(locationService.getAllCountriesDebug());
    }

    @GetMapping("/debug/countries/count")
    @Operation(summary = "Debug: Count all countries")
    public ResponseEntity<Long> countAllCountries() {
        return ResponseEntity.ok(locationService.countAllCountries());
    }

    @GetMapping("/debug/countries/count-by-flag")
    @Operation(summary = "Debug: Count countries by flag value")
    public ResponseEntity<List<Object[]>> countByFlag() {
        return ResponseEntity.ok(locationService.countByFlag());
    }
}
