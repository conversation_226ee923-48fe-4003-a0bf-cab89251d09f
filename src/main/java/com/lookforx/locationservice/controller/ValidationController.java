package com.lookforx.locationservice.controller;

import com.lookforx.locationservice.service.LocationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Validation Controller
 * 
 * High-performance validation endpoints for other microservices.
 * These endpoints are heavily cached and optimized for fast validation checks.
 */
@RestController
@RequestMapping("/api/v1/validation")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Location Validation", description = "APIs for validating location data existence")
public class ValidationController {

    private final LocationService locationService;

    @GetMapping("/country/{countryId}/exists")
    @Operation(summary = "Check if country exists", 
               description = "Validate if a country exists and is active. Used by other microservices for data validation.")
    public ResponseEntity<Boolean> existsCountry(
            @Parameter(description = "Country ID to validate") @PathVariable Long countryId) {
        
        log.debug("Validating country existence: {}", countryId);
        boolean exists = locationService.existsCountry(countryId);
        return ResponseEntity.ok(exists);
    }

    @GetMapping("/state/{stateId}/exists")
    @Operation(summary = "Check if state exists", 
               description = "Validate if a state/province exists and is active.")
    public ResponseEntity<Boolean> existsState(
            @Parameter(description = "State ID to validate") @PathVariable Long stateId) {
        
        log.debug("Validating state existence: {}", stateId);
        boolean exists = locationService.existsState(stateId);
        return ResponseEntity.ok(exists);
    }

    @GetMapping("/city/{cityId}/exists")
    @Operation(summary = "Check if city exists", 
               description = "Validate if a city exists and is active.")
    public ResponseEntity<Boolean> existsCity(
            @Parameter(description = "City ID to validate") @PathVariable Long cityId) {
        
        log.debug("Validating city existence: {}", cityId);
        boolean exists = locationService.existsCity(cityId);
        return ResponseEntity.ok(exists);
    }

    @GetMapping("/state/{stateId}/country/{countryId}/exists")
    @Operation(summary = "Check if state exists in country", 
               description = "Validate if a state exists within the specified country.")
    public ResponseEntity<Boolean> existsStateInCountry(
            @Parameter(description = "State ID to validate") @PathVariable Long stateId,
            @Parameter(description = "Country ID to validate against") @PathVariable Long countryId) {
        
        log.debug("Validating state {} in country {}", stateId, countryId);
        boolean exists = locationService.existsStateInCountry(stateId, countryId);
        return ResponseEntity.ok(exists);
    }

    @GetMapping("/city/{cityId}/state/{stateId}/country/{countryId}/exists")
    @Operation(summary = "Check if city exists in state and country", 
               description = "Validate if a city exists within the specified state and country.")
    public ResponseEntity<Boolean> existsCityInStateAndCountry(
            @Parameter(description = "City ID to validate") @PathVariable Long cityId,
            @Parameter(description = "State ID to validate against") @PathVariable Long stateId,
            @Parameter(description = "Country ID to validate against") @PathVariable Long countryId) {
        
        log.debug("Validating city {} in state {} and country {}", cityId, stateId, countryId);
        boolean exists = locationService.existsCityInStateAndCountry(cityId, stateId, countryId);
        return ResponseEntity.ok(exists);
    }

    // Batch validation endpoints for better performance
    @PostMapping("/countries/exists")
    @Operation(summary = "Batch validate countries", 
               description = "Validate multiple countries at once for better performance.")
    public ResponseEntity<java.util.Map<Long, Boolean>> existsCountries(
            @Parameter(description = "List of country IDs to validate") @RequestBody java.util.List<Long> countryIds) {
        
        log.debug("Batch validating {} countries", countryIds.size());
        java.util.Map<Long, Boolean> results = new java.util.HashMap<>();
        
        for (Long countryId : countryIds) {
            results.put(countryId, locationService.existsCountry(countryId));
        }
        
        return ResponseEntity.ok(results);
    }

    @PostMapping("/states/exists")
    @Operation(summary = "Batch validate states", 
               description = "Validate multiple states at once for better performance.")
    public ResponseEntity<java.util.Map<Long, Boolean>> existsStates(
            @Parameter(description = "List of state IDs to validate") @RequestBody java.util.List<Long> stateIds) {
        
        log.debug("Batch validating {} states", stateIds.size());
        java.util.Map<Long, Boolean> results = new java.util.HashMap<>();
        
        for (Long stateId : stateIds) {
            results.put(stateId, locationService.existsState(stateId));
        }
        
        return ResponseEntity.ok(results);
    }

    @PostMapping("/cities/exists")
    @Operation(summary = "Batch validate cities", 
               description = "Validate multiple cities at once for better performance.")
    public ResponseEntity<java.util.Map<Long, Boolean>> existsCities(
            @Parameter(description = "List of city IDs to validate") @RequestBody java.util.List<Long> cityIds) {
        
        log.debug("Batch validating {} cities", cityIds.size());
        java.util.Map<Long, Boolean> results = new java.util.HashMap<>();
        
        for (Long cityId : cityIds) {
            results.put(cityId, locationService.existsCity(cityId));
        }
        
        return ResponseEntity.ok(results);
    }

    // Health check endpoint for validation service
    @GetMapping("/health")
    @Operation(summary = "Validation service health check", 
               description = "Check if validation service is operational.")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Validation service is operational");
    }
}
