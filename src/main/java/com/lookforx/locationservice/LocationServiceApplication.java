package com.lookforx.locationservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Location Service Application
 * 
 * High-performance microservice for managing geographical location data
 * including countries, regions, states, and cities.
 * 
 * Features:
 * - Redis caching for optimal performance
 * - PostgreSQL with optimized queries
 * - Async processing capabilities
 * - Service discovery integration
 * - Comprehensive location hierarchy management
 */
@SpringBootApplication(
        scanBasePackages = {
                "com.lookforx.locationservice",
                "com.lookforx.common"
        }
)
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.lookforx.common.client")
@EnableCaching
@EnableAsync
@EnableTransactionManagement
public class LocationServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(LocationServiceApplication.class, args);
    }
}
