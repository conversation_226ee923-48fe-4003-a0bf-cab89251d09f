package com.lookforx.locationservice.exception;

/**
 * Location Not Found Exception
 * 
 * Thrown when a requested location (country, state, or city) is not found.
 */
public class LocationNotFoundException extends RuntimeException {
    
    public LocationNotFoundException(String message) {
        super(message);
    }
    
    public LocationNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
    
    public static LocationNotFoundException country(Long countryId) {
        return new LocationNotFoundException("Country not found with id: " + countryId);
    }
    
    public static LocationNotFoundException state(Long stateId) {
        return new LocationNotFoundException("State not found with id: " + stateId);
    }
    
    public static LocationNotFoundException city(Long cityId) {
        return new LocationNotFoundException("City not found with id: " + cityId);
    }
    
    public static LocationNotFoundException countryByIso2(String iso2) {
        return new LocationNotFoundException("Country not found with ISO2 code: " + iso2);
    }
}
