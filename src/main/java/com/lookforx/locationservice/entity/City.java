package com.lookforx.locationservice.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * City Entity
 * 
 * Represents a city within a state and country.
 * Heavily optimized with composite indexes for efficient location-based queries.
 * This is the most frequently queried entity, so performance is critical.
 */
@Entity
@Table(name = "cities", indexes = {
    @Index(name = "idx_city_state_id", columnList = "state_id"),
    @Index(name = "idx_city_country_id", columnList = "country_id"),
    @Index(name = "idx_city_name", columnList = "name"),
    @Index(name = "idx_city_state_name", columnList = "state_id, name"),
    @Index(name = "idx_city_country_name", columnList = "country_id, name"),
    @Index(name = "idx_city_country_state", columnList = "country_id, state_id"),
    @Index(name = "idx_city_flag", columnList = "flag"),
    @Index(name = "idx_city_coordinates", columnList = "latitude, longitude")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class City {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "state_id", nullable = false)
    private Long stateId;

    @Column(name = "state_code", nullable = false)
    private String stateCode;

    @Column(name = "country_id", nullable = false)
    private Long countryId;

    @Column(name = "country_code", nullable = false, length = 2)
    private String countryCode;

    @Column(name = "latitude", nullable = false, precision = 10, scale = 8)
    private BigDecimal latitude;

    @Column(name = "longitude", nullable = false, precision = 11, scale = 8)
    private BigDecimal longitude;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "flag", nullable = false)
    private Integer flag;

    @Column(name = "wikiDataId")
    private String wikiDataId;

    // Many-to-one relationships for efficient joins
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "state_id", insertable = false, updatable = false)
    private State state;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "country_id", insertable = false, updatable = false)
    private Country country;

    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        updatedAt = LocalDateTime.now();
        if (flag == null) {
            flag = 1;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
