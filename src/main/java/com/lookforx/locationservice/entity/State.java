package com.lookforx.locationservice.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * State Entity
 * 
 * Represents a state/province within a country.
 * Optimized with composite indexes for efficient country-based queries.
 */
@Entity
@Table(name = "states", indexes = {
    @Index(name = "idx_state_country_id", columnList = "country_id"),
    @Index(name = "idx_state_name", columnList = "name"),
    @Index(name = "idx_state_country_name", columnList = "country_id, name"),
    @Index(name = "idx_state_country_code", columnList = "country_code"),
    @Index(name = "idx_state_flag", columnList = "flag")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
public class State {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "country_id", nullable = false)
    private Long countryId;

    @Column(name = "country_code", nullable = false, length = 2)
    private String countryCode;

    @Column(name = "fips_code")
    private String fipsCode;

    @Column(name = "iso2")
    private String iso2;

    @Column(name = "type", length = 191)
    private String type;

    @Column(name = "level")
    private Integer level;

    @Column(name = "parent_id")
    private Integer parentId;

    @Column(name = "native")
    private String nativeName;

    @Column(name = "latitude", precision = 10, scale = 8)
    private BigDecimal latitude;

    @Column(name = "longitude", precision = 11, scale = 8)
    private BigDecimal longitude;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "flag", nullable = false)
    private Integer flag;

    @Column(name = "wikiDataId")
    private String wikiDataId;

    // Many-to-one relationship with Country
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "country_id", insertable = false, updatable = false)
    private Country country;

    // One-to-many relationship with Cities
    @OneToMany(mappedBy = "stateId", fetch = FetchType.LAZY)
    private List<City> cities;

    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        updatedAt = LocalDateTime.now();
        if (flag == null) {
            flag = 1;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
