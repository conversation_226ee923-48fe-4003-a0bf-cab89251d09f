package com.lookforx.locationservice.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Region Entity
 * 
 * Represents a geographical region containing multiple countries.
 * Cached for optimal performance as region data rarely changes.
 */
@Entity
@Table(name = "regions", indexes = {
    @Index(name = "idx_region_name", columnList = "name"),
    @Index(name = "idx_region_flag", columnList = "flag")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class Region {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "translations", columnDefinition = "TEXT")
    private String translations;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "flag", nullable = false)
    private Integer flag;

    @Column(name = "wikiDataId")
    private String wikiDataId;

    // Lazy loaded relationships
    @OneToMany(mappedBy = "regionId", fetch = FetchType.LAZY)
    private List<Subregion> subregions;

    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        updatedAt = LocalDateTime.now();
        if (flag == null) {
            flag = 1;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
