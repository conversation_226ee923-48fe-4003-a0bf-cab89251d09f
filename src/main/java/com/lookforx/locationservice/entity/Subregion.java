package com.lookforx.locationservice.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.time.LocalDateTime;

/**
 * Subregion Entity
 * 
 * Represents a geographical subregion within a region.
 * Cached for optimal performance as subregion data rarely changes.
 */
@Entity
@Table(name = "subregions", indexes = {
    @Index(name = "idx_subregion_name", columnList = "name"),
    @Index(name = "idx_subregion_region_id", columnList = "region_id"),
    @Index(name = "idx_subregion_flag", columnList = "flag")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class Subregion {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "translations", columnDefinition = "TEXT")
    private String translations;

    @Column(name = "region_id", nullable = false)
    private Long regionId;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "flag", nullable = false)
    private Integer flag;

    @Column(name = "wikiDataId")
    private String wikiDataId;

    // Many-to-one relationship with Region
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "region_id", insertable = false, updatable = false)
    private Region region;

    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        updatedAt = LocalDateTime.now();
        if (flag == null) {
            flag = 1;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
