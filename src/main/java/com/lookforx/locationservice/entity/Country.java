package com.lookforx.locationservice.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Country Entity
 * 
 * Represents a country with all its geographical and political information.
 * Optimized for high-performance queries with proper indexing and caching.
 */
@Entity
@Table(name = "countries", indexes = {
    @Index(name = "idx_country_name", columnList = "name"),
    @Index(name = "idx_country_iso2", columnList = "iso2"),
    @Index(name = "idx_country_iso3", columnList = "iso3"),
    @Index(name = "idx_country_flag", columnList = "flag")
})
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Cache(usage = CacheConcurrencyStrategy.READ_ONLY)
public class Country {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "iso3", length = 3)
    private String iso3;

    @Column(name = "numeric_code", length = 3)
    private String numericCode;

    @Column(name = "iso2", length = 2)
    private String iso2;

    @Column(name = "phonecode")
    private String phonecode;

    @Column(name = "capital")
    private String capital;

    @Column(name = "currency")
    private String currency;

    @Column(name = "currency_name")
    private String currencyName;

    @Column(name = "currency_symbol")
    private String currencySymbol;

    @Column(name = "tld")
    private String tld;

    @Column(name = "native")
    private String nativeName;

    @Column(name = "region")
    private String region;

    @Column(name = "region_id")
    private Long regionId;

    @Column(name = "subregion")
    private String subregion;

    @Column(name = "subregion_id")
    private Long subregionId;

    @Column(name = "nationality")
    private String nationality;

    @Column(name = "timezones", columnDefinition = "TEXT")
    private String timezones;

    @Column(name = "translations", columnDefinition = "TEXT")
    private String translations;

    @Column(name = "latitude", precision = 10, scale = 8)
    private BigDecimal latitude;

    @Column(name = "longitude", precision = 11, scale = 8)
    private BigDecimal longitude;

    @Column(name = "emoji", length = 191)
    private String emoji;

    @Column(name = "emojiU", length = 191)
    private String emojiUnicodeValue;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "flag", nullable = false)
    private Integer flag;

    @Column(name = "wikiDataId")
    private String wikiDataId;

    // Lazy loaded relationships for performance
    @OneToMany(mappedBy = "countryId", fetch = FetchType.LAZY)
    private List<State> states;

    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        updatedAt = LocalDateTime.now();
        if (flag == null) {
            flag = 1;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
