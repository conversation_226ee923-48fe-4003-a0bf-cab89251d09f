package com.lookforx.locationservice.service.impl;

import com.lookforx.locationservice.dto.CityDto;
import com.lookforx.locationservice.dto.CountryDto;
import com.lookforx.locationservice.dto.LocationSearchDto;
import com.lookforx.locationservice.dto.StateDto;
import com.lookforx.locationservice.repository.CityRepository;
import com.lookforx.locationservice.repository.CountryRepository;
import com.lookforx.locationservice.repository.StateRepository;
import com.lookforx.locationservice.service.LocationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Location Service Implementation
 * 
 * High-performance implementation with aggressive caching and optimized queries.
 * Uses Redis for caching frequently accessed location data.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class LocationServiceImpl implements LocationService {

    private final CountryRepository countryRepository;
    private final StateRepository stateRepository;
    private final CityRepository cityRepository;

    // Country operations
    @Override
    @Cacheable(value = "countries", key = "'all'")
    public List<CountryDto> getAllCountries() {
        log.debug("Fetching all countries from database");
        return countryRepository.findAllActiveCountriesMinimal();
    }

    @Override
    @Cacheable(value = "countries", key = "'all_with_phonecode'")
    public List<CountryDto> getAllCountriesWithPhonecode() {
        log.debug("Fetching all countries with phonecode from database");
        return countryRepository.findAllActiveCountriesWithPhonecode();
    }

    @Override
    @Cacheable(value = "countries", key = "'popular'")
    public List<CountryDto> getPopularCountries() {
        log.debug("Fetching popular countries from database");
        return countryRepository.findPopularCountries();
    }

    @Override
    @Cacheable(value = "country", key = "#id", unless = "#result == null || !#result.isPresent()")
    public Optional<CountryDto> getCountryById(Long id) {
        log.debug("Fetching country by id: {}", id);
        return countryRepository.findByIdProjection(id);
    }

    @Override
    @Cacheable(value = "country", key = "'iso2_' + #iso2", unless = "#result == null || !#result.isPresent()")
    public Optional<CountryDto> getCountryByIso2(String iso2) {
        log.debug("Fetching country by ISO2: {}", iso2);
        return countryRepository.findByIso2Projection(iso2);
    }

    @Override
    @Cacheable(value = "country_search", key = "#query")
    public List<CountryDto> searchCountries(String query) {
        log.debug("Searching countries with query: {}", query);
        if (query == null || query.trim().isEmpty()) {
            return getAllCountries();
        }
        return countryRepository.searchCountriesByName(query.trim());
    }

    @Override
    @Cacheable(value = "countries_by_region", key = "#region")
    public List<CountryDto> getCountriesByRegion(String region) {
        log.debug("Fetching countries by region: {}", region);
        return countryRepository.findByRegion(region);
    }

    // State operations
    @Override
    @Cacheable(value = "states", key = "'country_' + #countryId")
    public List<StateDto> getStatesByCountryId(Long countryId) {
        log.debug("Fetching states by country id: {}", countryId);
        return stateRepository.findByCountryIdMinimal(countryId);
    }

    @Override
    @Cacheable(value = "states_with_info", key = "'country_' + #countryId")
    public List<StateDto> getStatesByCountryIdWithInfo(Long countryId) {
        log.debug("Fetching states with info by country id: {}", countryId);
        return stateRepository.findByCountryIdWithCountryInfo(countryId);
    }

    @Override
    public Page<StateDto> getStatesByCountryIdPaginated(Long countryId, Pageable pageable) {
        log.debug("Fetching states by country id with pagination: {}, page: {}", countryId, pageable.getPageNumber());
        return stateRepository.findByCountryIdPaginated(countryId, pageable);
    }

    @Override
    @Cacheable(value = "state", key = "#id", unless = "#result == null || !#result.isPresent()")
    public Optional<StateDto> getStateById(Long id) {
        log.debug("Fetching state by id: {}", id);
        return stateRepository.findByIdProjection(id);
    }

    @Override
    @Cacheable(value = "state_search", key = "#countryId + '_' + #query")
    public List<StateDto> searchStatesInCountry(Long countryId, String query) {
        log.debug("Searching states in country {} with query: {}", countryId, query);
        if (query == null || query.trim().isEmpty()) {
            return getStatesByCountryIdWithInfo(countryId);
        }
        return stateRepository.searchStatesByNameInCountry(countryId, query.trim());
    }

    @Override
    @Cacheable(value = "state_search_global", key = "#query")
    public List<StateDto> searchStatesGlobally(String query) {
        log.debug("Searching states globally with query: {}", query);
        if (query == null || query.trim().isEmpty()) {
            return new ArrayList<>();
        }
        return stateRepository.searchStatesGlobally(query.trim());
    }

    @Override
    @Cacheable(value = "states", key = "'popular'")
    public List<StateDto> getPopularStates() {
        log.debug("Fetching popular states from database");
        return stateRepository.findPopularStates();
    }

    @Override
    @Cacheable(value = "state_count", key = "'country_' + #countryId")
    public long getStatesCountByCountry(Long countryId) {
        log.debug("Getting states count by country: {}", countryId);
        return stateRepository.countByCountryId(countryId);
    }

    // City operations
    @Override
    @Cacheable(value = "cities", key = "'state_' + #stateId")
    public List<CityDto> getCitiesByStateId(Long stateId) {
        log.debug("Fetching cities by state id: {}", stateId);
        return cityRepository.findByStateIdMinimal(stateId);
    }

    @Override
    @Cacheable(value = "cities_with_hierarchy", key = "'state_' + #stateId")
    public List<CityDto> getCitiesByStateIdWithHierarchy(Long stateId) {
        log.debug("Fetching cities with hierarchy by state id: {}", stateId);
        return cityRepository.findByStateIdWithHierarchy(stateId);
    }

    @Override
    public Page<CityDto> getCitiesByCountryIdPaginated(Long countryId, Pageable pageable) {
        log.debug("Fetching cities by country id with pagination: {}, page: {}", countryId, pageable.getPageNumber());
        return cityRepository.findByCountryIdPaginated(countryId, pageable);
    }

    @Override
    @Cacheable(value = "city", key = "#id", unless = "#result == null || !#result.isPresent()")
    public Optional<CityDto> getCityById(Long id) {
        log.debug("Fetching city by id: {}", id);
        return cityRepository.findByIdProjection(id);
    }

    @Override
    @Cacheable(value = "city_search", key = "#stateId + '_' + #query")
    public List<CityDto> searchCitiesInState(Long stateId, String query) {
        log.debug("Searching cities in state {} with query: {}", stateId, query);
        if (query == null || query.trim().isEmpty()) {
            return getCitiesByStateIdWithHierarchy(stateId);
        }
        return cityRepository.searchCitiesByNameInState(stateId, query.trim());
    }

    @Override
    public Page<CityDto> searchCitiesInCountry(Long countryId, String query, Pageable pageable) {
        log.debug("Searching cities in country {} with query: {}, page: {}", countryId, query, pageable.getPageNumber());
        if (query == null || query.trim().isEmpty()) {
            return getCitiesByCountryIdPaginated(countryId, pageable);
        }
        return cityRepository.searchCitiesByNameInCountry(countryId, query.trim(), pageable);
    }

    @Override
    public Page<CityDto> searchCitiesGlobally(String query, Pageable pageable) {
        log.debug("Searching cities globally with query: {}, page: {}", query, pageable.getPageNumber());
        if (query == null || query.trim().isEmpty()) {
            return Page.empty(pageable);
        }
        return cityRepository.searchCitiesGlobally(query.trim(), pageable);
    }

    @Override
    @Cacheable(value = "cities_near", key = "#latitude + '_' + #longitude + '_' + #radiusKm + '_' + #limit")
    public List<CityDto> getCitiesNearLocation(BigDecimal latitude, BigDecimal longitude, Double radiusKm, Integer limit) {
        log.debug("Fetching cities near location: {}, {}, radius: {}, limit: {}", latitude, longitude, radiusKm, limit);
        List<Object[]> results = cityRepository.findCitiesNearCoordinates(latitude, longitude, radiusKm, limit);
        
        return results.stream()
                .map(row -> new CityDto(
                        ((Number) row[0]).longValue(), // id
                        (String) row[1], // name
                        ((Number) row[2]).longValue(), // stateId
                        (String) row[3], // stateName
                        null, // stateCode
                        ((Number) row[4]).longValue(), // countryId
                        (String) row[5], // countryName
                        (String) row[6], // countryCode
                        (BigDecimal) row[7], // latitude
                        (BigDecimal) row[8] // longitude
                ))
                .toList();
    }

    @Override
    @Cacheable(value = "cities", key = "'major'")
    public List<CityDto> getMajorCities() {
        log.debug("Fetching major cities from database");
        return cityRepository.findMajorCities();
    }

    @Override
    @Cacheable(value = "city_count", key = "'state_' + #stateId")
    public long getCitiesCountByState(Long stateId) {
        log.debug("Getting cities count by state: {}", stateId);
        return cityRepository.countByStateId(stateId);
    }

    @Override
    @Cacheable(value = "city_count", key = "'country_' + #countryId")
    public long getCitiesCountByCountry(Long countryId) {
        log.debug("Getting cities count by country: {}", countryId);
        return cityRepository.countByCountryId(countryId);
    }

    // Unified search
    @Override
    @Cacheable(value = "location_search", key = "#query + '_' + #type + '_' + #countryId + '_' + #stateId + '_' + #limit")
    public List<LocationSearchDto> searchLocations(String query, String type, Long countryId, Long stateId, Integer limit) {
        log.debug("Unified location search - query: {}, type: {}, countryId: {}, stateId: {}, limit: {}",
                  query, type, countryId, stateId, limit);

        List<LocationSearchDto> results = new ArrayList<>();

        if (query == null || query.trim().isEmpty()) {
            return results;
        }

        String searchQuery = query.trim();
        int searchLimit = limit != null ? limit : 20;

        // Search countries if type is null or "country"
        if (type == null || "country".equals(type)) {
            List<CountryDto> countries = searchCountries(searchQuery);
            countries.stream()
                    .limit(searchLimit)
                    .forEach(country -> results.add(new LocationSearchDto(
                            country.getId(), country.getName(), country.getIso2(), country.getEmoji())));
        }

        // Search states if type is null or "state"
        if (type == null || "state".equals(type)) {
            List<StateDto> states;
            if (countryId != null) {
                states = searchStatesInCountry(countryId, searchQuery);
            } else {
                states = searchStatesGlobally(searchQuery);
            }
            states.stream()
                    .limit(searchLimit)
                    .forEach(state -> results.add(new LocationSearchDto(
                            state.getId(), state.getName(), state.getCountryId(),
                            state.getCountryName(), state.getCountryCode(), null)));
        }

        // Search cities if type is null or "city"
        if (type == null || "city".equals(type)) {
            List<CityDto> cities;
            if (stateId != null) {
                cities = searchCitiesInState(stateId, searchQuery);
            } else if (countryId != null) {
                cities = searchCitiesInCountry(countryId, searchQuery, Pageable.ofSize(searchLimit))
                        .getContent();
            } else {
                cities = searchCitiesGlobally(searchQuery, Pageable.ofSize(searchLimit))
                        .getContent();
            }
            cities.forEach(city -> results.add(new LocationSearchDto(
                    city.getId(), city.getName(), city.getStateId(), city.getStateName(),
                    city.getCountryId(), city.getCountryName(), city.getCountryCode(), null)));
        }

        return results.stream().limit(searchLimit).toList();
    }

    // Validation operations
    @Override
    @Cacheable(value = "validation", key = "'country_' + #countryId")
    public boolean existsCountry(Long countryId) {
        log.debug("Validating country existence: {}", countryId);
        return countryRepository.existsByIdAndActive(countryId);
    }

    @Override
    @Cacheable(value = "validation", key = "'state_' + #stateId")
    public boolean existsState(Long stateId) {
        log.debug("Validating state existence: {}", stateId);
        return stateRepository.existsByIdAndActive(stateId);
    }

    @Override
    @Cacheable(value = "validation", key = "'city_' + #cityId")
    public boolean existsCity(Long cityId) {
        log.debug("Validating city existence: {}", cityId);
        return cityRepository.existsByIdAndActive(cityId);
    }

    @Override
    @Cacheable(value = "validation", key = "'state_in_country_' + #stateId + '_' + #countryId")
    public boolean existsStateInCountry(Long stateId, Long countryId) {
        log.debug("Validating state {} in country {}", stateId, countryId);
        return stateRepository.existsByIdAndCountryId(stateId, countryId);
    }

    @Override
    @Cacheable(value = "validation", key = "'city_in_state_country_' + #cityId + '_' + #stateId + '_' + #countryId")
    public boolean existsCityInStateAndCountry(Long cityId, Long stateId, Long countryId) {
        log.debug("Validating city {} in state {} and country {}", cityId, stateId, countryId);
        return cityRepository.existsByIdAndStateIdAndCountryId(cityId, stateId, countryId);
    }

    // Cache management
    @Override
    @CacheEvict(value = {"countries", "country", "countries_by_region"}, allEntries = true)
    public void refreshCountryCache() {
        log.info("Refreshing country cache");
    }

    @Override
    @CacheEvict(value = {"countries", "country", "countries_by_region", "states", "states_with_info",
                        "state", "cities", "cities_with_hierarchy", "city", "validation"}, allEntries = true)
    public void refreshLocationCache() {
        log.info("Refreshing all location cache");
    }

    @Override
    public void warmupCache() {
        log.info("Warming up location cache");
        try {
            // Warm up both country cache variants
            getAllCountries();
            getAllCountriesWithPhonecode();
            getPopularCountries();
            getMajorCities();
            getPopularStates();
            log.info("Cache warmup completed successfully");
        } catch (Exception e) {
            log.error("Error during cache warmup", e);
        }
    }

    // Debug methods
    @Override
    public List<CountryDto> getAllCountriesDebug() {
        log.debug("Debug: Fetching all countries without flag filter");
        return countryRepository.findAllCountriesDebug();
    }

    @Override
    public long countAllCountries() {
        log.debug("Debug: Counting all countries");
        return countryRepository.countAllCountries();
    }

    @Override
    public List<Object[]> countByFlag() {
        log.debug("Debug: Counting countries by flag value");
        return countryRepository.countByFlag();
    }
}
