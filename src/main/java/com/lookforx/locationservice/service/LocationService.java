package com.lookforx.locationservice.service;

import com.lookforx.locationservice.dto.CityDto;
import com.lookforx.locationservice.dto.CountryDto;
import com.lookforx.locationservice.dto.LocationSearchDto;
import com.lookforx.locationservice.dto.StateDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * Location Service Interface
 * 
 * High-performance service for managing geographical location data.
 * Provides comprehensive location hierarchy management with caching.
 */
public interface LocationService {

    // Country operations
    List<CountryDto> getAllCountries();
    List<CountryDto> getAllCountriesWithPhonecode();
    List<CountryDto> getPopularCountries();
    Optional<CountryDto> getCountryById(Long id);
    Optional<CountryDto> getCountryByIso2(String iso2);
    List<CountryDto> searchCountries(String query);
    List<CountryDto> getCountriesByRegion(String region);

    // State operations
    List<StateDto> getStatesByCountryId(Long countryId);
    List<StateDto> getStatesByCountryIdWithInfo(Long countryId);
    Page<StateDto> getStatesByCountryIdPaginated(Long countryId, Pageable pageable);
    Optional<StateDto> getStateById(Long id);
    List<StateDto> searchStatesInCountry(Long countryId, String query);
    List<StateDto> searchStatesGlobally(String query);
    List<StateDto> getPopularStates();
    long getStatesCountByCountry(Long countryId);

    // City operations
    List<CityDto> getCitiesByStateId(Long stateId);
    List<CityDto> getCitiesByStateIdWithHierarchy(Long stateId);
    Page<CityDto> getCitiesByCountryIdPaginated(Long countryId, Pageable pageable);
    Optional<CityDto> getCityById(Long id);
    List<CityDto> searchCitiesInState(Long stateId, String query);
    Page<CityDto> searchCitiesInCountry(Long countryId, String query, Pageable pageable);
    Page<CityDto> searchCitiesGlobally(String query, Pageable pageable);
    List<CityDto> getCitiesNearLocation(BigDecimal latitude, BigDecimal longitude, Double radiusKm, Integer limit);
    List<CityDto> getMajorCities();
    long getCitiesCountByState(Long stateId);
    long getCitiesCountByCountry(Long countryId);

    // Unified search
    List<LocationSearchDto> searchLocations(String query, String type, Long countryId, Long stateId, Integer limit);

    // Validation operations (for other microservices)
    boolean existsCountry(Long countryId);
    boolean existsState(Long stateId);
    boolean existsCity(Long cityId);
    boolean existsStateInCountry(Long stateId, Long countryId);
    boolean existsCityInStateAndCountry(Long cityId, Long stateId, Long countryId);

    // Cache management
    void refreshCountryCache();
    void refreshLocationCache();
    void warmupCache();

    // Debug methods
    List<CountryDto> getAllCountriesDebug();
    long countAllCountries();
    List<Object[]> countByFlag();
}
