package com.lookforx.locationservice.component;

import com.lookforx.locationservice.service.LocationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Cache Warmup Component
 * 
 * Automatically warms up the cache on application startup and periodically
 * refreshes frequently accessed data to ensure optimal performance.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CacheWarmupComponent {

    private final LocationService locationService;

    /**
     * Warm up cache on application startup
     */
    @EventListener(ApplicationReadyEvent.class)
    @Async
    public void warmupCacheOnStartup() {
        log.info("Starting cache warmup on application startup");
        try {
            locationService.warmupCache();
            log.info("Cache warmup completed successfully on startup");
        } catch (Exception e) {
            log.error("Error during cache warmup on startup", e);
        }
    }

    /**
     * Refresh cache every 6 hours to keep data fresh
     */
    @Scheduled(fixedRate = 21600000) // 6 hours in milliseconds
    @Async
    public void scheduledCacheRefresh() {
        log.info("Starting scheduled cache refresh");
        try {
            // Refresh popular data that's accessed frequently
            locationService.getAllCountries();
            locationService.getAllCountriesWithPhonecode();
            locationService.getPopularCountries();
            locationService.getMajorCities();
            locationService.getPopularStates();

            log.info("Scheduled cache refresh completed successfully");
        } catch (Exception e) {
            log.error("Error during scheduled cache refresh", e);
        }
    }

    /**
     * Refresh country cache daily (countries rarely change)
     */
    @Scheduled(cron = "0 0 2 * * ?") // Every day at 2 AM
    @Async
    public void dailyCountryCacheRefresh() {
        log.info("Starting daily country cache refresh");
        try {
            locationService.refreshCountryCache();
            // Re-populate country cache
            locationService.getAllCountries();
            locationService.getAllCountriesWithPhonecode();
            locationService.getPopularCountries();
            
            log.info("Daily country cache refresh completed successfully");
        } catch (Exception e) {
            log.error("Error during daily country cache refresh", e);
        }
    }

    /**
     * Full cache refresh weekly (Sunday at 3 AM)
     */
    @Scheduled(cron = "0 0 3 ? * SUN")
    @Async
    public void weeklyCacheRefresh() {
        log.info("Starting weekly full cache refresh");
        try {
            locationService.refreshLocationCache();
            // Re-populate all caches
            locationService.warmupCache();
            
            log.info("Weekly full cache refresh completed successfully");
        } catch (Exception e) {
            log.error("Error during weekly full cache refresh", e);
        }
    }
}
