package com.lookforx.categoryservice;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(
    scanBasePackages = {
        "com.lookforx.categoryservice",
        "com.lookforx.common"
    }
)
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.lookforx.common.client" , "com.lookforx.categoryservice.web"})
public class CategoryServiceApplication{
    public static void main(String[] args) {
            SpringApplication.run(CategoryServiceApplication.class, args);
        }
}
