package com.lookforx.categoryservice.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jackson Configuration for Category Service.
 *
 * Following Effective Java principles:
 * - Item 17: Minimize mutability (immutable configuration)
 * - Item 67: Optimize judiciously (proper JSON serialization)
 * - Item 2: Consider a builder when faced with many constructor parameters
 */
@Configuration
@Slf4j
public class JacksonConfig {

    /**
     * Configures ObjectMapper with proper settings for the application.
     * Following Effective Java Item 2: Consider a builder when faced with many constructor parameters.
     *
     * @return configured ObjectMapper instance
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        log.info("Configuring ObjectMapper with custom settings");

        ObjectMapper mapper = new ObjectMapper();

        // Register Java Time module for proper LocalDateTime handling
        mapper.registerModule(new JavaTimeModule());

        // Configure serialization features
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        mapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);

        // Configure deserialization features for better error handling
        mapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        mapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
        mapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);

        log.info("ObjectMapper configured successfully");
        return mapper;
    }
}
