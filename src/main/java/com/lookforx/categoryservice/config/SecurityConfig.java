package com.lookforx.categoryservice.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;

import java.util.Objects;

/**
 * Security Configuration for Category Service.
 *
 * Following Effective Java principles:
 * - Item 49: Check parameters for validity
 * - Item 17: Minimize mutability (immutable configuration)
 * - Item 67: Optimize judiciously (proper security configuration)
 */
@Configuration
@EnableWebSecurity
@Slf4j
public class SecurityConfig {

    /**
     * Configures the security filter chain for the application.
     * Following Effective Java Item 49: Check parameters for validity.
     *
     * @param http HttpSecurity configuration object, must not be null
     * @return configured SecurityFilterChain
     * @throws Exception if configuration fails
     * @throws IllegalArgumentException if http is null
     */
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        Objects.requireNonNull(http, "HttpSecurity cannot be null");

        log.info("Configuring security filter chain for category service");

        http
                // Disable CSRF for stateless API
                .csrf(AbstractHttpConfigurer::disable)

                // Configure session management as stateless
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS))

                // Configure authorization rules
                .authorizeHttpRequests(auth -> auth
                        // Allow health check endpoints
                        .requestMatchers("/actuator/health", "/actuator/info").permitAll()

                        // Allow Swagger/OpenAPI endpoints
                        .requestMatchers("/swagger-ui/**", "/v3/api-docs/**", "/swagger-resources/**").permitAll()

                        // Allow all API requests (adjust based on security requirements)
                        .requestMatchers("/api/**").permitAll()

                        // Require authentication for all other requests
                        .anyRequest().authenticated()
                )

                // Configure security headers
                .headers(headers -> headers
                        .frameOptions(frameOptions -> frameOptions.deny())
                        .contentTypeOptions(contentTypeOptions -> {})
                        .httpStrictTransportSecurity(hstsConfig -> hstsConfig
                                .maxAgeInSeconds(31536000)
                                .includeSubDomains(true)
                        )
                );

        SecurityFilterChain filterChain = http.build();
        log.info("Security filter chain configured successfully");

        return filterChain;
    }
}
