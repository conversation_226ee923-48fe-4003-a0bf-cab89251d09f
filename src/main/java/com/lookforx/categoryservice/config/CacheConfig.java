package com.lookforx.categoryservice.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.Objects;

/**
 * Redis Cache Configuration for Category Service.
 *
 * Following Effective Java principles:
 * - Item 49: Check parameters for validity
 * - Item 17: Minimize mutability (immutable configuration)
 * - Item 67: Optimize judiciously (proper cache configuration)
 * - Item 2: Consider a builder when faced with many constructor parameters
 */
@Configuration
@EnableCaching
@Slf4j
public class CacheConfig {

    // Default cache TTL - Following Effective Java Item 17 (immutable constants)
    private static final Duration DEFAULT_CACHE_TTL = Duration.ofMinutes(30);
    private static final String CACHE_KEY_PREFIX = "category-service:";

    @Value("${cache.categories.timeout:1800}") // 30 minutes default
    private long cacheTimeoutSeconds;

    /**
     * Configures RedisTemplate with proper serializers.
     * Following Effective Java Item 49: Check parameters for validity.
     *
     * @param factory Redis connection factory, must not be null
     * @return configured RedisTemplate
     * @throws IllegalArgumentException if factory is null
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        Objects.requireNonNull(factory, "RedisConnectionFactory cannot be null");

        log.info("Configuring RedisTemplate with StringRedisSerializer");

        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        // Use consistent serializers to avoid type casting issues
        StringRedisSerializer stringSerializer = new StringRedisSerializer();

        template.setKeySerializer(stringSerializer);
        template.setValueSerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        template.setHashValueSerializer(stringSerializer);

        // Enable default serialization for complex objects
        template.setDefaultSerializer(stringSerializer);
        template.afterPropertiesSet();

        log.info("RedisTemplate configured successfully");
        return template;
    }

    /**
     * Configures CacheManager with proper TTL and serialization.
     * Following Effective Java Item 2: Consider a builder when faced with many constructor parameters.
     *
     * @param factory Redis connection factory, must not be null
     * @return configured CacheManager
     * @throws IllegalArgumentException if factory is null
     */
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory factory) {
        Objects.requireNonNull(factory, "RedisConnectionFactory cannot be null");

        Duration cacheTtl = Duration.ofSeconds(Math.max(cacheTimeoutSeconds, 60)); // Minimum 1 minute
        log.info("Configuring CacheManager with TTL: {} seconds", cacheTtl.getSeconds());

        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(cacheTtl)
                .computePrefixWith(cacheName -> CACHE_KEY_PREFIX + cacheName + ":")
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues(); // Prevent caching null values

        RedisCacheManager cacheManager = RedisCacheManager.builder(factory)
                .cacheDefaults(config)
                .transactionAware() // Enable transaction support
                .build();

        log.info("CacheManager configured successfully with TTL: {}", cacheTtl);
        return cacheManager;
    }
}
