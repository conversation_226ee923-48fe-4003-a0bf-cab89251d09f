package com.lookforx.categoryservice.service;

import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.common.enums.LanguageCode;

/**
 * Service interface for managing category-related cache operations.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles cache management
 * - Interface Segregation: Focused interface for cache operations
 * - Dependency Inversion: Abstracts cache implementation details
 */
public interface CacheManagementService {

    /**
     * Invalidates all category-related cache entries.
     * Used when categories are created, updated, or deleted.
     */
    void invalidateAllCategoryCache();

    /**
     * Invalidates cache entries for a specific category type.
     * 
     * @param type the category type to invalidate cache for
     */
    void invalidateCacheByType(CategoryType type);

    /**
     * Invalidates simple categories cache for specific language and type.
     * 
     * @param language the language code
     * @param type the category type (can be null)
     * @param search the search term (can be null)
     */
    void invalidateSimpleCategoriesCache(LanguageCode language, CategoryType type, String search);

    /**
     * Clears all cache entries in the system.
     * Used for administrative purposes.
     */
    void clearAllCache();

    /**
     * Warms up the cache with frequently accessed data.
     */
    void warmUpCache();

    /**
     * Gets cache statistics for monitoring purposes.
     * 
     * @return cache statistics as a formatted string
     */
    String getCacheStatistics();
}
