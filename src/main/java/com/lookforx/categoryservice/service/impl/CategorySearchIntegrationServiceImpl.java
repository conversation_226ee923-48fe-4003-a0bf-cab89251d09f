package com.lookforx.categoryservice.service.impl;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.dto.CategoryDTO;
import com.lookforx.categoryservice.domain.mapper.CategoryMapper;
import com.lookforx.categoryservice.repository.CategoryRepository;
import com.lookforx.categoryservice.service.CategorySearchIntegrationService;
import com.lookforx.categoryservice.web.client.SearchCategoryDocument;
import com.lookforx.categoryservice.web.client.SearchClient;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Implementation of category search integration service.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles search service integration
 * - Open/Closed: Can be extended with new search strategies
 * - Dependency Inversion: Depends on abstractions (SearchClient, CategoryMapper)
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CategorySearchIntegrationServiceImpl implements CategorySearchIntegrationService {

    private final SearchClient searchClient;
    private final CategoryMapper categoryMapper;
    private final CategoryRepository categoryRepository;

    @Override
    @CircuitBreaker(name = "search-service", fallbackMethod = "indexCategoryFallback")
    @Retry(name = "search-service")
    public void indexCategory(Category category) {
        if (category == null) {
            log.warn("Attempted to index null category");
            return;
        }

        try {
            log.debug("Indexing category with ID: {}", category.getId());
            SearchCategoryDocument document = createSearchDocument(category);
            searchClient.indexCategory(document);
            log.info("Successfully indexed category with ID: {}", category.getId());
        } catch (Exception e) {
            log.error("Failed to index category with ID: {} - Error: {}", category.getId(), e.getMessage());
            throw e; // Let resilience4j handle the exception
        }
    }

    /**
     * Fallback method for indexCategory when search service is unavailable.
     * Clean Code: Meaningful method name, proper error handling.
     */
    public void indexCategoryFallback(Category category, Exception ex) {
        log.warn("Search service unavailable, using fallback for category ID: {} - Reason: {}",
                category != null ? category.getId() : "null", ex.getMessage());
        // Could implement: store in queue for later processing, use alternative search provider, etc.
    }

    @Override
    @CircuitBreaker(name = "search-service", fallbackMethod = "indexCategoryDTOFallback")
    @Retry(name = "search-service")
    public void indexCategory(CategoryDTO categoryDTO) {
        if (categoryDTO == null) {
            log.warn("Attempted to index null category DTO");
            return;
        }

        try {
            log.debug("Indexing category DTO with ID: {}", categoryDTO.getId());
            SearchCategoryDocument document = createSearchDocument(categoryDTO);
            searchClient.indexCategory(document);
            log.info("Successfully indexed category DTO with ID: {}", categoryDTO.getId());
        } catch (Exception e) {
            log.error("Failed to index category DTO with ID: {} - Error: {}", categoryDTO.getId(), e.getMessage());
            throw e; // Let resilience4j handle the exception
        }
    }

    /**
     * Fallback method for indexCategory(CategoryDTO) when search service is unavailable.
     */
    public void indexCategoryDTOFallback(CategoryDTO categoryDTO, Exception ex) {
        log.warn("Search service unavailable, using fallback for category DTO ID: {} - Reason: {}",
                categoryDTO != null ? categoryDTO.getId() : "null", ex.getMessage());
    }

    @Override
    @CircuitBreaker(name = "search-service", fallbackMethod = "updateCategoryIndexFallback")
    @Retry(name = "search-service")
    public void updateCategoryIndex(Category category) {
        if (category == null || category.getId() == null) {
            log.warn("Attempted to update index for null category or category without ID");
            return;
        }

        try {
            log.debug("Updating search index for category with ID: {}", category.getId());
            SearchCategoryDocument document = createSearchDocument(category);
            searchClient.updateCategory(category.getId().toString(), document);
            log.info("Successfully updated search index for category with ID: {}", category.getId());
        } catch (Exception e) {
            log.error("Failed to update search index for category with ID: {} - Error: {}", category.getId(), e.getMessage());
            throw e; // Let resilience4j handle the exception
        }
    }

    /**
     * Fallback method for updateCategoryIndex when search service is unavailable.
     */
    public void updateCategoryIndexFallback(Category category, Exception ex) {
        log.warn("Search service unavailable, using fallback for updating category ID: {} - Reason: {}",
                category != null ? category.getId() : "null", ex.getMessage());
    }

    @Override
    public void removeCategoryFromIndex(Long categoryId) {
        if (categoryId == null) {
            log.warn("Attempted to remove category from index with null ID");
            return;
        }

        try {
            log.debug("Removing category from search index with ID: {}", categoryId);
            searchClient.deleteCategory(categoryId.toString());
            log.info("Successfully removed category from search index with ID: {}", categoryId);
        } catch (Exception e) {
            log.error("Failed to remove category from search index with ID: {}", categoryId, e);
        }
    }

    @Override
    public void bulkIndexCategories(List<Category> categories) {
        if (categories == null || categories.isEmpty()) {
            log.info("No categories to bulk index");
            return;
        }

        try {
            log.info("Starting bulk indexing of {} categories", categories.size());
            
            List<SearchCategoryDocument> documents = categories.stream()
                    .filter(category -> category != null && category.getId() != null)
                    .map(this::createSearchDocument)
                    .collect(Collectors.toList());

            if (!documents.isEmpty()) {
                searchClient.bulkIndexCategories(documents);
                log.info("Successfully bulk indexed {} categories", documents.size());
            } else {
                log.warn("No valid categories found for bulk indexing");
            }
        } catch (Exception e) {
            log.error("Failed to bulk index categories", e);
            throw new RuntimeException("Bulk indexing failed", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public void syncAllCategoriesToSearch() {
        log.info("Starting synchronization of all categories to search service");
        
        try {
            List<Category> allCategories = categoryRepository.findAll();
            log.info("Found {} categories to synchronize", allCategories.size());
            
            if (!allCategories.isEmpty()) {
                bulkIndexCategories(allCategories);
                log.info("Successfully synchronized all categories to search service");
            } else {
                log.info("No categories found to synchronize");
            }
        } catch (Exception e) {
            log.error("Failed to synchronize categories to search service", e);
            throw new RuntimeException("Category synchronization failed", e);
        }
    }

    @Override
    public void clearSearchIndex() {
        try {
            log.info("Clearing all categories from search index");
            searchClient.clearAllCategories();
            log.info("Successfully cleared all categories from search index");
        } catch (Exception e) {
            log.error("Failed to clear search index", e);
            throw new RuntimeException("Failed to clear search index", e);
        }
    }

    @Override
    public boolean isSearchServiceHealthy() {
        try {
            // Simple health check by trying to call search client
            // Since isIndexHealthy() doesn't exist, we'll use a different approach
            return true; // Placeholder - implement actual health check
        } catch (Exception e) {
            log.error("Error checking search service health", e);
            return false;
        }
    }

    @Override
    public String getSearchServiceStatistics() {
        try {
            // This would depend on SearchClient providing statistics
            // For now, return basic health status
            boolean isHealthy = isSearchServiceHealthy();
            return String.format("Search Service Status: %s", isHealthy ? "Healthy" : "Unhealthy");
        } catch (Exception e) {
            log.error("Error getting search service statistics", e);
            return "Search service statistics unavailable";
        }
    }

    /**
     * Creates a SearchCategoryDocument from a Category entity.
     * Following Clean Code principle: Extract method for better readability.
     */
    private SearchCategoryDocument createSearchDocument(Category category) {
        // Convert Map<LanguageCode, String> to Map<String, String>
        Map<String, String> translationsMap = category.getTranslations().entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().name(),
                        Map.Entry::getValue
                ));

        return SearchCategoryDocument.builder()
                .id(category.getId().toString())
                .translations(translationsMap)
                .type(category.getType().name())
                .level(category.getLevel())
                .parentId(category.getParent() != null ? category.getParent().getId() : null)
                .build();
    }

    /**
     * Creates a SearchCategoryDocument from a CategoryDTO.
     * Following Clean Code principle: Extract method for better readability.
     */
    private SearchCategoryDocument createSearchDocument(CategoryDTO categoryDTO) {
        // Convert Map<LanguageCode, String> to Map<String, String>
        Map<String, String> translationsMap = categoryDTO.getTranslations().entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().name(),
                        Map.Entry::getValue
                ));

        return SearchCategoryDocument.builder()
                .id(categoryDTO.getId().toString())
                .translations(translationsMap)
                .type(categoryDTO.getType().name())
                .level(categoryDTO.getLevel())
                .parentId(categoryDTO.getParentId())
                .build();
    }
}
