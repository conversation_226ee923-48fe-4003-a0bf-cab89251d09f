package com.lookforx.categoryservice.service.impl;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.categoryservice.domain.api.request.CreateCategoryRequest;
import com.lookforx.categoryservice.domain.api.request.UpdateCategoryRequest;
import com.lookforx.categoryservice.domain.api.response.SimpleCategoryResponse;
import com.lookforx.categoryservice.domain.dto.CategoryDTO;
import com.lookforx.categoryservice.domain.mapper.CategoryMapper;
import com.lookforx.categoryservice.repository.CategoryRepository;
import com.lookforx.categoryservice.service.CategoryService;

import com.lookforx.categoryservice.service.CategorySearchIntegrationService;
import com.lookforx.categoryservice.service.CategoryValidationService;
import com.lookforx.categoryservice.service.builder.SimpleCategoryResponseBuilder;
import com.lookforx.categoryservice.service.factory.CategoryNameResolverFactory;
import com.lookforx.categoryservice.web.handler.exception.CategoryNotFoundException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.*;
import java.util.Set;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * Implementation of CategoryService following Effective Java principles.
 *
 * Key improvements:
 * - Immutable constants for cache keys (Item 17)
 * - Proper parameter validation (Item 49)
 * - Defensive copying where needed (Item 50)
 * - Consistent error handling (Item 72)
 * - Method composition for better readability (Item 67)
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CategoryServiceImpl implements CategoryService {

    // Immutable constants for cache keys - Following Effective Java Item 17
    private static final String CACHE_KEY_ALL_CATEGORIES = "all_categories";
    private static final String CACHE_KEY_ALL_CATEGORIES_FLAT = "all_categories_flat";
    private static final String CACHE_KEY_ROOT_CATEGORIES_PRODUCT = "root_categories_PRODUCT";
    private static final String CACHE_KEY_ROOT_CATEGORIES_SERVICE = "root_categories_SERVICE";
    private static final String CACHE_KEY_CATEGORY_PREFIX = "category_";

    // Default sort order for consistent results
    private static final Sort DEFAULT_SORT = Sort.by(Sort.Direction.ASC, "level", "id");

    @Value("${cache.categories.timeout:3600}")
    private long cacheTimeout;

    // Final fields for immutability - Following Effective Java Item 17
    private final CategoryRepository categoryRepository;
    private final CategoryMapper categoryMapper;

    private final CategorySearchIntegrationService searchIntegrationService;
    private final CategoryValidationService validationService;
    private final RedisTemplate<String, Object> redisTemplate;
    private final ObjectMapper objectMapper;
    private final CacheManager cacheManager;

    // Design Pattern dependencies - Clean Code: Dependency injection
    private final SimpleCategoryResponseBuilder simpleCategoryResponseBuilder;
    private final CategoryNameResolverFactory nameResolverFactory;

    /**
     * Retrieves categories filtered by parent ID and type.
     * If parentId is null, returns root categories.
     * Results are sorted by level (ascending) then by ID (ascending).
     *
     * Following Effective Java Item 49: Check parameters for validity
     *
     * @param parentId the ID of the parent category (optional)
     * @param type     the category type (PRODUCT or SERVICE), must not be null
     * @return an immutable list of {@link CategoryDTO} matching the criteria
     * @throws IllegalArgumentException if type is null
     */
    @Override
    @Transactional(readOnly = true)
    public List<CategoryDTO> getCategoriesByParentAndType(Long parentId, CategoryType type) {
        Objects.requireNonNull(type, "Category type cannot be null");

        List<Category> categories = findCategoriesByParentAndType(parentId, type);

        return categories.stream()
                .map(categoryMapper::toDTO)
                .collect(Collectors.toUnmodifiableList());
    }

    /**
     * Creates a new category with proper validation and error handling.
     *
     * Following Effective Java principles:
     * - Item 49: Check parameters for validity
     * - Item 72: Favor the use of standard exceptions
     * - Item 67: Optimize judiciously (separate concerns)
     *
     * @param request the request containing the new category data, must not be null
     * @return the saved category as a {@link CategoryDTO}
     * @throws IllegalArgumentException if request is null
     * @throws ParentCategoryNotFoundException if the specified parentId does not exist
     */
    @Override
    @Transactional
    @CacheEvict(value = {"categories", "categoriesByType", "rootCategories", "categoryHierarchy"}, allEntries = true)
    public CategoryDTO createCategory(CreateCategoryRequest request) {
        log.info("Creating new category with type: {}", request != null ? request.getType() : "null");

        // Validation - delegated to validation service (SRP)
        validationService.validateCreateRequest(request);

        // Business logic - core responsibility
        Category parent = getParentCategoryIfExists(request.getParentId());
        Category toSave = categoryMapper.toEntity(request, parent);
        Category saved = categoryRepository.save(toSave);
        CategoryDTO dto = categoryMapper.toDTO(saved);

        // Cache is automatically invalidated by @CacheEvict annotation

        // TODO: Re-enable search indexing when Elasticsearch is stable
        // Temporarily disabled due to Elasticsearch timeout issues
        log.info("Search indexing temporarily disabled for category ID: {}", saved.getId());

        /*
        // Synchronous search indexing - PostgreSQL and Elasticsearch parallel write
        try {
            log.debug("Attempting to index category ID: {} in search service", saved.getId());
            searchIntegrationService.indexCategory(saved);
            log.debug("Successfully indexed category ID: {} in search service", saved.getId());
        } catch (Exception e) {
            log.error("Search indexing failed for category ID: {} - Error: {}. Rolling back transaction.",
                    saved.getId(), e.getMessage());
            throw new RuntimeException("Failed to index category in search service: " + e.getMessage(), e);
        }
        */

        // Clear manual cache after category creation
        clearManualCache();

        log.info("Successfully created category with ID: {}", saved.getId());
        return dto;
    }

    /**
     * Helper method to find categories by parent and type.
     * Following Effective Java Item 67: Optimize judiciously - extract common logic.
     */
    private List<Category> findCategoriesByParentAndType(Long parentId, CategoryType type) {
        return (parentId != null)
                ? categoryRepository.findByParentIdAndType(parentId, type, DEFAULT_SORT)
                : categoryRepository.findByParentIsNullAndType(type, DEFAULT_SORT);
    }

    /**
     * Helper method to get parent category if exists.
     * Following Clean Code principle: Descriptive naming.
     */
    private Category getParentCategoryIfExists(Long parentId) {
        if (parentId == null) {
            return null;
        }
        return categoryRepository.findById(parentId)
                .orElseThrow(() -> new CategoryNotFoundException(parentId));
    }



    /**
     * Updates an existing category with proper validation and error handling.
     *
     * Following Effective Java principles:
     * - Item 49: Check parameters for validity
     * - Item 67: Optimize judiciously (separate concerns)
     * - Item 72: Favor the use of standard exceptions
     *
     * @param categoryId the ID of the category to update, must not be null
     * @param request    the request containing updated data, must not be null
     * @return the updated category as a {@link CategoryDTO}
     * @throws IllegalArgumentException if categoryId or request is null
     * @throws CategoryNotFoundException if the categoryId does not exist
     * @throws ParentCategoryNotFoundException if the specified parentId does not exist
     */
    @Override
    @Transactional
    @CacheEvict(value = {"categories", "categoriesByType", "rootCategories", "categoryHierarchy"}, allEntries = true)
    public CategoryDTO updateCategory(Long categoryId, UpdateCategoryRequest request) {
        log.info("Updating category with ID: {}", categoryId);

        // Validation - delegated to validation service (SRP)
        validationService.validateUpdateRequest(categoryId, request);

        // Business logic - core responsibility
        Category existing = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new CategoryNotFoundException(categoryId));

        Category parent = getParentCategoryIfExists(request.getParentId());
        categoryMapper.updateEntity(request, existing, parent);
        Category updated = categoryRepository.save(existing);
        CategoryDTO dto = categoryMapper.toDTO(updated);

        // Cache is automatically invalidated by @CacheEvict annotation

        // Synchronous search indexing - PostgreSQL and Elasticsearch parallel write
        try {
            searchIntegrationService.updateCategoryIndex(updated);
            log.debug("Successfully updated search index for category ID: {}", categoryId);
        } catch (Exception e) {
            log.error("Search index update failed for category ID: {} - Error: {}. Rolling back transaction.",
                    categoryId, e.getMessage());
            throw new RuntimeException("Failed to update category in search service: " + e.getMessage(), e);
        }

        // Clear manual cache after category update
        clearManualCache();

        log.info("Successfully updated category with ID: {}", categoryId);
        return dto;
    }



    /**
     * Deletes a category by its ID with proper validation.
     *
     * Following Effective Java principles:
     * - Item 49: Check parameters for validity
     * - Item 67: Optimize judiciously (separate concerns)
     * - Item 72: Favor the use of standard exceptions
     *
     * @param categoryId the ID of the category to delete, must not be null
     * @throws IllegalArgumentException if categoryId is null
     * @throws CategoryNotFoundException if the categoryId does not exist
     * @throws CategoryHasSubcategoriesException if the category has child categories
     */
    @Override
    @Transactional
    @CacheEvict(value = {"categories", "categoriesByType", "rootCategories", "categoryHierarchy"}, allEntries = true)
    public void deleteCategory(Long categoryId) {
        log.info("Deleting category with ID: {}", categoryId);

        // Validation - delegated to validation service (SRP)
        validationService.validateDeleteOperation(categoryId);

        // Business logic - core responsibility
        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new CategoryNotFoundException(categoryId));

        categoryRepository.delete(category);

        // Cache is automatically invalidated by @CacheEvict annotation

        // Synchronous search index removal - PostgreSQL and Elasticsearch parallel write
        try {
            searchIntegrationService.removeCategoryFromIndex(categoryId);
            log.debug("Successfully removed category ID: {} from search index", categoryId);
        } catch (Exception e) {
            log.error("Search index removal failed for category ID: {} - Error: {}. Rolling back transaction.",
                    categoryId, e.getMessage());
            throw new RuntimeException("Failed to remove category from search service: " + e.getMessage(), e);
        }

        // Clear manual cache after category deletion
        clearManualCache();

        log.info("Successfully deleted category with ID: {}", categoryId);
    }



    /**
     * Retrieves all categories with caching support.
     *
     * Following Effective Java principles:
     * - Item 67: Optimize judiciously (caching for performance)
     * - Item 50: Make defensive copies when needed
     *
     * @return an immutable list of all categories as {@link CategoryDTO}
     */
    @Override
    @Transactional(readOnly = true)
    public List<CategoryDTO> getAllCategories() {
        return retrieveCategoriesWithCaching(CACHE_KEY_ALL_CATEGORIES, this::fetchAllCategoriesFromDatabase);
    }

    /**
     * Retrieves a single category by its ID with caching support.
     *
     * Following Effective Java principles:
     * - Item 49: Check parameters for validity
     * - Item 67: Optimize judiciously (caching for performance)
     *
     * @param categoryId the ID of the category to retrieve, must not be null
     * @return the category as a {@link CategoryDTO}
     * @throws IllegalArgumentException if categoryId is null
     * @throws CategoryNotFoundException if the categoryId does not exist
     */
    @Override
    @Transactional(readOnly = true)
    public CategoryDTO getCategoryById(Long categoryId) {
        Objects.requireNonNull(categoryId, "Category ID cannot be null");

        String cacheKey = CACHE_KEY_CATEGORY_PREFIX + categoryId;

        return retrieveCategoryWithCaching(cacheKey, () -> fetchCategoryFromDatabase(categoryId));
    }

    /**
     * Retrieves categories from cache or database using Template Method pattern.
     * Clean Code: Meaningful names, single responsibility, proper error handling.
     */
    private List<CategoryDTO> retrieveCategoriesWithCaching(String cacheKey,
                                                           java.util.function.Supplier<List<CategoryDTO>> databaseSupplier) {
        Optional<List<CategoryDTO>> cachedCategories = tryRetrieveFromCache(cacheKey);
        if (cachedCategories.isPresent()) {
            return cachedCategories.get();
        }

        List<CategoryDTO> categoriesFromDatabase = databaseSupplier.get();
        storeCategoriesInCache(cacheKey, categoriesFromDatabase);
        return Collections.unmodifiableList(categoriesFromDatabase);
    }

    /**
     * Attempts to retrieve categories from cache.
     * Clean Code: Single responsibility, meaningful return type.
     */
    private Optional<List<CategoryDTO>> tryRetrieveFromCache(String cacheKey) {
        try {
            String cachedJsonValue = (String) redisTemplate.opsForValue().get(cacheKey);
            if (cachedJsonValue != null) {
                TypeReference<List<CategoryDTO>> categoryListType = new TypeReference<List<CategoryDTO>>() {};
                List<CategoryDTO> deserializedCategories = objectMapper.readValue(cachedJsonValue, categoryListType);
                return Optional.of(Collections.unmodifiableList(deserializedCategories));
            }
        } catch (Exception cacheReadException) {
            log.warn("Failed to read categories from cache for key {}: {}", cacheKey, cacheReadException.getMessage());
        }
        return Optional.empty();
    }

    /**
     * Retrieves single category from cache or database using Template Method pattern.
     * Clean Code: Meaningful names, single responsibility, proper error handling.
     */
    private CategoryDTO retrieveCategoryWithCaching(String cacheKey,
                                                   java.util.function.Supplier<CategoryDTO> databaseSupplier) {
        Optional<CategoryDTO> cachedCategory = tryRetrieveSingleCategoryFromCache(cacheKey);
        if (cachedCategory.isPresent()) {
            return cachedCategory.get();
        }

        CategoryDTO categoryFromDatabase = databaseSupplier.get();
        storeSingleCategoryInCache(cacheKey, categoryFromDatabase);
        return categoryFromDatabase;
    }

    /**
     * Attempts to retrieve single category from cache.
     * Clean Code: Single responsibility, meaningful return type.
     */
    private Optional<CategoryDTO> tryRetrieveSingleCategoryFromCache(String cacheKey) {
        try {
            String cachedJsonValue = (String) redisTemplate.opsForValue().get(cacheKey);
            if (cachedJsonValue != null) {
                CategoryDTO deserializedCategory = objectMapper.readValue(cachedJsonValue, CategoryDTO.class);
                return Optional.of(deserializedCategory);
            }
        } catch (Exception cacheReadException) {
            log.warn("Failed to read category from cache for key {}: {}", cacheKey, cacheReadException.getMessage());
        }
        return Optional.empty();
    }

    /**
     * Helper method to fetch all categories from database.
     */
    private List<CategoryDTO> fetchAllCategoriesFromDatabase() {
        return categoryRepository.findAll()
                .stream()
                .map(categoryMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Helper method to fetch category from database.
     */
    private CategoryDTO fetchCategoryFromDatabase(Long categoryId) {
        Category category = categoryRepository.findById(categoryId)
                .orElseThrow(() -> new CategoryNotFoundException(categoryId));
        return categoryMapper.toDTO(category);
    }

    /**
     * Stores categories list in cache with proper error handling.
     * Clean Code: Meaningful names, single responsibility.
     */
    private void storeCategoriesInCache(String cacheKey, List<CategoryDTO> categories) {
        try {
            String serializedCategories = objectMapper.writeValueAsString(categories);
            redisTemplate.opsForValue().set(cacheKey, serializedCategories, Duration.ofSeconds(cacheTimeout));
        } catch (Exception serializationException) {
            log.warn("Failed to store categories in cache for key {}: {}", cacheKey, serializationException.getMessage());
        }
    }

    /**
     * Stores single category in cache with proper error handling.
     * Clean Code: Meaningful names, single responsibility.
     */
    private void storeSingleCategoryInCache(String cacheKey, CategoryDTO category) {
        try {
            String serializedCategory = objectMapper.writeValueAsString(category);
            redisTemplate.opsForValue().set(cacheKey, serializedCategory, Duration.ofSeconds(cacheTimeout));
        } catch (Exception serializationException) {
            log.warn("Failed to store category in cache for key {}: {}", cacheKey, serializationException.getMessage());
        }
    }

    /**
     * Retrieves all categories as a flat list (no hierarchical structure).
     *
     * Following Effective Java Item 50: Make defensive copies when needed
     *
     * @return an immutable flat list of all categories as {@link CategoryDTO}
     */
    @Override
    @Transactional(readOnly = true)
    public List<CategoryDTO> getAllCategoriesFlat() {
        return retrieveCategoriesWithCaching(CACHE_KEY_ALL_CATEGORIES_FLAT, this::fetchAllCategoriesFlatFromDatabase);
    }

    /**
     * Helper method to fetch all categories flat from database.
     */
    private List<CategoryDTO> fetchAllCategoriesFlatFromDatabase() {
        return categoryRepository.findAll()
                .stream()
                .map(categoryMapper::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * Retrieves root categories (parent is null) by type.
     * This method returns only the main categories without their subcategories.
     *
     * Following Effective Java principles:
     * - Item 49: Check parameters for validity
     * - Item 50: Make defensive copies when needed
     *
     * @param type the category type (PRODUCT or SERVICE), must not be null
     * @return an immutable list of root categories as {@link CategoryDTO}
     * @throws IllegalArgumentException if type is null
     */
    @Override
    @Transactional(readOnly = true)
    public List<CategoryDTO> getRootCategoriesByType(CategoryType type) {
        Objects.requireNonNull(type, "Category type cannot be null");

        String cacheKey = "root_categories_" + type.name();
        return retrieveCategoriesWithCaching(cacheKey, () -> fetchRootCategoriesFromDatabase(type));
    }

    /**
     * Helper method to fetch root categories from database.
     */
    private List<CategoryDTO> fetchRootCategoriesFromDatabase(CategoryType type) {
        List<Category> rootCategories = categoryRepository.findByParentIsNullAndType(type, DEFAULT_SORT);

        return rootCategories.stream()
                .map(this::createRootCategoryDTO)
                .collect(Collectors.toList());
    }

    /**
     * Helper method to create root category DTO without subcategories.
     * Following Effective Java Item 67: Optimize judiciously - extract method for clarity.
     */
    private CategoryDTO createRootCategoryDTO(Category category) {
        return CategoryDTO.builder()
                .id(category.getId())
                .parentId(null)
                .translations(category.getTranslations())
                .type(category.getType())
                .level(category.getLevel())
                .subcategoryDTOs(Collections.emptySet()) // No subcategories for root list
                .createdBy(category.getCreatedBy())
                .createdAt(category.getCreatedAt())
                .updatedAt(category.getUpdatedAt())
                .build();
    }



    /**
     * Get simplified categories for form dropdowns with optional filtering
     * @param language Language code for category names
     * @param type Optional category type filter
     * @param search Optional search term for category names
     * @return List of simplified category responses
     */
    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "simpleCategories", key = "#language.name() + '_' + (#type != null ? #type.name() : 'ALL') + '_' + (#search != null ? #search.toLowerCase() : '')")
    public List<SimpleCategoryResponse> getSimpleCategories(LanguageCode language, CategoryType type, String search) {
        log.info("Retrieving simple categories for language: {}, type: {}, search: '{}'",
                language, type, search);

        List<Category> categoriesFromDatabase = fetchCategoriesByTypeFilter(type);
        List<SimpleCategoryResponse> simpleCategoryResponses = simpleCategoryResponseBuilder.buildListFrom(categoriesFromDatabase, language);
        List<SimpleCategoryResponse> filteredAndSortedResponses = applySearchFilterAndSorting(simpleCategoryResponses, search);

        log.info("Returning {} simple categories after filtering and sorting", filteredAndSortedResponses.size());
        return filteredAndSortedResponses;
    }

    /**
     * Fetches categories from database based on type filter.
     * Clean Code: Descriptive method name, single responsibility.
     */
    private List<Category> fetchCategoriesByTypeFilter(CategoryType type) {
        if (type != null) {
            return categoryRepository.findAll().stream()
                    .filter(category -> category.getType() == type)
                    .collect(Collectors.toList());
        }
        return categoryRepository.findAll();
    }

    /**
     * Applies search filtering and sorting to simple category responses.
     * Clean Code: Descriptive method name, single responsibility.
     */
    private List<SimpleCategoryResponse> applySearchFilterAndSorting(List<SimpleCategoryResponse> responses, String search) {
        return responses.stream()
                .filter(response -> matchesSearchCriteria(response, search))
                .sorted(this::compareSimpleCategoryResponses)
                .collect(Collectors.toList());
    }

    /**
     * Checks if a category response matches the search criteria.
     * Clean Code: Meaningful method name, single responsibility.
     */
    private boolean matchesSearchCriteria(SimpleCategoryResponse response, String search) {
        if (search == null || search.trim().isEmpty()) {
            return true;
        }

        String searchLowerCase = search.toLowerCase();
        return response.getName().toLowerCase().contains(searchLowerCase) ||
               response.getFullPath().toLowerCase().contains(searchLowerCase);
    }

    /**
     * Compares two SimpleCategoryResponse objects for sorting.
     * Clean Code: Descriptive method name, single responsibility.
     * Sort by level first, then by name.
     */
    private int compareSimpleCategoryResponses(SimpleCategoryResponse first, SimpleCategoryResponse second) {
        int levelComparison = first.getLevel().compareTo(second.getLevel());
        if (levelComparison != 0) {
            return levelComparison;
        }
        return first.getName().compareTo(second.getName());
    }

    // Removed toSimpleCategoryResponse and buildFullPath methods
    // These are now handled by SimpleCategoryResponseBuilder using Builder pattern
    // Clean Code: Eliminated code duplication, single responsibility

    /**
     * Sync all categories to search service
     */
    @Override
    public void syncAllCategoriesToSearch() {
        log.info("Delegating sync operation to search integration service");
        searchIntegrationService.syncAllCategoriesToSearch();
    }

    @Override
    public void clearAllCache() {
        log.info("Starting manual cache clear operation");

        try {
            // Clear manual cache keys
            List<String> manualCacheKeys = Arrays.asList(
                CACHE_KEY_ALL_CATEGORIES,
                CACHE_KEY_ALL_CATEGORIES_FLAT,
                CACHE_KEY_ROOT_CATEGORIES_PRODUCT,
                CACHE_KEY_ROOT_CATEGORIES_SERVICE,
                "root_categories_PRODUCT",
                "root_categories_SERVICE"
            );

            int clearedCount = 0;
            for (String key : manualCacheKeys) {
                if (redisTemplate.hasKey(key)) {
                    redisTemplate.delete(key);
                    clearedCount++;
                    log.info("Cleared manual cache key: {}", key);
                }
            }

            // Clear all cache keys with category prefix
            Set<String> categoryKeys = redisTemplate.keys(CACHE_KEY_CATEGORY_PREFIX + "*");
            if (categoryKeys != null && !categoryKeys.isEmpty()) {
                redisTemplate.delete(categoryKeys);
                clearedCount += categoryKeys.size();
                log.info("Cleared {} category prefix cache keys", categoryKeys.size());
            }

            log.info("Total manual cache keys cleared: {}", clearedCount);

            // Also clear Spring Cache Manager caches
            cacheManager.getCacheNames().forEach(cacheName -> {
                Cache cache = cacheManager.getCache(cacheName);
                if (cache != null) {
                    cache.clear();
                    log.info("Cleared Spring cache: {}", cacheName);
                }
            });

            log.info("All category cache cleared successfully");
        } catch (Exception e) {
            log.error("Failed to clear cache: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to clear cache", e);
        }
    }

    /**
     * Helper method to clear manual cache keys after CRUD operations
     */
    private void clearManualCache() {
        try {
            // Clear manual cache keys
            List<String> manualCacheKeys = Arrays.asList(
                CACHE_KEY_ALL_CATEGORIES,
                CACHE_KEY_ALL_CATEGORIES_FLAT,
                CACHE_KEY_ROOT_CATEGORIES_PRODUCT,
                CACHE_KEY_ROOT_CATEGORIES_SERVICE,
                "root_categories_PRODUCT",
                "root_categories_SERVICE"
            );

            for (String key : manualCacheKeys) {
                if (redisTemplate.hasKey(key)) {
                    redisTemplate.delete(key);
                    log.debug("Cleared manual cache key: {}", key);
                }
            }

            // Clear all cache keys with category prefix
            Set<String> categoryKeys = redisTemplate.keys(CACHE_KEY_CATEGORY_PREFIX + "*");
            if (categoryKeys != null && !categoryKeys.isEmpty()) {
                redisTemplate.delete(categoryKeys);
                log.debug("Cleared {} category prefix cache keys", categoryKeys.size());
            }

            log.debug("Manual cache cleared successfully");
        } catch (Exception e) {
            log.warn("Failed to clear manual cache: {}", e.getMessage());
            // Don't throw exception - CRUD operation should succeed even if cache clearing fails
        }
    }
}
