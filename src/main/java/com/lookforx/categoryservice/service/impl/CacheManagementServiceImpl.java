package com.lookforx.categoryservice.service.impl;

import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.categoryservice.service.CacheManagementService;
import com.lookforx.common.enums.LanguageCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * Implementation of cache management service for categories.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles cache operations
 * - Open/Closed: Can be extended with new cache strategies
 * - Dependency Inversion: Depends on abstractions (CacheManager, RedisTemplate)
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheManagementServiceImpl implements CacheManagementService {

    private final RedisTemplate<String, Object> redisTemplate;
    private final CacheManager cacheManager;

    // Cache key constants - following Clean Code naming conventions
    private static final String ALL_CATEGORIES_CACHE_KEY = "all_categories";
    private static final String ALL_CATEGORIES_FLAT_CACHE_KEY = "all_categories_flat";
    private static final String ROOT_CATEGORIES_PREFIX = "root_categories_";
    private static final String SIMPLE_CATEGORIES_CACHE_NAME = "simpleCategories";
    private static final String CATEGORIES_BY_PARENT_TYPE_CACHE_NAME = "categoriesByParentAndType";

    @Override
    public void invalidateAllCategoryCache() {
        log.info("Invalidating all category cache entries");
        
        try {
            // Clear Redis cache keys
            redisTemplate.delete(ALL_CATEGORIES_CACHE_KEY);
            redisTemplate.delete(ALL_CATEGORIES_FLAT_CACHE_KEY);
            
            // Clear root categories for all types
            for (CategoryType type : CategoryType.values()) {
                redisTemplate.delete(ROOT_CATEGORIES_PREFIX + type.name());
            }
            
            // Clear Spring Cache Manager caches
            clearSpringCaches();
            
            log.info("Successfully invalidated all category cache entries");
        } catch (Exception e) {
            log.error("Error invalidating category cache", e);
            throw new RuntimeException("Failed to invalidate category cache", e);
        }
    }

    @Override
    public void invalidateCacheByType(CategoryType type) {
        log.info("Invalidating cache for category type: {}", type);
        
        try {
            // Clear type-specific cache
            redisTemplate.delete(ROOT_CATEGORIES_PREFIX + type.name());
            
            // Clear general caches that might contain this type
            redisTemplate.delete(ALL_CATEGORIES_CACHE_KEY);
            redisTemplate.delete(ALL_CATEGORIES_FLAT_CACHE_KEY);
            
            // Clear Spring caches
            clearSpringCaches();
            
            log.info("Successfully invalidated cache for category type: {}", type);
        } catch (Exception e) {
            log.error("Error invalidating cache for type: {}", type, e);
            throw new RuntimeException("Failed to invalidate cache for type: " + type, e);
        }
    }

    @Override
    public void invalidateSimpleCategoriesCache(LanguageCode language, CategoryType type, String search) {
        log.info("Invalidating simple categories cache for language: {}, type: {}, search: {}", 
                language, type, search);
        
        try {
            var cache = cacheManager.getCache(SIMPLE_CATEGORIES_CACHE_NAME);
            if (cache != null) {
                // Build cache key same as in service
                String cacheKey = language.name() + "_" + 
                                (type != null ? type.name() : "ALL") + "_" + 
                                (search != null ? search.toLowerCase() : "");
                cache.evict(cacheKey);
            }
            
            log.info("Successfully invalidated simple categories cache");
        } catch (Exception e) {
            log.error("Error invalidating simple categories cache", e);
            throw new RuntimeException("Failed to invalidate simple categories cache", e);
        }
    }

    @Override
    public void clearAllCache() {
        log.info("Clearing all cache entries in the system");
        
        try {
            // Clear all Redis keys with pattern matching
            Set<String> keys = redisTemplate.keys("*");
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("Cleared {} Redis cache keys", keys.size());
            }
            
            // Clear all Spring caches
            cacheManager.getCacheNames().forEach(cacheName -> {
                var cache = cacheManager.getCache(cacheName);
                if (cache != null) {
                    cache.clear();
                }
            });
            
            log.info("Successfully cleared all cache entries");
        } catch (Exception e) {
            log.error("Error clearing all cache", e);
            throw new RuntimeException("Failed to clear all cache", e);
        }
    }

    @Override
    public void warmUpCache() {
        log.info("Warming up category cache");
        // Implementation would depend on specific warming strategy
        // This is a placeholder for future implementation
        log.info("Cache warm-up completed");
    }

    @Override
    public String getCacheStatistics() {
        try {
            Set<String> keys = redisTemplate.keys("*");
            int keyCount = keys != null ? keys.size() : 0;
            
            return String.format("Cache Statistics: Total Redis keys: %d, Active caches: %s", 
                    keyCount, cacheManager.getCacheNames());
        } catch (Exception e) {
            log.error("Error getting cache statistics", e);
            return "Cache statistics unavailable";
        }
    }

    /**
     * Helper method to clear Spring Cache Manager caches.
     * Following Clean Code principle: Extract method for better readability.
     */
    private void clearSpringCaches() {
        var simpleCategoriesCache = cacheManager.getCache(SIMPLE_CATEGORIES_CACHE_NAME);
        if (simpleCategoriesCache != null) {
            simpleCategoriesCache.clear();
        }
        
        var categoriesByParentTypeCache = cacheManager.getCache(CATEGORIES_BY_PARENT_TYPE_CACHE_NAME);
        if (categoriesByParentTypeCache != null) {
            categoriesByParentTypeCache.clear();
        }
    }
}
