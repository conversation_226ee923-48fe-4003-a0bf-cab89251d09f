package com.lookforx.categoryservice.service.impl;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.api.request.CreateCategoryRequest;
import com.lookforx.categoryservice.domain.api.request.UpdateCategoryRequest;
import com.lookforx.categoryservice.repository.CategoryRepository;
import com.lookforx.categoryservice.service.CategoryValidationService;
import com.lookforx.categoryservice.web.handler.exception.CategoryHasSubcategoriesException;
import com.lookforx.categoryservice.web.handler.exception.CategoryNotFoundException;
import com.lookforx.categoryservice.web.handler.exception.ParentCategoryNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Implementation of category validation service.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles validation logic
 * - Open/Closed: Can be extended with new validation rules
 * - Dependency Inversion: Depends on abstractions (CategoryRepository)
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CategoryValidationServiceImpl implements CategoryValidationService {

    private final CategoryRepository categoryRepository;

    // Business rule constants - following Clean Code naming conventions
    private static final int MAX_HIERARCHY_DEPTH = 5;
    private static final int MIN_CATEGORY_LEVEL = 1;
    private static final int MAX_CATEGORY_LEVEL = 10;

    @Override
    public void validateCreateRequest(CreateCategoryRequest request) {
        log.debug("Validating create category request");
        
        if (request == null) {
            throw new IllegalArgumentException("Create category request cannot be null");
        }

        validateTranslations(request.getTranslations());
        validateCategoryType(request.getType());
        validateCategoryLevel(request.getLevel());
        
        if (request.getParentId() != null) {
            validateParentCategoryExists(request.getParentId());
            validateParentChildRelationship(request.getParentId(), request.getLevel());
        }

        log.debug("Create category request validation passed");
    }

    @Override
    public void validateUpdateRequest(Long categoryId, UpdateCategoryRequest request) {
        log.debug("Validating update category request for ID: {}", categoryId);
        
        if (categoryId == null) {
            throw new IllegalArgumentException("Category ID cannot be null");
        }
        
        if (request == null) {
            throw new IllegalArgumentException("Update category request cannot be null");
        }

        validateCategoryExists(categoryId);
        validateTranslations(request.getTranslations());
        validateCategoryType(request.getType());
        validateCategoryLevel(request.getLevel());

        if (request.getParentId() != null) {
            validateNotSelfParent(categoryId, request.getParentId());
            validateParentCategoryExists(request.getParentId());
            validateParentChildRelationship(request.getParentId(), request.getLevel());
        }

        log.debug("Update category request validation passed for ID: {}", categoryId);
    }

    @Override
    public void validateDeleteOperation(Long categoryId) {
        log.debug("Validating delete operation for category ID: {}", categoryId);
        
        if (categoryId == null) {
            throw new IllegalArgumentException("Category ID cannot be null");
        }

        validateCategoryExists(categoryId);
        
        // Check if category has subcategories
        List<Category> subcategories = categoryRepository.findByParentId(categoryId);
        if (!subcategories.isEmpty()) {
            log.warn("Cannot delete category with subcategories: ID = {}", categoryId);
            throw new CategoryHasSubcategoriesException(categoryId);
        }

        log.debug("Delete operation validation passed for category ID: {}", categoryId);
    }

    @Override
    public void validateParentChildRelationship(Long parentId, Integer childLevel) {
        if (parentId == null) {
            // Root category - no parent validation needed
            return;
        }

        log.debug("Validating parent-child relationship: parent={}, childLevel={}", parentId, childLevel);
        
        Category parent = categoryRepository.findById(parentId)
                .orElseThrow(() -> new ParentCategoryNotFoundException(parentId));

        // Validate level hierarchy
        if (childLevel != null && parent.getLevel() != null) {
            if (childLevel <= parent.getLevel()) {
                throw new IllegalArgumentException(
                    String.format("Child category level (%d) must be greater than parent level (%d)", 
                                childLevel, parent.getLevel()));
            }
        }

        log.debug("Parent-child relationship validation passed");
    }

    @Override
    public void validateHierarchyDepth(Category category) {
        if (category == null) {
            return;
        }

        log.debug("Validating hierarchy depth for category ID: {}", category.getId());
        
        int depth = calculateCategoryDepth(category);
        if (depth > MAX_HIERARCHY_DEPTH) {
            throw new IllegalArgumentException(
                String.format("Category hierarchy depth (%d) exceeds maximum allowed depth (%d)", 
                            depth, MAX_HIERARCHY_DEPTH));
        }

        log.debug("Hierarchy depth validation passed for category ID: {}", category.getId());
    }

    @Override
    public void validateCategoryExists(Long categoryId) {
        if (categoryId == null) {
            throw new IllegalArgumentException("Category ID cannot be null");
        }

        if (!categoryRepository.existsById(categoryId)) {
            log.warn("Category not found: ID = {}", categoryId);
            throw new CategoryNotFoundException(categoryId);
        }
    }

    @Override
    public void validateParentCategoryExists(Long parentId) {
        if (parentId == null) {
            return; // Null parent is valid (root category)
        }

        if (!categoryRepository.existsById(parentId)) {
            log.warn("Parent category not found: ID = {}", parentId);
            throw new ParentCategoryNotFoundException(parentId);
        }
    }

    @Override
    public void validateBusinessRules(Category category) {
        if (category == null) {
            throw new IllegalArgumentException("Category cannot be null");
        }

        log.debug("Validating business rules for category ID: {}", category.getId());
        
        // Validate translations are not empty
        if (category.getTranslations() == null || category.getTranslations().isEmpty()) {
            throw new IllegalStateException("Category must have at least one translation");
        }

        // Validate category type is set
        if (category.getType() == null) {
            throw new IllegalStateException("Category type cannot be null");
        }

        // Validate level is within bounds
        if (category.getLevel() == null || category.getLevel() < MIN_CATEGORY_LEVEL) {
            throw new IllegalStateException("Category level must be at least " + MIN_CATEGORY_LEVEL);
        }

        log.debug("Business rules validation passed for category ID: {}", category.getId());
    }

    /**
     * Validates category translations.
     * Following Clean Code principle: Extract method for better readability.
     */
    private void validateTranslations(Object translations) {
        if (translations == null) {
            throw new IllegalArgumentException("Category translations cannot be null");
        }
        // Additional translation validation logic can be added here
    }

    /**
     * Validates category type.
     * Following Clean Code principle: Extract method for better readability.
     */
    private void validateCategoryType(Object type) {
        if (type == null) {
            throw new IllegalArgumentException("Category type cannot be null");
        }
    }

    /**
     * Validates category level.
     * Following Clean Code principle: Extract method for better readability.
     */
    private void validateCategoryLevel(Integer level) {
        if (level == null) {
            throw new IllegalArgumentException("Category level cannot be null");
        }
        
        if (level < MIN_CATEGORY_LEVEL || level > MAX_CATEGORY_LEVEL) {
            throw new IllegalArgumentException(
                String.format("Category level must be between %d and %d", 
                            MIN_CATEGORY_LEVEL, MAX_CATEGORY_LEVEL));
        }
    }

    /**
     * Validates that a category is not trying to set itself as parent.
     * Following Clean Code principle: Extract method for better readability.
     */
    private void validateNotSelfParent(Long categoryId, Long parentId) {
        if (categoryId.equals(parentId)) {
            throw new IllegalArgumentException("Category cannot be its own parent");
        }
    }

    /**
     * Calculates the depth of a category in the hierarchy.
     * Following Clean Code principle: Extract method for better readability.
     */
    private int calculateCategoryDepth(Category category) {
        int depth = 1;
        Category current = category;
        
        while (current.getParent() != null) {
            depth++;
            current = current.getParent();
            
            // Prevent infinite loops in case of circular references
            if (depth > MAX_HIERARCHY_DEPTH + 1) {
                throw new IllegalStateException("Circular reference detected in category hierarchy");
            }
        }
        
        return depth;
    }
}
