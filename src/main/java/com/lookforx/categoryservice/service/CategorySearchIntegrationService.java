package com.lookforx.categoryservice.service;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.dto.CategoryDTO;

import java.util.List;

/**
 * Service interface for managing category search integration.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles search service integration
 * - Interface Segregation: Focused interface for search operations
 * - Dependency Inversion: Abstracts search implementation details
 */
public interface CategorySearchIntegrationService {

    /**
     * Indexes a single category in the search service.
     * 
     * @param category the category to index
     */
    void indexCategory(Category category);

    /**
     * Indexes a category DTO in the search service.
     * 
     * @param categoryDTO the category DTO to index
     */
    void indexCategory(CategoryDTO categoryDTO);

    /**
     * Updates a category in the search index.
     * 
     * @param category the category to update
     */
    void updateCategoryIndex(Category category);

    /**
     * Removes a category from the search index.
     * 
     * @param categoryId the ID of the category to remove
     */
    void removeCategoryFromIndex(Long categoryId);

    /**
     * Bulk indexes multiple categories.
     * 
     * @param categories the list of categories to index
     */
    void bulkIndexCategories(List<Category> categories);

    /**
     * Synchronizes all categories with the search service.
     * This method should be used carefully as it can be resource-intensive.
     */
    void syncAllCategoriesToSearch();

    /**
     * Clears all categories from the search index.
     * Used for administrative purposes.
     */
    void clearSearchIndex();

    /**
     * Checks if the search service is available and healthy.
     * 
     * @return true if search service is available, false otherwise
     */
    boolean isSearchServiceHealthy();

    /**
     * Gets search service statistics.
     * 
     * @return search service statistics as a formatted string
     */
    String getSearchServiceStatistics();
}
