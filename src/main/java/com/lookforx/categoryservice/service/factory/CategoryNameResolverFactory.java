package com.lookforx.categoryservice.service.factory;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.service.strategy.CategoryNameResolver;
import com.lookforx.common.enums.LanguageCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Factory for creating appropriate CategoryNameResolver instances.
 * 
 * Design Pattern: Factory Pattern
 * Clean Code: Single responsibility, dependency injection, meaningful names
 * 
 * This factory selects the most appropriate resolver based on the category
 * and language requirements, following the Chain of Responsibility pattern
 * to find the first suitable resolver.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class CategoryNameResolverFactory {
    
    private final List<CategoryNameResolver> availableResolvers;
    
    /**
     * Creates the most appropriate CategoryNameResolver for the given context.
     * 
     * Clean Code: Meaningful method name, single responsibility
     * Design Pattern: Factory Method with Chain of Responsibility
     * 
     * @param category the category that needs name resolution
     * @param requestedLanguage the preferred language
     * @return the most suitable resolver, never null
     * @throws IllegalStateException if no suitable resolver is found
     */
    public CategoryNameResolver createResolverFor(Category category, LanguageCode requestedLanguage) {
        log.debug("Finding resolver for category ID: {} and language: {}", 
                 category != null ? category.getId() : "null", requestedLanguage);
        
        CategoryNameResolver suitableResolver = findSuitableResolver(category, requestedLanguage);
        
        if (suitableResolver == null) {
            String errorMessage = String.format(
                "No suitable CategoryNameResolver found for category ID: %s and language: %s", 
                category != null ? category.getId() : "null", 
                requestedLanguage
            );
            log.error(errorMessage);
            throw new IllegalStateException(errorMessage);
        }
        
        log.debug("Selected resolver: {} for category ID: {}", 
                 suitableResolver.getClass().getSimpleName(), 
                 category != null ? category.getId() : "null");
        
        return suitableResolver;
    }
    
    /**
     * Finds the first suitable resolver using Chain of Responsibility pattern.
     * Clean Code: Descriptive method name, single responsibility.
     */
    private CategoryNameResolver findSuitableResolver(Category category, LanguageCode requestedLanguage) {
        return availableResolvers.stream()
                .filter(resolver -> resolver.canResolve(category, requestedLanguage))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * Returns the number of available resolvers.
     * Useful for monitoring and debugging purposes.
     */
    public int getAvailableResolverCount() {
        return availableResolvers.size();
    }
    
    /**
     * Returns the names of all available resolvers.
     * Useful for monitoring and debugging purposes.
     */
    public List<String> getAvailableResolverNames() {
        return availableResolvers.stream()
                .map(resolver -> resolver.getClass().getSimpleName())
                .toList();
    }
}
