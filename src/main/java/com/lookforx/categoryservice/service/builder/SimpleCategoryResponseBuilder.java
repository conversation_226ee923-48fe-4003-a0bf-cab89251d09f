package com.lookforx.categoryservice.service.builder;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.api.response.SimpleCategoryResponse;
import com.lookforx.categoryservice.service.factory.CategoryNameResolverFactory;
import com.lookforx.categoryservice.service.strategy.CategoryNameResolver;
import com.lookforx.common.enums.LanguageCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * Builder for creating SimpleCategoryResponse objects with proper name resolution.
 * 
 * Design Pattern: Builder Pattern
 * Clean Code: Fluent interface, single responsibility, meaningful names
 * 
 * This builder encapsulates the complex logic of creating SimpleCategoryResponse
 * objects with proper language fallback and path building.
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class SimpleCategoryResponseBuilder {
    
    private final CategoryNameResolverFactory nameResolverFactory;
    
    /**
     * Builds a SimpleCategoryResponse from a Category entity.
     * 
     * Clean Code: Meaningful method name, single responsibility
     * Design Pattern: Builder pattern with fluent interface
     * 
     * @param category the source category entity
     * @param language the preferred language for the response
     * @return a fully constructed SimpleCategoryResponse
     */
    public SimpleCategoryResponse buildFrom(Category category, LanguageCode language) {
        if (category == null) {
            log.warn("Attempted to build SimpleCategoryResponse from null category");
            throw new IllegalArgumentException("Category cannot be null");
        }
        
        if (language == null) {
            log.warn("Attempted to build SimpleCategoryResponse with null language");
            throw new IllegalArgumentException("Language cannot be null");
        }
        
        log.debug("Building SimpleCategoryResponse for category ID: {} in language: {}", 
                 category.getId(), language);
        
        CategoryNameResolver nameResolver = nameResolverFactory.createResolverFor(category, language);
        String resolvedName = nameResolver.resolveCategoryName(category, language);
        String fullPath = buildFullCategoryPath(category, language, nameResolver);
        
        return SimpleCategoryResponse.builder()
                .id(category.getId())
                .name(resolvedName)
                .type(category.getType())
                .level(category.getLevel())
                .fullPath(fullPath)
                .build();
    }
    
    /**
     * Builds a list of SimpleCategoryResponse objects from a list of categories.
     * 
     * Clean Code: Meaningful method name, single responsibility
     * 
     * @param categories the source category entities
     * @param language the preferred language for the responses
     * @return a list of fully constructed SimpleCategoryResponse objects
     */
    public List<SimpleCategoryResponse> buildListFrom(List<Category> categories, LanguageCode language) {
        if (categories == null) {
            log.warn("Attempted to build SimpleCategoryResponse list from null categories");
            return new ArrayList<>();
        }
        
        log.debug("Building {} SimpleCategoryResponse objects in language: {}", 
                 categories.size(), language);
        
        return categories.stream()
                .map(category -> buildFrom(category, language))
                .toList();
    }
    
    /**
     * Builds the full hierarchical path for a category.
     * 
     * Clean Code: Descriptive method name, single responsibility
     * Example: "Electronics > Computers > Laptops"
     * 
     * @param category the category to build path for
     * @param language the preferred language
     * @param nameResolver the resolver to use for name resolution
     * @return the full path string
     */
    private String buildFullCategoryPath(Category category, LanguageCode language, CategoryNameResolver nameResolver) {
        if (category.getParent() == null) {
            return nameResolver.resolveCategoryName(category, language);
        }
        
        List<String> pathComponents = collectPathComponents(category, language, nameResolver);
        return String.join(" > ", pathComponents);
    }
    
    /**
     * Collects all path components from root to the given category.
     * 
     * Clean Code: Descriptive method name, single responsibility
     * 
     * @param category the target category
     * @param language the preferred language
     * @param nameResolver the resolver to use for name resolution
     * @return list of path components from root to target
     */
    private List<String> collectPathComponents(Category category, LanguageCode language, CategoryNameResolver nameResolver) {
        List<String> pathComponents = new ArrayList<>();
        Category currentCategory = category;
        
        while (currentCategory != null) {
            String categoryName = nameResolver.resolveCategoryName(currentCategory, language);
            pathComponents.add(0, categoryName); // Add to beginning for correct order
            currentCategory = currentCategory.getParent();
        }
        
        return pathComponents;
    }
}
