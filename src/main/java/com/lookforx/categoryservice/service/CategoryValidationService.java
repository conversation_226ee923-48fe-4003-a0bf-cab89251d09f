package com.lookforx.categoryservice.service;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.api.request.CreateCategoryRequest;
import com.lookforx.categoryservice.domain.api.request.UpdateCategoryRequest;

/**
 * Service interface for category validation operations.
 *
 * Following SOLID principles:
 * - Single Responsibility: Only handles category validation
 * - Interface Segregation: Focused interface for validation operations
 * - Open/Closed: Can be extended with new validation rules
 */
public interface CategoryValidationService {

    /**
     * Validates a create category request.
     *
     * @param request the create category request to validate
     * @throws IllegalArgumentException if validation fails
     */
    void validateCreateRequest(CreateCategoryRequest request);

    /**
     * Validates an update category request.
     *
     * @param categoryId the ID of the category being updated
     * @param request the update category request to validate
     * @throws IllegalArgumentException if validation fails
     */
    void validateUpdateRequest(Long categoryId, UpdateCategoryRequest request);

    /**
     * Validates if a category can be deleted.
     *
     * @param categoryId the ID of the category to delete
     * @throws IllegalStateException if category cannot be deleted
     */
    void validateDeleteOperation(Long categoryId);

    /**
     * Validates parent-child relationship constraints.
     *
     * @param parentId the parent category ID (can be null)
     * @param childLevel the level of the child category
     * @throws IllegalArgumentException if relationship is invalid
     */
    void validateParentChildRelationship(Long parentId, Integer childLevel);

    /**
     * Validates category hierarchy depth constraints.
     *
     * @param category the category to validate
     * @throws IllegalArgumentException if hierarchy is too deep
     */
    void validateHierarchyDepth(Category category);

    /**
     * Validates if category exists.
     *
     * @param categoryId the category ID to check
     * @throws com.lookforx.categoryservice.web.handler.exception.CategoryNotFoundException if category doesn't exist
     */
    void validateCategoryExists(Long categoryId);

    /**
     * Validates if parent category exists (when parentId is not null).
     *
     * @param parentId the parent category ID to check
     * @throws com.lookforx.categoryservice.web.handler.exception.ParentCategoryNotFoundException if parent doesn't exist
     */
    void validateParentCategoryExists(Long parentId);

    /**
     * Validates business rules for category operations.
     *
     * @param category the category to validate
     * @throws IllegalStateException if business rules are violated
     */
    void validateBusinessRules(Category category);
}
