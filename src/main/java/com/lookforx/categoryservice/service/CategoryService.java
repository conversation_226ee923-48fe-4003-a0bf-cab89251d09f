package com.lookforx.categoryservice.service;

import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.categoryservice.domain.api.request.CreateCategoryRequest;
import com.lookforx.categoryservice.domain.api.request.UpdateCategoryRequest;
import com.lookforx.categoryservice.domain.api.response.SimpleCategoryResponse;
import com.lookforx.categoryservice.domain.dto.CategoryDTO;

import java.util.List;

public interface CategoryService {

    CategoryDTO createCategory(CreateCategoryRequest request);

    CategoryDTO updateCategory(Long categoryId, UpdateCategoryRequest request);

    CategoryDTO getCategoryById(Long categoryId);

    List<CategoryDTO> getCategoriesByParentAndType(Long parentId, CategoryType type);

    void deleteCategory(Long categoryId);

    List<CategoryDTO> getAllCategories();

    List<CategoryDTO> getAllCategoriesFlat();

    List<CategoryDTO> getRootCategoriesByType(CategoryType type);

    /**
     * Get simplified categories for form dropdowns with optional filtering
     * @param language Language code for category names
     * @param type Optional category type filter
     * @param search Optional search term for category names
     * @return List of simplified category responses
     */
    List<SimpleCategoryResponse> getSimpleCategories(LanguageCode language, CategoryType type, String search);

    /**
     * Sync all categories to search service
     */
    void syncAllCategoriesToSearch();

    /**
     * Clear all category cache entries
     */
    void clearAllCache();

}
