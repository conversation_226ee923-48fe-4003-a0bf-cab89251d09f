package com.lookforx.categoryservice.service.strategy;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.common.enums.LanguageCode;

/**
 * Strategy interface for resolving category names in different languages.
 * 
 * Design Pattern: Strategy Pattern
 * Clean Code: Interface segregation, single responsibility
 * 
 * This interface defines the contract for different strategies of resolving
 * category names when the requested language is not available.
 */
public interface CategoryNameResolver {
    
    /**
     * Resolves the category name for the specified language.
     * If the requested language is not available, applies fallback strategy.
     * 
     * @param category the category containing translations
     * @param requestedLanguage the preferred language
     * @return the resolved category name, never null
     */
    String resolveCategoryName(Category category, LanguageCode requestedLanguage);
    
    /**
     * Checks if this resolver can handle the given category and language combination.
     * 
     * @param category the category to check
     * @param requestedLanguage the requested language
     * @return true if this resolver can handle the request
     */
    boolean canResolve(Category category, LanguageCode requestedLanguage);
}
