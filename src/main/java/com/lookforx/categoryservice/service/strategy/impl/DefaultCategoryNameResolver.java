package com.lookforx.categoryservice.service.strategy.impl;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.service.strategy.CategoryNameResolver;
import com.lookforx.common.enums.LanguageCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Default implementation of CategoryNameResolver using fallback strategy.
 * 
 * Design Pattern: Strategy Pattern implementation
 * Clean Code: Single responsibility, meaningful names, proper error handling
 * 
 * Fallback order:
 * 1. Requested language
 * 2. English (EN) as universal fallback
 * 3. First available translation
 * 4. Default "Unknown Category" text
 */
@Component
@Slf4j
public class DefaultCategoryNameResolver implements CategoryNameResolver {
    
    private static final String UNKNOWN_CATEGORY_FALLBACK = "Unknown Category";
    
    @Override
    public String resolveCategoryName(Category category, LanguageCode requestedLanguage) {
        if (category == null || category.getTranslations() == null) {
            log.warn("Category or translations are null, returning fallback name");
            return UNKNOWN_CATEGORY_FALLBACK;
        }
        
        Map<LanguageCode, String> translations = category.getTranslations();
        
        // Strategy 1: Try requested language
        String nameInRequestedLanguage = translations.get(requestedLanguage);
        if (isValidCategoryName(nameInRequestedLanguage)) {
            return nameInRequestedLanguage;
        }
        
        // Strategy 2: Fallback to English
        String nameInEnglish = translations.get(LanguageCode.EN);
        if (isValidCategoryName(nameInEnglish)) {
            log.debug("Using English fallback for category ID: {}", category.getId());
            return nameInEnglish;
        }
        
        // Strategy 3: Use first available translation
        String firstAvailableTranslation = findFirstAvailableTranslation(translations);
        if (firstAvailableTranslation != null) {
            log.debug("Using first available translation for category ID: {}", category.getId());
            return firstAvailableTranslation;
        }
        
        // Strategy 4: Ultimate fallback
        log.warn("No valid translations found for category ID: {}, using fallback", category.getId());
        return UNKNOWN_CATEGORY_FALLBACK;
    }
    
    @Override
    public boolean canResolve(Category category, LanguageCode requestedLanguage) {
        return category != null && 
               category.getTranslations() != null && 
               !category.getTranslations().isEmpty();
    }
    
    /**
     * Validates if a category name is valid (not null and not empty after trimming).
     * Clean Code: Meaningful method name, single responsibility.
     */
    private boolean isValidCategoryName(String categoryName) {
        return categoryName != null && !categoryName.trim().isEmpty();
    }
    
    /**
     * Finds the first available valid translation from the translations map.
     * Clean Code: Descriptive method name, single responsibility.
     */
    private String findFirstAvailableTranslation(Map<LanguageCode, String> translations) {
        return translations.values().stream()
                .filter(this::isValidCategoryName)
                .findFirst()
                .orElse(null);
    }
}
