package com.lookforx.categoryservice.repository;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.CategoryType;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Category entity operations.
 *
 * Following Effective Java principles:
 * - Item 49: Check parameters for validity (using @NonNull)
 * - Item 67: Optimize judiciously (efficient query methods)
 * - Item 72: Favor the use of standard exceptions
 */
@Repository
public interface CategoryRepository extends JpaRepository<Category, Long> {

    /**
     * Finds categories by parent ID and type with sorting.
     *
     * @param parentId the parent category ID, can be null
     * @param type the category type, must not be null
     * @param sort the sort specification, must not be null
     * @return list of categories matching the criteria
     */
    List<Category> findByParentIdAndType(Long parentId, @NonNull CategoryType type, @NonNull Sort sort);

    /**
     * Finds root categories (parent is null) by type with sorting.
     *
     * @param type the category type, must not be null
     * @param sort the sort specification, must not be null
     * @return list of root categories matching the criteria
     */
    List<Category> findByParentIsNullAndType(@NonNull CategoryType type, @NonNull Sort sort);

    /**
     * Finds categories by type only.
     *
     * @param type the category type, must not be null
     * @return list of categories of the specified type
     */
    List<Category> findByType(@NonNull CategoryType type);

    /**
     * Finds categories by level.
     *
     * @param level the category level, must not be null
     * @return list of categories at the specified level
     */
    List<Category> findByLevel(@NonNull Integer level);

    /**
     * Finds categories by parent ID.
     *
     * @param parentId the parent category ID, can be null
     * @return list of categories with the specified parent
     */
    List<Category> findByParentId(Long parentId);

    /**
     * Checks if a category exists by ID.
     * More efficient than findById when only existence check is needed.
     *
     * @param id the category ID, must not be null
     * @return true if category exists, false otherwise
     */
    boolean existsById(@NonNull Long id);

    /**
     * Counts categories by type.
     *
     * @param type the category type, must not be null
     * @return number of categories of the specified type
     */
    long countByType(@NonNull CategoryType type);

    /**
     * Custom query to find categories with their subcategories count.
     * Following Effective Java Item 67: Optimize judiciously - efficient data retrieval.
     *
     * @param type the category type, must not be null
     * @return list of categories with subcategory counts
     */
    @Query("SELECT c FROM Category c LEFT JOIN FETCH c.subcategories WHERE c.type = :type")
    List<Category> findByTypeWithSubcategories(@Param("type") @NonNull CategoryType type);

    /**
     * Custom query to find category by ID with all its relationships loaded.
     * Following Effective Java Item 67: Optimize judiciously - avoid N+1 queries.
     *
     * @param id the category ID, must not be null
     * @return optional category with relationships loaded
     */
    @Query("SELECT c FROM Category c LEFT JOIN FETCH c.subcategories LEFT JOIN FETCH c.parent WHERE c.id = :id")
    Optional<Category> findByIdWithRelationships(@Param("id") @NonNull Long id);
}
