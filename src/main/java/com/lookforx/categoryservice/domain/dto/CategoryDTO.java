package com.lookforx.categoryservice.domain.dto;

import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.common.enums.LanguageCode;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Immutable Data Transfer Object for Category.
 *
 * Following Effective Java principles:
 * - Immutable class design (Item 17)
 * - Defensive copying for mutable fields (Item 50)
 * - Proper equals and hashCode implementation (Item 10, 11)
 * - Builder pattern for object creation (Item 2)
 */
@Getter
@Builder
@AllArgsConstructor
@EqualsAndHashCode
@ToString
public class CategoryDTO {
    private final Long id;
    private final Long parentId;
    @Builder.Default
    private final Map<LanguageCode, String> translations = new HashMap<>();
    private final CategoryType type;
    private final Integer level;
    @Builder.Default
    private final Set<CategoryDTO> subcategoryDTOs = new HashSet<>();

    private final String createdBy;
    private final LocalDateTime createdAt;
    private final LocalDateTime updatedAt;

    /**
     * Default constructor for frameworks that require it.
     * Creates an empty CategoryDTO with default values.
     */
    public CategoryDTO() {
        this.id = null;
        this.parentId = null;
        this.translations = Collections.emptyMap();
        this.type = null;
        this.level = null;
        this.subcategoryDTOs = Collections.emptySet();
        this.createdBy = null;
        this.createdAt = null;
        this.updatedAt = null;
    }

    /**
     * Returns an immutable copy of translations to prevent external modification.
     * Following Effective Java Item 50: Make defensive copies when needed.
     */
    public Map<LanguageCode, String> getTranslations() {
        return translations == null ? Collections.emptyMap() : Collections.unmodifiableMap(translations);
    }

    /**
     * Returns an immutable copy of subcategory DTOs to prevent external modification.
     * Following Effective Java Item 50: Make defensive copies when needed.
     */
    public Set<CategoryDTO> getSubcategoryDTOs() {
        return subcategoryDTOs == null ? Collections.emptySet() : Collections.unmodifiableSet(subcategoryDTOs);
    }

    /**
     * Builder class with defensive copying for mutable fields.
     * Following Effective Java Item 2: Consider a builder when faced with many constructor parameters.
     */
    public static class CategoryDTOBuilder {
        private Long id;
        private Long parentId;
        private Map<LanguageCode, String> translations = new HashMap<>();
        private CategoryType type;
        private Integer level;
        private Set<CategoryDTO> subcategoryDTOs = new HashSet<>();
        private String createdBy;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;

        /**
         * Sets translations with defensive copying.
         */
        public CategoryDTOBuilder translations(Map<LanguageCode, String> translations) {
            if (translations != null) {
                this.translations = new HashMap<>(translations);
            }
            return this;
        }

        /**
         * Sets subcategory DTOs with defensive copying.
         */
        public CategoryDTOBuilder subcategoryDTOs(Set<CategoryDTO> subcategoryDTOs) {
            if (subcategoryDTOs != null) {
                this.subcategoryDTOs = new HashSet<>(subcategoryDTOs);
            }
            return this;
        }

        public CategoryDTOBuilder id(Long id) {
            this.id = id;
            return this;
        }

        public CategoryDTOBuilder parentId(Long parentId) {
            this.parentId = parentId;
            return this;
        }

        public CategoryDTOBuilder type(CategoryType type) {
            this.type = type;
            return this;
        }

        public CategoryDTOBuilder level(Integer level) {
            this.level = level;
            return this;
        }

        public CategoryDTOBuilder createdBy(String createdBy) {
            this.createdBy = createdBy;
            return this;
        }

        public CategoryDTOBuilder createdAt(LocalDateTime createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public CategoryDTOBuilder updatedAt(LocalDateTime updatedAt) {
            this.updatedAt = updatedAt;
            return this;
        }

        /**
         * Validates required fields before building.
         * Following Effective Java Item 49: Check parameters for validity.
         */
        public CategoryDTO build() {
            // Validation can be added here if needed
            return new CategoryDTO(id, parentId,
                translations != null ? new HashMap<>(translations) : new HashMap<>(),
                type, level,
                subcategoryDTOs != null ? new HashSet<>(subcategoryDTOs) : new HashSet<>(),
                createdBy, createdAt, updatedAt);
        }
    }
}
