package com.lookforx.categoryservice.domain;

import com.lookforx.common.entity.BaseEntity;
import com.lookforx.common.enums.LanguageCode;
import jakarta.persistence.*;
import lombok.*;

import java.util.*;

/**
 * Category entity representing a hierarchical category structure.
 *
 * This class follows Effective Java principles:
 * - Immutable collections are returned via defensive copying
 * - Proper equals and hashCode implementation
 * - Builder pattern for object creation
 * - Null safety through validation
 */
@Entity
@Table(name = "categories", indexes = {
        @Index(name = "idx_parent_id", columnList = "parent_id")
})
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public class Category extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    private Category parent;

    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Builder.Default
    private Set<Category> subcategories = new HashSet<>();

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(
            name = "category_translations",
            joinColumns = @JoinColumn(name = "category_id"),
            indexes = {
                    @Index(name = "idx_language_code", columnList = "language_code")
            }
    )
    @MapKeyColumn(name = "language_code")
    @MapKeyEnumerated(EnumType.STRING)
    @Column(name = "category_name")
    @Builder.Default
    private Map<LanguageCode, String> translations = new HashMap<>();

    @Enumerated(EnumType.STRING)
    @Column(name = "category_type", nullable = false)
    @EqualsAndHashCode.Include
    private CategoryType type;

    @Column(name = "level", nullable = false)
    @EqualsAndHashCode.Include
    private Integer level;

    /**
     * Returns an immutable copy of subcategories to prevent external modification.
     * Following Effective Java Item 50: Make defensive copies when needed.
     */
    public Set<Category> getSubcategories() {
        return subcategories == null ? Collections.emptySet() : Collections.unmodifiableSet(new HashSet<>(subcategories));
    }

    /**
     * Returns an immutable copy of translations to prevent external modification.
     * Following Effective Java Item 50: Make defensive copies when needed.
     */
    public Map<LanguageCode, String> getTranslations() {
        return translations == null ? Collections.emptyMap() : Collections.unmodifiableMap(new HashMap<>(translations));
    }

    /**
     * Sets the parent category with null safety.
     * Following Effective Java Item 49: Check parameters for validity.
     */
    public void setParent(Category parent) {
        this.parent = parent;
    }

    /**
     * Sets translations with defensive copying and null safety.
     * Following Effective Java Item 50: Make defensive copies when needed.
     */
    public void setTranslations(Map<LanguageCode, String> translations) {
        if (this.translations == null) {
            this.translations = new HashMap<>();
        }
        this.translations.clear();
        if (translations != null) {
            this.translations.putAll(translations);
        }
    }

    /**
     * Sets the category type with validation.
     * Following Effective Java Item 49: Check parameters for validity.
     */
    public void setType(CategoryType type) {
        this.type = Objects.requireNonNull(type, "Category type cannot be null");
    }

    /**
     * Sets the level with validation.
     * Following Effective Java Item 49: Check parameters for validity.
     */
    public void setLevel(Integer level) {
        if (level != null && level < 0) {
            throw new IllegalArgumentException("Category level cannot be negative");
        }
        this.level = level;
    }

    /**
     * Adds a subcategory with proper bidirectional relationship management.
     * Following Effective Java Item 17: Minimize mutability where possible.
     */
    public void addSubcategory(Category subcategory) {
        if (subcategory == null) {
            return;
        }
        if (this.subcategories == null) {
            this.subcategories = new HashSet<>();
        }
        this.subcategories.add(subcategory);
        subcategory.setParent(this);
    }

    /**
     * Removes a subcategory with proper bidirectional relationship management.
     */
    public void removeSubcategory(Category subcategory) {
        if (subcategory == null || this.subcategories == null) {
            return;
        }
        this.subcategories.remove(subcategory);
        subcategory.setParent(null);
    }
}

