package com.lookforx.categoryservice.domain.api.response;

import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.common.enums.LanguageCode;
import lombok.*;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Immutable response object for category data.
 *
 * Following Effective Java principles:
 * - Immutable class design (Item 17)
 * - Defensive copying for mutable fields (Item 50)
 * - Proper equals and hashCode implementation (Item 10, 11)
 * - Builder pattern for object creation (Item 2)
 */
@Getter
@Builder
@AllArgsConstructor
@EqualsAndHashCode
@ToString
public class CategoryResponse {
    private final Long id;
    private final Long parentId;
    @Builder.Default
    private final Map<LanguageCode, String> translations = new HashMap<>();
    private final CategoryType type;
    private final Integer level;
    @Builder.Default
    private final Set<CategoryResponse> subcategories = new HashSet<>();

    private final String createdBy;
    private final LocalDateTime createdAt;
    private final LocalDateTime revisedAt;

    /**
     * Default constructor for frameworks that require it.
     */
    public CategoryResponse() {
        this.id = null;
        this.parentId = null;
        this.translations = Collections.emptyMap();
        this.type = null;
        this.level = null;
        this.subcategories = Collections.emptySet();
        this.createdBy = null;
        this.createdAt = null;
        this.revisedAt = null;
    }

    /**
     * Returns an immutable copy of translations to prevent external modification.
     * Following Effective Java Item 50: Make defensive copies when needed.
     */
    public Map<LanguageCode, String> getTranslations() {
        return translations == null ? Collections.emptyMap() : Collections.unmodifiableMap(translations);
    }

    /**
     * Returns an immutable copy of subcategories to prevent external modification.
     * Following Effective Java Item 50: Make defensive copies when needed.
     */
    public Set<CategoryResponse> getSubcategories() {
        return subcategories == null ? Collections.emptySet() : Collections.unmodifiableSet(subcategories);
    }

    /**
     * Builder class with defensive copying for mutable fields.
     */
    public static class CategoryResponseBuilder {
        private Long id;
        private Long parentId;
        private Map<LanguageCode, String> translations = new HashMap<>();
        private CategoryType type;
        private Integer level;
        private Set<CategoryResponse> subcategories = new HashSet<>();
        private String createdBy;
        private LocalDateTime createdAt;
        private LocalDateTime revisedAt;

        /**
         * Sets translations with defensive copying.
         */
        public CategoryResponseBuilder translations(Map<LanguageCode, String> translations) {
            if (translations != null) {
                this.translations = new HashMap<>(translations);
            }
            return this;
        }

        /**
         * Sets subcategories with defensive copying.
         */
        public CategoryResponseBuilder subcategories(Set<CategoryResponse> subcategories) {
            if (subcategories != null) {
                this.subcategories = new HashSet<>(subcategories);
            }
            return this;
        }

        public CategoryResponseBuilder id(Long id) {
            this.id = id;
            return this;
        }

        public CategoryResponseBuilder parentId(Long parentId) {
            this.parentId = parentId;
            return this;
        }

        public CategoryResponseBuilder type(CategoryType type) {
            this.type = type;
            return this;
        }

        public CategoryResponseBuilder level(Integer level) {
            this.level = level;
            return this;
        }

        public CategoryResponseBuilder createdBy(String createdBy) {
            this.createdBy = createdBy;
            return this;
        }

        public CategoryResponseBuilder createdAt(LocalDateTime createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public CategoryResponseBuilder revisedAt(LocalDateTime revisedAt) {
            this.revisedAt = revisedAt;
            return this;
        }

        /**
         * Builds the response with defensive copying.
         */
        public CategoryResponse build() {
            return new CategoryResponse(id, parentId,
                translations != null ? new HashMap<>(translations) : new HashMap<>(),
                type, level,
                subcategories != null ? new HashSet<>(subcategories) : new HashSet<>(),
                createdBy, createdAt, revisedAt);
        }
    }
}
