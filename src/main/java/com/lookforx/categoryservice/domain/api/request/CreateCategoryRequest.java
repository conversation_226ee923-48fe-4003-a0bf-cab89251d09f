package com.lookforx.categoryservice.domain.api.request;

import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.common.enums.LanguageCode;
import jakarta.validation.constraints.*;
import lombok.*;

import java.util.*;

/**
 * Immutable request object for creating a new category.
 *
 * Following Effective Java principles:
 * - Immutable class design (Item 17)
 * - Defensive copying for mutable fields (Item 50)
 * - Proper validation (Item 49)
 * - Builder pattern for object creation (Item 2)
 */
@Getter
@Builder
@AllArgsConstructor
@EqualsAndHashCode
@ToString
public class CreateCategoryRequest {

    private final Long parentId;

    @NotNull(message = "Translations cannot be null")
    @NotEmpty(message = "Translations cannot be empty")
    @Builder.Default
    private final Map<LanguageCode, String> translations = new HashMap<>();

    @NotNull(message = "Category type cannot be null")
    private final CategoryType type;

    @NotNull(message = "Level cannot be null")
    @Min(value = 0, message = "Level must be non-negative")
    @Max(value = 10, message = "Level cannot exceed 10")
    private final Integer level;

    /**
     * Default constructor for frameworks that require it.
     */
    public CreateCategoryRequest() {
        this.parentId = null;
        this.translations = Collections.emptyMap();
        this.type = null;
        this.level = null;
    }

    /**
     * Returns an immutable copy of translations to prevent external modification.
     * Following Effective Java Item 50: Make defensive copies when needed.
     */
    public Map<LanguageCode, String> getTranslations() {
        return translations == null ? Collections.emptyMap() : Collections.unmodifiableMap(translations);
    }

    /**
     * Builder class with defensive copying for mutable fields.
     */
    public static class CreateCategoryRequestBuilder {
        private Long parentId;
        private Map<LanguageCode, String> translations = new HashMap<>();
        private CategoryType type;
        private Integer level;

        /**
         * Sets translations with defensive copying and validation.
         * Following Effective Java Item 50: Make defensive copies when needed.
         */
        public CreateCategoryRequestBuilder translations(Map<LanguageCode, String> translations) {
            if (translations != null) {
                // Validate that translations are not empty strings
                Map<LanguageCode, String> validatedTranslations = new HashMap<>();
                translations.forEach((key, value) -> {
                    if (key != null && value != null && !value.trim().isEmpty()) {
                        validatedTranslations.put(key, value.trim());
                    }
                });
                this.translations = validatedTranslations;
            }
            return this;
        }

        public CreateCategoryRequestBuilder parentId(Long parentId) {
            this.parentId = parentId;
            return this;
        }

        public CreateCategoryRequestBuilder type(CategoryType type) {
            this.type = type;
            return this;
        }

        public CreateCategoryRequestBuilder level(Integer level) {
            this.level = level;
            return this;
        }

        /**
         * Validates required fields before building.
         * Following Effective Java Item 49: Check parameters for validity.
         */
        public CreateCategoryRequest build() {
            // Additional validation can be performed here
            if (level != null && level < 0) {
                throw new IllegalArgumentException("Level cannot be negative");
            }

            return new CreateCategoryRequest(parentId,
                translations != null ? new HashMap<>(translations) : new HashMap<>(),
                type, level);
        }
    }
}
