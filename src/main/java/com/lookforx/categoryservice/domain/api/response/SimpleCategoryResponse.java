package com.lookforx.categoryservice.domain.api.response;

import com.lookforx.categoryservice.domain.CategoryType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * Immutable simplified category response for form dropdowns and simple listings.
 * Contains only essential information without subcategories.
 *
 * Following Effective Java principles:
 * - Immutable class design (Item 17)
 * - Proper equals and hashCode implementation (Item 10, 11)
 * - Builder pattern for object creation (Item 2)
 * - Null safety through validation (Item 49)
 */
@Getter
@Builder
@AllArgsConstructor
@EqualsAndHashCode
@ToString
@Schema(description = "Simplified category response for form dropdowns")
public class SimpleCategoryResponse {

    @Schema(description = "Category ID", example = "1")
    private final Long id;

    @Schema(description = "Category name in specified language", example = "Electronics & Technology")
    private final String name;

    @Schema(description = "Parent category ID", example = "null")
    private final Long parentId;

    @Schema(description = "Category type", example = "PRODUCT")
    private final CategoryType type;

    @Schema(description = "Category level in hierarchy", example = "1")
    private final Integer level;

    @Schema(description = "Full path for display (e.g., 'Electronics > Computers > Laptops')", example = "Electronics & Technology")
    private final String fullPath;

    @Schema(description = "Whether this category is active", example = "true")
    @Builder.Default
    private final Boolean active = Boolean.TRUE;

    /**
     * Default constructor for frameworks that require it.
     */
    public SimpleCategoryResponse() {
        this.id = null;
        this.name = null;
        this.parentId = null;
        this.type = null;
        this.level = null;
        this.fullPath = null;
        this.active = Boolean.TRUE;
    }

    /**
     * Builder class with validation.
     */
    public static class SimpleCategoryResponseBuilder {
        private Long id;
        private String name;
        private Long parentId;
        private CategoryType type;
        private Integer level;
        private String fullPath;
        private Boolean active = Boolean.TRUE;

        /**
         * Sets the name with null and empty string validation.
         * Following Effective Java Item 49: Check parameters for validity.
         */
        public SimpleCategoryResponseBuilder name(String name) {
            this.name = (name != null && !name.trim().isEmpty()) ? name.trim() : null;
            return this;
        }

        /**
         * Sets the full path with null and empty string validation.
         */
        public SimpleCategoryResponseBuilder fullPath(String fullPath) {
            this.fullPath = (fullPath != null && !fullPath.trim().isEmpty()) ? fullPath.trim() : null;
            return this;
        }

        /**
         * Sets the level with validation.
         * Following Effective Java Item 49: Check parameters for validity.
         */
        public SimpleCategoryResponseBuilder level(Integer level) {
            if (level != null && level < 0) {
                throw new IllegalArgumentException("Category level cannot be negative");
            }
            this.level = level;
            return this;
        }

        public SimpleCategoryResponseBuilder id(Long id) {
            this.id = id;
            return this;
        }

        public SimpleCategoryResponseBuilder parentId(Long parentId) {
            this.parentId = parentId;
            return this;
        }

        public SimpleCategoryResponseBuilder type(CategoryType type) {
            this.type = type;
            return this;
        }

        public SimpleCategoryResponseBuilder active(Boolean active) {
            this.active = active;
            return this;
        }

        /**
         * Builds the response with validation.
         */
        public SimpleCategoryResponse build() {
            return new SimpleCategoryResponse(id, name, parentId, type, level, fullPath,
                active != null ? active : Boolean.TRUE);
        }
    }
}
