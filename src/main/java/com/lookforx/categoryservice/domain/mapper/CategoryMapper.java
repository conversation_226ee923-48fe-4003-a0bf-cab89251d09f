package com.lookforx.categoryservice.domain.mapper;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.api.request.CreateCategoryRequest;
import com.lookforx.categoryservice.domain.api.request.UpdateCategoryRequest;
import com.lookforx.categoryservice.domain.api.response.CategoryResponse;
import com.lookforx.categoryservice.domain.dto.CategoryDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Mapper class for Category entity and related DTOs.
 *
 * Following Effective Java principles:
 * - Item 49: Check parameters for validity
 * - Item 50: Make defensive copies when needed
 * - Item 67: Optimize judiciously (efficient mapping)
 * - Item 72: Favor the use of standard exceptions
 */
@Component
@Slf4j
public class CategoryMapper {

    /**
     * Converts CreateCategoryRequest to Category entity.
     * Following Effective Java Item 49: Check parameters for validity.
     *
     * @param request the create request, must not be null
     * @param parent the parent category, can be null for root categories
     * @return new Category entity
     * @throws IllegalArgumentException if request is null
     */
    public Category toEntity(@NonNull CreateCategoryRequest request, @Nullable Category parent) {
        Objects.requireNonNull(request, "CreateCategoryRequest cannot be null");

        log.debug("Converting CreateCategoryRequest to Category entity");

        return Category.builder()
                .parent(parent)
                .translations(safeGetTranslations(request.getTranslations()))
                .type(request.getType())
                .level(request.getLevel())
                .subcategories(new HashSet<>())
                .build();
    }

    /**
     * Updates existing Category entity with UpdateCategoryRequest data.
     * Following Effective Java Item 49: Check parameters for validity.
     *
     * @param request the update request, must not be null
     * @param existing the existing category entity, must not be null
     * @param parent the new parent category, can be null
     * @throws IllegalArgumentException if request or existing is null
     */
    public void updateEntity(@NonNull UpdateCategoryRequest request,
                           @NonNull Category existing,
                           @Nullable Category parent) {
        Objects.requireNonNull(request, "UpdateCategoryRequest cannot be null");
        Objects.requireNonNull(existing, "Existing category cannot be null");

        log.debug("Updating Category entity with ID: {}", existing.getId());

        existing.setParent(parent);
        existing.setTranslations(safeGetTranslations(request.getTranslations()));
        existing.setType(request.getType());
        existing.setLevel(request.getLevel());
    }

    /**
     * Converts CategoryDTO to CategoryResponse.
     * Following Effective Java Item 49: Check parameters for validity.
     *
     * @param dto the category DTO, must not be null
     * @return CategoryResponse
     * @throws IllegalArgumentException if dto is null
     */
    public CategoryResponse toResponse(@NonNull CategoryDTO dto) {
        Objects.requireNonNull(dto, "CategoryDTO cannot be null");

        log.debug("Converting CategoryDTO to CategoryResponse for ID: {}", dto.getId());

        Set<CategoryResponse> subcategories = safeMapSubcategoriesToResponse(dto.getSubcategoryDTOs());

        return CategoryResponse.builder()
                .id(dto.getId())
                .parentId(dto.getParentId())
                .translations(safeGetTranslations(dto.getTranslations()))
                .type(dto.getType())
                .level(dto.getLevel())
                .subcategories(subcategories)
                .createdBy(dto.getCreatedBy())
                .createdAt(dto.getCreatedAt())
                .revisedAt(dto.getUpdatedAt())
                .build();
    }

    /**
     * Converts Category entity to CategoryDTO.
     * Following Effective Java Item 49: Check parameters for validity.
     *
     * @param entity the category entity, must not be null
     * @return CategoryDTO
     * @throws IllegalArgumentException if entity is null
     */
    public CategoryDTO toDTO(@NonNull Category entity) {
        Objects.requireNonNull(entity, "Category entity cannot be null");

        log.debug("Converting Category entity to CategoryDTO for ID: {}", entity.getId());

        Set<CategoryDTO> subcategoryDTOs = safeMapSubcategoriesToDTO(entity.getSubcategories());
        Long parentId = extractParentId(entity.getParent());

        return CategoryDTO.builder()
                .id(entity.getId())
                .parentId(parentId)
                .translations(safeGetTranslations(entity.getTranslations()))
                .type(entity.getType())
                .level(entity.getLevel())
                .subcategoryDTOs(subcategoryDTOs)
                .createdBy(entity.getCreatedBy())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }

    /**
     * Helper method to safely map subcategories to DTOs.
     * Following Effective Java Item 50: Make defensive copies when needed.
     */
    private Set<CategoryDTO> safeMapSubcategoriesToDTO(@Nullable Set<Category> subcategories) {
        if (subcategories == null || subcategories.isEmpty()) {
            return Collections.emptySet();
        }

        return subcategories.stream()
                .filter(Objects::nonNull)
                .map(this::toDTO)
                .collect(Collectors.toUnmodifiableSet());
    }

    /**
     * Helper method to safely map subcategory DTOs to responses.
     * Following Effective Java Item 50: Make defensive copies when needed.
     */
    private Set<CategoryResponse> safeMapSubcategoriesToResponse(@Nullable Set<CategoryDTO> subcategoryDTOs) {
        if (subcategoryDTOs == null || subcategoryDTOs.isEmpty()) {
            return Collections.emptySet();
        }

        return subcategoryDTOs.stream()
                .filter(Objects::nonNull)
                .map(this::toResponse)
                .collect(Collectors.toUnmodifiableSet());
    }

    /**
     * Helper method to safely get translations with defensive copying.
     * Following Effective Java Item 50: Make defensive copies when needed.
     */
    private Map<com.lookforx.common.enums.LanguageCode, String> safeGetTranslations(
            @Nullable Map<com.lookforx.common.enums.LanguageCode, String> translations) {
        if (translations == null || translations.isEmpty()) {
            return Collections.emptyMap();
        }

        return new HashMap<>(translations);
    }

    /**
     * Helper method to safely extract parent ID.
     * Following Effective Java Item 67: Optimize judiciously - extract common logic.
     */
    private Long extractParentId(@Nullable Category parent) {
        return parent != null ? parent.getId() : null;
    }
}
