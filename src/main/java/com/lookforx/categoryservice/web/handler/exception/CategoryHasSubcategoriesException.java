package com.lookforx.categoryservice.web.handler.exception;

import com.lookforx.common.exception.BusinessException;

import java.util.Objects;

/**
 * Exception thrown when trying to delete a Category that still has subcategories.
 *
 * Following Effective Java principles:
 * - Item 72: Favor the use of standard exceptions
 * - Item 75: Include failure-capture information in detail messages
 * - Item 76: Strive for failure atomicity
 * - Item 17: Minimize mutability (immutable error code)
 */
public class CategoryHasSubcategoriesException extends BusinessException {

    private static final String ERROR_CODE = "CATEGORY_HAS_SUBCATEGORIES";

    private final Long categoryId;

    /**
     * Constructs a CategoryHasSubcategoriesException with the specified category ID.
     *
     * @param categoryId the ID of the category that has subcategories, must not be null
     * @throws IllegalArgumentException if categoryId is null
     */
    public CategoryHasSubcategoriesException(Long categoryId) {
        super(
                ERROR_CODE,
                createDetailMessage(categoryId),
                Objects.requireNonNull(categoryId, "Category ID cannot be null")
        );
        this.categoryId = categoryId;
    }

    /**
     * Returns the ID of the category that has subcategories.
     *
     * @return the category ID, never null
     */
    public Long getCategoryId() {
        return categoryId;
    }

    /**
     * Creates a detailed error message including the category ID.
     * Following Effective Java Item 75: Include failure-capture information in detail messages.
     */
    private static String createDetailMessage(Long categoryId) {
        Objects.requireNonNull(categoryId, "Category ID cannot be null");
        return String.format("Category with ID %d has subcategories and cannot be deleted. " +
                "Please delete all subcategories first.", categoryId);
    }

    /**
     * Returns a detailed message including the category ID.
     */
    @Override
    public String getMessage() {
        return createDetailMessage(categoryId);
    }
}