package com.lookforx.categoryservice.web.handler.exception;

import com.lookforx.common.exception.ResourceNotFoundException;

import java.util.Objects;

/**
 * Exception thrown when a Category with a given ID is not found.
 *
 * Following Effective Java principles:
 * - Item 72: Favor the use of standard exceptions
 * - Item 75: Include failure-capture information in detail messages
 * - Item 76: Strive for failure atomicity
 */
public class CategoryNotFoundException extends ResourceNotFoundException {

    private final Long categoryId;

    /**
     * Constructs a CategoryNotFoundException with the specified category ID.
     *
     * @param categoryId the ID of the category that was not found, must not be null
     * @throws IllegalArgumentException if categoryId is null
     */
    public CategoryNotFoundException(Long categoryId) {
        super("Category", Objects.requireNonNull(categoryId, "Category ID cannot be null"));
        this.categoryId = categoryId;
    }

    /**
     * Returns the ID of the category that was not found.
     *
     * @return the category ID, never null
     */
    public Long getCategoryId() {
        return categoryId;
    }

    /**
     * Returns a detailed message including the category ID.
     * Following Effective Java Item 75: Include failure-capture information in detail messages.
     */
    @Override
    public String getMessage() {
        return String.format("Category with ID %d was not found", categoryId);
    }
}
