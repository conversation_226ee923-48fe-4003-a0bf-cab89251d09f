package com.lookforx.categoryservice.web.handler;

import com.lookforx.categoryservice.web.handler.exception.CategoryHasSubcategoriesException;
import com.lookforx.categoryservice.web.handler.exception.CategoryNotFoundException;
import com.lookforx.categoryservice.web.handler.exception.ParentCategoryNotFoundException;
import com.lookforx.common.dto.ErrorResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.lookforx.common.util.ExceptionMessageUtil.fetchAndFormat;
import static com.lookforx.common.util.ExceptionMessageUtil.resolveLanguage;

/**
 * Global exception handler for Category-related errors.
 *
 * Following Effective Java principles:
 * - Item 72: Favor the use of standard exceptions
 * - Item 75: Include failure-capture information in detail messages
 * - Item 67: Optimize judiciously (consistent error response format)
 * - Item 49: Check parameters for validity
 */
@RestControllerAdvice
@Slf4j
@Profile("!test")
public class ApplicationExceptionHandler {

    // Constants for error types - Following Effective Java Item 17
    private static final String CATEGORY_NOT_FOUND_ERROR = "Category Not Found";
    private static final String PARENT_CATEGORY_NOT_FOUND_ERROR = "Parent Category Not Found";
    private static final String CATEGORY_HAS_SUBCATEGORIES_ERROR = "Category Has Subcategories";
    private static final String VALIDATION_ERROR = "Validation Error";
    private static final String INVALID_REQUEST_ERROR = "Invalid Request";
    private static final String INTERNAL_SERVER_ERROR = "Internal Server Error";

    /**
     * Handles CategoryNotFoundException with proper logging and localized messages.
     * Following Effective Java Item 75: Include failure-capture information in detail messages.
     */
    @ExceptionHandler(CategoryNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleCategoryNotFound(
            CategoryNotFoundException ex, HttpServletRequest request) {

        log.warn("Category not found: ID = {}", ex.getCategoryId());

        String language = resolveLanguage(request);
        String message = fetchAndFormat("CATEGORY_NOT_FOUND", language, ex.getCategoryId());

        ErrorResponse errorResponse = createErrorResponse(
                HttpStatus.NOT_FOUND,
                CATEGORY_NOT_FOUND_ERROR,
                message
        );

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    /**
     * Handles ParentCategoryNotFoundException with proper logging and localized messages.
     * Following Effective Java Item 75: Include failure-capture information in detail messages.
     */
    @ExceptionHandler(ParentCategoryNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleParentCategoryNotFound(
            ParentCategoryNotFoundException ex, HttpServletRequest request) {

        log.warn("Parent category not found: ID = {}", ex.getParentId());

        String language = resolveLanguage(request);
        String message = fetchAndFormat("PARENT_CATEGORY_NOT_FOUND", language, ex.getParentId());

        ErrorResponse errorResponse = createErrorResponse(
                HttpStatus.NOT_FOUND,
                PARENT_CATEGORY_NOT_FOUND_ERROR,
                message
        );

        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
    }

    /**
     * Handles CategoryHasSubcategoriesException with proper logging and localized messages.
     * Following Effective Java Item 75: Include failure-capture information in detail messages.
     */
    @ExceptionHandler(CategoryHasSubcategoriesException.class)
    public ResponseEntity<ErrorResponse> handleCategoryHasSubcategories(
            CategoryHasSubcategoriesException ex, HttpServletRequest request) {

        log.warn("Cannot delete category with subcategories: ID = {}", ex.getCategoryId());

        String language = resolveLanguage(request);
        String message = fetchAndFormat("CATEGORY_HAS_SUBCATEGORIES", language, ex.getCategoryId());

        ErrorResponse errorResponse = createErrorResponse(
                HttpStatus.CONFLICT,
                CATEGORY_HAS_SUBCATEGORIES_ERROR,
                message
        );

        return ResponseEntity.status(HttpStatus.CONFLICT).body(errorResponse);
    }

    /**
     * Handles validation errors from @Valid annotations.
     * Following Effective Java Item 67: Optimize judiciously - extract common error handling logic.
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationErrors(
            MethodArgumentNotValidException ex, HttpServletRequest request) {

        log.warn("Validation failed for request: {}", request.getRequestURI());

        String language = resolveLanguage(request);
        Map<String, String> fieldErrors = extractFieldErrors(ex);
        String fields = String.join(", ", fieldErrors.keySet());
        String message = fetchAndFormat("VALIDATION_FAILED", language, fields);

        ErrorResponse errorResponse = ErrorResponse.builder()
                .status(HttpStatus.BAD_REQUEST.value())
                .error(VALIDATION_ERROR)
                .message(message)
                .fieldErrors(fieldErrors)
                .path(request.getRequestURI())
                .timestamp(LocalDateTime.now())
                .build();

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ErrorResponse> handleMissingParams(
            MissingServletRequestParameterException ex,
            HttpServletRequest request) {

        // 1) Resolve the user’s language
        String lang = resolveLanguage(request);

        // 2) Fetch a localized message, injecting the missing parameter name
        String message = fetchAndFormat(
                "MISSING_PARAMETER",
                lang,
                ex.getParameterName()
        );

        // 3) Build the ErrorResponse with path and timestamp
        ErrorResponse error = ErrorResponse.builder()
                .status(HttpStatus.BAD_REQUEST.value())
                .error(HttpStatus.BAD_REQUEST.getReasonPhrase())
                .message(message)
                .path(request.getRequestURI())
                .timestamp(LocalDateTime.now())
                .build();

        return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(error);
    }

    /**
     * Handles constraint violations from @Validated annotations.
     * Following Effective Java Item 67: Optimize judiciously - consistent error handling.
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ErrorResponse> handleConstraintViolation(
            ConstraintViolationException ex, HttpServletRequest request) {

        log.warn("Constraint violation for request: {}", request.getRequestURI());

        String language = resolveLanguage(request);
        String violations = ex.getConstraintViolations().stream()
                .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
                .collect(Collectors.joining(", "));

        String message = fetchAndFormat("CONSTRAINT_VIOLATION", language, violations);

        ErrorResponse errorResponse = createErrorResponse(
                HttpStatus.BAD_REQUEST,
                VALIDATION_ERROR,
                message
        );

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handles method argument type mismatch errors.
     * Following Effective Java Item 67: Optimize judiciously - consistent error handling.
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ErrorResponse> handleTypeMismatch(
            MethodArgumentTypeMismatchException ex, HttpServletRequest request) {

        log.warn("Type mismatch for parameter: {} with value: {}",
                ex.getName(), ex.getValue());

        String language = resolveLanguage(request);
        String message = fetchAndFormat("TYPE_MISMATCH", language, ex.getName(), ex.getValue());

        ErrorResponse errorResponse = createErrorResponse(
                HttpStatus.BAD_REQUEST,
                INVALID_REQUEST_ERROR,
                message
        );

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handles general IllegalArgumentException.
     * Following Effective Java Item 72: Favor the use of standard exceptions.
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponse> handleIllegalArgument(
            IllegalArgumentException ex, HttpServletRequest request) {

        log.warn("Illegal argument: {}", ex.getMessage());

        ErrorResponse errorResponse = createErrorResponse(
                HttpStatus.BAD_REQUEST,
                INVALID_REQUEST_ERROR,
                ex.getMessage()
        );

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /**
     * Handles unexpected exceptions.
     * Following Effective Java Item 75: Include failure-capture information in detail messages.
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(
            Exception ex, HttpServletRequest request) {

        log.error("Unexpected error occurred for request: {}", request.getRequestURI(), ex);

        String language = resolveLanguage(request);
        String message = fetchAndFormat("INTERNAL_SERVER_ERROR", language);

        ErrorResponse errorResponse = createErrorResponse(
                HttpStatus.INTERNAL_SERVER_ERROR,
                INTERNAL_SERVER_ERROR,
                message
        );

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * Helper method to create ErrorResponse objects.
     * Following Effective Java Item 67: Optimize judiciously - extract common logic.
     */
    private ErrorResponse createErrorResponse(HttpStatus status, String error, String message) {
        return ErrorResponse.builder()
                .status(status.value())
                .error(error)
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }

    /**
     * Helper method to extract field errors from validation exception.
     * Following Effective Java Item 67: Optimize judiciously - extract common logic.
     */
    private Map<String, String> extractFieldErrors(MethodArgumentNotValidException ex) {
        return ex.getBindingResult()
                .getFieldErrors()
                .stream()
                .collect(Collectors.toMap(
                        FieldError::getField,
                        fieldError -> Optional.ofNullable(fieldError.getDefaultMessage()).orElse("Invalid value"),
                        (existing, replacement) -> existing
                ));
    }

}
