package com.lookforx.categoryservice.web.handler.exception;

import com.lookforx.common.exception.ResourceNotFoundException;

import java.util.Objects;

/**
 * Exception thrown when a parent Category with a given ID is not found.
 *
 * Following Effective Java principles:
 * - Item 72: Favor the use of standard exceptions
 * - Item 75: Include failure-capture information in detail messages
 * - Item 76: Strive for failure atomicity
 */
public class ParentCategoryNotFoundException extends ResourceNotFoundException {

    private final Long parentId;

    /**
     * Constructs a ParentCategoryNotFoundException with the specified parent category ID.
     *
     * @param parentId the ID of the parent category that was not found, must not be null
     * @throws IllegalArgumentException if parentId is null
     */
    public ParentCategoryNotFoundException(Long parentId) {
        super("ParentCategory", Objects.requireNonNull(parentId, "Parent category ID cannot be null"));
        this.parentId = parentId;
    }

    /**
     * Returns the ID of the parent category that was not found.
     *
     * @return the parent category ID, never null
     */
    public Long getParentId() {
        return parentId;
    }

    /**
     * Returns a detailed message including the parent category ID.
     * Following Effective Java Item 75: Include failure-capture information in detail messages.
     */
    @Override
    public String getMessage() {
        return String.format("Parent category with ID %d was not found", parentId);
    }
}