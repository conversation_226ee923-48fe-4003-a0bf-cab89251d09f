package com.lookforx.categoryservice.web.client;

import com.lookforx.categoryservice.domain.dto.CategoryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * Feign interface for talking to the search-microservice.
 * It must match the REST endpoints you exposed in your Search Service.
 *
 * Resilience Configuration:
 * - Circuit Breaker: Prevents cascading failures
 * - Retry: Automatic retry on transient failures
 * - Timeout: Prevents hanging requests
 */
@FeignClient(
    name = "search-microservice",
    configuration = SearchClientConfig.class
)
public interface SearchClient {

    /**
     * Index a new category document.
     */
    @PostMapping("/api/v1/categories")
    void indexCategory(@RequestBody SearchCategoryDocument document);

    /**
     * Update an existing category document.
     */
    @PutMapping("/api/v1/categories/{id}")
    void updateCategory(
            @PathVariable("id") String id,
            @RequestBody SearchCategoryDocument document
    );

    /**
     * Delete a category document by ID.
     */
    @DeleteMapping("/api/v1/categories/{id}")
    void deleteCategory(@PathVariable("id") String id);

    /**
     * Clear all category documents from the index.
     */
    @DeleteMapping("/api/v1/categories/clear-all")
    void clearAllCategories();

    /**
     * Bulk index multiple category documents.
     */
    @PostMapping("/api/v1/categories/bulk-index")
    void bulkIndexCategories(@RequestBody java.util.List<SearchCategoryDocument> documents);

}
