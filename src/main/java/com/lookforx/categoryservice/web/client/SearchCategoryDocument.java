package com.lookforx.categoryservice.web.client;

import lombok.*;

import java.time.LocalDate;
import java.util.Map;

/**
 * DTO for sending category data to search service.
 * This matches the CategoryDocument structure in search service.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
public class SearchCategoryDocument {
    
    private String id;
    private Long parentId;
    private Map<String, String> translations;
    private String type;
    private Integer level;
    private LocalDate createdAt;
    private LocalDate updatedAt;
    private String createdBy;
}
