package com.lookforx.categoryservice.web.client;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * Configuration for Search Service Feign Client with resilience patterns.
 * 
 * Design Patterns: Configuration Pattern
 * Clean Code: Single responsibility, meaningful names
 * Resilience: Timeout, retry, and logging configuration
 */
@Configuration
@Slf4j
public class SearchClientConfig {

    /**
     * Configure request options with appropriate timeouts for search operations.
     * 
     * Clean Code: Meaningful method name, proper timeout values
     * Resilience: Prevents hanging requests with reasonable timeouts
     */
    @Bean
    public Request.Options requestOptions() {
        return new Request.Options(
            2, TimeUnit.SECONDS,   // Connect timeout - 2 seconds
            3, TimeUnit.SECONDS,   // Read timeout - 3 seconds for search operations
            true                   // Follow redirects
        );
    }

    /**
     * Configure retry policy for transient failures.
     * 
     * Clean Code: Descriptive configuration
     * Resilience: Exponential backoff with reasonable limits
     */
    @Bean
    public Retryer retryer() {
        return new Retryer.Default(
            1000,  // Initial interval - 1 second
            5000,  // Max interval - 5 seconds
            3      // Max attempts
        );
    }

    /**
     * Configure logging level for debugging and monitoring.
     * 
     * Clean Code: Appropriate logging level
     * Monitoring: Helps with troubleshooting timeout issues
     */
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.BASIC;
    }

    /**
     * Custom error decoder for better error handling.
     * This could be extended to handle specific search service errors.
     */
    @Bean
    public feign.codec.ErrorDecoder errorDecoder() {
        return new SearchServiceErrorDecoder();
    }

    /**
     * Custom error decoder for search service specific errors.
     * 
     * Clean Code: Single responsibility, meaningful class name
     * Resilience: Proper error classification for circuit breaker
     */
    public static class SearchServiceErrorDecoder implements feign.codec.ErrorDecoder {
        
        private final feign.codec.ErrorDecoder defaultErrorDecoder = new Default();

        @Override
        public Exception decode(String methodKey, feign.Response response) {
            log.debug("Search service error - Method: {}, Status: {}, Reason: {}", 
                     methodKey, response.status(), response.reason());
            
            // Handle specific search service errors
            switch (response.status()) {
                case 408: // Request Timeout
                case 504: // Gateway Timeout
                    return new java.net.SocketTimeoutException("Search service timeout: " + response.reason());
                case 502: // Bad Gateway
                case 503: // Service Unavailable
                    return new feign.RetryableException(
                        response.status(),
                        "Search service unavailable: " + response.reason(),
                        response.request().httpMethod(),
                        (java.util.Date) null,
                        response.request()
                    );
                default:
                    return defaultErrorDecoder.decode(methodKey, response);
            }
        }
    }
}
