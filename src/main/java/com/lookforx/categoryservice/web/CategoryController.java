package com.lookforx.categoryservice.web;

import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.categoryservice.domain.api.request.CreateCategoryRequest;
import com.lookforx.categoryservice.domain.api.request.UpdateCategoryRequest;
import com.lookforx.categoryservice.domain.api.response.CategoryResponse;
import com.lookforx.categoryservice.domain.api.response.SimpleCategoryResponse;
import com.lookforx.categoryservice.domain.dto.CategoryDTO;
import com.lookforx.categoryservice.domain.mapper.CategoryMapper;
import com.lookforx.categoryservice.service.CategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * REST Controller for Category management operations.
 *
 * Following Effective Java principles:
 * - Item 49: Check parameters for validity
 * - Item 67: Optimize judiciously (proper HTTP status codes)
 * - Item 72: Favor the use of standard exceptions
 * - Proper validation and error handling
 */
@RestController
@RequestMapping("/api/v1/categories")
@RequiredArgsConstructor
@Validated
@Slf4j
@Tag(name = "Categories", description = "APIs for managing categories")
public class CategoryController {

    // Final fields for immutability - Following Effective Java Item 17
    private final CategoryService categoryService;
    private final CategoryMapper categoryMapper;

    @Operation(
            summary = "Create Category",
            description = "Create a new category with translations, type and level"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "201",
                    description = "Category created successfully",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CategoryResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Invalid request data", content = @Content),
            @ApiResponse(responseCode = "409", description = "Category already exists", content = @Content)
    })
    @PostMapping
    public ResponseEntity<CategoryResponse> createCategory(
            @Valid @NotNull @RequestBody CreateCategoryRequest request) {

        log.debug("Creating category with type: {} and level: {}", request.getType(), request.getLevel());

        CategoryDTO dto = categoryService.createCategory(request);
        CategoryResponse response = categoryMapper.toResponse(dto);

        log.info("Successfully created category with ID: {}", dto.getId());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(
            summary = "Update Category",
            description = "Update an existing category by ID"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "Category updated successfully",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CategoryResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Invalid input", content = @Content),
            @ApiResponse(responseCode = "404", description = "Category not found", content = @Content)
    })
    @PutMapping("/{id}")
    public ResponseEntity<CategoryResponse> updateCategory(
            @PathVariable @NotNull @Positive Long id,
            @Valid @NotNull @RequestBody UpdateCategoryRequest request) {

        log.debug("Updating category with ID: {}", id);

        CategoryDTO dto = categoryService.updateCategory(id, request);
        CategoryResponse response = categoryMapper.toResponse(dto);

        log.info("Successfully updated category with ID: {}", id);
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Get All Categories",
            description = "Retrieve a list of all categories with optional flat structure"
    )
    @ApiResponse(
            responseCode = "200",
            description = "List of categories",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = CategoryResponse.class, type = "array")
            )
    )
    @GetMapping
    public ResponseEntity<List<CategoryResponse>> getAllCategories(
            @RequestParam(required = false, defaultValue = "false") boolean flat) {

        log.debug("Fetching all categories with flat structure: {}", flat);

        List<CategoryDTO> dtos = flat
                ? categoryService.getAllCategoriesFlat()
                : categoryService.getAllCategories();

        List<CategoryResponse> responses = dtos.stream()
                .map(categoryMapper::toResponse)
                .collect(Collectors.toUnmodifiableList());

        log.info("Successfully fetched {} categories", responses.size());
        return ResponseEntity.ok(responses);
    }

    @Operation(
            summary = "Get Root Categories by Type",
            description = "Retrieve root categories (main categories without subcategories) by type for tab display"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "List of root categories",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CategoryResponse.class, type = "array")
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Invalid category type", content = @Content)
    })
    @GetMapping("/root")
    public ResponseEntity<List<CategoryResponse>> getRootCategoriesByType(
            @RequestParam @NotNull CategoryType type) {

        log.debug("Fetching root categories for type: {}", type);

        List<CategoryDTO> dtos = categoryService.getRootCategoriesByType(type);
        List<CategoryResponse> responses = dtos.stream()
                .map(categoryMapper::toResponse)
                .collect(Collectors.toUnmodifiableList());

        log.info("Successfully fetched {} root categories for type: {}", responses.size(), type);
        return ResponseEntity.ok(responses);
    }

    @Operation(
            summary = "Get Category by ID",
            description = "Retrieve a single category by its ID"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "Category found",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CategoryResponse.class)
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Invalid category ID", content = @Content),
            @ApiResponse(responseCode = "404", description = "Category not found", content = @Content)
    })
    @GetMapping("/{id}")
    public ResponseEntity<CategoryResponse> getCategoryById(
            @PathVariable @NotNull @Positive Long id) {

        log.debug("Fetching category with ID: {}", id);

        CategoryDTO dto = categoryService.getCategoryById(id);
        CategoryResponse response = categoryMapper.toResponse(dto);

        log.info("Successfully fetched category with ID: {}", id);
        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Get Categories by Parent and Type",
            description = "Retrieve categories filtered by optional parentId and required type"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "Filtered list of categories",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = CategoryResponse.class, type = "array")
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters", content = @Content)
    })
    @GetMapping("/by-parent-and-type")
    public ResponseEntity<List<CategoryResponse>> getByParentAndType(
            @RequestParam(required = false) Long parentId,
            @RequestParam @NotNull CategoryType type) {

        log.debug("Fetching categories by parent ID: {} and type: {}", parentId, type);

        List<CategoryDTO> dtos = categoryService.getCategoriesByParentAndType(parentId, type);
        List<CategoryResponse> responses = dtos.stream()
                .map(categoryMapper::toResponse)
                .collect(Collectors.toUnmodifiableList());

        log.info("Successfully fetched {} categories for parent ID: {} and type: {}",
                responses.size(), parentId, type);
        return ResponseEntity.ok(responses);
    }

    @Operation(
            summary = "Get Simple Categories for Forms",
            description = "Retrieve all categories in a flat structure with specified language for form dropdowns"
    )
    @ApiResponses({
            @ApiResponse(
                    responseCode = "200",
                    description = "List of simple categories",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = SimpleCategoryResponse.class, type = "array")
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Invalid request parameters", content = @Content)
    })
    @GetMapping("/simple")
    public ResponseEntity<List<SimpleCategoryResponse>> getSimpleCategories(
            @RequestParam @NotNull LanguageCode language,
            @RequestParam(required = false) CategoryType type,
            @RequestParam(required = false) String search) {

        log.debug("Fetching simple categories for language: {}, type: {}, search: {}",
                language, type, search);

        List<SimpleCategoryResponse> categories = categoryService.getSimpleCategories(language, type, search);

        log.info("Successfully fetched {} simple categories", categories.size());
        return ResponseEntity.ok(categories);
    }

    @Operation(
            summary = "Delete Category",
            description = "Delete a category by its ID if it has no subcategories"
    )
    @ApiResponses({
            @ApiResponse(responseCode = "204", description = "Category deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid category ID", content = @Content),
            @ApiResponse(responseCode = "404", description = "Category not found", content = @Content),
            @ApiResponse(responseCode = "409", description = "Category has subcategories, cannot delete", content = @Content)
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCategory(
            @PathVariable @NotNull @Positive Long id) {

        log.debug("Deleting category with ID: {}", id);

        categoryService.deleteCategory(id);

        log.info("Successfully deleted category with ID: {}", id);
        return ResponseEntity.noContent().build();
    }

    @Operation(
            summary = "Sync Categories to Search Service",
            description = "Sync all categories to Elasticsearch via search service"
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Categories synced successfully"),
            @ApiResponse(responseCode = "500", description = "Internal server error during sync", content = @Content)
    })
    @PostMapping("/sync-to-search")
    public ResponseEntity<String> syncToSearch() {
        log.info("Starting category sync to search service");

        try {
            categoryService.syncAllCategoriesToSearch();
            String message = "Categories synced to search service successfully";
            log.info(message);
            return ResponseEntity.ok(message);
        } catch (Exception e) {
            String errorMessage = "Error syncing categories: " + e.getMessage();
            log.error("Failed to sync categories to search service", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorMessage);
        }
    }

    @Operation(
            summary = "Clear All Category Cache",
            description = "Clear all Redis cache entries for categories"
    )
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Cache cleared successfully"),
            @ApiResponse(responseCode = "500", description = "Internal server error during cache clear", content = @Content)
    })
    @PostMapping("/clear-cache")
    public ResponseEntity<String> clearCache() {
        log.info("Starting category cache clear operation");

        try {
            categoryService.clearAllCache();
            String message = "All category cache cleared successfully";
            log.info(message);
            return ResponseEntity.ok(message);
        } catch (Exception e) {
            String errorMessage = "Error clearing cache: " + e.getMessage();
            log.error("Failed to clear category cache", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorMessage);
        }
    }

}