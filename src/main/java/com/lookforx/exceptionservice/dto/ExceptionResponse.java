package com.lookforx.exceptionservice.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lookforx.common.enums.LanguageCode;
import lombok.*;
import org.springframework.http.HttpStatus;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Exception bilgilerini istemciye döndürmek için kullanılan DTO.
 * Implements Builder Pattern with validation and immutability.
 *
 * Following Effective Java principles:
 * - Item 2: Consider a builder when faced with many constructor parameters
 * - Item 17: Minimize mutability
 * - Item 50: Make defensive copies when needed
 */
@Getter
@Builder(toBuilder = true)
public final class ExceptionResponse {
    /**
     * Exception'ın benzersiz tanımlayıcısı
     */
    private final Long id;

    /**
     * Exception kodu (örn. USER_NOT_FOUND, INVALID_INPUT)
     */
    private final String exceptionCode;

    /**
     * Farklı dillerdeki hata mesajları (defensive copy)
     * Key: Dil kodu
     * Value: İlgili dildeki hata mesajı
     */
    private final Map<LanguageCode, String> messages;

    /**
     * Exception'ın HTTP durum kodu
     */
    private final HttpStatus httpStatus;

    /**
     * Exception'ın oluşturulma tarihi
     */
    private final LocalDateTime createdAt;

    /**
     * Exception'ın son güncellenme tarihi
     */
    private final LocalDateTime updatedAt;

    /**
     * Jackson constructor for deserialization.
     * Required for proper JSON deserialization with immutable objects.
     */
    @JsonCreator
    public ExceptionResponse(
            @JsonProperty("id") Long id,
            @JsonProperty("exceptionCode") String exceptionCode,
            @JsonProperty("messages") Map<LanguageCode, String> messages,
            @JsonProperty("httpStatus") HttpStatus httpStatus,
            @JsonProperty("createdAt") LocalDateTime createdAt,
            @JsonProperty("updatedAt") LocalDateTime updatedAt) {
        this.id = id;
        this.exceptionCode = exceptionCode;
        this.messages = messages != null ? new HashMap<>(messages) : new HashMap<>();
        this.httpStatus = httpStatus != null ? httpStatus : HttpStatus.INTERNAL_SERVER_ERROR;
        this.createdAt = createdAt != null ? createdAt : LocalDateTime.now();
        this.updatedAt = updatedAt != null ? updatedAt : this.createdAt;
    }

    /**
     * Gets a defensive copy of messages to prevent external modification.
     * Following Effective Java Item 50: Make defensive copies when needed
     *
     * @return a new map containing all messages
     */
    public Map<LanguageCode, String> getMessages() {
        return messages != null ? new HashMap<>(messages) : new HashMap<>();
    }

    /**
     * Custom builder class with validation.
     */
    public static class ExceptionResponseBuilder {

        /**
         * Sets messages with defensive copying and validation.
         *
         * @param messages the messages map
         * @return this builder
         */
        public ExceptionResponseBuilder messages(Map<LanguageCode, String> messages) {
            if (messages != null) {
                this.messages = new HashMap<>(messages);
                // Remove null values
                this.messages.entrySet().removeIf(entry ->
                    entry.getKey() == null || entry.getValue() == null || entry.getValue().trim().isEmpty());
            }
            return this;
        }

        /**
         * Adds a single message for a specific language.
         *
         * @param language the language code
         * @param message the message
         * @return this builder
         */
        public ExceptionResponseBuilder addMessage(LanguageCode language, String message) {
            if (language != null && message != null && !message.trim().isEmpty()) {
                if (this.messages == null) {
                    this.messages = new HashMap<>();
                }
                this.messages.put(language, message.trim());
            }
            return this;
        }

        /**
         * Sets default values and validates before building.
         *
         * @return the built ExceptionResponse
         */
        public ExceptionResponse build() {
            // Set defaults
            if (this.httpStatus == null) {
                this.httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
            }
            if (this.createdAt == null) {
                this.createdAt = LocalDateTime.now();
            }
            if (this.updatedAt == null) {
                this.updatedAt = this.createdAt;
            }
            if (this.messages == null) {
                this.messages = new HashMap<>();
            }

            // Validate required fields
            Objects.requireNonNull(this.exceptionCode, "Exception code cannot be null");
            if (this.exceptionCode.trim().isEmpty()) {
                throw new IllegalArgumentException("Exception code cannot be empty");
            }

            return new ExceptionResponse(this.id, this.exceptionCode.trim().toUpperCase(),
                                       new HashMap<>(this.messages), this.httpStatus,
                                       this.createdAt, this.updatedAt);
        }
    }
}