package com.lookforx.exceptionservice.dto;

import com.lookforx.common.enums.LanguageCode;
import lombok.*;

import java.util.Objects;

/**
 * Response DTO for exception message retrieval.
 * Follows Clean Code principles with immutability and meaningful names.
 *
 * @param exceptionCode the unique exception identifier
 * @param languageCode the language of the message
 * @param message the localized exception message
 */
@Getter
@Builder(toBuilder = true)
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public final class ExceptionMessageResponse {

    /**
     * The unique exception identifier (e.g., "USER_NOT_FOUND").
     */
    private final String exceptionCode;

    /**
     * The language code for the message.
     */
    private final LanguageCode languageCode;

    /**
     * The localized exception message.
     */
    private final String message;

    /**
     * Custom builder with validation.
     */
    public static class ExceptionMessageResponseBuilder {

        /**
         * Builds the response with validation.
         *
         * @return validated exception message response
         * @throws IllegalArgumentException if required fields are missing
         */
        public ExceptionMessageResponse build() {
            validateRequiredFields();
            return new ExceptionMessageResponse(
                normalizeExceptionCode(this.exceptionCode),
                this.languageCode,
                normalizeMessage(this.message)
            );
        }

        /**
         * Validates that all required fields are present.
         */
        private void validateRequiredFields() {
            Objects.requireNonNull(this.exceptionCode, "Exception code cannot be null");
            Objects.requireNonNull(this.languageCode, "Language code cannot be null");
            Objects.requireNonNull(this.message, "Message cannot be null");

            if (this.exceptionCode.trim().isEmpty()) {
                throw new IllegalArgumentException("Exception code cannot be empty");
            }
            if (this.message.trim().isEmpty()) {
                throw new IllegalArgumentException("Message cannot be empty");
            }
        }

        /**
         * Normalizes exception code by trimming and converting to uppercase.
         */
        private String normalizeExceptionCode(String code) {
            return code.trim().toUpperCase();
        }

        /**
         * Normalizes message by trimming whitespace.
         */
        private String normalizeMessage(String msg) {
            return msg.trim();
        }
    }
}