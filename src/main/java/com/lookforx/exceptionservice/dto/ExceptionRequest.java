package com.lookforx.exceptionservice.dto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lookforx.common.enums.LanguageCode;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.*;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

/**
 * Exception oluşturma veya güncelleme için kullanılan DTO.
 * Implements immutable DTO pattern following Effective Java principles.
 *
 * Following Effective Java principles:
 * - Item 17: Minimize mutability
 * - Item 50: Make defensive copies when needed
 */
@Getter
@Builder(toBuilder = true)
public final class ExceptionRequest {
    
    /**
     * Exception kodu. Büyük harfler ve alt çizgi (_) içerebilir.
     * Örnek: USER_NOT_FOUND, INVALID_INPUT, PAYMENT_FAILED
     */
    @NotBlank(message = "Exception kodu boş olamaz")
    @Pattern(regexp = "^[A-Z][A-Z0-9_]*$", message = "Exception kodu büyük harfler, rakamlar ve alt çizgilerden oluşmalıdır ve bir harf ile başlamalıdır")
    private final String exceptionCode;
    
    /**
     * Farklı dillerdeki hata mesajları.
     * Key: Dil kodu (LanguageCode enum'ından)
     * Value: İlgili dildeki hata mesajı
     * 
     * En az bir dil için mesaj belirtilmelidir (tercihen İngilizce).
     */
    @NotEmpty(message = "En az bir dil için hata mesajı belirtilmelidir")
    private final Map<LanguageCode, String> messages;

    /**
     * Exception'ın HTTP durum kodu.
     * Varsayılan olarak INTERNAL_SERVER_ERROR (500).
     */
    @NotNull(message = "HTTP durum kodu belirtilmelidir")
    private final HttpStatus httpStatus;

    /**
     * Jackson constructor for deserialization.
     * Required for proper JSON deserialization with immutable objects.
     */
    @JsonCreator
    public ExceptionRequest(
            @JsonProperty("exceptionCode") String exceptionCode,
            @JsonProperty("messages") Map<LanguageCode, String> messages,
            @JsonProperty("httpStatus") HttpStatus httpStatus) {
        this.exceptionCode = exceptionCode;
        this.messages = messages != null ? new HashMap<>(messages) : new HashMap<>();
        this.httpStatus = httpStatus != null ? httpStatus : HttpStatus.INTERNAL_SERVER_ERROR;
    }

    /**
     * Gets a defensive copy of messages to prevent external modification.
     * Following Effective Java Item 50: Make defensive copies when needed
     *
     * @return a new map containing all messages
     */
    public Map<LanguageCode, String> getMessages() {
        return messages != null ? new HashMap<>(messages) : new HashMap<>();
    }
}
