package com.lookforx.exceptionservice.strategy;

import com.lookforx.common.enums.LanguageCode;

/**
 * Strategy Pattern for Exception Message Retrieval.
 * Defines different strategies for retrieving localized exception messages.
 * 
 * Design Pattern: Strategy Pattern
 * Purpose: Encapsulate different message retrieval algorithms and make them interchangeable
 * Benefits:
 * - Open/Closed Principle: Easy to add new retrieval strategies
 * - Single Responsibility: Each strategy handles one specific retrieval method
 * - Runtime flexibility: Can switch strategies based on context
 */
public interface MessageRetrievalStrategy {
    
    /**
     * Retrieves a localized message for the given exception code and language.
     * 
     * @param exceptionCode The exception code to retrieve message for
     * @param languageCode The desired language for the message
     * @return The localized message, or null if not found
     */
    String retrieveMessage(String exceptionCode, LanguageCode languageCode);
    
    /**
     * Returns the priority of this strategy.
     * Lower numbers indicate higher priority.
     * 
     * @return The priority level (1 = highest priority)
     */
    int getPriority();
    
    /**
     * Indicates whether this strategy can handle the given parameters.
     * 
     * @param exceptionCode The exception code
     * @param languageCode The language code
     * @return true if this strategy can handle the request
     */
    boolean canHandle(String exceptionCode, LanguageCode languageCode);
    
    /**
     * Returns a descriptive name for this strategy.
     * 
     * @return The strategy name
     */
    String getStrategyName();
}
