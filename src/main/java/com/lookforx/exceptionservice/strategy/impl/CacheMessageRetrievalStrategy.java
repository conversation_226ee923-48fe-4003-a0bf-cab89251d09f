package com.lookforx.exceptionservice.strategy.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.strategy.MessageRetrievalStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Cache-based message retrieval strategy.
 * Retrieves messages from Redis cache for better performance.
 * 
 * Design Pattern: Strategy Pattern Implementation
 * Priority: 0 (Highest - Performance optimization)
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CacheMessageRetrievalStrategy implements MessageRetrievalStrategy {
    
    private final CacheManager cacheManager;
    private static final String CACHE_NAME = "exception-messages";
    
    @Override
    public String retrieveMessage(String exceptionCode, LanguageCode languageCode) {
        Objects.requireNonNull(exceptionCode, "Exception code cannot be null");
        Objects.requireNonNull(languageCode, "Language code cannot be null");
        
        Cache cache = cacheManager.getCache(CACHE_NAME);
        if (cache == null) {
            log.debug("Cache '{}' not available", CACHE_NAME);
            return null;
        }
        
        String cacheKey = buildCacheKey(exceptionCode, languageCode);
        Cache.ValueWrapper valueWrapper = cache.get(cacheKey);
        
        if (valueWrapper != null) {
            String cachedMessage = (String) valueWrapper.get();
            log.debug("Retrieved message from cache for key: {}", cacheKey);
            return cachedMessage;
        }
        
        log.debug("Message not found in cache for key: {}", cacheKey);
        return null;
    }
    
    @Override
    public int getPriority() {
        return 0; // Highest priority - performance optimization
    }
    
    @Override
    public boolean canHandle(String exceptionCode, LanguageCode languageCode) {
        // Cache strategy can handle any valid parameters if cache is available
        return exceptionCode != null && !exceptionCode.trim().isEmpty() 
               && languageCode != null 
               && cacheManager.getCache(CACHE_NAME) != null;
    }
    
    @Override
    public String getStrategyName() {
        return "Cache Message Retrieval Strategy";
    }
    
    /**
     * Caches a message for future retrieval.
     */
    public void cacheMessage(String exceptionCode, LanguageCode languageCode, String message) {
        Cache cache = cacheManager.getCache(CACHE_NAME);
        if (cache != null && message != null) {
            String cacheKey = buildCacheKey(exceptionCode, languageCode);
            cache.put(cacheKey, message);
            log.debug("Cached message for key: {}", cacheKey);
        }
    }
    
    private String buildCacheKey(String exceptionCode, LanguageCode languageCode) {
        return String.format("%s:%s", exceptionCode, languageCode.name());
    }
}
