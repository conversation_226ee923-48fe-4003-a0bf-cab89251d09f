package com.lookforx.exceptionservice.strategy.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import com.lookforx.exceptionservice.strategy.MessageRetrievalStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * Database-based message retrieval strategy.
 * Retrieves messages from the database with fallback to English.
 * 
 * Design Pattern: Strategy Pattern Implementation
 * Priority: 1 (Highest - Primary source)
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DatabaseMessageRetrievalStrategy implements MessageRetrievalStrategy {
    
    private final ExceptionRepository exceptionRepository;
    
    @Override
    public String retrieveMessage(String exceptionCode, LanguageCode languageCode) {
        Objects.requireNonNull(exceptionCode, "Exception code cannot be null");
        Objects.requireNonNull(languageCode, "Language code cannot be null");
        
        log.debug("Retrieving message from database for code: {} in language: {}", 
                 exceptionCode, languageCode);
        
        // Try to get message with fallback to English
        return exceptionRepository.findMessageWithFallback(exceptionCode, languageCode, LanguageCode.EN)
                .orElse(null);
    }
    
    @Override
    public int getPriority() {
        return 1; // Highest priority - primary source
    }
    
    @Override
    public boolean canHandle(String exceptionCode, LanguageCode languageCode) {
        // Database strategy can handle any valid parameters
        return exceptionCode != null && !exceptionCode.trim().isEmpty() 
               && languageCode != null;
    }
    
    @Override
    public String getStrategyName() {
        return "Database Message Retrieval Strategy";
    }
}
