package com.lookforx.exceptionservice.strategy.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.strategy.MessageRetrievalStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Default message retrieval strategy.
 * Provides fallback messages when other strategies fail.
 * 
 * Design Pattern: Strategy Pattern Implementation
 * Priority: 999 (Lowest - Fallback strategy)
 */
@Slf4j
@Component
public class DefaultMessageRetrievalStrategy implements MessageRetrievalStrategy {
    
    private static final Map<LanguageCode, String> DEFAULT_MESSAGES = createDefaultMessages();

    private static Map<LanguageCode, String> createDefaultMessages() {
        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "An error occurred. Please contact support team.");
        messages.put(LanguageCode.TR, "Bir hata oluştu. Lütfen destek ekibiyle iletişime geçin.");
        messages.put(LanguageCode.DE, "Ein Fehler ist aufgetreten. Bitte wenden Sie sich an das Support-Team.");
        messages.put(LanguageCode.FR, "Une erreur s'est produite. Veuillez contacter l'équipe de support.");
        messages.put(LanguageCode.ES, "Se produjo un error. Póngase en contacto con el equipo de soporte.");
        messages.put(LanguageCode.IT, "Si è verificato un errore. Si prega di contattare il team di supporto.");
        messages.put(LanguageCode.PT, "Ocorreu um erro. Entre em contato com a equipe de suporte.");
        messages.put(LanguageCode.RU, "Произошла ошибка. Обратитесь в службу поддержки.");
        messages.put(LanguageCode.ZH, "发生错误。请联系支持团队。");
        messages.put(LanguageCode.JA, "エラーが発生しました。サポートチームにお問い合わせください。");
        messages.put(LanguageCode.KO, "오류가 발생했습니다. 지원팀에 문의하세요.");
        messages.put(LanguageCode.AR, "حدث خطأ. يرجى الاتصال بفريق الدعم.");
        return messages;
    }
    
    @Override
    public String retrieveMessage(String exceptionCode, LanguageCode languageCode) {
        Objects.requireNonNull(exceptionCode, "Exception code cannot be null");
        Objects.requireNonNull(languageCode, "Language code cannot be null");
        
        log.debug("Using default message for code: {} in language: {}", exceptionCode, languageCode);
        
        // Try to get message in requested language
        String message = DEFAULT_MESSAGES.get(languageCode);
        
        // Fallback to English if requested language not available
        if (message == null) {
            message = DEFAULT_MESSAGES.get(LanguageCode.EN);
            log.debug("Fallback to English default message for unsupported language: {}", languageCode);
        }
        
        return message;
    }
    
    @Override
    public int getPriority() {
        return 999; // Lowest priority - fallback strategy
    }
    
    @Override
    public boolean canHandle(String exceptionCode, LanguageCode languageCode) {
        // Default strategy can always handle any request as fallback
        return exceptionCode != null && languageCode != null;
    }
    
    @Override
    public String getStrategyName() {
        return "Default Message Retrieval Strategy";
    }
}
