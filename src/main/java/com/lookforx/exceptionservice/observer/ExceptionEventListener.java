package com.lookforx.exceptionservice.observer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Event listener for exception-related events.
 * 
 * Design Pattern: Observer Pattern Implementation
 * Purpose: Handle exception events asynchronously for logging, monitoring, etc.
 * Benefits:
 * - Decoupled event handling
 * - Asynchronous processing
 * - Easy to add new event handlers
 * - Centralized event processing
 */
@Slf4j
@Component
public class ExceptionEventListener {
    
    /**
     * Handles exception created events.
     */
    @Async
    @EventListener
    public void handleExceptionCreated(ExceptionEvent.ExceptionCreatedEvent event) {
        log.info("Exception created: {} with {} translations at {}", 
                event.getExceptionEntity().getExceptionCode(),
                event.getExceptionEntity().getTranslations().size(),
                event.getTimestamp());
        
        // Additional processing can be added here:
        // - Send notification to monitoring system
        // - Update metrics
        // - Trigger cache warming
        // - Send Kafka event
    }
    
    /**
     * Handles exception updated events.
     */
    @Async
    @EventListener
    public void handleExceptionUpdated(ExceptionEvent.ExceptionUpdatedEvent event) {
        log.info("Exception updated: {} at {}", 
                event.getExceptionEntity().getExceptionCode(),
                event.getTimestamp());
        
        // Log changes if needed
        if (event.getPreviousState() != null) {
            log.debug("Previous state had {} translations, new state has {} translations",
                     event.getPreviousState().getTranslations().size(),
                     event.getExceptionEntity().getTranslations().size());
        }
        
        // Additional processing:
        // - Invalidate cache
        // - Send update notification
        // - Update search index
    }
    
    /**
     * Handles exception deleted events.
     */
    @Async
    @EventListener
    public void handleExceptionDeleted(ExceptionEvent.ExceptionDeletedEvent event) {
        log.info("Exception deleted: {} at {}", 
                event.getExceptionEntity().getExceptionCode(),
                event.getTimestamp());
        
        // Additional processing:
        // - Clear cache entries
        // - Remove from search index
        // - Send deletion notification
        // - Update metrics
    }
    
    /**
     * Handles exception message retrieved events.
     */
    @Async
    @EventListener
    public void handleExceptionMessageRetrieved(ExceptionEvent.ExceptionMessageRetrievedEvent event) {
        log.debug("Exception message retrieved: {} in language {} using strategy '{}' at {}", 
                 event.getExceptionEntity().getExceptionCode(),
                 event.getLanguageCode(),
                 event.getStrategyUsed(),
                 event.getTimestamp());
        
        // Additional processing:
        // - Update usage statistics
        // - Monitor performance metrics
        // - Track popular languages
        // - Optimize caching strategy
    }
    
    /**
     * Generic event handler for all exception events.
     * Useful for cross-cutting concerns like auditing.
     */
    @Async
    @EventListener
    public void handleAllExceptionEvents(ExceptionEvent event) {
        // Audit logging
        log.trace("Exception event occurred: {} for entity {} at {}", 
                 event.getEventType(),
                 event.getExceptionEntity().getExceptionCode(),
                 event.getTimestamp());
        
        // Additional cross-cutting concerns:
        // - Audit trail
        // - Security monitoring
        // - Performance tracking
        // - Business intelligence
    }
}
