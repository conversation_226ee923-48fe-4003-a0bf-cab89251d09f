package com.lookforx.exceptionservice.observer;

import com.lookforx.exceptionservice.domain.ExceptionEntity;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.time.LocalDateTime;

/**
 * Base class for exception-related events.
 * 
 * Design Pattern: Observer Pattern (Spring Events)
 * Purpose: Notify interested parties about exception-related operations
 */
@Getter
public abstract class ExceptionEvent extends ApplicationEvent {

    private final ExceptionEntity exceptionEntity;
    private final LocalDateTime eventTimestamp;
    private final String eventType;
    
    protected ExceptionEvent(Object source, ExceptionEntity exceptionEntity, String eventType) {
        super(source);
        this.exceptionEntity = exceptionEntity;
        this.eventType = eventType;
        this.eventTimestamp = LocalDateTime.now();
    }
    
    /**
     * Exception created event.
     */
    public static class ExceptionCreatedEvent extends ExceptionEvent {
        public ExceptionCreatedEvent(Object source, ExceptionEntity exceptionEntity) {
            super(source, exceptionEntity, "EXCEPTION_CREATED");
        }
    }
    
    /**
     * Exception updated event.
     */
    public static class ExceptionUpdatedEvent extends ExceptionEvent {
        @Getter
        private final ExceptionEntity previousState;
        
        public ExceptionUpdatedEvent(Object source, ExceptionEntity exceptionEntity, ExceptionEntity previousState) {
            super(source, exceptionEntity, "EXCEPTION_UPDATED");
            this.previousState = previousState;
        }
    }
    
    /**
     * Exception deleted event.
     */
    public static class ExceptionDeletedEvent extends ExceptionEvent {
        public ExceptionDeletedEvent(Object source, ExceptionEntity exceptionEntity) {
            super(source, exceptionEntity, "EXCEPTION_DELETED");
        }
    }
    
    /**
     * Exception message retrieved event.
     */
    public static class ExceptionMessageRetrievedEvent extends ExceptionEvent {
        @Getter
        private final String languageCode;
        @Getter
        private final String retrievedMessage;
        @Getter
        private final String strategyUsed;
        
        public ExceptionMessageRetrievedEvent(Object source, ExceptionEntity exceptionEntity, 
                                            String languageCode, String retrievedMessage, String strategyUsed) {
            super(source, exceptionEntity, "EXCEPTION_MESSAGE_RETRIEVED");
            this.languageCode = languageCode;
            this.retrievedMessage = retrievedMessage;
            this.strategyUsed = strategyUsed;
        }
    }
}
