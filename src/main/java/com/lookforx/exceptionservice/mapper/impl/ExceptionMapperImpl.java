package com.lookforx.exceptionservice.mapper.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import com.lookforx.exceptionservice.mapper.ExceptionMapper;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Implementation of ExceptionMapper.
 * 
 * SOLID Principles Applied:
 * - Single Responsibility: Only handles mapping operations
 * - Open/Closed: Open for extension via interface
 * - Dependency Inversion: Implements abstraction
 */
@Component
public class ExceptionMapperImpl implements ExceptionMapper {
    
    @Override
    public ExceptionResponse toResponse(ExceptionEntity entity) {
        Objects.requireNonNull(entity, "Exception entity cannot be null");
        
        return ExceptionResponse.builder()
                .id(entity.getId())
                .exceptionCode(entity.getExceptionCode())
                .messages(createDefensiveCopyOfTranslations(entity.getTranslations()))
                .httpStatus(Objects.requireNonNullElse(entity.getHttpStatus(), HttpStatus.INTERNAL_SERVER_ERROR))
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }
    
    @Override
    public ExceptionEntity toEntity(ExceptionRequest request) {
        Objects.requireNonNull(request, "Exception request cannot be null");
        
        String normalizedCode = normalizeExceptionCode(request.getExceptionCode());
        
        return ExceptionEntity.builder()
                .exceptionCode(normalizedCode)
                .translations(createDefensiveCopyOfTranslations(request.getMessages()))
                .httpStatus(determineHttpStatus(request.getHttpStatus()))
                .build();
    }
    
    @Override
    public ExceptionEntity updateEntity(ExceptionEntity entity, ExceptionRequest request) {
        Objects.requireNonNull(entity, "Exception entity cannot be null");
        Objects.requireNonNull(request, "Exception request cannot be null");
        
        entity.setTranslations(createDefensiveCopyOfTranslations(request.getMessages()));
        entity.setHttpStatus(determineHttpStatus(request.getHttpStatus()));
        entity.setUpdatedAt(LocalDateTime.now());
        
        return entity;
    }
    
    /**
     * Normalizes exception code by trimming and converting to uppercase.
     * 
     * @param exceptionCode the raw exception code
     * @return normalized exception code
     */
    private String normalizeExceptionCode(String exceptionCode) {
        if (exceptionCode == null) {
            return null;
        }
        return exceptionCode.trim().toUpperCase();
    }
    
    /**
     * Determines HTTP status with default fallback.
     * 
     * @param requestedStatus the requested HTTP status
     * @return HTTP status (default: INTERNAL_SERVER_ERROR)
     */
    private HttpStatus determineHttpStatus(HttpStatus requestedStatus) {
        return Objects.requireNonNullElse(requestedStatus, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * Creates a defensive copy of translations map to prevent external modification.
     * Following Effective Java Item 50: Make defensive copies when needed
     * 
     * @param original the original translations map
     * @return a defensive copy of the translations
     */
    private Map<LanguageCode, String> createDefensiveCopyOfTranslations(Map<LanguageCode, String> original) {
        if (original == null) {
            return new HashMap<>();
        }
        
        Map<LanguageCode, String> copy = new HashMap<>();
        original.forEach((key, value) -> {
            if (key != null && value != null && !value.trim().isEmpty()) {
                copy.put(key, value.trim());
            }
        });
        
        return copy;
    }
}
