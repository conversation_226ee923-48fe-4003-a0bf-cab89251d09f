package com.lookforx.exceptionservice.mapper;

import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;

/**
 * Mapper interface for converting between domain entities and DTOs.
 * 
 * SOLID Principles Applied:
 * - Single Responsibility: Only handles mapping operations
 * - Interface Segregation: Focused interface for mapping
 * - Dependency Inversion: Abstracts mapping logic
 */
public interface ExceptionMapper {
    
    /**
     * Maps ExceptionEntity to ExceptionResponse.
     * 
     * @param entity the exception entity
     * @return the exception response DTO
     */
    ExceptionResponse toResponse(ExceptionEntity entity);
    
    /**
     * Maps ExceptionRequest to ExceptionEntity.
     * 
     * @param request the exception request DTO
     * @return the exception entity
     */
    ExceptionEntity toEntity(ExceptionRequest request);
    
    /**
     * Updates an existing entity with data from request.
     * 
     * @param entity the existing entity to update
     * @param request the update request
     * @return the updated entity
     */
    ExceptionEntity updateEntity(ExceptionEntity entity, ExceptionRequest request);
}
