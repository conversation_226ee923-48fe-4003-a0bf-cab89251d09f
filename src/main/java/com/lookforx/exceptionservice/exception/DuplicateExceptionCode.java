package com.lookforx.exceptionservice.exception;

import com.lookforx.common.exception.BaseException;
import org.springframework.http.HttpStatus;

import java.io.Serial;

public class DuplicateExceptionCode extends BaseException {

    @Serial
    private static final long serialVersionUID = -591255064690151384L;

    /** HTTP status to return for this error */
    public static final HttpStatus STATUS = HttpStatus.CONFLICT;

    private static final String ERROR_CODE = "DUPLICATE_EXCEPTION_CODE";
    private static final String DEFAULT_MESSAGE = "Exception code already exists: ";

    /**
     * @param exceptionCode the duplicate exception code
     */
    public DuplicateExceptionCode(String exceptionCode) {
        super(ERROR_CODE, DEFAULT_MESSAGE + exceptionCode, HttpStatus.CONFLICT, exceptionCode);
    }
}
