package com.lookforx.exceptionservice.exception;

import com.lookforx.common.exception.BaseException;
import org.springframework.http.HttpStatus;

import java.io.Serial;
import java.util.Objects;

/**
 * Exception entity bulunamadığında fırlatılan exception.
 *
 * Following Effective Java principles:
 * - Item 72: Favor the use of standard exceptions
 * - Item 75: Include failure-capture information in detail messages
 * - Item 76: Strive for failure atomicity
 */
public final class ExceptionNotFoundException extends BaseException {

    @Serial
    private static final long serialVersionUID = -8022123804054558025L;

    public static final HttpStatus STATUS = HttpStatus.NOT_FOUND;

    private static final String ERROR_CODE = "EXCEPTION_NOT_FOUND";
    private static final String DEFAULT_MESSAGE_FOR_ID = "Exception not found with id: ";
    private static final String DEFAULT_MESSAGE_FOR_CODE = "Exception not found with code: ";

    private final Object identifier;

    /**
     * Constructs an ExceptionNotFoundException with the specified ID.
     * Following Effective Java Item 75: Include failure-capture information in detail messages.
     *
     * @param id the ID of the exception that was not found, must not be null
     * @throws IllegalArgumentException if id is null
     */
    public ExceptionNotFoundException(Long id) {
        super(ERROR_CODE, DEFAULT_MESSAGE_FOR_ID + Objects.requireNonNull(id, "ID cannot be null"),
              HttpStatus.NOT_FOUND, id);
        this.identifier = id;
    }

    /**
     * Constructs an ExceptionNotFoundException with the specified exception code.
     * Following Effective Java Item 75: Include failure-capture information in detail messages.
     *
     * @param exceptionCode the exception code that was not found, must not be null
     * @throws IllegalArgumentException if exceptionCode is null or empty
     */
    public ExceptionNotFoundException(String exceptionCode) {
        super(ERROR_CODE, DEFAULT_MESSAGE_FOR_CODE + validateExceptionCode(exceptionCode),
              HttpStatus.NOT_FOUND, exceptionCode);
        this.identifier = exceptionCode;
    }

    /**
     * Gets the identifier (ID or code) that was not found.
     *
     * @return the identifier that was not found
     */
    public Object getIdentifier() {
        return identifier;
    }

    /**
     * Validates the exception code parameter.
     *
     * @param exceptionCode the exception code to validate
     * @return the validated exception code
     * @throws IllegalArgumentException if the code is null or empty
     */
    private static String validateExceptionCode(String exceptionCode) {
        Objects.requireNonNull(exceptionCode, "Exception code cannot be null");
        if (exceptionCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Exception code cannot be empty");
        }
        return exceptionCode.trim();
    }

}