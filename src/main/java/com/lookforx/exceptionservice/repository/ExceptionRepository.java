package com.lookforx.exceptionservice.repository;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Optional;


/**
 * Repository interface for ExceptionEntity.
 * Follows Repository Pattern with custom query methods.
 *
 * Following Effective Java principles:
 * - Item 64: Refer to objects by their interfaces
 * - Item 20: Prefer interfaces to abstract classes
 */
public interface ExceptionRepository extends JpaRepository<ExceptionEntity, Long> {
    
    /**
     * Finds exception entity by its unique exception code.
     * Clean Code: Meaningful method names that express intent clearly.
     *
     * @param exceptionCode the unique exception identifier
     * @return optional exception entity if found
     */
    Optional<ExceptionEntity> findByExceptionCode(String exceptionCode);

    /**
     * Searches for exception entities by partial exception code match (case insensitive).
     * Supports pagination for large result sets.
     *
     * @param exceptionCode partial exception code to search for
     * @param pageable pagination and sorting configuration
     * @return paginated list of matching exception entities
     */
    Page<ExceptionEntity> findByExceptionCodeContainingIgnoreCase(String exceptionCode, Pageable pageable);
    
    /**
     * Retrieves localized message for specific exception code and language.
     * Clean Code: Query methods should have descriptive names and clear documentation.
     *
     * @param exceptionCode the unique exception identifier
     * @param languageCode the requested language for the message
     * @return optional localized message if available
     */
    @Query("SELECT e.translations[:languageCode] FROM ExceptionEntity e WHERE e.exceptionCode = :exceptionCode")
    Optional<String> findMessageByExceptionCodeAndLanguageCode(
            @Param("exceptionCode") String exceptionCode,
            @Param("languageCode") LanguageCode languageCode);


    /**
     * Fallback’lı mesaj araması: önce istenen dil, sonra İngilizce
     */
    default Optional<String> findMessageWithFallback(String exceptionCode,
                                                     LanguageCode requested,
                                                     LanguageCode fallback) {
        // 1) Try requested language
        Optional<String> msg = findMessageByExceptionCodeAndLanguageCode(exceptionCode, requested);
        if (msg.isPresent()) {
            return msg;
        }

        // 2) Then fallback (e.g. EN) if different
        if (!requested.equals(fallback)) {
            msg = findMessageByExceptionCodeAndLanguageCode(exceptionCode, fallback);
            if (msg.isPresent()) {
                return msg;
            }
        }

        // 3) None found
        return Optional.empty();

    }

}