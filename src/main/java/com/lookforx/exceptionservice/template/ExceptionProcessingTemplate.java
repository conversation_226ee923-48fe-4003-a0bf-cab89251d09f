package com.lookforx.exceptionservice.template;

import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import lombok.extern.slf4j.Slf4j;

/**
 * Template Method Pattern for Exception Processing.
 * Defines the skeleton of exception processing algorithm.
 * 
 * Design Pattern: Template Method Pattern
 * Purpose: Define the skeleton of an algorithm and let subclasses override specific steps
 * Benefits:
 * - Code reuse through common algorithm structure
 * - Controlled extension points
 * - Consistent processing flow
 * - Easy to add new processing types
 */
@Slf4j
public abstract class ExceptionProcessingTemplate {
    
    /**
     * Template method that defines the processing algorithm.
     * This method should not be overridden by subclasses.
     * 
     * @param request The exception request to process
     * @return The processed exception entity
     */
    public final ExceptionEntity processException(ExceptionRequest request) {
        log.debug("Starting exception processing for code: {}", request.getExceptionCode());
        
        // Step 1: Pre-processing validation
        preProcessValidation(request);
        
        // Step 2: Business rule validation
        validateBusinessRules(request);
        
        // Step 3: Create or retrieve entity
        ExceptionEntity entity = createOrRetrieveEntity(request);
        
        // Step 4: Apply transformations
        entity = applyTransformations(entity, request);
        
        // Step 5: Perform specific processing
        entity = performSpecificProcessing(entity, request);
        
        // Step 6: Post-processing
        postProcessing(entity);
        
        // Step 7: Finalization
        finalizeProcessing(entity);
        
        log.debug("Completed exception processing for code: {}", entity.getExceptionCode());
        return entity;
    }
    
    /**
     * Pre-processing validation step.
     * Default implementation provides basic validation.
     * Can be overridden for specific validation needs.
     * 
     * @param request The exception request
     */
    protected void preProcessValidation(ExceptionRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("Exception request cannot be null");
        }
        if (request.getExceptionCode() == null || request.getExceptionCode().trim().isEmpty()) {
            throw new IllegalArgumentException("Exception code cannot be null or empty");
        }
        log.debug("Pre-processing validation completed for code: {}", request.getExceptionCode());
    }
    
    /**
     * Business rule validation step.
     * Must be implemented by subclasses to define specific business rules.
     * 
     * @param request The exception request
     */
    protected abstract void validateBusinessRules(ExceptionRequest request);
    
    /**
     * Create or retrieve entity step.
     * Must be implemented by subclasses to define entity creation/retrieval logic.
     * 
     * @param request The exception request
     * @return The exception entity
     */
    protected abstract ExceptionEntity createOrRetrieveEntity(ExceptionRequest request);
    
    /**
     * Apply transformations step.
     * Default implementation returns entity unchanged.
     * Can be overridden to apply specific transformations.
     * 
     * @param entity The exception entity
     * @param request The exception request
     * @return The transformed entity
     */
    protected ExceptionEntity applyTransformations(ExceptionEntity entity, ExceptionRequest request) {
        log.debug("Applying default transformations for code: {}", entity.getExceptionCode());
        return entity;
    }
    
    /**
     * Perform specific processing step.
     * Must be implemented by subclasses to define specific processing logic.
     * 
     * @param entity The exception entity
     * @param request The exception request
     * @return The processed entity
     */
    protected abstract ExceptionEntity performSpecificProcessing(ExceptionEntity entity, ExceptionRequest request);
    
    /**
     * Post-processing step.
     * Default implementation provides basic post-processing.
     * Can be overridden for specific post-processing needs.
     * 
     * @param entity The processed entity
     */
    protected void postProcessing(ExceptionEntity entity) {
        log.debug("Post-processing completed for code: {}", entity.getExceptionCode());
    }
    
    /**
     * Finalization step.
     * Default implementation provides basic finalization.
     * Can be overridden for specific finalization needs.
     * 
     * @param entity The final entity
     */
    protected void finalizeProcessing(ExceptionEntity entity) {
        log.debug("Processing finalized for code: {}", entity.getExceptionCode());
    }
    
    /**
     * Hook method for error handling.
     * Can be overridden to provide custom error handling.
     * 
     * @param error The error that occurred
     * @param request The exception request being processed
     */
    protected void handleProcessingError(Exception error, ExceptionRequest request) {
        log.error("Error processing exception request for code: {}", 
                 request != null ? request.getExceptionCode() : "unknown", error);
        throw new RuntimeException("Exception processing failed", error);
    }
}
