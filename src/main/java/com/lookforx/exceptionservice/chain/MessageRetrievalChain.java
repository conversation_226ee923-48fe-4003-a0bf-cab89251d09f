package com.lookforx.exceptionservice.chain;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.strategy.MessageRetrievalStrategy;
import com.lookforx.exceptionservice.strategy.impl.CacheMessageRetrievalStrategy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * Chain of Responsibility Pattern for Message Retrieval.
 * Processes message retrieval requests through a chain of strategies.
 * 
 * Design Pattern: Chain of Responsibility + Strategy Pattern
 * Purpose: Process requests through a chain of handlers with different strategies
 * Benefits:
 * - Decouples sender from receiver
 * - Dynamic chain configuration
 * - Easy to add/remove handlers
 * - Automatic fallback mechanism
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MessageRetrievalChain {
    
    private final List<MessageRetrievalStrategy> strategies;
    private final CacheMessageRetrievalStrategy cacheStrategy;
    
    /**
     * Processes message retrieval request through the chain of strategies.
     * 
     * @param exceptionCode The exception code to retrieve message for
     * @param languageCode The desired language for the message
     * @return The localized message, or null if not found by any strategy
     */
    public String processRequest(String exceptionCode, LanguageCode languageCode) {
        Objects.requireNonNull(exceptionCode, "Exception code cannot be null");
        Objects.requireNonNull(languageCode, "Language code cannot be null");
        
        log.debug("Processing message retrieval request for code: {} in language: {}", 
                 exceptionCode, languageCode);
        
        // Sort strategies by priority (lower number = higher priority)
        List<MessageRetrievalStrategy> sortedStrategies = strategies.stream()
                .filter(strategy -> strategy.canHandle(exceptionCode, languageCode))
                .sorted(Comparator.comparingInt(MessageRetrievalStrategy::getPriority))
                .toList();
        
        log.debug("Found {} applicable strategies for request", sortedStrategies.size());
        
        String retrievedMessage = null;
        
        // Process through chain of strategies
        for (MessageRetrievalStrategy strategy : sortedStrategies) {
            try {
                log.debug("Trying strategy: {}", strategy.getStrategyName());
                
                String message = strategy.retrieveMessage(exceptionCode, languageCode);
                
                if (message != null && !message.trim().isEmpty()) {
                    log.debug("Message retrieved successfully using strategy: {}", 
                             strategy.getStrategyName());
                    
                    retrievedMessage = message;
                    
                    // Cache the message for future use (if not from cache strategy)
                    if (!(strategy instanceof CacheMessageRetrievalStrategy)) {
                        cacheStrategy.cacheMessage(exceptionCode, languageCode, message);
                    }
                    
                    break; // Stop processing chain once message is found
                }
                
            } catch (Exception e) {
                log.warn("Strategy '{}' failed to retrieve message: {}", 
                        strategy.getStrategyName(), e.getMessage());
                // Continue to next strategy in chain
            }
        }
        
        if (retrievedMessage == null) {
            log.warn("No strategy in chain could retrieve message for code: {} in language: {}", 
                    exceptionCode, languageCode);
        }
        
        return retrievedMessage;
    }
    
    /**
     * Gets information about available strategies in the chain.
     * 
     * @return List of strategy information
     */
    public List<String> getChainInfo() {
        return strategies.stream()
                .sorted(Comparator.comparingInt(MessageRetrievalStrategy::getPriority))
                .map(strategy -> String.format("Priority %d: %s", 
                                              strategy.getPriority(), 
                                              strategy.getStrategyName()))
                .toList();
    }
}
