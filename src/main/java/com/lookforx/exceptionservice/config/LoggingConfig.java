package com.lookforx.exceptionservice.config;

import com.lookforx.common.kafka.LogEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for logging and debugging LogEventPublisher
 */
@Configuration
@Slf4j
public class LoggingConfig {

    @Autowired
    private ApplicationContext applicationContext;

    @Bean
    public CommandLineRunner debugLogEventPublisher() {
        return args -> {
            try {
                LogEventPublisher publisher = applicationContext.getBean(LogEventPublisher.class);
                log.info("✅ LogEventPublisher bean found: {}", publisher.getClass().getName());
            } catch (Exception e) {
                log.error("❌ LogEventPublisher bean NOT found: {}", e.getMessage());
                
                // List all beans containing "log" or "kafka"
                String[] beanNames = applicationContext.getBeanDefinitionNames();
                log.info("Available beans containing 'log' or 'kafka':");
                for (String beanName : beanNames) {
                    if (beanName.toLowerCase().contains("log") || beanName.toLowerCase().contains("kafka")) {
                        log.info("  - {}: {}", beanName, applicationContext.getBean(beanName).getClass().getName());
                    }
                }
            }
        };
    }
}
