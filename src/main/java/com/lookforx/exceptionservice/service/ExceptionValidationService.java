package com.lookforx.exceptionservice.service;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.dto.ExceptionRequest;

/**
 * Service interface for validating exception-related operations.
 * 
 * SOLID Principles Applied:
 * - Single Responsibility: Only handles validation logic
 * - Interface Segregation: Focused interface for validation
 * - Dependency Inversion: Abstracts validation logic
 */
public interface ExceptionValidationService {
    
    /**
     * Validates parameters for message retrieval operations.
     * 
     * @param exceptionCode the exception code to validate
     * @param languageCode the language code to validate
     * @throws IllegalArgumentException if parameters are invalid
     */
    void validateMessageRetrievalParameters(String exceptionCode, LanguageCode languageCode);
    
    /**
     * Validates exception creation request.
     * 
     * @param request the exception creation request
     * @throws IllegalArgumentException if request is invalid
     */
    void validateExceptionCreationRequest(ExceptionRequest request);
    
    /**
     * Validates that exception code is unique.
     * 
     * @param exceptionCode the exception code to check
     * @throws com.lookforx.exceptionservice.exception.DuplicateExceptionCode if code already exists
     */
    void validateExceptionCodeUniqueness(String exceptionCode);
    
    /**
     * Validates that exception exists.
     * 
     * @param exceptionCode the exception code to check
     * @throws com.lookforx.exceptionservice.exception.ExceptionNotFoundException if exception doesn't exist
     */
    void validateExceptionExists(String exceptionCode);
    
    /**
     * Validates that exception exists by ID.
     * 
     * @param id the exception ID to check
     * @throws com.lookforx.exceptionservice.exception.ExceptionNotFoundException if exception doesn't exist
     */
    void validateExceptionExistsById(Long id);
    
    /**
     * Normalizes exception code by trimming and converting to uppercase.
     * 
     * @param exceptionCode the raw exception code
     * @return normalized exception code
     */
    String normalizeExceptionCode(String exceptionCode);
}
