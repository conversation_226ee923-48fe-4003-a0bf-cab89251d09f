package com.lookforx.exceptionservice.service.impl;

import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import com.lookforx.exceptionservice.mapper.ExceptionMapper;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import com.lookforx.exceptionservice.service.ExceptionManagementService;
import com.lookforx.exceptionservice.service.ExceptionValidationService;
import com.lookforx.exceptionservice.template.impl.CreateExceptionProcessor;
import com.lookforx.exceptionservice.template.impl.UpdateExceptionProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Implementation of ExceptionManagementService using Template Method and Factory Patterns.
 *
 * Design Patterns Applied:
 * - Template Method: Standardized processing flow for create/update operations
 * - Factory Pattern: Entity creation through specialized factory
 *
 * SOLID Principles Applied:
 * - Single Responsibility: Only handles exception CRUD operations coordination
 * - Open/Closed: Open for extension via new processors
 * - Dependency Inversion: Depends on abstractions (interfaces)
 */
@Service
@Slf4j
@Transactional(readOnly = true)
public class ExceptionManagementServiceImpl implements ExceptionManagementService {
    
    private static final String CACHE_NAME_EXCEPTION_MESSAGES = "exception-messages";
    
    private final ExceptionRepository exceptionRepository;
    private final ExceptionValidationService validationService;
    private final ExceptionMapper exceptionMapper;
    private final CreateExceptionProcessor createProcessor;
    private final UpdateExceptionProcessor updateProcessor;

    /**
     * Constructor injection following Dependency Inversion Principle.
     * Uses Template Method processors for standardized operations.
     *
     * @param exceptionRepository the repository for exception entities
     * @param validationService the validation service
     * @param exceptionMapper the mapper service
     * @param createProcessor the template processor for creation
     * @param updateProcessor the template processor for updates
     */
    public ExceptionManagementServiceImpl(ExceptionRepository exceptionRepository,
                                        ExceptionValidationService validationService,
                                        ExceptionMapper exceptionMapper,
                                        CreateExceptionProcessor createProcessor,
                                        UpdateExceptionProcessor updateProcessor) {
        this.exceptionRepository = Objects.requireNonNull(exceptionRepository,
                                                         "Exception repository cannot be null");
        this.validationService = Objects.requireNonNull(validationService,
                                                       "Validation service cannot be null");
        this.exceptionMapper = Objects.requireNonNull(exceptionMapper,
                                                     "Exception mapper cannot be null");
        this.createProcessor = Objects.requireNonNull(createProcessor,
                                                     "Create processor cannot be null");
        this.updateProcessor = Objects.requireNonNull(updateProcessor,
                                                     "Update processor cannot be null");
    }
    
    @Override
    @Transactional
    @CacheEvict(value = CACHE_NAME_EXCEPTION_MESSAGES, allEntries = true)
    public ExceptionResponse createException(ExceptionRequest request) {
        log.debug("Creating exception using Template Method pattern for code: {}", request.getExceptionCode());

        // Design Pattern: Template Method - standardized creation process
        ExceptionEntity createdEntity = createProcessor.processException(request);

        // SOLID: Single Responsibility - delegate mapping
        ExceptionResponse response = exceptionMapper.toResponse(createdEntity);

        log.info("Exception created successfully with ID: {} and code: {}",
                createdEntity.getId(), createdEntity.getExceptionCode());

        return response;
    }
    
    @Override
    @Transactional
    @CacheEvict(value = CACHE_NAME_EXCEPTION_MESSAGES, allEntries = true)
    public ExceptionResponse updateException(Long id, ExceptionRequest request) {
        log.debug("Updating exception using Template Method pattern for ID: {} and code: {}",
                 id, request.getExceptionCode());

        // Design Pattern: Template Method - standardized update process
        updateProcessor.setEntityId(id);
        ExceptionEntity updatedEntity = updateProcessor.processException(request);

        // SOLID: Single Responsibility - delegate mapping
        ExceptionResponse response = exceptionMapper.toResponse(updatedEntity);

        log.info("Exception updated successfully with ID: {} and code: {}",
                updatedEntity.getId(), updatedEntity.getExceptionCode());

        return response;
    }
    
    @Override
    @Transactional
    @CacheEvict(value = CACHE_NAME_EXCEPTION_MESSAGES, allEntries = true)
    public void deleteException(Long id) {
        // SOLID: Single Responsibility - delegate validation
        validationService.validateExceptionExistsById(id);
        
        exceptionRepository.deleteById(id);
        log.info("Deleted exception with id: {}", id);
    }
    
    @Override
    public List<ExceptionResponse> getAllExceptions() {
        log.debug("Retrieving all exceptions from database");
        
        return exceptionRepository.findAll().stream()
                .map(exceptionMapper::toResponse)
                .collect(Collectors.toList());
    }
    
    @Override
    public Page<ExceptionResponse> getAllExceptions(Pageable pageable, String search) {
        log.debug("Retrieving exceptions with pagination: {}, search: {}", pageable, search);
        
        Page<ExceptionEntity> entityPage;
        if (search != null && !search.trim().isEmpty()) {
            entityPage = exceptionRepository.findByExceptionCodeContainingIgnoreCase(search.trim(), pageable);
        } else {
            entityPage = exceptionRepository.findAll(pageable);
        }
        
        return entityPage.map(exceptionMapper::toResponse);
    }
    
    @Override
    public ExceptionResponse getExceptionById(Long id) {
        // SOLID: Single Responsibility - delegate validation
        validationService.validateExceptionExistsById(id);
        
        ExceptionEntity entity = exceptionRepository.findById(id).orElseThrow();
        return exceptionMapper.toResponse(entity);
    }
}
