package com.lookforx.exceptionservice.service.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.chain.MessageRetrievalChain;
import com.lookforx.exceptionservice.service.ExceptionMessageService;
import com.lookforx.exceptionservice.service.ExceptionValidationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Implementation of ExceptionMessageService using Chain of Responsibility Pattern.
 *
 * Design Patterns Applied:
 * - Chain of Responsibility: Message retrieval through strategy chain
 * - Strategy Pattern: Different retrieval strategies (cache, database, default)
 *
 * SOLID Principles Applied:
 * - Single Responsibility: Only handles message retrieval coordination
 * - Open/Closed: Open for extension via new strategies
 * - Dependency Inversion: Depends on abstractions (interfaces)
 */
@Service
@Slf4j
@Transactional(readOnly = true)
public class ExceptionMessageServiceImpl implements ExceptionMessageService {
    
    // SOLID: Named Constants
    private static final LanguageCode DEFAULT_FALLBACK_LANGUAGE = LanguageCode.EN;
    private static final String CACHE_NAME_EXCEPTION_MESSAGES = "exception-messages";
    private static final String CACHE_KEY_SEPARATOR = "_";
    
    private final MessageRetrievalChain messageRetrievalChain;
    private final ExceptionValidationService validationService;

    /**
     * Constructor injection following Dependency Inversion Principle.
     * Uses Chain of Responsibility pattern for message retrieval.
     *
     * @param messageRetrievalChain the chain of message retrieval strategies
     * @param validationService the validation service
     */
    public ExceptionMessageServiceImpl(MessageRetrievalChain messageRetrievalChain,
                                     ExceptionValidationService validationService) {
        this.messageRetrievalChain = Objects.requireNonNull(messageRetrievalChain,
                                                           "Message retrieval chain cannot be null");
        this.validationService = Objects.requireNonNull(validationService,
                                                       "Validation service cannot be null");
    }
    
    @Override
    @Cacheable(value = CACHE_NAME_EXCEPTION_MESSAGES, key = "#exceptionCode + '" + CACHE_KEY_SEPARATOR + "' + #languageCode")
    public String retrieveLocalizedMessage(String exceptionCode, LanguageCode languageCode) {
        // SOLID: Single Responsibility - delegate validation
        validationService.validateMessageRetrievalParameters(exceptionCode, languageCode);

        String normalizedCode = validationService.normalizeExceptionCode(exceptionCode);
        logMessageRetrieval(normalizedCode, languageCode);

        // SOLID: Single Responsibility - delegate existence check
        validationService.validateExceptionExists(normalizedCode);

        // Design Pattern: Chain of Responsibility - delegate to message retrieval chain
        return messageRetrievalChain.processRequest(normalizedCode, languageCode);
    }
    
    /**
     * Logs message retrieval operation for debugging purposes.
     * 
     * @param normalizedCode the normalized exception code
     * @param languageCode the requested language code
     */
    private void logMessageRetrieval(String normalizedCode, LanguageCode languageCode) {
        log.debug("Retrieving exception message for code: {} and language: {}", normalizedCode, languageCode);
    }
    
    /**
     * Gets information about the message retrieval chain.
     * Useful for debugging and monitoring.
     *
     * @return List of strategy information in the chain
     */
    public java.util.List<String> getChainInfo() {
        return messageRetrievalChain.getChainInfo();
    }
}
