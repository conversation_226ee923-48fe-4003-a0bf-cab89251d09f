package com.lookforx.exceptionservice.service.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.exception.DuplicateExceptionCode;
import com.lookforx.exceptionservice.exception.ExceptionNotFoundException;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import com.lookforx.exceptionservice.service.ExceptionValidationService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * Implementation of ExceptionValidationService.
 * 
 * SOLID Principles Applied:
 * - Single Responsibility: Only handles validation logic
 * - Open/Closed: Open for extension via interface
 * - Dependency Inversion: Depends on abstractions (interfaces)
 */
@Service
public class ExceptionValidationServiceImpl implements ExceptionValidationService {
    
    private final ExceptionRepository exceptionRepository;
    
    /**
     * Constructor injection following Dependency Inversion Principle.
     * 
     * @param exceptionRepository the repository for exception entities
     */
    public ExceptionValidationServiceImpl(ExceptionRepository exceptionRepository) {
        this.exceptionRepository = Objects.requireNonNull(exceptionRepository, 
                                                         "Exception repository cannot be null");
    }
    
    @Override
    public void validateMessageRetrievalParameters(String exceptionCode, LanguageCode languageCode) {
        Objects.requireNonNull(exceptionCode, "Exception code cannot be null");
        Objects.requireNonNull(languageCode, "Language code cannot be null");
        
        if (exceptionCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Exception code cannot be empty");
        }
    }
    
    @Override
    public void validateExceptionCreationRequest(ExceptionRequest request) {
        Objects.requireNonNull(request, "Exception request cannot be null");
    }
    
    @Override
    public void validateExceptionCodeUniqueness(String exceptionCode) {
        String normalizedCode = normalizeExceptionCode(exceptionCode);
        exceptionRepository.findByExceptionCode(normalizedCode)
                .ifPresent(existingException -> {
                    throw new DuplicateExceptionCode(normalizedCode);
                });
    }
    
    @Override
    public void validateExceptionExists(String exceptionCode) {
        String normalizedCode = normalizeExceptionCode(exceptionCode);
        exceptionRepository.findByExceptionCode(normalizedCode)
                .orElseThrow(() -> new ExceptionNotFoundException(normalizedCode));
    }
    
    @Override
    public void validateExceptionExistsById(Long id) {
        Objects.requireNonNull(id, "Exception ID cannot be null");
        exceptionRepository.findById(id)
                .orElseThrow(() -> new ExceptionNotFoundException(id));
    }
    
    @Override
    public String normalizeExceptionCode(String exceptionCode) {
        if (exceptionCode == null) {
            return null;
        }
        return exceptionCode.trim().toUpperCase();
    }
}
