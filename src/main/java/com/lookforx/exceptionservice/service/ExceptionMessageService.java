package com.lookforx.exceptionservice.service;

import com.lookforx.common.enums.LanguageCode;

/**
 * Service interface for retrieving localized exception messages.
 * 
 * SOLID Principles Applied:
 * - Single Responsibility: Only handles message retrieval
 * - Interface Segregation: Focused interface with minimal methods
 * - Dependency Inversion: Abstracts message retrieval logic
 */
public interface ExceptionMessageService {
    
    /**
     * Retrieves localized exception message for the given exception code and language.
     * 
     * @param exceptionCode the unique exception identifier
     * @param languageCode the requested language for the message
     * @return localized exception message
     * @throws IllegalArgumentException if parameters are null or empty
     * @throws com.lookforx.exceptionservice.exception.ExceptionNotFoundException if exception code is not found
     */
    String retrieveLocalizedMessage(String exceptionCode, LanguageCode languageCode);
}
