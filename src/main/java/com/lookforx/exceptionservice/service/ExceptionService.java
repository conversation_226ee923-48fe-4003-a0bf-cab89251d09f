package com.lookforx.exceptionservice.service;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * Facade service that delegates to specialized services following SOLID principles.
 * 
 * SOLID Principles Applied:
 * - Single Responsibility: Acts as a facade, delegates to specialized services
 * - Open/Closed: Open for extension via composition
 * - Dependency Inversion: Depends on abstractions (interfaces)
 * - Interface Segregation: Uses focused interfaces
 */
@Service
@Slf4j
public class ExceptionService {
    
    private final ExceptionMessageService messageService;
    private final ExceptionManagementService managementService;
    
    /**
     * Constructor injection following Dependency Inversion Principle.
     * 
     * @param messageService the message retrieval service
     * @param managementService the exception management service
     */
    public ExceptionService(ExceptionMessageService messageService,
                          ExceptionManagementService managementService) {
        this.messageService = Objects.requireNonNull(messageService, 
                                                    "Message service cannot be null");
        this.managementService = Objects.requireNonNull(managementService,
                                                       "Management service cannot be null");
    }
    
    /**
     * Retrieves localized exception message for the given exception code and language.
     * 
     * @param exceptionCode the unique exception identifier
     * @param languageCode the requested language for the message
     * @return localized exception message
     */
    public String retrieveLocalizedExceptionMessage(String exceptionCode, LanguageCode languageCode) {
        // SOLID: Single Responsibility - delegate to specialized service
        return messageService.retrieveLocalizedMessage(exceptionCode, languageCode);
    }
    
    /**
     * Retrieves all exception definitions.
     * 
     * @return list of all exception responses
     */
    public List<ExceptionResponse> getAllExceptions() {
        // SOLID: Single Responsibility - delegate to specialized service
        return managementService.getAllExceptions();
    }
    
    /**
     * Retrieves exception definitions with pagination and search.
     * 
     * @param pageable pagination configuration
     * @param search optional search term
     * @return paginated exception responses
     */
    public Page<ExceptionResponse> getAllExceptions(Pageable pageable, String search) {
        // SOLID: Single Responsibility - delegate to specialized service
        return managementService.getAllExceptions(pageable, search);
    }
    
    /**
     * Retrieves a specific exception definition by ID.
     * 
     * @param id the exception ID
     * @return the exception response
     */
    public ExceptionResponse getExceptionById(Long id) {
        // SOLID: Single Responsibility - delegate to specialized service
        return managementService.getExceptionById(id);
    }
    
    /**
     * Creates a new exception definition.
     * 
     * @param request the exception creation request
     * @return the created exception response
     */
    public ExceptionResponse createException(ExceptionRequest request) {
        // SOLID: Single Responsibility - delegate to specialized service
        return managementService.createException(request);
    }
    
    /**
     * Updates an existing exception definition.
     * 
     * @param id the exception ID to update
     * @param request the update request
     * @return the updated exception response
     */
    public ExceptionResponse updateException(Long id, ExceptionRequest request) {
        // SOLID: Single Responsibility - delegate to specialized service
        return managementService.updateException(id, request);
    }
    
    /**
     * Deletes an exception definition.
     * 
     * @param id the exception ID to delete
     */
    public void deleteException(Long id) {
        // SOLID: Single Responsibility - delegate to specialized service
        managementService.deleteException(id);
    }
}
