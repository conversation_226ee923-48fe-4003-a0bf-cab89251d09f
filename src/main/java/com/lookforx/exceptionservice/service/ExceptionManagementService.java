package com.lookforx.exceptionservice.service;

import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Service interface for managing exception definitions (CRUD operations).
 * 
 * SOLID Principles Applied:
 * - Single Responsibility: Only handles exception CRUD operations
 * - Interface Segregation: Focused interface for management operations
 * - Dependency Inversion: Abstracts exception management logic
 */
public interface ExceptionManagementService {
    
    /**
     * Creates a new exception definition.
     * 
     * @param request the exception creation request
     * @return the created exception response
     * @throws IllegalArgumentException if request is null
     * @throws com.lookforx.exceptionservice.exception.DuplicateExceptionCode if exception code already exists
     */
    ExceptionResponse createException(ExceptionRequest request);
    
    /**
     * Updates an existing exception definition.
     * 
     * @param id the exception ID to update
     * @param request the update request
     * @return the updated exception response
     * @throws com.lookforx.exceptionservice.exception.ExceptionNotFoundException if exception is not found
     */
    ExceptionResponse updateException(Long id, ExceptionRequest request);
    
    /**
     * Deletes an exception definition.
     * 
     * @param id the exception ID to delete
     * @throws com.lookforx.exceptionservice.exception.ExceptionNotFoundException if exception is not found
     */
    void deleteException(Long id);
    
    /**
     * Retrieves all exception definitions.
     * 
     * @return list of all exception responses
     */
    List<ExceptionResponse> getAllExceptions();
    
    /**
     * Retrieves exception definitions with pagination and search.
     * 
     * @param pageable pagination configuration
     * @param search optional search term
     * @return paginated exception responses
     */
    Page<ExceptionResponse> getAllExceptions(Pageable pageable, String search);
    
    /**
     * Retrieves a specific exception definition by ID.
     * 
     * @param id the exception ID
     * @return the exception response
     * @throws com.lookforx.exceptionservice.exception.ExceptionNotFoundException if exception is not found
     */
    ExceptionResponse getExceptionById(Long id);
}
