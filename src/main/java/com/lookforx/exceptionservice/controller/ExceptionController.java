package com.lookforx.exceptionservice.controller;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.dto.ExceptionMessageResponse;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import com.lookforx.exceptionservice.service.ExceptionMessageService;
import com.lookforx.exceptionservice.service.ExceptionManagementService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

import java.util.Objects;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing application exceptions.
 * Follows SOLID principles with dependency inversion and single responsibility.
 * <p>
 * SOLID Principles Applied:
 * - Single Responsibility: Only handles HTTP request/response mapping
 * - Dependency Inversion: Depends on service abstractions
 * - Interface Segregation: Uses focused service interfaces
 */
@RestController
@RequestMapping("/api/v1/exceptions")
@Tag(name = "Exceptions", description = "API for exception management and lookup")
public class ExceptionController {

    // SOLID: Named Constants
    private static final String DEFAULT_PAGE_SIZE = "20";
    private static final String DEFAULT_PAGE_NUMBER = "0";
    private static final String DEFAULT_SORT_FIELD = "exceptionCode";
    private static final String DEFAULT_SORT_DIRECTION = "asc";
    private static final String SORT_DIRECTION_DESC = "desc";

    // SOLID: Dependency Inversion - depend on abstractions
    private final ExceptionMessageService messageService;
    private final ExceptionManagementService managementService;

    /**
     * Constructor injection following Dependency Inversion Principle.
     *
     * @param messageService the message retrieval service
     * @param managementService the exception management service
     */
    public ExceptionController(ExceptionMessageService messageService,
                             ExceptionManagementService managementService) {
        this.messageService = Objects.requireNonNull(messageService,
                                                    "Message service cannot be null");
        this.managementService = Objects.requireNonNull(managementService,
                                                       "Management service cannot be null");
    }

    /**
     * Retrieve all defined exceptions with pagination and search.
     *
     * @param page page number (0-based)
     * @param size page size
     * @param sortBy field to sort by
     * @param sortDir sort direction (asc/desc)
     * @param search search term for exception code or messages
     * @param unpaged whether to return all results without pagination
     * @return paginated list of exception definitions or all exceptions if unpaged=true
     */
    @Operation(summary = "Get all exceptions with pagination", description = "Returns a paginated list of exception definitions with optional search.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved list",
                    content = @Content(mediaType = "application/json"))
    })
    @GetMapping
    public ResponseEntity<?> getAllExceptions(
            @RequestParam(defaultValue = DEFAULT_PAGE_NUMBER) int page,
            @RequestParam(defaultValue = DEFAULT_PAGE_SIZE) int size,
            @RequestParam(defaultValue = DEFAULT_SORT_FIELD) String sortBy,
            @RequestParam(defaultValue = DEFAULT_SORT_DIRECTION) String sortDir,
            @RequestParam(required = false) String search,
            @RequestParam(defaultValue = "false") boolean unpaged) {

        if (unpaged) {
            // SOLID: Single Responsibility - delegate to specialized service
            return ResponseEntity.ok(managementService.getAllExceptions());
        }

        // SOLID: Single Responsibility - delegate to specialized service
        Pageable pageable = createPageableRequest(page, size, sortBy, sortDir);
        return ResponseEntity.ok(managementService.getAllExceptions(pageable, search));
    }

    /**
     * Create a new exception definition.
     *
     * @param request the exception details to create
     * @return the created exception definition
     */
    @Operation(summary = "Create exception", description = "Creates a new exception definition.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Exception created successfully",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = ExceptionResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    @PostMapping
    public ResponseEntity<ExceptionResponse> createException(@Valid @RequestBody ExceptionRequest request) {
        // SOLID: Single Responsibility - delegate to specialized service
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(managementService.createException(request));
    }

    /**
     * Get an exception definition by ID.
     *
     * @param id the ID of the exception to retrieve
     * @return the exception definition
     */
    @Operation(summary = "Get exception by ID", description = "Retrieves an exception definition by its ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Exception found",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = ExceptionResponse.class))),
            @ApiResponse(responseCode = "404", description = "Exception not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<ExceptionResponse> getExceptionById(@PathVariable Long id) {
        // SOLID: Single Responsibility - delegate to specialized service
        return ResponseEntity.ok(managementService.getExceptionById(id));
    }

    /**
     * Update an existing exception definition.
     *
     * @param id      the ID of the exception to update
     * @param request the updated exception details
     * @return the updated exception definition
     */
    @Operation(summary = "Update exception", description = "Updates an existing exception definition by ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Exception updated successfully",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = ExceptionResponse.class))),
            @ApiResponse(responseCode = "404", description = "Exception not found"),
            @ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    @PutMapping("/{id}")
    public ResponseEntity<ExceptionResponse> updateException(
            @PathVariable Long id,
            @Valid @RequestBody ExceptionRequest request) {
        // SOLID: Single Responsibility - delegate to specialized service
        return ResponseEntity.ok(managementService.updateException(id, request));
    }

    /**
     * Delete an exception definition.
     *
     * @param id the ID of the exception to delete
     */
    @Operation(summary = "Delete exception", description = "Deletes an exception definition by ID.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Exception deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Exception not found")
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteException(@PathVariable Long id) {
        // SOLID: Single Responsibility - delegate to specialized service
        managementService.deleteException(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Lookup the localized message for a given exception code.
     *
     * @param exceptionCode the code of the exception
     * @param languageCode  the target language for the message
     * @return the localized exception message
     */
    @Operation(summary = "Get exception message", description = "Retrieves a localized exception message by exception code and language.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Message retrieved successfully",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = ExceptionMessageResponse.class))),
            @ApiResponse(responseCode = "404", description = "Exception code not found")
    })
    @GetMapping("/exception-message")
    public ResponseEntity<ExceptionMessageResponse> getExceptionMessage(
            @RequestParam String exceptionCode,
            @RequestParam LanguageCode languageCode) {
        // SOLID: Single Responsibility - delegate to specialized service
        String message = messageService.retrieveLocalizedMessage(exceptionCode, languageCode);
        ExceptionMessageResponse response = buildExceptionMessageResponse(exceptionCode, languageCode, message);
        return ResponseEntity.ok(response);
    }

    // SOLID: Helper methods for pagination and response building

    /**
     * Creates pageable request with sorting configuration.
     *
     * @param page page number
     * @param size page size
     * @param sortBy field to sort by
     * @param sortDir sort direction
     * @return configured pageable request
     */
    private Pageable createPageableRequest(int page, int size, String sortBy, String sortDir) {
        Sort sort = createSortConfiguration(sortBy, sortDir);
        return PageRequest.of(page, size, sort);
    }

    /**
     * Creates sort configuration based on field and direction.
     *
     * @param sortBy field to sort by
     * @param sortDir sort direction
     * @return sort configuration
     */
    private Sort createSortConfiguration(String sortBy, String sortDir) {
        Sort.Direction direction = SORT_DIRECTION_DESC.equalsIgnoreCase(sortDir)
            ? Sort.Direction.DESC
            : Sort.Direction.ASC;
        return Sort.by(direction, sortBy);
    }

    /**
     * Builds exception message response with provided data.
     *
     * @param exceptionCode the exception code
     * @param languageCode the language code
     * @param message the localized message
     * @return built exception message response
     */
    private ExceptionMessageResponse buildExceptionMessageResponse(String exceptionCode,
                                                                  LanguageCode languageCode,
                                                                  String message) {
        return ExceptionMessageResponse.builder()
                .exceptionCode(exceptionCode)
                .languageCode(languageCode)
                .message(message)
                .build();
    }

}