package com.lookforx.exceptionservice.domain.factory;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Factory Pattern for creating ExceptionEntity instances.
 * Encapsulates the complex logic of creating properly configured exception entities.
 * 
 * Design Pattern: Factory Pattern
 * Purpose: Centralize object creation logic and ensure consistent entity creation
 * Benefits:
 * - Encapsulates complex creation logic
 * - Ensures consistent object initialization
 * - Easy to modify creation rules in one place
 * - Supports different creation scenarios
 */
@Slf4j
@Component
public class ExceptionEntityFactory {
    
    /**
     * Creates a new ExceptionEntity from an ExceptionRequest.
     * 
     * @param request The exception request containing entity data
     * @return A properly configured ExceptionEntity
     * @throws IllegalArgumentException if request is invalid
     */
    public ExceptionEntity createFromRequest(ExceptionRequest request) {
        Objects.requireNonNull(request, "Exception request cannot be null");
        validateRequest(request);
        
        log.debug("Creating ExceptionEntity from request for code: {}", request.getExceptionCode());
        
        ExceptionEntity entity = ExceptionEntity.builder()
                .exceptionCode(normalizeExceptionCode(request.getExceptionCode()))
                .translations(createDefensiveCopyOfTranslations(request.getMessages()))
                .httpStatus(determineHttpStatus(request.getHttpStatus()))
                .build();
        
        log.debug("Created ExceptionEntity with code: {} and {} translations", 
                 entity.getExceptionCode(), entity.getTranslations().size());
        
        return entity;
    }
    
    /**
     * Creates a default ExceptionEntity with minimal configuration.
     * 
     * @param exceptionCode The exception code
     * @return A basic ExceptionEntity with default values
     */
    public ExceptionEntity createDefault(String exceptionCode) {
        Objects.requireNonNull(exceptionCode, "Exception code cannot be null");
        
        log.debug("Creating default ExceptionEntity for code: {}", exceptionCode);
        
        Map<LanguageCode, String> defaultMessages = Map.of(
            LanguageCode.EN, "An error occurred: " + exceptionCode,
            LanguageCode.TR, "Bir hata oluştu: " + exceptionCode
        );
        
        return ExceptionEntity.builder()
                .exceptionCode(normalizeExceptionCode(exceptionCode))
                .translations(new HashMap<>(defaultMessages))
                .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .build();
    }
    
    /**
     * Creates an ExceptionEntity for system-generated exceptions.
     * 
     * @param exceptionCode The exception code
     * @param httpStatus The HTTP status
     * @param englishMessage The English message
     * @return A system-configured ExceptionEntity
     */
    public ExceptionEntity createSystemException(String exceptionCode, 
                                               HttpStatus httpStatus, 
                                               String englishMessage) {
        Objects.requireNonNull(exceptionCode, "Exception code cannot be null");
        Objects.requireNonNull(httpStatus, "HTTP status cannot be null");
        Objects.requireNonNull(englishMessage, "English message cannot be null");
        
        log.debug("Creating system ExceptionEntity for code: {}", exceptionCode);
        
        Map<LanguageCode, String> systemMessages = new HashMap<>();
        systemMessages.put(LanguageCode.EN, englishMessage);
        
        return ExceptionEntity.builder()
                .exceptionCode(normalizeExceptionCode(exceptionCode))
                .translations(systemMessages)
                .httpStatus(httpStatus)
                .build();
    }
    
    /**
     * Updates an existing ExceptionEntity with new data from request.
     * 
     * @param existingEntity The existing entity to update
     * @param request The request containing new data
     * @return The updated entity
     */
    public ExceptionEntity updateFromRequest(ExceptionEntity existingEntity, ExceptionRequest request) {
        Objects.requireNonNull(existingEntity, "Existing entity cannot be null");
        Objects.requireNonNull(request, "Exception request cannot be null");
        validateRequest(request);
        
        log.debug("Updating ExceptionEntity with id: {} from request", existingEntity.getId());
        
        existingEntity.setExceptionCode(normalizeExceptionCode(request.getExceptionCode()));
        existingEntity.setTranslations(createDefensiveCopyOfTranslations(request.getMessages()));
        existingEntity.setHttpStatus(determineHttpStatus(request.getHttpStatus()));
        
        return existingEntity;
    }
    
    private void validateRequest(ExceptionRequest request) {
        if (request.getExceptionCode() == null || request.getExceptionCode().trim().isEmpty()) {
            throw new IllegalArgumentException("Exception code cannot be null or empty");
        }
        
        if (request.getMessages() == null || request.getMessages().isEmpty()) {
            throw new IllegalArgumentException("Messages cannot be null or empty");
        }
        
        if (!request.getMessages().containsKey(LanguageCode.EN)) {
            throw new IllegalArgumentException("English message is required");
        }
    }
    
    private String normalizeExceptionCode(String exceptionCode) {
        return exceptionCode.trim()
                .toUpperCase(java.util.Locale.ENGLISH)
                .replace(" ", "_")
                .replace("İ", "I")
                .replace("Ğ", "G")
                .replace("Ü", "U")
                .replace("Ş", "S")
                .replace("Ö", "O")
                .replace("Ç", "C");
    }
    
    private HttpStatus determineHttpStatus(HttpStatus requestedStatus) {
        return requestedStatus != null ? requestedStatus : HttpStatus.INTERNAL_SERVER_ERROR;
    }
    
    private Map<LanguageCode, String> createDefensiveCopyOfTranslations(Map<LanguageCode, String> original) {
        Map<LanguageCode, String> copy = new HashMap<>();
        if (original != null) {
            original.forEach((key, value) -> {
                if (key != null && value != null && !value.trim().isEmpty()) {
                    copy.put(key, value.trim());
                }
            });
        }
        return copy;
    }
}
