package com.lookforx.requestservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * Feign client for Location Service (Reference Data Service)
 */
@FeignClient(name = "reference-data-microservice", path = "/api/v1/reference-data")
public interface LocationServiceClient {
    
    /**
     * Validate if country exists
     */
    @GetMapping("/countries/{countryId}/exists")
    Boolean existsCountry(@PathVariable String countryId);
    
    /**
     * Validate if city exists in country
     */
    @GetMapping("/countries/{countryId}/cities/{cityId}/exists")
    Boolean existsCity(@PathVariable String countryId, @PathVariable String cityId);
    
    /**
     * Validate if district exists in city
     */
    @GetMapping("/countries/{countryId}/cities/{cityId}/districts/{districtId}/exists")
    Boolean existsDistrict(@PathVariable String countryId, @PathVariable String cityId, @PathVariable String districtId);
}
