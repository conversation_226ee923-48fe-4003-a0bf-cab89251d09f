package com.lookforx.requestservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * Feign client for Form Service
 */
@FeignClient(name = "question-form-microservice", path = "/api/v1/form-submissions")
public interface FormServiceClient {
    
    /**
     * Validate if submission form exists
     */
    @GetMapping("/exists/{submissionFormId}")
    Boolean existsBySubmissionFormId(@PathVariable String submissionFormId);
    
    /**
     * Get form submission by request ID
     */
    @GetMapping("/request/{requestId}")
    Object getFormSubmissionByRequestId(@PathVariable String requestId);
}
