package com.lookforx.requestservice.client;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * Feign client for Category Service
 */
@FeignClient(name = "category-microservice", path = "/api/v1/categories")
public interface CategoryServiceClient {
    
    /**
     * Validate if category exists
     */
    @GetMapping("/{categoryId}/exists")
    Boolean existsCategory(@PathVariable String categoryId);
    
    /**
     * Get category information
     */
    @GetMapping("/{categoryId}")
    Object getCategoryById(@PathVariable String categoryId);
}
