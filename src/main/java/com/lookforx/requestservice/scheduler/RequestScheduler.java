package com.lookforx.requestservice.scheduler;

import com.lookforx.requestservice.service.DraftService;
import com.lookforx.requestservice.service.MetricsService;
import com.lookforx.requestservice.service.RequestService;
import com.lookforx.requestservice.repository.RequestRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Scheduled tasks for Request service
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RequestScheduler {

    private final RequestService requestService;
    private final DraftService draftService;
    private final MetricsService metricsService;
    private final RequestRepository requestRepository;

    /**
     * Expire old requests every day at 01:00 AM
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void expireOldRequests() {
        log.info("Starting expired requests cleanup job");
        try {
            requestService.expireRequests();
            log.info("Expired requests cleanup job completed successfully");
        } catch (Exception e) {
            log.error("Error during expired requests cleanup job: {}", e.getMessage(), e);
        }
    }

    /**
     * Clean up old drafts every day at 02:00 AM
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupOldDrafts() {
        log.info("Starting old drafts cleanup job");
        try {
            draftService.cleanupOldDrafts();
            log.info("Old drafts cleanup job completed successfully");
        } catch (Exception e) {
            log.error("Error during old drafts cleanup job: {}", e.getMessage(), e);
        }
    }

    /**
     * Update metrics every hour
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void updateMetrics() {
        log.info("Updating metrics");
        try {
            long activeRequests = requestRepository.countByStatusAndDeleted(
                com.lookforx.requestservice.domain.RequestStatus.ACTIVE, false);
            long totalRequests = requestRepository.count();

            metricsService.setActiveRequests(activeRequests);
            metricsService.setTotalRequests(totalRequests);

            log.info("Metrics updated - Active: {}, Total: {}", activeRequests, totalRequests);
        } catch (Exception e) {
            log.error("Error updating metrics: {}", e.getMessage(), e);
        }
    }
}
