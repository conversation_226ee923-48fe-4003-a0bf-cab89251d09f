package com.lookforx.requestservice.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis Cache Configuration with different TTL strategies
 */
@Configuration
@EnableCaching
public class CacheConfig {
    
    public static final String REQUEST_CACHE = "requests";
    public static final String USER_REQUESTS_CACHE = "user-requests";
    public static final String SEARCH_RESULTS_CACHE = "search-results";
    public static final String CATEGORY_REQUESTS_CACHE = "category-requests";
    public static final String POPULAR_REQUESTS_CACHE = "popular-requests";
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofHours(1))
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();
        
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // Request details - 2 hours TTL
        cacheConfigurations.put(REQUEST_CACHE, defaultConfig.entryTtl(Duration.ofHours(2)));
        
        // User requests - 30 minutes TTL (more dynamic)
        cacheConfigurations.put(USER_REQUESTS_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // Search results - 15 minutes TTL (very dynamic)
        cacheConfigurations.put(SEARCH_RESULTS_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(15)));
        
        // Category requests - 1 hour TTL
        cacheConfigurations.put(CATEGORY_REQUESTS_CACHE, defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // Popular requests - 6 hours TTL (less dynamic)
        cacheConfigurations.put(POPULAR_REQUESTS_CACHE, defaultConfig.entryTtl(Duration.ofHours(6)));
        
        return RedisCacheManager.builder(connectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
