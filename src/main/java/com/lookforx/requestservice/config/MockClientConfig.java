package com.lookforx.requestservice.config;

import com.lookforx.requestservice.client.CategoryServiceClient;
import com.lookforx.requestservice.client.FormServiceClient;
import com.lookforx.requestservice.client.LocationServiceClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * Mock client configuration for development/testing
 */
@Configuration
@Profile({"dev", "test"})
public class MockClientConfig {
    
    @Bean
    @ConditionalOnMissingBean
    public FormServiceClient formServiceClient() {
        return new FormServiceClient() {
            @Override
            public Boolean existsBySubmissionFormId(String submissionFormId) {
                // Mock implementation - always return true for development
                return true;
            }

            @Override
            public Object getFormSubmissionByRequestId(String requestId) {
                // Mock implementation - return empty object
                return new Object();
            }
        };
    }
    
    @Bean
    @ConditionalOnMissingBean
    public CategoryServiceClient categoryServiceClient() {
        return new CategoryServiceClient() {
            @Override
            public Boolean existsCategory(String categoryId) {
                // Mock implementation - always return true for development
                return true;
            }

            @Override
            public Object getCategoryById(String categoryId) {
                // Mock implementation - return empty object
                return new Object();
            }
        };
    }
    
    @Bean
    @ConditionalOnMissingBean
    public LocationServiceClient locationServiceClient() {
        return new LocationServiceClient() {
            @Override
            public Boolean existsCountry(String countryId) {
                // Mock implementation - return true for common countries
                return countryId != null && (
                    countryId.equals("TR") || 
                    countryId.equals("US") || 
                    countryId.equals("GB") || 
                    countryId.equals("DE") ||
                    countryId.equals("FR")
                );
            }
            
            @Override
            public Boolean existsCity(String countryId, String cityId) {
                // Mock implementation - always return true if country exists
                return existsCountry(countryId);
            }
            
            @Override
            public Boolean existsDistrict(String countryId, String cityId, String districtId) {
                // Mock implementation - always return true if city exists
                return existsCity(countryId, cityId);
            }
        };
    }
}
