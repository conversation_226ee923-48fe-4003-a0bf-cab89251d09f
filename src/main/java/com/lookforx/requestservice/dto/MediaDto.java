package com.lookforx.requestservice.dto;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * DTO for media information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MediaDto {
    
    @Size(max = 3, message = "Maximum 3 images allowed")
    private List<@NotBlank @Pattern(regexp = "^https?://.*", message = "Invalid image URL") String> imageUrls;
    
    @Size(max = 3, message = "Maximum 3 documents allowed")
    private List<@NotBlank @Pattern(regexp = "^https?://.*", message = "Invalid document URL") String> documentUrls;
    
    @Pattern(regexp = "^https?://.*", message = "Invalid video URL")
    private String videoUrl;
}
