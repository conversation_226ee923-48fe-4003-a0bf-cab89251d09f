package com.lookforx.requestservice.dto;

import com.lookforx.requestservice.domain.LocationInfo;
import com.lookforx.requestservice.domain.MediaInfo;
import com.lookforx.requestservice.domain.PriceRange;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for request draft
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestDraftDto {
    
    private String id;
    private String userId;
    private String categoryId;
    private String submissionFormId;
    private String title;
    private String description;
    private LocationInfo location;
    private PriceRange priceRange;
    private MediaInfo media;
    private LocalDateTime expiresAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Boolean autoSaved;
    private Integer completionPercentage;
}
