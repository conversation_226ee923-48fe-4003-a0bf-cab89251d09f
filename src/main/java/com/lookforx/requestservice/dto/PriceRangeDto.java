package com.lookforx.requestservice.dto;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * DTO for price range information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PriceRangeDto {
    
    @NotNull(message = "Minimum price is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Minimum price must be greater than 0")
    private BigDecimal minPrice;
    
    @NotNull(message = "Maximum price is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Maximum price must be greater than 0")
    private BigDecimal maxPrice;
    
    @NotBlank(message = "Currency is required")
    @Pattern(regexp = "^[A-Z]{3}$", message = "Currency must be a valid 3-letter code")
    private String currency;
}
