package com.lookforx.requestservice.dto;

import com.lookforx.requestservice.domain.RequestStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for request response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestResponseDto {
    
    private String id;
    private String userId;
    private String categoryId;
    private String submissionFormId;
    private String title;
    private String description;
    private LocationDto location;
    private PriceRangeDto priceRange;
    private MediaDto media;
    private RequestStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime expiresAt;
    private LocalDateTime updatedAt;
}
