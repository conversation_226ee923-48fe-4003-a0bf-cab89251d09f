package com.lookforx.requestservice.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * DTO for creating a new request
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateRequestDto {
    
    @NotBlank(message = "User ID is required")
    private String userId;
    
    @NotBlank(message = "Category ID is required")
    private String categoryId;
    
    @NotBlank(message = "Submission form ID is required")
    private String submissionFormId;
    
    @NotBlank(message = "Title is required")
    @Size(min = 3, max = 200, message = "Title must be between 3 and 200 characters")
    private String title;
    
    @NotBlank(message = "Description is required")
    @Size(min = 10, max = 5000, message = "Description must be between 10 and 5000 characters")
    private String description;
    
    @NotNull(message = "Location is required")
    @Valid
    private LocationDto location;
    
    @NotNull(message = "Price range is required")
    @Valid
    private PriceRangeDto priceRange;
    
    @NotNull(message = "Media is required")
    @Valid
    private MediaDto media;
    
    @NotNull(message = "Expiry date is required")
    @Future(message = "Expiry date must be in the future")
    private LocalDateTime expiresAt;
}
