package com.lookforx.requestservice.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for location information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationDto {
    
    @NotBlank(message = "Country ID is required")
    private String countryId;
    
    @NotBlank(message = "City ID is required")
    private String cityId;
    
    @NotBlank(message = "District ID is required")
    private String districtId;
}
