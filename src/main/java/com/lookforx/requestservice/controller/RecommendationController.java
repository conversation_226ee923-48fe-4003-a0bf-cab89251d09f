package com.lookforx.requestservice.controller;

import com.lookforx.common.dto.ApiResponse;
import com.lookforx.requestservice.dto.RequestResponseDto;
import com.lookforx.requestservice.service.RecommendationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for Request Recommendations
 */
@RestController
@RequestMapping("/api/v1/recommendations")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Request Recommendations", description = "APIs for getting request recommendations")
public class RecommendationController {
    
    private final RecommendationService recommendationService;
    
    @GetMapping("/similar/{requestId}")
    @Operation(summary = "Get similar requests", description = "Get requests similar to the specified request")
    public ResponseEntity<ApiResponse<List<RequestResponseDto>>> getSimilarRequests(
            @Parameter(description = "Request ID") @PathVariable String requestId,
            @Parameter(description = "Number of recommendations") @RequestParam(defaultValue = "5") int limit) {
        
        log.info("Getting similar requests for: {}", requestId);
        List<RequestResponseDto> recommendations = recommendationService.getSimilarRequests(requestId, limit);
        
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
    
    @GetMapping("/trending")
    @Operation(summary = "Get trending requests", description = "Get trending requests in specified location")
    public ResponseEntity<ApiResponse<List<RequestResponseDto>>> getTrendingRequests(
            @Parameter(description = "Country ID") @RequestParam(required = false) String countryId,
            @Parameter(description = "City ID") @RequestParam(required = false) String cityId,
            @Parameter(description = "Number of recommendations") @RequestParam(defaultValue = "10") int limit) {
        
        log.info("Getting trending requests for location: {}/{}", countryId, cityId);
        List<RequestResponseDto> trending = recommendationService.getTrendingRequests(countryId, cityId, limit);
        
        return ResponseEntity.ok(ApiResponse.success(trending));
    }
    
    @GetMapping("/popular/category/{categoryId}")
    @Operation(summary = "Get popular requests by category", description = "Get popular requests in specified category")
    public ResponseEntity<ApiResponse<List<RequestResponseDto>>> getPopularRequestsByCategory(
            @Parameter(description = "Category ID") @PathVariable String categoryId,
            @Parameter(description = "Number of recommendations") @RequestParam(defaultValue = "10") int limit) {
        
        log.info("Getting popular requests for category: {}", categoryId);
        List<RequestResponseDto> popular = recommendationService.getPopularRequestsByCategory(categoryId, limit);
        
        return ResponseEntity.ok(ApiResponse.success(popular));
    }
    
    @GetMapping("/user/{userId}")
    @Operation(summary = "Get personalized recommendations", description = "Get personalized recommendations for user")
    public ResponseEntity<ApiResponse<List<RequestResponseDto>>> getRecommendationsForUser(
            @Parameter(description = "User ID") @PathVariable String userId,
            @Parameter(description = "Number of recommendations") @RequestParam(defaultValue = "10") int limit) {
        
        log.info("Getting recommendations for user: {}", userId);
        List<RequestResponseDto> recommendations = recommendationService.getRecommendationsForUser(userId, limit);
        
        return ResponseEntity.ok(ApiResponse.success(recommendations));
    }
}
