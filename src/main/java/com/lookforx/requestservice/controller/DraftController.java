package com.lookforx.requestservice.controller;

import com.lookforx.common.dto.ApiResponse;
import com.lookforx.requestservice.dto.CreateRequestDto;
import com.lookforx.requestservice.dto.RequestDraftDto;
import com.lookforx.requestservice.service.DraftService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * REST Controller for Request Drafts
 */
@RestController
@RequestMapping("/api/v1/drafts")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Request Drafts", description = "APIs for managing request drafts")
public class DraftController {
    
    private final DraftService draftService;
    
    @PostMapping("/save")
    @Operation(summary = "Save draft", description = "Save a request draft")
    public ResponseEntity<ApiResponse<RequestDraftDto>> saveDraft(
            @Parameter(description = "User ID") @RequestParam String userId,
            @RequestBody CreateRequestDto createRequestDto) {
        
        log.info("Saving draft for user: {}", userId);
        RequestDraftDto draft = draftService.saveDraft(userId, createRequestDto);
        
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(draft, "Draft saved successfully"));
    }
    
    @PostMapping("/auto-save")
    @Operation(summary = "Auto-save draft", description = "Auto-save a request draft")
    public ResponseEntity<ApiResponse<Void>> autoSaveDraft(
            @Parameter(description = "User ID") @RequestParam String userId,
            @RequestBody CreateRequestDto createRequestDto) {
        
        log.info("Auto-saving draft for user: {}", userId);
        draftService.autoSaveDraft(userId, createRequestDto);
        
        return ResponseEntity.ok(ApiResponse.success(null, "Draft auto-saved successfully"));
    }
    
    @GetMapping("/user/{userId}")
    @Operation(summary = "Get user drafts", description = "Get all drafts for a user")
    public ResponseEntity<ApiResponse<List<RequestDraftDto>>> getUserDrafts(
            @Parameter(description = "User ID") @PathVariable String userId) {
        
        log.info("Getting drafts for user: {}", userId);
        List<RequestDraftDto> drafts = draftService.getUserDrafts(userId);
        
        return ResponseEntity.ok(ApiResponse.success(drafts));
    }
    
    @GetMapping("/{draftId}")
    @Operation(summary = "Get draft by ID", description = "Get a specific draft by ID")
    public ResponseEntity<ApiResponse<RequestDraftDto>> getDraftById(
            @Parameter(description = "Draft ID") @PathVariable String draftId,
            @Parameter(description = "User ID") @RequestParam String userId) {
        
        log.info("Getting draft: {} for user: {}", draftId, userId);
        Optional<RequestDraftDto> draft = draftService.getDraftById(draftId, userId);
        
        if (draft.isPresent()) {
            return ResponseEntity.ok(ApiResponse.success(draft.get()));
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    @DeleteMapping("/{draftId}")
    @Operation(summary = "Delete draft", description = "Delete a draft")
    public ResponseEntity<ApiResponse<Void>> deleteDraft(
            @Parameter(description = "Draft ID") @PathVariable String draftId,
            @Parameter(description = "User ID") @RequestParam String userId) {
        
        log.info("Deleting draft: {} for user: {}", draftId, userId);
        draftService.deleteDraft(draftId, userId);
        
        return ResponseEntity.ok(ApiResponse.success(null, "Draft deleted successfully"));
    }
    
    @PostMapping("/{draftId}/convert")
    @Operation(summary = "Convert draft to request", description = "Convert a draft to a request DTO")
    public ResponseEntity<ApiResponse<CreateRequestDto>> convertDraftToRequest(
            @Parameter(description = "Draft ID") @PathVariable String draftId,
            @Parameter(description = "User ID") @RequestParam String userId) {
        
        log.info("Converting draft: {} to request for user: {}", draftId, userId);
        Optional<RequestDraftDto> draft = draftService.getDraftById(draftId, userId);
        
        if (draft.isPresent()) {
            CreateRequestDto requestDto = draftService.convertDraftToRequest(draft.get());
            return ResponseEntity.ok(ApiResponse.success(requestDto));
        } else {
            return ResponseEntity.notFound().build();
        }
    }
}
