package com.lookforx.requestservice.controller;

import com.lookforx.common.dto.ApiResponse;
import com.lookforx.requestservice.domain.RequestStatus;
import com.lookforx.requestservice.dto.CreateRequestDto;
import com.lookforx.requestservice.dto.RequestResponseDto;
import com.lookforx.requestservice.dto.UpdateRequestDto;
import com.lookforx.requestservice.service.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * REST Controller for Request operations
 */
@RestController
@RequestMapping("/api/v1/requests")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Request Management", description = "APIs for managing requests")
public class RequestController {
    
    private final RequestService requestService;
    
    @PostMapping
    @Operation(summary = "Create a new request", description = "Creates a new request with the provided information")
    public ResponseEntity<ApiResponse<RequestResponseDto>> createRequest(
            @Valid @RequestBody CreateRequestDto createRequestDto) {
        
        log.info("Creating request for user: {}", createRequestDto.getUserId());
        RequestResponseDto response = requestService.createRequest(createRequestDto);
        
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success(response, "Request created successfully"));
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "Get request by ID", description = "Retrieves a request by its ID")
    public ResponseEntity<ApiResponse<RequestResponseDto>> getRequestById(
            @Parameter(description = "Request ID") @PathVariable String id) {
        
        log.info("Fetching request with ID: {}", id);
        RequestResponseDto response = requestService.getRequestById(id);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }
    
    @PutMapping("/{id}")
    @Operation(summary = "Update request", description = "Updates an existing request")
    public ResponseEntity<ApiResponse<RequestResponseDto>> updateRequest(
            @Parameter(description = "Request ID") @PathVariable String id,
            @Parameter(description = "User ID") @RequestParam String userId,
            @Valid @RequestBody UpdateRequestDto updateRequestDto) {
        
        log.info("Updating request with ID: {} for user: {}", id, userId);
        RequestResponseDto response = requestService.updateRequest(id, updateRequestDto, userId);
        
        return ResponseEntity.ok(ApiResponse.success(response, "Request updated successfully"));
    }
    
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete request", description = "Deletes a request")
    public ResponseEntity<ApiResponse<Void>> deleteRequest(
            @Parameter(description = "Request ID") @PathVariable String id,
            @Parameter(description = "User ID") @RequestParam String userId) {
        
        log.info("Deleting request with ID: {} for user: {}", id, userId);
        requestService.deleteRequest(id, userId);
        
        return ResponseEntity.ok(ApiResponse.success(null, "Request deleted successfully"));
    }
    
    @GetMapping("/user/{userId}")
    @Operation(summary = "Get requests by user ID", description = "Retrieves requests for a specific user with pagination")
    public ResponseEntity<ApiResponse<Page<RequestResponseDto>>> getRequestsByUserId(
            @Parameter(description = "User ID") @PathVariable String userId,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        log.info("Fetching requests for user: {} with pagination", userId);
        Page<RequestResponseDto> response = requestService.getRequestsByUserId(userId, pageable);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }
    
    @GetMapping("/user/{userId}/status/{status}")
    @Operation(summary = "Get requests by user ID and status", description = "Retrieves requests for a specific user and status with pagination")
    public ResponseEntity<ApiResponse<Page<RequestResponseDto>>> getRequestsByUserIdAndStatus(
            @Parameter(description = "User ID") @PathVariable String userId,
            @Parameter(description = "Request status") @PathVariable RequestStatus status,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        log.info("Fetching requests for user: {} with status: {}", userId, status);
        Page<RequestResponseDto> response = requestService.getRequestsByUserIdAndStatus(userId, status, pageable);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }
    
    @GetMapping("/category/{categoryId}")
    @Operation(summary = "Get requests by category", description = "Retrieves active requests for a specific category with pagination")
    public ResponseEntity<ApiResponse<Page<RequestResponseDto>>> getRequestsByCategory(
            @Parameter(description = "Category ID") @PathVariable String categoryId,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        log.info("Fetching requests for category: {}", categoryId);
        Page<RequestResponseDto> response = requestService.getRequestsByCategory(categoryId, pageable);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }
    
    @GetMapping("/active")
    @Operation(summary = "Get active requests", description = "Retrieves all active requests with pagination")
    public ResponseEntity<ApiResponse<Page<RequestResponseDto>>> getActiveRequests(
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        log.info("Fetching active requests");
        Page<RequestResponseDto> response = requestService.getActiveRequests(pageable);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }
    
    @GetMapping("/search")
    @Operation(summary = "Search requests", description = "Search requests by location, category, and title with pagination")
    public ResponseEntity<ApiResponse<Page<RequestResponseDto>>> searchRequests(
            @Parameter(description = "Country ID") @RequestParam(required = false) String countryId,
            @Parameter(description = "City ID") @RequestParam(required = false) String cityId,
            @Parameter(description = "District ID") @RequestParam(required = false) String districtId,
            @Parameter(description = "Category ID") @RequestParam(required = false) String categoryId,
            @Parameter(description = "Title search") @RequestParam(required = false) String title,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        log.info("Searching requests with filters");
        Page<RequestResponseDto> response = requestService.searchRequests(countryId, cityId, districtId, categoryId, title, pageable);
        
        return ResponseEntity.ok(ApiResponse.success(response));
    }
    
    @PatchMapping("/{id}/status")
    @Operation(summary = "Change request status", description = "Changes the status of a request")
    public ResponseEntity<ApiResponse<RequestResponseDto>> changeRequestStatus(
            @Parameter(description = "Request ID") @PathVariable String id,
            @Parameter(description = "User ID") @RequestParam String userId,
            @Parameter(description = "New status") @RequestParam RequestStatus status) {
        
        log.info("Changing request status to {} for request: {} by user: {}", status, id, userId);
        RequestResponseDto response = requestService.changeRequestStatus(id, status, userId);
        
        return ResponseEntity.ok(ApiResponse.success(response, "Request status changed successfully"));
    }
}
