package com.lookforx.requestservice.mapper;

import com.lookforx.requestservice.domain.LocationInfo;
import com.lookforx.requestservice.domain.MediaInfo;
import com.lookforx.requestservice.domain.PriceRange;
import com.lookforx.requestservice.domain.Request;
import com.lookforx.requestservice.dto.*;
import org.springframework.stereotype.Component;

/**
 * Mapper for Request entity and DTOs
 */
@Component
public class RequestMapper {
    
    public Request toEntity(CreateRequestDto dto) {
        return Request.builder()
                .userId(dto.getUserId())
                .categoryId(dto.getCategoryId())
                .submissionFormId(dto.getSubmissionFormId())
                .title(dto.getTitle())
                .description(dto.getDescription())
                .location(toLocationInfo(dto.getLocation()))
                .priceRange(toPriceRange(dto.getPriceRange()))
                .media(toMediaInfo(dto.getMedia()))
                .expiresAt(dto.getExpiresAt())
                .build();
    }
    
    public RequestResponseDto toDto(Request entity) {
        return RequestResponseDto.builder()
                .id(entity.getId())
                .userId(entity.getUserId())
                .categoryId(entity.getCategoryId())
                .submissionFormId(entity.getSubmissionFormId())
                .title(entity.getTitle())
                .description(entity.getDescription())
                .location(toLocationDto(entity.getLocation()))
                .priceRange(toPriceRangeDto(entity.getPriceRange()))
                .media(toMediaDto(entity.getMedia()))
                .status(entity.getStatus())
                .createdAt(entity.getCreatedAt())
                .expiresAt(entity.getExpiresAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }
    
    public LocationInfo toLocationInfo(LocationDto dto) {
        if (dto == null) return null;
        return LocationInfo.builder()
                .countryId(dto.getCountryId())
                .cityId(dto.getCityId())
                .districtId(dto.getDistrictId())
                .build();
    }
    
    public LocationDto toLocationDto(LocationInfo entity) {
        if (entity == null) return null;
        return LocationDto.builder()
                .countryId(entity.getCountryId())
                .cityId(entity.getCityId())
                .districtId(entity.getDistrictId())
                .build();
    }
    
    public PriceRange toPriceRange(PriceRangeDto dto) {
        if (dto == null) return null;
        return PriceRange.builder()
                .minPrice(dto.getMinPrice())
                .maxPrice(dto.getMaxPrice())
                .currency(dto.getCurrency())
                .build();
    }
    
    public PriceRangeDto toPriceRangeDto(PriceRange entity) {
        if (entity == null) return null;
        return PriceRangeDto.builder()
                .minPrice(entity.getMinPrice())
                .maxPrice(entity.getMaxPrice())
                .currency(entity.getCurrency())
                .build();
    }
    
    public MediaInfo toMediaInfo(MediaDto dto) {
        if (dto == null) return null;
        return MediaInfo.builder()
                .imageUrls(dto.getImageUrls())
                .documentUrls(dto.getDocumentUrls())
                .videoUrl(dto.getVideoUrl())
                .build();
    }
    
    public MediaDto toMediaDto(MediaInfo entity) {
        if (entity == null) return null;
        return MediaDto.builder()
                .imageUrls(entity.getImageUrls())
                .documentUrls(entity.getDocumentUrls())
                .videoUrl(entity.getVideoUrl())
                .build();
    }
}
