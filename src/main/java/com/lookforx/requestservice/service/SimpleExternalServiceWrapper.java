package com.lookforx.requestservice.service;

import com.lookforx.requestservice.client.CategoryServiceClient;
import com.lookforx.requestservice.client.FormServiceClient;
import com.lookforx.requestservice.client.LocationServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * Simple wrapper service for external service calls (dev profile)
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Profile("dev")
public class SimpleExternalServiceWrapper {
    
    private final FormServiceClient formServiceClient;
    private final LocationServiceClient locationServiceClient;
    private final CategoryServiceClient categoryServiceClient;
    
    public CompletableFuture<Boolean> validateFormSubmission(String submissionFormId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Boolean result = formServiceClient.existsBySubmissionFormId(submissionFormId);
                return result != null && result;
            } catch (Exception e) {
                log.error("Error validating form submission: {}", submissionFormId, e);
                return true; // Return true for dev environment
            }
        });
    }
    
    public CompletableFuture<Boolean> validateCategory(String categoryId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Boolean result = categoryServiceClient.existsCategory(categoryId);
                return result != null && result;
            } catch (Exception e) {
                log.error("Error validating category: {}", categoryId, e);
                return true; // Return true for dev environment
            }
        });
    }
    
    public CompletableFuture<Boolean> validateLocation(String countryId, String cityId, String districtId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Boolean countryExists = locationServiceClient.existsCountry(countryId);
                if (countryExists == null || !countryExists) {
                    return false;
                }
                
                Boolean cityExists = locationServiceClient.existsCity(countryId, cityId);
                if (cityExists == null || !cityExists) {
                    return false;
                }
                
                Boolean districtExists = locationServiceClient.existsDistrict(countryId, cityId, districtId);
                return districtExists != null && districtExists;
                
            } catch (Exception e) {
                log.error("Error validating location: {}/{}/{}", countryId, cityId, districtId, e);
                return true; // Return true for dev environment
            }
        });
    }
}
