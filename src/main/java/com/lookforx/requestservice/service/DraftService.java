package com.lookforx.requestservice.service;

import com.lookforx.requestservice.domain.RequestDraft;
import com.lookforx.requestservice.dto.CreateRequestDto;
import com.lookforx.requestservice.dto.RequestDraftDto;
import com.lookforx.requestservice.mapper.RequestMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing request drafts
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DraftService {
    
    private final MongoTemplate mongoTemplate;
    private final RequestMapper requestMapper;
    
    /**
     * Save draft (manual save)
     */
    public RequestDraftDto saveDraft(String userId, CreateRequestDto createRequestDto) {
        try {
            RequestDraft draft = RequestDraft.builder()
                    .userId(userId)
                    .categoryId(createRequestDto.getCategoryId())
                    .submissionFormId(createRequestDto.getSubmissionFormId())
                    .title(createRequestDto.getTitle())
                    .description(createRequestDto.getDescription())
                    .location(requestMapper.toLocationInfo(createRequestDto.getLocation()))
                    .priceRange(requestMapper.toPriceRange(createRequestDto.getPriceRange()))
                    .media(requestMapper.toMediaInfo(createRequestDto.getMedia()))
                    .expiresAt(createRequestDto.getExpiresAt())
                    .autoSaved(false)
                    .completionPercentage(calculateCompletionPercentage(createRequestDto))
                    .createdAt(LocalDateTime.now())
                    .updatedAt(LocalDateTime.now())
                    .build();
            
            RequestDraft savedDraft = mongoTemplate.save(draft);
            return mapToDto(savedDraft);
            
        } catch (Exception e) {
            log.error("Error saving draft for user: {}", userId, e);
            throw new RuntimeException("Failed to save draft", e);
        }
    }
    
    /**
     * Auto-save draft (asynchronous)
     */
    @Async
    public void autoSaveDraft(String userId, CreateRequestDto createRequestDto) {
        try {
            // Check if there's an existing auto-saved draft for this user
            Query query = new Query();
            query.addCriteria(Criteria.where("userId").is(userId)
                    .and("autoSaved").is(true));
            
            RequestDraft existingDraft = mongoTemplate.findOne(query, RequestDraft.class);
            
            if (existingDraft != null) {
                // Update existing auto-saved draft
                updateDraftFromDto(existingDraft, createRequestDto);
                existingDraft.setUpdatedAt(LocalDateTime.now());
                existingDraft.setCompletionPercentage(calculateCompletionPercentage(createRequestDto));
                mongoTemplate.save(existingDraft);
            } else {
                // Create new auto-saved draft
                RequestDraft draft = RequestDraft.builder()
                        .userId(userId)
                        .categoryId(createRequestDto.getCategoryId())
                        .submissionFormId(createRequestDto.getSubmissionFormId())
                        .title(createRequestDto.getTitle())
                        .description(createRequestDto.getDescription())
                        .location(requestMapper.toLocationInfo(createRequestDto.getLocation()))
                        .priceRange(requestMapper.toPriceRange(createRequestDto.getPriceRange()))
                        .media(requestMapper.toMediaInfo(createRequestDto.getMedia()))
                        .expiresAt(createRequestDto.getExpiresAt())
                        .autoSaved(true)
                        .completionPercentage(calculateCompletionPercentage(createRequestDto))
                        .createdAt(LocalDateTime.now())
                        .updatedAt(LocalDateTime.now())
                        .build();
                
                mongoTemplate.save(draft);
            }
            
        } catch (Exception e) {
            log.error("Error auto-saving draft for user: {}", userId, e);
            // Don't throw exception for auto-save failures
        }
    }
    
    /**
     * Get user's drafts
     */
    public List<RequestDraftDto> getUserDrafts(String userId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("userId").is(userId));
            
            List<RequestDraft> drafts = mongoTemplate.find(query, RequestDraft.class);
            return drafts.stream()
                    .map(this::mapToDto)
                    .toList();
                    
        } catch (Exception e) {
            log.error("Error getting drafts for user: {}", userId, e);
            return List.of();
        }
    }
    
    /**
     * Get draft by ID
     */
    public Optional<RequestDraftDto> getDraftById(String draftId, String userId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").is(draftId)
                    .and("userId").is(userId));
            
            RequestDraft draft = mongoTemplate.findOne(query, RequestDraft.class);
            return draft != null ? Optional.of(mapToDto(draft)) : Optional.empty();
            
        } catch (Exception e) {
            log.error("Error getting draft: {} for user: {}", draftId, userId, e);
            return Optional.empty();
        }
    }
    
    /**
     * Delete draft
     */
    public void deleteDraft(String draftId, String userId) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("id").is(draftId)
                    .and("userId").is(userId));
            
            mongoTemplate.remove(query, RequestDraft.class);
            
        } catch (Exception e) {
            log.error("Error deleting draft: {} for user: {}", draftId, userId, e);
            throw new RuntimeException("Failed to delete draft", e);
        }
    }
    
    /**
     * Convert draft to request DTO
     */
    public CreateRequestDto convertDraftToRequest(RequestDraftDto draft) {
        return CreateRequestDto.builder()
                .userId(draft.getUserId())
                .categoryId(draft.getCategoryId())
                .submissionFormId(draft.getSubmissionFormId())
                .title(draft.getTitle())
                .description(draft.getDescription())
                .location(requestMapper.toLocationDto(draft.getLocation()))
                .priceRange(requestMapper.toPriceRangeDto(draft.getPriceRange()))
                .media(requestMapper.toMediaDto(draft.getMedia()))
                .expiresAt(draft.getExpiresAt())
                .build();
    }
    
    /**
     * Clean up old drafts (older than 30 days)
     */
    public void cleanupOldDrafts() {
        try {
            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            Query query = new Query();
            query.addCriteria(Criteria.where("updatedAt").lt(thirtyDaysAgo));
            
            long deletedCount = mongoTemplate.remove(query, RequestDraft.class).getDeletedCount();
            log.info("Cleaned up {} old drafts", deletedCount);
            
        } catch (Exception e) {
            log.error("Error cleaning up old drafts", e);
        }
    }
    
    private int calculateCompletionPercentage(CreateRequestDto dto) {
        int totalFields = 8;
        int completedFields = 0;
        
        if (dto.getTitle() != null && !dto.getTitle().trim().isEmpty()) completedFields++;
        if (dto.getDescription() != null && !dto.getDescription().trim().isEmpty()) completedFields++;
        if (dto.getCategoryId() != null) completedFields++;
        if (dto.getSubmissionFormId() != null) completedFields++;
        if (dto.getLocation() != null) completedFields++;
        if (dto.getPriceRange() != null) completedFields++;
        if (dto.getMedia() != null) completedFields++;
        if (dto.getExpiresAt() != null) completedFields++;
        
        return (completedFields * 100) / totalFields;
    }
    
    private void updateDraftFromDto(RequestDraft draft, CreateRequestDto dto) {
        if (dto.getCategoryId() != null) draft.setCategoryId(dto.getCategoryId());
        if (dto.getSubmissionFormId() != null) draft.setSubmissionFormId(dto.getSubmissionFormId());
        if (dto.getTitle() != null) draft.setTitle(dto.getTitle());
        if (dto.getDescription() != null) draft.setDescription(dto.getDescription());
        if (dto.getLocation() != null) draft.setLocation(requestMapper.toLocationInfo(dto.getLocation()));
        if (dto.getPriceRange() != null) draft.setPriceRange(requestMapper.toPriceRange(dto.getPriceRange()));
        if (dto.getMedia() != null) draft.setMedia(requestMapper.toMediaInfo(dto.getMedia()));
        if (dto.getExpiresAt() != null) draft.setExpiresAt(dto.getExpiresAt());
    }
    
    private RequestDraftDto mapToDto(RequestDraft draft) {
        return RequestDraftDto.builder()
                .id(draft.getId())
                .userId(draft.getUserId())
                .categoryId(draft.getCategoryId())
                .submissionFormId(draft.getSubmissionFormId())
                .title(draft.getTitle())
                .description(draft.getDescription())
                .location(draft.getLocation())
                .priceRange(draft.getPriceRange())
                .media(draft.getMedia())
                .expiresAt(draft.getExpiresAt())
                .createdAt(draft.getCreatedAt())
                .updatedAt(draft.getUpdatedAt())
                .autoSaved(draft.getAutoSaved())
                .completionPercentage(draft.getCompletionPercentage())
                .build();
    }
}
