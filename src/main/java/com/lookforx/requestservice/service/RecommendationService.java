package com.lookforx.requestservice.service;

import com.lookforx.requestservice.domain.Request;
import com.lookforx.requestservice.domain.RequestStatus;
import com.lookforx.requestservice.dto.RequestResponseDto;
import com.lookforx.requestservice.mapper.RequestMapper;
import com.lookforx.requestservice.repository.RequestRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for providing request recommendations
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RecommendationService {
    
    private final RequestRepository requestRepository;
    private final MongoTemplate mongoTemplate;
    private final RequestMapper requestMapper;
    
    /**
     * Get similar requests based on category and location
     */
    @Cacheable(value = "similar-requests", key = "#requestId")
    public List<RequestResponseDto> getSimilarRequests(String requestId, int limit) {
        try {
            Request request = requestRepository.findById(requestId).orElse(null);
            if (request == null) {
                return List.of();
            }
            
            Query query = new Query();
            query.addCriteria(Criteria.where("id").ne(requestId)
                    .and("status").is(RequestStatus.ACTIVE)
                    .and("deleted").ne(true));
            
            // Add category similarity
            if (request.getCategoryId() != null) {
                query.addCriteria(Criteria.where("categoryId").is(request.getCategoryId()));
            }
            
            // Add location similarity
            if (request.getLocation() != null) {
                Criteria locationCriteria = new Criteria();
                if (request.getLocation().getCountryId() != null) {
                    locationCriteria = locationCriteria.and("location.countryId")
                            .is(request.getLocation().getCountryId());
                }
                if (request.getLocation().getCityId() != null) {
                    locationCriteria = locationCriteria.and("location.cityId")
                            .is(request.getLocation().getCityId());
                }
                query.addCriteria(locationCriteria);
            }
            
            query.with(Sort.by(Sort.Direction.DESC, "createdAt"));
            query.limit(limit);
            
            List<Request> similarRequests = mongoTemplate.find(query, Request.class);
            return similarRequests.stream()
                    .map(requestMapper::toDto)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("Error getting similar requests for: {}", requestId, e);
            return List.of();
        }
    }
    
    /**
     * Get trending requests in user's location
     */
    @Cacheable(value = "trending-requests", key = "#countryId + '_' + #cityId")
    public List<RequestResponseDto> getTrendingRequests(String countryId, String cityId, int limit) {
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("status").is(RequestStatus.ACTIVE)
                    .and("deleted").ne(true));
            
            if (countryId != null) {
                query.addCriteria(Criteria.where("location.countryId").is(countryId));
            }
            
            if (cityId != null) {
                query.addCriteria(Criteria.where("location.cityId").is(cityId));
            }
            
            // Sort by creation date (trending = recently created)
            query.with(Sort.by(Sort.Direction.DESC, "createdAt"));
            query.limit(limit);
            
            List<Request> trendingRequests = mongoTemplate.find(query, Request.class);
            return trendingRequests.stream()
                    .map(requestMapper::toDto)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("Error getting trending requests for location: {}/{}", countryId, cityId, e);
            return List.of();
        }
    }
    
    /**
     * Get popular requests by category
     */
    @Cacheable(value = "popular-requests", key = "#categoryId")
    public List<RequestResponseDto> getPopularRequestsByCategory(String categoryId, int limit) {
        try {
            List<Request> popularRequests = requestRepository.findByCategoryIdAndStatus(
                    categoryId, 
                    RequestStatus.ACTIVE, 
                    PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdAt"))
            ).getContent();
            
            return popularRequests.stream()
                    .map(requestMapper::toDto)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("Error getting popular requests for category: {}", categoryId, e);
            return List.of();
        }
    }
    
    /**
     * Get recommendations for a user based on their previous requests
     */
    @Cacheable(value = "user-recommendations", key = "#userId")
    public List<RequestResponseDto> getRecommendationsForUser(String userId, int limit) {
        try {
            // Get user's recent requests to understand preferences
            List<Request> userRequests = requestRepository.findByUserId(
                    userId, 
                    PageRequest.of(0, 5, Sort.by(Sort.Direction.DESC, "createdAt"))
            ).getContent();
            
            if (userRequests.isEmpty()) {
                // If no history, return trending requests
                return getTrendingRequests(null, null, limit);
            }
            
            // Extract categories and locations from user's history
            List<String> userCategories = userRequests.stream()
                    .map(Request::getCategoryId)
                    .distinct()
                    .collect(Collectors.toList());
            
            List<String> userCountries = userRequests.stream()
                    .filter(r -> r.getLocation() != null && r.getLocation().getCountryId() != null)
                    .map(r -> r.getLocation().getCountryId())
                    .distinct()
                    .collect(Collectors.toList());
            
            Query query = new Query();
            query.addCriteria(Criteria.where("userId").ne(userId)
                    .and("status").is(RequestStatus.ACTIVE)
                    .and("deleted").ne(true));
            
            // Add preference-based criteria
            Criteria preferenceCriteria = new Criteria();
            if (!userCategories.isEmpty()) {
                preferenceCriteria = preferenceCriteria.orOperator(
                        Criteria.where("categoryId").in(userCategories)
                );
            }
            
            if (!userCountries.isEmpty()) {
                preferenceCriteria = preferenceCriteria.orOperator(
                        Criteria.where("location.countryId").in(userCountries)
                );
            }
            
            query.addCriteria(preferenceCriteria);
            query.with(Sort.by(Sort.Direction.DESC, "createdAt"));
            query.limit(limit);
            
            List<Request> recommendations = mongoTemplate.find(query, Request.class);
            return recommendations.stream()
                    .map(requestMapper::toDto)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("Error getting recommendations for user: {}", userId, e);
            return List.of();
        }
    }
}
