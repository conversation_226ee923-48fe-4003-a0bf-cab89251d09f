package com.lookforx.requestservice.service;

import com.lookforx.requestservice.domain.RequestStatus;
import com.lookforx.requestservice.dto.CreateRequestDto;
import com.lookforx.requestservice.dto.RequestResponseDto;
import com.lookforx.requestservice.dto.UpdateRequestDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service interface for Request operations
 */
public interface RequestService {
    
    /**
     * Create a new request
     */
    RequestResponseDto createRequest(CreateRequestDto createRequestDto);
    
    /**
     * Get request by ID
     */
    RequestResponseDto getRequestById(String id);
    
    /**
     * Update request
     */
    RequestResponseDto updateRequest(String id, UpdateRequestDto updateRequestDto, String userId);
    
    /**
     * Delete request
     */
    void deleteRequest(String id, String userId);
    
    /**
     * Get requests by user ID with pagination
     */
    Page<RequestResponseDto> getRequestsByUserId(String userId, Pageable pageable);
    
    /**
     * Get requests by category with pagination
     */
    Page<RequestResponseDto> getRequestsByCategory(String categoryId, Pageable pageable);
    
    /**
     * Get active requests with pagination
     */
    Page<RequestResponseDto> getActiveRequests(Pageable pageable);
    
    /**
     * Search requests by location and category
     */
    Page<RequestResponseDto> searchRequests(String countryId, String cityId, String districtId, String categoryId, String title, Pageable pageable);
    
    /**
     * Expire outdated requests
     */
    void expireRequests();
    
    /**
     * Change request status
     */
    RequestResponseDto changeRequestStatus(String id, RequestStatus status, String userId);
    
    /**
     * Get requests by user ID and status
     */
    Page<RequestResponseDto> getRequestsByUserIdAndStatus(String userId, RequestStatus status, Pageable pageable);
}
