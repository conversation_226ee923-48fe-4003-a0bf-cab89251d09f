package com.lookforx.requestservice.service.impl;

import com.lookforx.common.events.RequestCreatedEvent;
import com.lookforx.common.events.RequestStatusChangedEvent;
import com.lookforx.common.kafka.KafkaTopics;
import com.lookforx.requestservice.client.CategoryServiceClient;
import com.lookforx.requestservice.client.FormServiceClient;
import com.lookforx.requestservice.client.LocationServiceClient;
import com.lookforx.requestservice.domain.Request;
import com.lookforx.requestservice.domain.RequestStatus;
import com.lookforx.requestservice.dto.CreateRequestDto;
import com.lookforx.requestservice.dto.RequestResponseDto;
import com.lookforx.requestservice.dto.UpdateRequestDto;
import com.lookforx.requestservice.exception.RequestNotFoundException;
import com.lookforx.requestservice.exception.RequestValidationException;
import com.lookforx.requestservice.mapper.RequestMapper;
import com.lookforx.requestservice.repository.RequestRepository;
import com.lookforx.requestservice.service.RequestService;
import com.lookforx.requestservice.service.AuditService;
import com.lookforx.requestservice.service.DuplicateDetectionService;
import com.lookforx.requestservice.service.SimpleExternalServiceWrapper;
import com.lookforx.requestservice.service.MetricsService;
import com.lookforx.requestservice.service.RateLimitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Implementation of RequestService
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RequestServiceImpl implements RequestService {
    
    private final RequestRepository requestRepository;
    private final RequestMapper requestMapper;
    private final FormServiceClient formServiceClient;
    private final LocationServiceClient locationServiceClient;
    private final CategoryServiceClient categoryServiceClient;
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final SimpleExternalServiceWrapper externalServiceWrapper;
    private final RateLimitService rateLimitService;
    private final MetricsService metricsService;
    private final AuditService auditService;
    private final DuplicateDetectionService duplicateDetectionService;
    
    @Override
    @Transactional
    @CacheEvict(value = {"user-requests", "search-results", "category-requests", "popular-requests"}, allEntries = true)
    public RequestResponseDto createRequest(CreateRequestDto createRequestDto) {
        log.info("Creating request for user: {}", createRequestDto.getUserId());

        // Start metrics timer
        var timer = metricsService.startRequestCreationTimer();
        long startTime = System.currentTimeMillis();
        boolean success = false;
        String errorMessage = null;

        try {
            // Rate limiting check
            if (!rateLimitService.canCreateRequest(createRequestDto.getUserId())) {
                metricsService.incrementRateLimitExceeded();
                throw new RequestValidationException("Rate limit exceeded for request creation");
            }

            // Duplicate detection
            if (duplicateDetectionService.isDuplicateRequest(createRequestDto)) {
                log.warn("Potential duplicate request detected for user: {}", createRequestDto.getUserId());
                // You might want to return the duplicates or just warn
            }

            // Validate external dependencies with circuit breaker
            validateExternalDependenciesAsync(createRequestDto);

            // Map DTO to domain entity
            Request request = requestMapper.toEntity(createRequestDto);
            request.setCreatedAt(LocalDateTime.now());
            request.setUpdatedAt(LocalDateTime.now());
            request.setStatus(RequestStatus.ACTIVE);
            request.setDeleted(false);

            // Save request
            Request savedRequest = requestRepository.save(request);

            // Publish event
            publishRequestCreatedEvent(savedRequest);

            // Update metrics
            metricsService.incrementRequestsCreated();
            success = true;

            log.info("Request created successfully with ID: {}", savedRequest.getId());
            return requestMapper.toDto(savedRequest);

        } catch (Exception e) {
            errorMessage = e.getMessage();
            metricsService.incrementValidationErrors();
            throw e;
        } finally {
            // Record metrics
            metricsService.recordRequestCreationTime(timer);

            // Audit logging
            long duration = System.currentTimeMillis() - startTime;
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("title", createRequestDto.getTitle());
            requestData.put("categoryId", createRequestDto.getCategoryId());

            auditService.logRequestCreation(
                null, // Request ID not available in case of failure
                createRequestDto.getUserId(),
                null, // IP address would come from request context
                requestData,
                success,
                errorMessage,
                duration
            );
        }
    }
    
    @Override
    @Cacheable(value = "requests", key = "#id")
    public RequestResponseDto getRequestById(String id) {
        log.info("Fetching request with ID: {}", id);

        long startTime = System.currentTimeMillis();
        boolean success = false;

        try {
            Request request = requestRepository.findById(id)
                    .filter(r -> !Boolean.TRUE.equals(r.getDeleted())) // Exclude soft deleted
                    .orElseThrow(() -> new RequestNotFoundException("Request not found with ID: " + id));

            // Update metrics
            metricsService.incrementRequestsViewed();
            success = true;

            return requestMapper.toDto(request);

        } finally {
            // Audit logging
            long duration = System.currentTimeMillis() - startTime;
            auditService.logRequestView(id, null, null, success, duration);
        }
    }
    
    @Override
    @Transactional
    public RequestResponseDto updateRequest(String id, UpdateRequestDto updateRequestDto, String userId) {
        log.info("Updating request with ID: {} for user: {}", id, userId);
        
        Request request = requestRepository.findByIdAndUserId(id, userId)
                .orElseThrow(() -> new RequestNotFoundException("Request not found or unauthorized"));
        
        RequestStatus oldStatus = request.getStatus();
        
        // Update only allowed fields
        if (updateRequestDto.getTitle() != null) {
            request.setTitle(updateRequestDto.getTitle());
        }
        if (updateRequestDto.getDescription() != null) {
            request.setDescription(updateRequestDto.getDescription());
        }
        if (updateRequestDto.getPriceRange() != null) {
            request.setPriceRange(requestMapper.toPriceRange(updateRequestDto.getPriceRange()));
        }
        if (updateRequestDto.getMedia() != null) {
            request.setMedia(requestMapper.toMediaInfo(updateRequestDto.getMedia()));
        }
        if (updateRequestDto.getExpiresAt() != null) {
            validateExpiryDate(updateRequestDto.getExpiresAt());
            request.setExpiresAt(updateRequestDto.getExpiresAt());
        }
        
        request.setUpdatedAt(LocalDateTime.now());
        
        Request updatedRequest = requestRepository.save(request);
        
        log.info("Request updated successfully with ID: {}", updatedRequest.getId());
        return requestMapper.toDto(updatedRequest);
    }
    
    @Override
    @Transactional
    @CacheEvict(value = {"requests", "user-requests", "search-results", "category-requests"}, allEntries = true)
    public void deleteRequest(String id, String userId) {
        log.info("Soft deleting request with ID: {} for user: {}", id, userId);

        long startTime = System.currentTimeMillis();
        boolean success = false;
        String errorMessage = null;

        try {
            Request request = requestRepository.findByIdAndUserId(id, userId)
                    .filter(r -> !Boolean.TRUE.equals(r.getDeleted()))
                    .orElseThrow(() -> new RequestNotFoundException("Request not found or unauthorized"));

            // Soft delete
            request.setDeleted(true);
            request.setDeletedAt(LocalDateTime.now());
            request.setDeletedBy(userId);
            request.setUpdatedAt(LocalDateTime.now());

            requestRepository.save(request);

            // Update metrics
            metricsService.incrementRequestsDeleted();
            success = true;

            log.info("Request soft deleted successfully with ID: {}", id);

        } catch (Exception e) {
            errorMessage = e.getMessage();
            throw e;
        } finally {
            // Audit logging
            long duration = System.currentTimeMillis() - startTime;
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("requestId", id);

            auditService.logRequestDeletion(id, userId, null, requestData, success, errorMessage, duration);
        }
    }
    
    @Override
    public Page<RequestResponseDto> getRequestsByUserId(String userId, Pageable pageable) {
        log.info("Fetching requests for user: {}", userId);
        
        Page<Request> requests = requestRepository.findByUserId(userId, pageable);
        return requests.map(requestMapper::toDto);
    }
    
    @Override
    public Page<RequestResponseDto> getRequestsByCategory(String categoryId, Pageable pageable) {
        log.info("Fetching requests for category: {}", categoryId);
        
        Page<Request> requests = requestRepository.findByCategoryIdAndStatus(categoryId, RequestStatus.ACTIVE, pageable);
        return requests.map(requestMapper::toDto);
    }
    
    @Override
    public Page<RequestResponseDto> getActiveRequests(Pageable pageable) {
        log.info("Fetching active requests");
        
        Page<Request> requests = requestRepository.findByStatus(RequestStatus.ACTIVE, pageable);
        return requests.map(requestMapper::toDto);
    }
    
    @Override
    public Page<RequestResponseDto> searchRequests(String countryId, String cityId, String districtId, String categoryId, String title, Pageable pageable) {
        log.info("Searching requests with filters - country: {}, city: {}, district: {}, category: {}, title: {}", 
                countryId, cityId, districtId, categoryId, title);
        
        Page<Request> requests;
        
        if (title != null && !title.trim().isEmpty()) {
            requests = requestRepository.findByTitleContainingIgnoreCaseAndActiveStatus(title.trim(), pageable);
        } else if (countryId != null && cityId != null && districtId != null) {
            requests = requestRepository.findByFullLocationAndActiveStatus(countryId, cityId, districtId, pageable);
        } else if (countryId != null && cityId != null) {
            requests = requestRepository.findByLocationAndActiveStatus(countryId, cityId, pageable);
        } else if (categoryId != null) {
            requests = requestRepository.findByCategoryIdAndStatus(categoryId, RequestStatus.ACTIVE, pageable);
        } else {
            requests = requestRepository.findByStatus(RequestStatus.ACTIVE, pageable);
        }
        
        return requests.map(requestMapper::toDto);
    }
    
    @Override
    @Transactional
    public void expireRequests() {
        log.info("Expiring outdated requests");
        
        List<Request> expiredRequests = requestRepository.findExpiredRequests(LocalDateTime.now());
        
        expiredRequests.forEach(request -> {
            RequestStatus oldStatus = request.getStatus();
            request.setStatus(RequestStatus.EXPIRED);
            request.setUpdatedAt(LocalDateTime.now());
            
            // Publish status change event
            publishRequestStatusChangedEvent(request, oldStatus, RequestStatus.EXPIRED);
        });
        
        requestRepository.saveAll(expiredRequests);
        
        log.info("Expired {} requests", expiredRequests.size());
    }
    
    @Override
    @Transactional
    public RequestResponseDto changeRequestStatus(String id, RequestStatus status, String userId) {
        log.info("Changing request status to {} for request: {} by user: {}", status, id, userId);
        
        Request request = requestRepository.findByIdAndUserId(id, userId)
                .orElseThrow(() -> new RequestNotFoundException("Request not found or unauthorized"));
        
        RequestStatus oldStatus = request.getStatus();
        request.setStatus(status);
        request.setUpdatedAt(LocalDateTime.now());
        
        Request updatedRequest = requestRepository.save(request);
        
        // Publish status change event
        publishRequestStatusChangedEvent(updatedRequest, oldStatus, status);
        
        log.info("Request status changed successfully");
        return requestMapper.toDto(updatedRequest);
    }
    
    @Override
    public Page<RequestResponseDto> getRequestsByUserIdAndStatus(String userId, RequestStatus status, Pageable pageable) {
        log.info("Fetching requests for user: {} with status: {}", userId, status);
        
        Page<Request> requests = requestRepository.findByUserIdAndStatus(userId, status, pageable);
        return requests.map(requestMapper::toDto);
    }
    
    private void validateExternalDependenciesAsync(CreateRequestDto createRequestDto) {
        var timer = metricsService.startExternalServiceCallTimer();

        try {
            // Async validation with circuit breaker
            CompletableFuture<Boolean> formValidation = externalServiceWrapper
                    .validateFormSubmission(createRequestDto.getSubmissionFormId());

            CompletableFuture<Boolean> categoryValidation = externalServiceWrapper
                    .validateCategory(createRequestDto.getCategoryId());

            CompletableFuture<Boolean> locationValidation = externalServiceWrapper
                    .validateLocation(
                            createRequestDto.getLocation().getCountryId(),
                            createRequestDto.getLocation().getCityId(),
                            createRequestDto.getLocation().getDistrictId()
                    );

            // Wait for all validations to complete
            try {
                CompletableFuture.allOf(formValidation, categoryValidation, locationValidation).join();
            } catch (Exception e) {
                throw new RequestValidationException("Validation timeout or failure: " + e.getMessage());
            }

            // Check results
            try {
                if (!formValidation.get()) {
                    throw new RequestValidationException("Invalid submission form ID: " + createRequestDto.getSubmissionFormId());
                }

                if (!categoryValidation.get()) {
                    throw new RequestValidationException("Invalid category ID: " + createRequestDto.getCategoryId());
                }

                if (!locationValidation.get()) {
                    throw new RequestValidationException("Invalid location information");
                }
            } catch (InterruptedException | java.util.concurrent.ExecutionException e) {
                throw new RequestValidationException("Validation execution failed: " + e.getMessage());
            }

            // Validate expiry date (max 60 days)
            validateExpiryDate(createRequestDto.getExpiresAt());

        } catch (Exception e) {
            metricsService.incrementExternalServiceErrors();
            if (e instanceof RequestValidationException) {
                throw e;
            }
            throw new RequestValidationException("External service validation failed: " + e.getMessage());
        } finally {
            metricsService.recordExternalServiceCallTime(timer);
        }
    }

    private void validateExternalDependencies(CreateRequestDto createRequestDto) {
        // Fallback synchronous validation method
        validateExternalDependenciesAsync(createRequestDto);
    }
    
    private void validateLocation(String countryId, String cityId, String districtId) {
        try {
            Boolean countryExists = locationServiceClient.existsCountry(countryId);
            if (countryExists == null || !countryExists) {
                throw new RequestValidationException("Invalid country ID: " + countryId);
            }
            
            Boolean cityExists = locationServiceClient.existsCity(countryId, cityId);
            if (cityExists == null || !cityExists) {
                throw new RequestValidationException("Invalid city ID: " + cityId);
            }
            
            Boolean districtExists = locationServiceClient.existsDistrict(countryId, cityId, districtId);
            if (districtExists == null || !districtExists) {
                throw new RequestValidationException("Invalid district ID: " + districtId);
            }
        } catch (Exception e) {
            log.error("Error validating location: {}", e.getMessage());
            throw new RequestValidationException("Unable to validate location information");
        }
    }
    
    private void validateExpiryDate(LocalDateTime expiresAt) {
        if (expiresAt.isAfter(LocalDateTime.now().plusDays(60))) {
            throw new RequestValidationException("Expiry date cannot be more than 60 days from now");
        }
    }
    
    private void publishRequestCreatedEvent(Request request) {
        try {
            // Convert String IDs to Long for event compatibility
            Long requestIdLong = null;
            Long userIdLong = null;
            Long categoryIdLong = null;

            try {
                requestIdLong = Long.valueOf(request.getId());
            } catch (NumberFormatException e) {
                log.warn("Cannot convert request ID to Long: {}", request.getId());
            }

            try {
                userIdLong = Long.valueOf(request.getUserId());
            } catch (NumberFormatException e) {
                log.warn("Cannot convert user ID to Long: {}", request.getUserId());
            }

            try {
                categoryIdLong = Long.valueOf(request.getCategoryId());
            } catch (NumberFormatException e) {
                log.warn("Cannot convert category ID to Long: {}", request.getCategoryId());
            }

            RequestCreatedEvent event = RequestCreatedEvent.builder()
                    .requestId(requestIdLong)
                    .userId(String.valueOf(userIdLong))
                    .categoryId(categoryIdLong)
                    .title(request.getTitle())
                    .description(request.getDescription())
                    .budget(request.getPriceRange() != null ? request.getPriceRange().getMaxPrice() : null)
                    .currency(request.getPriceRange() != null ? request.getPriceRange().getCurrency() : null)
                    .deadline(request.getExpiresAt() != null ? request.getExpiresAt().toString() : null)
                    .serviceName("request-microservice")
                    .build();

            kafkaTemplate.send(KafkaTopics.REQUEST_EVENTS, event);
            log.info("Published RequestCreatedEvent for request: {}", request.getId());
        } catch (Exception e) {
            log.error("Error publishing RequestCreatedEvent: {}", e.getMessage(), e);
        }
    }

    private void publishRequestStatusChangedEvent(Request request, RequestStatus oldStatus, RequestStatus newStatus) {
        try {
            // Convert String IDs to Long for event compatibility
            Long requestIdLong = null;
            Long userIdLong = null;

            try {
                requestIdLong = Long.valueOf(request.getId());
            } catch (NumberFormatException e) {
                log.warn("Cannot convert request ID to Long: {}", request.getId());
            }

            try {
                userIdLong = Long.valueOf(request.getUserId());
            } catch (NumberFormatException e) {
                log.warn("Cannot convert user ID to Long: {}", request.getUserId());
            }

            RequestStatusChangedEvent event = RequestStatusChangedEvent.builder()
                    .requestId(requestIdLong)
                    .userId(String.valueOf(userIdLong))
                    .requestTitle(request.getTitle())
                    .oldStatus(oldStatus.name())
                    .newStatus(newStatus.name())
                    .serviceName("request-microservice")
                    .build();

            kafkaTemplate.send(KafkaTopics.REQUEST_EVENTS, event);
            log.info("Published RequestStatusChangedEvent for request: {}", request.getId());
        } catch (Exception e) {
            log.error("Error publishing RequestStatusChangedEvent: {}", e.getMessage(), e);
        }
    }
}
