package com.lookforx.requestservice.service;

import com.lookforx.requestservice.client.CategoryServiceClient;
import com.lookforx.requestservice.client.FormServiceClient;
import com.lookforx.requestservice.client.LocationServiceClient;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * Wrapper service for external service calls with circuit breaker, retry, and timeout
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Profile("!dev") // Disable for dev profile to avoid Resilience4j issues
public class ExternalServiceWrapper {
    
    private final FormServiceClient formServiceClient;
    private final LocationServiceClient locationServiceClient;
    private final CategoryServiceClient categoryServiceClient;
    
    @CircuitBreaker(name = "form-service", fallbackMethod = "formServiceFallback")
    @Retry(name = "form-service")
    @TimeLimiter(name = "form-service")
    public CompletableFuture<Boolean> validateFormSubmission(String submissionFormId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Boolean result = formServiceClient.existsBySubmissionFormId(submissionFormId);
                return result != null && result;
            } catch (Exception e) {
                log.error("Error validating form submission: {}", submissionFormId, e);
                throw e;
            }
        });
    }
    
    @CircuitBreaker(name = "category-service", fallbackMethod = "categoryServiceFallback")
    @Retry(name = "category-service")
    @TimeLimiter(name = "category-service")
    public CompletableFuture<Boolean> validateCategory(String categoryId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Boolean result = categoryServiceClient.existsCategory(categoryId);
                return result != null && result;
            } catch (Exception e) {
                log.error("Error validating category: {}", categoryId, e);
                throw e;
            }
        });
    }
    
    @CircuitBreaker(name = "location-service", fallbackMethod = "locationServiceFallback")
    @Retry(name = "location-service")
    @TimeLimiter(name = "location-service")
    public CompletableFuture<Boolean> validateLocation(String countryId, String cityId, String districtId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Boolean countryExists = locationServiceClient.existsCountry(countryId);
                if (countryExists == null || !countryExists) {
                    return false;
                }
                
                Boolean cityExists = locationServiceClient.existsCity(countryId, cityId);
                if (cityExists == null || !cityExists) {
                    return false;
                }
                
                Boolean districtExists = locationServiceClient.existsDistrict(countryId, cityId, districtId);
                return districtExists != null && districtExists;
                
            } catch (Exception e) {
                log.error("Error validating location: {}/{}/{}", countryId, cityId, districtId, e);
                throw e;
            }
        });
    }
    
    // Fallback methods
    public CompletableFuture<Boolean> formServiceFallback(String submissionFormId, Exception ex) {
        log.warn("Form service fallback triggered for submission: {}, error: {}", submissionFormId, ex.getMessage());
        // In case of service failure, we might want to allow the request to proceed
        // This depends on business requirements
        return CompletableFuture.completedFuture(true);
    }
    
    public CompletableFuture<Boolean> categoryServiceFallback(String categoryId, Exception ex) {
        log.warn("Category service fallback triggered for category: {}, error: {}", categoryId, ex.getMessage());
        // For category validation, we might be more strict
        return CompletableFuture.completedFuture(false);
    }
    
    public CompletableFuture<Boolean> locationServiceFallback(String countryId, String cityId, String districtId, Exception ex) {
        log.warn("Location service fallback triggered for location: {}/{}/{}, error: {}", 
                countryId, cityId, districtId, ex.getMessage());
        // For location validation, we might allow if it's a known country
        return CompletableFuture.completedFuture(isKnownCountry(countryId));
    }
    
    private boolean isKnownCountry(String countryId) {
        // Basic fallback validation for common countries
        return countryId != null && (
                countryId.equals("TR") || 
                countryId.equals("US") || 
                countryId.equals("GB") || 
                countryId.equals("DE") ||
                countryId.equals("FR")
        );
    }
}
