package com.lookforx.requestservice.service;

import com.lookforx.requestservice.domain.Request;
import com.lookforx.requestservice.domain.RequestStatus;
import com.lookforx.requestservice.dto.CreateRequestDto;
import com.lookforx.requestservice.dto.RequestResponseDto;
import com.lookforx.requestservice.mapper.RequestMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for detecting duplicate requests
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DuplicateDetectionService {
    
    private final MongoTemplate mongoTemplate;
    private final RequestMapper requestMapper;
    
    /**
     * Check for potential duplicate requests
     */
    public List<RequestResponseDto> findPotentialDuplicates(CreateRequestDto createRequestDto) {
        try {
            Query query = new Query();
            
            // Check for requests from the same user
            query.addCriteria(Criteria.where("userId").is(createRequestDto.getUserId())
                    .and("status").is(RequestStatus.ACTIVE)
                    .and("deleted").ne(true));
            
            // Check for similar title (case-insensitive)
            String titlePattern = ".*" + escapeRegex(createRequestDto.getTitle().toLowerCase()) + ".*";
            query.addCriteria(Criteria.where("title").regex(titlePattern, "i"));
            
            // Check for same category
            if (createRequestDto.getCategoryId() != null) {
                query.addCriteria(Criteria.where("categoryId").is(createRequestDto.getCategoryId()));
            }
            
            // Check for same location
            if (createRequestDto.getLocation() != null) {
                Criteria locationCriteria = new Criteria();
                if (createRequestDto.getLocation().getCountryId() != null) {
                    locationCriteria = locationCriteria.and("location.countryId")
                            .is(createRequestDto.getLocation().getCountryId());
                }
                if (createRequestDto.getLocation().getCityId() != null) {
                    locationCriteria = locationCriteria.and("location.cityId")
                            .is(createRequestDto.getLocation().getCityId());
                }
                query.addCriteria(locationCriteria);
            }
            
            // Only check recent requests (last 30 days)
            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            query.addCriteria(Criteria.where("createdAt").gte(thirtyDaysAgo));
            
            List<Request> potentialDuplicates = mongoTemplate.find(query, Request.class);
            
            // Filter by similarity score
            return potentialDuplicates.stream()
                    .filter(request -> calculateSimilarityScore(createRequestDto, request) > 0.7)
                    .map(requestMapper::toDto)
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.error("Error finding potential duplicates", e);
            return List.of();
        }
    }
    
    /**
     * Check if a request is likely a duplicate
     */
    public boolean isDuplicateRequest(CreateRequestDto createRequestDto) {
        List<RequestResponseDto> duplicates = findPotentialDuplicates(createRequestDto);
        return !duplicates.isEmpty();
    }
    
    /**
     * Calculate similarity score between two requests
     */
    private double calculateSimilarityScore(CreateRequestDto newRequest, Request existingRequest) {
        double score = 0.0;
        int factors = 0;
        
        // Title similarity (40% weight)
        if (newRequest.getTitle() != null && existingRequest.getTitle() != null) {
            double titleSimilarity = calculateStringSimilarity(
                    newRequest.getTitle().toLowerCase(), 
                    existingRequest.getTitle().toLowerCase()
            );
            score += titleSimilarity * 0.4;
            factors++;
        }
        
        // Description similarity (30% weight)
        if (newRequest.getDescription() != null && existingRequest.getDescription() != null) {
            double descSimilarity = calculateStringSimilarity(
                    newRequest.getDescription().toLowerCase(), 
                    existingRequest.getDescription().toLowerCase()
            );
            score += descSimilarity * 0.3;
            factors++;
        }
        
        // Category match (20% weight)
        if (newRequest.getCategoryId() != null && existingRequest.getCategoryId() != null) {
            if (newRequest.getCategoryId().equals(existingRequest.getCategoryId())) {
                score += 0.2;
            }
            factors++;
        }
        
        // Location match (10% weight)
        if (newRequest.getLocation() != null && existingRequest.getLocation() != null) {
            double locationSimilarity = calculateLocationSimilarity(
                    newRequest.getLocation(), 
                    existingRequest.getLocation()
            );
            score += locationSimilarity * 0.1;
            factors++;
        }
        
        return factors > 0 ? score : 0.0;
    }
    
    /**
     * Calculate string similarity using Levenshtein distance
     */
    private double calculateStringSimilarity(String s1, String s2) {
        if (s1 == null || s2 == null) return 0.0;
        if (s1.equals(s2)) return 1.0;
        
        int maxLength = Math.max(s1.length(), s2.length());
        if (maxLength == 0) return 1.0;
        
        int distance = levenshteinDistance(s1, s2);
        return 1.0 - (double) distance / maxLength;
    }
    
    /**
     * Calculate Levenshtein distance between two strings
     */
    private int levenshteinDistance(String s1, String s2) {
        int[][] dp = new int[s1.length() + 1][s2.length() + 1];
        
        for (int i = 0; i <= s1.length(); i++) {
            dp[i][0] = i;
        }
        
        for (int j = 0; j <= s2.length(); j++) {
            dp[0][j] = j;
        }
        
        for (int i = 1; i <= s1.length(); i++) {
            for (int j = 1; j <= s2.length(); j++) {
                int cost = s1.charAt(i - 1) == s2.charAt(j - 1) ? 0 : 1;
                dp[i][j] = Math.min(Math.min(
                        dp[i - 1][j] + 1,      // deletion
                        dp[i][j - 1] + 1),     // insertion
                        dp[i - 1][j - 1] + cost // substitution
                );
            }
        }
        
        return dp[s1.length()][s2.length()];
    }
    
    /**
     * Calculate location similarity
     */
    private double calculateLocationSimilarity(
            com.lookforx.requestservice.dto.LocationDto loc1,
            com.lookforx.requestservice.domain.LocationInfo loc2) {
        
        double similarity = 0.0;
        int matches = 0;
        int total = 0;
        
        // Country match
        if (loc1.getCountryId() != null && loc2.getCountryId() != null) {
            if (loc1.getCountryId().equals(loc2.getCountryId())) {
                matches++;
            }
            total++;
        }
        
        // City match
        if (loc1.getCityId() != null && loc2.getCityId() != null) {
            if (loc1.getCityId().equals(loc2.getCityId())) {
                matches++;
            }
            total++;
        }
        
        // District match
        if (loc1.getDistrictId() != null && loc2.getDistrictId() != null) {
            if (loc1.getDistrictId().equals(loc2.getDistrictId())) {
                matches++;
            }
            total++;
        }
        
        return total > 0 ? (double) matches / total : 0.0;
    }
    
    /**
     * Escape special regex characters
     */
    private String escapeRegex(String input) {
        return input.replaceAll("[.*+?^${}()|\\[\\]\\\\]", "\\\\$0");
    }
}
