package com.lookforx.requestservice.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * Redis-based distributed rate limiting service
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class RateLimitService {
    
    private final StringRedisTemplate redisTemplate;
    
    // Rate limits per hour
    private static final int CREATE_REQUEST_LIMIT = 10; // 10 requests per hour per user
    private static final int SEARCH_LIMIT = 100; // 100 searches per hour per user
    private static final int UPDATE_LIMIT = 20; // 20 updates per hour per user
    
    /**
     * Check if user can create a request
     */
    public boolean canCreateRequest(String userId) {
        return checkRateLimit("create_request:" + userId, CREATE_REQUEST_LIMIT, Duration.ofHours(1));
    }
    
    /**
     * Check if user can perform search
     */
    public boolean canSearch(String userId) {
        return checkRateLimit("search:" + userId, SEARCH_LIMIT, Duration.ofHours(1));
    }
    
    /**
     * Check if user can update request
     */
    public boolean canUpdateRequest(String userId) {
        return checkRateLimit("update_request:" + userId, UPDATE_LIMIT, Duration.ofHours(1));
    }
    
    /**
     * Check if IP can make requests (for anonymous users)
     */
    public boolean canMakeRequest(String ipAddress) {
        return checkRateLimit("ip:" + ipAddress, 50, Duration.ofHours(1));
    }
    
    /**
     * Generic rate limiting check using sliding window
     */
    private boolean checkRateLimit(String key, int limit, Duration window) {
        try {
            String currentCount = redisTemplate.opsForValue().get(key);
            
            if (currentCount == null) {
                // First request
                redisTemplate.opsForValue().set(key, "1", window);
                return true;
            }
            
            int count = Integer.parseInt(currentCount);
            if (count >= limit) {
                log.warn("Rate limit exceeded for key: {}, current count: {}, limit: {}", key, count, limit);
                return false;
            }
            
            // Increment counter
            redisTemplate.opsForValue().increment(key);
            
            // Set expiry if not set (in case of race condition)
            if (redisTemplate.getExpire(key, TimeUnit.SECONDS) == -1) {
                redisTemplate.expire(key, window);
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("Error checking rate limit for key: {}", key, e);
            // In case of Redis failure, allow the request (fail open)
            return true;
        }
    }
    
    /**
     * Get remaining requests for a key
     */
    public int getRemainingRequests(String userId, String operation) {
        String key = operation + ":" + userId;
        int limit = getLimit(operation);
        
        try {
            String currentCount = redisTemplate.opsForValue().get(key);
            if (currentCount == null) {
                return limit;
            }
            
            int count = Integer.parseInt(currentCount);
            return Math.max(0, limit - count);
            
        } catch (Exception e) {
            log.error("Error getting remaining requests for key: {}", key, e);
            return limit; // Return full limit in case of error
        }
    }
    
    private int getLimit(String operation) {
        return switch (operation) {
            case "create_request" -> CREATE_REQUEST_LIMIT;
            case "search" -> SEARCH_LIMIT;
            case "update_request" -> UPDATE_LIMIT;
            default -> 50;
        };
    }
}
