package com.lookforx.requestservice.service;

import com.lookforx.requestservice.domain.AuditLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Service for audit logging
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AuditService {
    
    private final MongoTemplate mongoTemplate;
    
    @Async
    public void logOperation(String entityType, String entityId, String operation, 
                           String userId, String ipAddress, String userAgent,
                           Map<String, Object> oldValues, Map<String, Object> newValues,
                           boolean success, String errorMessage, long durationMs) {
        try {
            AuditLog auditLog = AuditLog.builder()
                    .entityType(entityType)
                    .entityId(entityId)
                    .operation(operation)
                    .userId(userId)
                    .ipAddress(ipAddress)
                    .userAgent(userAgent)
                    .oldValues(oldValues)
                    .newValues(newValues)
                    .timestamp(LocalDateTime.now())
                    .success(success)
                    .errorMessage(errorMessage)
                    .durationMs(durationMs)
                    .build();
            
            mongoTemplate.save(auditLog);
            
        } catch (Exception e) {
            log.error("Failed to save audit log", e);
        }
    }
    
    @Async
    public void logRequestCreation(String requestId, String userId, String ipAddress, 
                                 Map<String, Object> requestData, boolean success, 
                                 String errorMessage, long durationMs) {
        logOperation("REQUEST", requestId, "CREATE", userId, ipAddress, null,
                    null, requestData, success, errorMessage, durationMs);
    }
    
    @Async
    public void logRequestUpdate(String requestId, String userId, String ipAddress,
                               Map<String, Object> oldValues, Map<String, Object> newValues,
                               boolean success, String errorMessage, long durationMs) {
        logOperation("REQUEST", requestId, "UPDATE", userId, ipAddress, null,
                    oldValues, newValues, success, errorMessage, durationMs);
    }
    
    @Async
    public void logRequestDeletion(String requestId, String userId, String ipAddress,
                                 Map<String, Object> requestData, boolean success,
                                 String errorMessage, long durationMs) {
        logOperation("REQUEST", requestId, "DELETE", userId, ipAddress, null,
                    requestData, null, success, errorMessage, durationMs);
    }
    
    @Async
    public void logRequestView(String requestId, String userId, String ipAddress,
                             boolean success, long durationMs) {
        logOperation("REQUEST", requestId, "VIEW", userId, ipAddress, null,
                    null, null, success, null, durationMs);
    }
}
