package com.lookforx.requestservice.service;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;

import java.util.concurrent.atomic.AtomicLong;

/**
 * Custom metrics service for business and technical metrics
 */
@Service
@RequiredArgsConstructor
public class MetricsService {
    
    private final MeterRegistry meterRegistry;
    
    // Counters
    private Counter requestsCreated;
    private Counter requestsUpdated;
    private Counter requestsDeleted;
    private Counter requestsViewed;
    private Counter searchesPerformed;
    private Counter rateLimitExceeded;
    private Counter validationErrors;
    private Counter externalServiceErrors;

    // Timers
    private Timer requestCreationTime;
    private Timer requestUpdateTime;
    private Timer searchTime;
    private Timer externalServiceCallTime;

    // Gauges
    private final AtomicLong activeRequests = new AtomicLong(0);
    private final AtomicLong totalRequests = new AtomicLong(0);

    @PostConstruct
    public void initializeMetrics() {
        // Initialize counters
        this.requestsCreated = Counter.builder("requests.created")
                .description("Number of requests created")
                .register(meterRegistry);

        this.requestsUpdated = Counter.builder("requests.updated")
                .description("Number of requests updated")
                .register(meterRegistry);

        this.requestsDeleted = Counter.builder("requests.deleted")
                .description("Number of requests deleted")
                .register(meterRegistry);

        this.requestsViewed = Counter.builder("requests.viewed")
                .description("Number of requests viewed")
                .register(meterRegistry);

        this.searchesPerformed = Counter.builder("searches.performed")
                .description("Number of searches performed")
                .register(meterRegistry);

        this.rateLimitExceeded = Counter.builder("rate.limit.exceeded")
                .description("Number of rate limit violations")
                .register(meterRegistry);

        this.validationErrors = Counter.builder("validation.errors")
                .description("Number of validation errors")
                .register(meterRegistry);

        this.externalServiceErrors = Counter.builder("external.service.errors")
                .description("Number of external service errors")
                .register(meterRegistry);

        // Initialize timers
        this.requestCreationTime = Timer.builder("request.creation.time")
                .description("Time taken to create a request")
                .register(meterRegistry);

        this.requestUpdateTime = Timer.builder("request.update.time")
                .description("Time taken to update a request")
                .register(meterRegistry);

        this.searchTime = Timer.builder("search.time")
                .description("Time taken to perform search")
                .register(meterRegistry);

        this.externalServiceCallTime = Timer.builder("external.service.call.time")
                .description("Time taken for external service calls")
                .register(meterRegistry);

        // Initialize gauges using MeterRegistry directly
        meterRegistry.gauge("requests.active", activeRequests, AtomicLong::doubleValue);
        meterRegistry.gauge("requests.total", totalRequests, AtomicLong::doubleValue);
    }
    
    // Counter methods
    public void incrementRequestsCreated() {
        requestsCreated.increment();
        totalRequests.incrementAndGet();
        activeRequests.incrementAndGet();
    }
    
    public void incrementRequestsUpdated() {
        requestsUpdated.increment();
    }
    
    public void incrementRequestsDeleted() {
        requestsDeleted.increment();
        activeRequests.decrementAndGet();
    }
    
    public void incrementRequestsViewed() {
        requestsViewed.increment();
    }
    
    public void incrementSearchesPerformed() {
        searchesPerformed.increment();
    }
    
    public void incrementRateLimitExceeded() {
        rateLimitExceeded.increment();
    }
    
    public void incrementValidationErrors() {
        validationErrors.increment();
    }
    
    public void incrementExternalServiceErrors() {
        externalServiceErrors.increment();
    }
    
    // Timer methods
    public Timer.Sample startRequestCreationTimer() {
        return Timer.start(meterRegistry);
    }
    
    public void recordRequestCreationTime(Timer.Sample sample) {
        sample.stop(requestCreationTime);
    }
    
    public Timer.Sample startRequestUpdateTimer() {
        return Timer.start(meterRegistry);
    }
    
    public void recordRequestUpdateTime(Timer.Sample sample) {
        sample.stop(requestUpdateTime);
    }
    
    public Timer.Sample startSearchTimer() {
        return Timer.start(meterRegistry);
    }
    
    public void recordSearchTime(Timer.Sample sample) {
        sample.stop(searchTime);
    }
    
    public Timer.Sample startExternalServiceCallTimer() {
        return Timer.start(meterRegistry);
    }
    
    public void recordExternalServiceCallTime(Timer.Sample sample) {
        sample.stop(externalServiceCallTime);
    }
    
    // Gauge methods
    public void setActiveRequests(long count) {
        activeRequests.set(count);
    }
    
    public void setTotalRequests(long count) {
        totalRequests.set(count);
    }
}
