package com.lookforx.requestservice.interceptor;

import com.lookforx.requestservice.service.RateLimitService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * Interceptor for rate limiting
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RateLimitInterceptor implements HandlerInterceptor {
    
    private final RateLimitService rateLimitService;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String userId = request.getParameter("userId");
        String ipAddress = getClientIpAddress(request);
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        
        // Skip rate limiting for health checks and internal endpoints
        if (requestURI.contains("/actuator") || requestURI.contains("/swagger") || requestURI.contains("/api-docs")) {
            return true;
        }
        
        boolean allowed = true;
        
        // Check user-based rate limits
        if (userId != null) {
            if (requestURI.contains("/requests") && "POST".equals(method)) {
                allowed = rateLimitService.canCreateRequest(userId);
            } else if (requestURI.contains("/search")) {
                allowed = rateLimitService.canSearch(userId);
            } else if (requestURI.contains("/requests") && ("PUT".equals(method) || "PATCH".equals(method))) {
                allowed = rateLimitService.canUpdateRequest(userId);
            }
        } else {
            // Check IP-based rate limits for anonymous users
            allowed = rateLimitService.canMakeRequest(ipAddress);
        }
        
        if (!allowed) {
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.setContentType("application/json");
            response.getWriter().write("{\"error\":\"Rate limit exceeded\",\"message\":\"Too many requests. Please try again later.\"}");
            return false;
        }
        
        return true;
    }
    
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
