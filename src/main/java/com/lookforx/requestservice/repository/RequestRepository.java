package com.lookforx.requestservice.repository;

import com.lookforx.requestservice.domain.Request;
import com.lookforx.requestservice.domain.RequestStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for Request entity
 */
@Repository
public interface RequestRepository extends MongoRepository<Request, String> {
    
    Page<Request> findByUserId(String userId, Pageable pageable);
    
    Page<Request> findByCategoryId(String categoryId, Pageable pageable);
    
    Page<Request> findByStatus(RequestStatus status, Pageable pageable);
    
    @Query("{'userId': ?0, 'status': ?1}")
    Page<Request> findByUserIdAndStatus(String userId, RequestStatus status, Pageable pageable);
    
    @Query("{'categoryId': ?0, 'status': ?1}")
    Page<Request> findByCategoryIdAndStatus(String categoryId, RequestStatus status, Pageable pageable);
    
    @Query("{'expiresAt': {$lt: ?0}, 'status': 'ACTIVE'}")
    List<Request> findExpiredRequests(LocalDateTime now);
    
    @Query("{'location.countryId': ?0, 'location.cityId': ?1, 'status': 'ACTIVE'}")
    Page<Request> findByLocationAndActiveStatus(String countryId, String cityId, Pageable pageable);
    
    @Query("{'location.countryId': ?0, 'location.cityId': ?1, 'location.districtId': ?2, 'status': 'ACTIVE'}")
    Page<Request> findByFullLocationAndActiveStatus(String countryId, String cityId, String districtId, Pageable pageable);
    
    Optional<Request> findByIdAndUserId(String id, String userId);
    
    boolean existsBySubmissionFormId(String submissionFormId);
    
    @Query("{'title': {$regex: ?0, $options: 'i'}, 'status': 'ACTIVE', 'deleted': {$ne: true}}")
    Page<Request> findByTitleContainingIgnoreCaseAndActiveStatus(String title, Pageable pageable);

    long countByStatusAndDeleted(RequestStatus status, Boolean deleted);
}
