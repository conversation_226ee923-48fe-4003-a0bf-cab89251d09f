package com.lookforx.requestservice.exception;

import com.lookforx.common.exception.BaseException;
import org.springframework.http.HttpStatus;

/**
 * Exception thrown when request validation fails
 */
public class RequestValidationException extends BaseException {
    
    public RequestValidationException(String message) {
        super(message, "REQUEST_VALIDATION_ERROR", HttpStatus.BAD_REQUEST);
    }
}
