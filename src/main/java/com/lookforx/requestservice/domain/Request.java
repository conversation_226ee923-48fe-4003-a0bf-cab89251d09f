package com.lookforx.requestservice.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * Main Request entity stored in MongoDB
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "requests")
public class Request {
    
    @Id
    private String id;
    
    @Field("user_id")
    @Indexed
    private String userId;
    
    @Field("category_id")
    @Indexed
    private String categoryId;
    
    @Field("submission_form_id")
    private String submissionFormId;
    
    @Field("title")
    private String title;
    
    @Field("description")
    private String description; // Rich HTML content
    
    @Field("location")
    private LocationInfo location;
    
    @Field("price_range")
    private PriceRange priceRange;
    
    @Field("media")
    private MediaInfo media;
    
    @Field("status")
    @Indexed
    @Builder.Default
    private RequestStatus status = RequestStatus.ACTIVE;
    
    @Field("created_at")
    @CreatedDate
    private LocalDateTime createdAt;
    
    @Field("expires_at")
    private LocalDateTime expiresAt;
    
    @Field("updated_at")
    @LastModifiedDate
    private LocalDateTime updatedAt;

    @Field("deleted")
    @Builder.Default
    private Boolean deleted = false;

    @Field("deleted_at")
    private LocalDateTime deletedAt;

    @Field("deleted_by")
    private String deletedBy;
}
