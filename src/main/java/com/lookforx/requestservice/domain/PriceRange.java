package com.lookforx.requestservice.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;

import java.math.BigDecimal;

/**
 * Embedded document representing price range information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PriceRange {
    
    @Field("min_price")
    private BigDecimal minPrice;
    
    @Field("max_price")
    private BigDecimal maxPrice;
    
    @Field("currency")
    private String currency;
}
