package com.lookforx.requestservice.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * Embedded document representing location information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationInfo {
    
    @Field("country_id")
    private String countryId;
    
    @Field("city_id")
    private String cityId;
    
    @Field("district_id")
    private String districtId;
}
