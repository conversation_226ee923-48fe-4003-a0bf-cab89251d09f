package com.lookforx.requestservice.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Audit log entity for tracking all operations
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "audit_logs")
public class AuditLog {
    
    @Id
    private String id;
    
    @Field("entity_type")
    @Indexed
    private String entityType; // REQUEST, USER, etc.
    
    @Field("entity_id")
    @Indexed
    private String entityId;
    
    @Field("operation")
    @Indexed
    private String operation; // CREATE, UPDATE, DELETE, VIEW
    
    @Field("user_id")
    @Indexed
    private String userId;
    
    @Field("ip_address")
    private String ipAddress;
    
    @Field("user_agent")
    private String userAgent;
    
    @Field("old_values")
    private Map<String, Object> oldValues;
    
    @Field("new_values")
    private Map<String, Object> newValues;
    
    @Field("timestamp")
    @CreatedDate
    @Indexed
    private LocalDateTime timestamp;
    
    @Field("success")
    @Indexed
    private Boolean success;
    
    @Field("error_message")
    private String errorMessage;
    
    @Field("duration_ms")
    private Long durationMs;
    
    @Field("additional_data")
    private Map<String, Object> additionalData;
}
