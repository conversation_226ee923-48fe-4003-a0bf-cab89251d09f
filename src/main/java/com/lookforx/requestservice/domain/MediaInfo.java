package com.lookforx.requestservice.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

/**
 * Embedded document representing media information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MediaInfo {
    
    @Field("image_urls")
    private List<String> imageUrls;
    
    @Field("document_urls")
    private List<String> documentUrls;
    
    @Field("video_url")
    private String videoUrl;
}
