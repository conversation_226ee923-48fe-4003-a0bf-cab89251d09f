package com.lookforx.requestservice.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * Entity for storing request drafts
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "request_drafts")
public class RequestDraft {
    
    @Id
    private String id;
    
    @Field("user_id")
    @Indexed
    private String userId;
    
    @Field("category_id")
    private String categoryId;
    
    @Field("submission_form_id")
    private String submissionFormId;
    
    @Field("title")
    private String title;
    
    @Field("description")
    private String description;
    
    @Field("location")
    private LocationInfo location;
    
    @Field("price_range")
    private PriceRange priceRange;
    
    @Field("media")
    private MediaInfo media;
    
    @Field("expires_at")
    private LocalDateTime expiresAt;
    
    @Field("created_at")
    @CreatedDate
    private LocalDateTime createdAt;
    
    @Field("updated_at")
    @LastModifiedDate
    private LocalDateTime updatedAt;
    
    @Field("auto_saved")
    @Builder.Default
    private Boolean autoSaved = false;
    
    @Field("completion_percentage")
    private Integer completionPercentage;
}
