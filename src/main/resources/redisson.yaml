singleServerConfig:
  address: "redis://localhost:6379"
  connectionMinimumIdleSize: 10
  connectionPoolSize: 64
  database: 0
  dnsMonitoringInterval: 5000
  subscriptionConnectionMinimumIdleSize: 1
  subscriptionConnectionPoolSize: 50
  subscriptionsPerConnection: 5
  clientName: "lookforx-support-service"
  retryAttempts: 3
  retryInterval: 1500
  timeout: 3000
  connectTimeout: 10000
  idleConnectionTimeout: 10000
  keepAlive: false
  tcpNoDelay: false

threads: 16
nettyThreads: 32
codec: !<org.redisson.codec.Kryo5Codec> {}
transportMode: "NIO"
