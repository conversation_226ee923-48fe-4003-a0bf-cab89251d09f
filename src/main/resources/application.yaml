spring:
  application:
    name: logging-microservice
  config:
    import: "optional:configserver:"
  cloud:
    config:
      discovery:
        enabled: true
        service-id: config-server
      fail-fast: true
  profiles:
    active: dev

logging:
  level:
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    com.lookforx.auth: DEBUG
    com.lookforx.auth.security: TRACE

server:
  port: 8100
  servlet:
    context-path: /api

eureka:
  instance:
    prefer-ip-address: true
    hostname: localhost
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
# LookForX Logging Configuration
lookforx:
  logging:
    consumer:
      enabled: true
    storage:
      enabled: true
    kafka:
      enabled: true
      appenders:
        enabled: false  # Disable Kafka appenders for logging service
      consumer:
        group-id: logging-service-group
