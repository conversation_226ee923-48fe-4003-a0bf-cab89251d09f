spring:
  application:
    name: search-microservice
  config:
    import: "optional:configserver:"
  cloud:
    discovery:
      enabled: true
      service-id: config-server
    fail-fast: true
  profiles:
    active: dev

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
  instance:
    preferIpAddress: true
server:
  port: 8088

elasticsearch:
  url: localhost:9200

# Logging Configuration
logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  level:
    root: INFO
    com.lookforx.searchservice: INFO

# LookForX Kafka Configuration
lookforx:
  kafka:
    enabled: true
    producer:
      enabled: true

