server:
  port: 8888
encrypt:
  key: my-secret-key

spring:
  application:
    name: config-server
  cloud:
    config:
      server:
        git:
          uri: https://github.com/aliturgutbozkurt/lookforx-config/
          default-label: main
          username: aliturgutbozkurt
          password: ****************************************
          clone-on-start: true
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always