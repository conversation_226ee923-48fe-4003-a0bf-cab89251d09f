spring:
  application:
    name: category-microservice
  config:
    import: "optional:configserver:"
  cloud:
    config:
      discovery:
        enabled: true
        service-id: config-server
      fail-fast: true
  profiles:
    active: dev

logging:
  level:
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    com.lookforx.auth: DEBUG
    com.lookforx.auth.security: TRACE

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
  instance:
    preferIpAddress: true
server:
  port: 8082

# LookForX Kafka Configuration
lookforx:
  kafka:
    enabled: true
    producer:
      enabled: true
