spring:
  application:
    name: media-microservice
  config:
    import: "optional:configserver:"
  cloud:
    discovery:
      enabled: true
      service-id: config-server
    fail-fast: true
  profiles:
    active: dev

logging:
  level:
    org.springframework.web: DEBUG
    com.lookforx.mediaservice: INFO

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
  instance:
    preferIpAddress: true
server:
  port: 8090

# LookForX Kafka Configuration
lookforx:
  kafka:
    enabled: true
    producer:
      enabled: true
