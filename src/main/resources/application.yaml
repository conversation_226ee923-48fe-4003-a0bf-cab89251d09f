spring:
  application:
    name: payment-microservice
  config:
    import: "optional:configserver:"
  cloud:
    discovery:
      enabled: true
      service-id: config-server
    fail-fast: true
  profiles:
    active: dev

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true
  instance:
    preferIpAddress: true
server:
  port: 8096

