-- Sample reference data for testing

-- Languages
INSERT INTO reference_data (type, code, properties, active, display_order, created_by, created_at, updated_at)
VALUES
('LANGUAGE', 'TR', '{"nativeName": "Türkçe", "iso639_1": "tr"}', true, 2, 'system', NOW(), NOW()),
('LANGUAGE', 'DE', '{"nativeName": "Deutsch", "iso639_1": "de"}', true, 3, 'system', NOW(), NOW()),
('LANGUAGE', 'FR', '{"nativeName": "Français", "iso639_1": "fr"}', true, 4, 'system', NOW(), NOW());

-- Language translations
INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'EN', 'Turkish' FROM reference_data rd WHERE rd.type = 'LANGUAGE' AND rd.code = 'TR';
INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'TR', 'Türkçe' FROM reference_data rd WHERE rd.type = 'LANGUAGE' AND rd.code = 'TR';

INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'EN', 'German' FROM reference_data rd WHERE rd.type = 'LANGUAGE' AND rd.code = 'DE';
INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'TR', 'Almanca' FROM reference_data rd WHERE rd.type = 'LANGUAGE' AND rd.code = 'DE';
INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'DE', 'Deutsch' FROM reference_data rd WHERE rd.type = 'LANGUAGE' AND rd.code = 'DE';

INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'EN', 'French' FROM reference_data rd WHERE rd.type = 'LANGUAGE' AND rd.code = 'FR';
INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'TR', 'Fransızca' FROM reference_data rd WHERE rd.type = 'LANGUAGE' AND rd.code = 'FR';
INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'FR', 'Français' FROM reference_data rd WHERE rd.type = 'LANGUAGE' AND rd.code = 'FR';

-- Countries
INSERT INTO reference_data (type, code, properties, active, display_order, created_by, created_at, updated_at)
VALUES
('COUNTRY', 'TR', '{"iso2": "TR", "iso3": "TUR", "phoneCode": "+90"}', true, 1, 'system', NOW(), NOW()),
('COUNTRY', 'US', '{"iso2": "US", "iso3": "USA", "phoneCode": "+1"}', true, 2, 'system', NOW(), NOW()),
('COUNTRY', 'DE', '{"iso2": "DE", "iso3": "DEU", "phoneCode": "+49"}', true, 3, 'system', NOW(), NOW());

-- Country translations
INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'EN', 'Turkey' FROM reference_data rd WHERE rd.type = 'COUNTRY' AND rd.code = 'TR';
INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'TR', 'Türkiye' FROM reference_data rd WHERE rd.type = 'COUNTRY' AND rd.code = 'TR';

INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'EN', 'United States' FROM reference_data rd WHERE rd.type = 'COUNTRY' AND rd.code = 'US';
INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'TR', 'Amerika Birleşik Devletleri' FROM reference_data rd WHERE rd.type = 'COUNTRY' AND rd.code = 'US';

INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'EN', 'Germany' FROM reference_data rd WHERE rd.type = 'COUNTRY' AND rd.code = 'DE';
INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'TR', 'Almanya' FROM reference_data rd WHERE rd.type = 'COUNTRY' AND rd.code = 'DE';
INSERT INTO reference_data_translations (reference_data_id, language_code, name)
SELECT rd.id, 'DE', 'Deutschland' FROM reference_data rd WHERE rd.type = 'COUNTRY' AND rd.code = 'DE';
