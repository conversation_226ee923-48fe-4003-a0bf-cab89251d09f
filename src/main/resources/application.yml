spring:
  application:
    name: request-microservice
  
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}
  
  config:
    import: "optional:configserver:http://localhost:8888"
  
  cloud:
    config:
      enabled: true
      fail-fast: false
      retry:
        initial-interval: 1000
        max-attempts: 6
        max-interval: 2000
        multiplier: 1.1

# Default configuration (will be overridden by config server)
server:
  port: ${SERVER_PORT:8111}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  health:
    mongo:
      enabled: true
    redis:
      enabled: true

# Eureka Client Configuration
eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_SERVER_URL:http://localhost:8761/eureka/}
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

# Logging Configuration
logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  level:
    root: INFO
    com.lookforx.requestservice: INFO

# LookForX Kafka Configuration
lookforx:
  kafka:
    enabled: true
    producer:
      enabled: true
