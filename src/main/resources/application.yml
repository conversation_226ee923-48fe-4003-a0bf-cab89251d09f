spring:
  application:
    name: auth-microservice
  config:
    import: "optional:configserver:"
  cloud:
    config:
      discovery:
        enabled: true
        service-id: config-server
      fail-fast: true
  profiles:
    active: dev

logging:
  level:
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    com.lookforx.auth: DEBUG
    com.lookforx.auth.security: TRACE

server:
  port: 8081
  servlet:
    context-path: /api

eureka:
  instance:
    prefer-ip-address: true
    hostname: localhost
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    fetch-registry: true
    register-with-eureka: true

# LookForX Kafka Configuration
lookforx:
  kafka:
    enabled: true
    producer:
      enabled: true

