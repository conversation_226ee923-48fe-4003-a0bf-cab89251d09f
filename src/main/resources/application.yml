# LookForX Common Configuration
spring:
  cloud:
    openfeign:
      lazy-attributes-resolution: true
      client:
        config:
          default:
            lazy: true
            connectTimeout: 5000
            readTimeout: 10000
            loggerLevel: basic
          spam-detection-service:
            lazy: true
            connectTimeout: 10000
            readTimeout: 60000
            loggerLevel: basic

# LookForX Common Properties
lookforx:
  spam-detection:
    enabled: false  # Disabled by default, enable in specific services
    url: http://spam-detection-service:8765
  logging:
    kafka:
      enabled: true
  jpa:
    auditing:
      enabled: false  # Disabled by default, enable in specific services
