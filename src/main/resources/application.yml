server:
  port: 8090

spring:
  application:
    name: customer-support-microservice

  config:
    import: "optional:configserver:"
  cloud:
    config:
      discovery:
        enabled: false
        service-id: config-server
      fail-fast: false
  profiles:
    active: dev

  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
    modules:
      - com.fasterxml.jackson.datatype.jsr310.JavaTimeModule




eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
    register-with-eureka: true
    fetch-registry: true
  instance:
    prefer-ip-address: true
    lease-renewal-interval-in-seconds: 30
    lease-expiration-duration-in-seconds: 90

