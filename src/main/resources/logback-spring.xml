<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Spring Profile Support -->
    <springProfile name="!prod">
        <property name="LOG_LEVEL" value="INFO"/>
        <property name="ROOT_LOG_LEVEL" value="INFO"/>
    </springProfile>
    <springProfile name="prod">
        <property name="LOG_LEVEL" value="WARN"/>
        <property name="ROOT_LOG_LEVEL" value="WARN"/>
    </springProfile>

    <!-- Properties will be set programmatically from Java configuration -->
    
    <!-- Console Appender with JSON format for structured logging -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>UTC</timeZone>
                    <pattern>yyyy-MM-dd'T'HH:mm:ss.SSS'Z'</pattern>
                </timestamp>
                <version/>
                <logLevel/>
                <message/>
                <mdc/>
                <arguments/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                        "service": "${SERVICE_NAME}",
                        "version": "${SERVICE_VERSION}",
                        "environment": "${ENVIRONMENT}",
                        "host": "${HOSTNAME:-unknown}",
                        "thread": "%thread",
                        "logger": "%logger{36}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>

    <!-- File Appender with Rolling Policy -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/${SERVICE_NAME}.log</file>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>UTC</timeZone>
                    <pattern>yyyy-MM-dd'T'HH:mm:ss.SSS'Z'</pattern>
                </timestamp>
                <version/>
                <logLevel/>
                <message/>
                <mdc/>
                <arguments/>
                <stackTrace/>
                <pattern>
                    <pattern>
                        {
                        "service": "${SERVICE_NAME}",
                        "version": "${SERVICE_VERSION}",
                        "environment": "${ENVIRONMENT}",
                        "host": "${HOSTNAME:-unknown}",
                        "thread": "%thread",
                        "logger": "%logger{36}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/${SERVICE_NAME}.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- Kafka Appenders will be created programmatically by LogbackConfigurer -->

    <!-- Async Kafka Appenders will be created programmatically by LogbackConfigurer -->

    <!-- Service-specific loggers -->
    <logger name="com.lookforx.auth" level="${LOG_LEVEL}"/>
    <logger name="com.lookforx.user" level="${LOG_LEVEL}"/>
    <logger name="com.lookforx.location" level="${LOG_LEVEL}"/>
    <logger name="com.lookforx.campaign" level="${LOG_LEVEL}"/>

    <logger name="com.lookforx.bid" level="${LOG_LEVEL}"/>
    <logger name="com.lookforx.category" level="${LOG_LEVEL}"/>
    <logger name="com.lookforx.customersupport" level="${LOG_LEVEL}"/>
    <logger name="com.lookforx.media" level="${LOG_LEVEL}"/>

    <logger name="com.lookforx.membership" level="${LOG_LEVEL}"/>
    <logger name="com.lookforx.messaging" level="${LOG_LEVEL}"/>
    <logger name="com.lookforx.notification" level="${LOG_LEVEL}"/>
    <logger name="com.lookforx.payment" level="${LOG_LEVEL}"/>
    <logger name="com.lookforx.questionform" level="${LOG_LEVEL}"/>
    <logger name="com.lookforx.request" level="${LOG_LEVEL}"/>

    <logger name="com.lookforx.search" level="${LOG_LEVEL}"/>

    <!-- Spring Framework loggers -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.web" level="INFO"/>
    <logger name="org.springframework.security" level="INFO"/>
    <logger name="org.springframework.kafka" level="INFO"/>
    
    <!-- Database loggers -->
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.hibernate.SQL" level="INFO"/>
    <logger name="org.mongodb.driver" level="INFO"/>
    
    <!-- Kafka loggers -->
    <logger name="org.apache.kafka" level="WARN"/>
    
    <!-- Zipkin/Tracing loggers -->
    <logger name="zipkin2" level="WARN"/>
    <logger name="io.micrometer.tracing" level="INFO"/>

    <!-- Root logger -->
    <root level="${ROOT_LOG_LEVEL}">
        <springProfile name="!prod">
            <appender-ref ref="CONSOLE"/>
        </springProfile>
        <appender-ref ref="FILE"/>
        <!-- Kafka appenders will be added programmatically by LogbackConfigurer -->
    </root>
</configuration>
