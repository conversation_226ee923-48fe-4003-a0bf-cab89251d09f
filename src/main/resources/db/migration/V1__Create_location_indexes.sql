-- Location Service Database Indexes
-- High-performance indexes for optimal query performance
-- Based on existing database structure with foreign key constraints

-- Countries table indexes
CREATE INDEX IF NOT EXISTS idx_countries_name ON countries(name);
CREATE INDEX IF NOT EXISTS idx_countries_iso2 ON countries(iso2);
CREATE INDEX IF NOT EXISTS idx_countries_iso3 ON countries(iso3);
CREATE INDEX IF NOT EXISTS idx_countries_flag ON countries(flag);
CREATE INDEX IF NOT EXISTS idx_countries_region ON countries(region);
CREATE INDEX IF NOT EXISTS idx_countries_region_id ON countries(region_id);
CREATE INDEX IF NOT EXISTS idx_countries_subregion_id ON countries(subregion_id);
CREATE INDEX IF NOT EXISTS idx_countries_name_flag ON countries(name, flag);
CREATE INDEX IF NOT EXISTS idx_countries_phonecode ON countries(phonecode);
CREATE INDEX IF NOT EXISTS idx_countries_currency ON countries(currency);

-- Full-text search index for countries
CREATE INDEX IF NOT EXISTS idx_countries_name_gin ON countries USING gin(to_tsvector('english', name));

-- Regions table indexes
CREATE INDEX IF NOT EXISTS idx_regions_name ON regions(name);
CREATE INDEX IF NOT EXISTS idx_regions_flag ON regions(flag);

-- Subregions table indexes
CREATE INDEX IF NOT EXISTS idx_subregions_name ON subregions(name);
CREATE INDEX IF NOT EXISTS idx_subregions_region_id ON subregions(region_id);
CREATE INDEX IF NOT EXISTS idx_subregions_flag ON subregions(flag);
CREATE INDEX IF NOT EXISTS idx_subregions_region_name ON subregions(region_id, name);

-- States table indexes (most important for performance)
CREATE INDEX IF NOT EXISTS idx_states_country_id ON states(country_id);
CREATE INDEX IF NOT EXISTS idx_states_name ON states(name);
CREATE INDEX IF NOT EXISTS idx_states_country_name ON states(country_id, name);
CREATE INDEX IF NOT EXISTS idx_states_country_code ON states(country_code);
CREATE INDEX IF NOT EXISTS idx_states_flag ON states(flag);
CREATE INDEX IF NOT EXISTS idx_states_iso2 ON states(iso2);
CREATE INDEX IF NOT EXISTS idx_states_country_flag ON states(country_id, flag);
CREATE INDEX IF NOT EXISTS idx_states_fips_code ON states(fips_code);
CREATE INDEX IF NOT EXISTS idx_states_type ON states(type);

-- Full-text search index for states
CREATE INDEX IF NOT EXISTS idx_states_name_gin ON states USING gin(to_tsvector('english', name));

-- Cities table indexes (critical for performance - most queried table)
CREATE INDEX IF NOT EXISTS idx_cities_state_id ON cities(state_id);
CREATE INDEX IF NOT EXISTS idx_cities_country_id ON cities(country_id);
CREATE INDEX IF NOT EXISTS idx_cities_name ON cities(name);
CREATE INDEX IF NOT EXISTS idx_cities_state_name ON cities(state_id, name);
CREATE INDEX IF NOT EXISTS idx_cities_country_name ON cities(country_id, name);
CREATE INDEX IF NOT EXISTS idx_cities_country_state ON cities(country_id, state_id);
CREATE INDEX IF NOT EXISTS idx_cities_flag ON cities(flag);
CREATE INDEX IF NOT EXISTS idx_cities_coordinates ON cities(latitude, longitude);
CREATE INDEX IF NOT EXISTS idx_cities_state_flag ON cities(state_id, flag);
CREATE INDEX IF NOT EXISTS idx_cities_country_flag ON cities(country_id, flag);
CREATE INDEX IF NOT EXISTS idx_cities_state_code ON cities(state_code);
CREATE INDEX IF NOT EXISTS idx_cities_country_code ON cities(country_code);

-- Full-text search index for cities
CREATE INDEX IF NOT EXISTS idx_cities_name_gin ON cities USING gin(to_tsvector('english', name));

-- Composite indexes for complex queries
CREATE INDEX IF NOT EXISTS idx_cities_country_state_name ON cities(country_id, state_id, name);
CREATE INDEX IF NOT EXISTS idx_cities_country_state_flag ON cities(country_id, state_id, flag);

-- Spatial index for location-based queries (PostGIS if available)
-- CREATE INDEX IF NOT EXISTS idx_cities_location ON cities USING gist(ST_Point(longitude, latitude));

-- Performance optimization: Partial indexes for active records only
CREATE INDEX IF NOT EXISTS idx_countries_active_name ON countries(name) WHERE flag = 1;
CREATE INDEX IF NOT EXISTS idx_states_active_country_name ON states(country_id, name) WHERE flag = 1;
CREATE INDEX IF NOT EXISTS idx_cities_active_state_name ON cities(state_id, name) WHERE flag = 1;
CREATE INDEX IF NOT EXISTS idx_cities_active_country_name ON cities(country_id, name) WHERE flag = 1;

-- Covering indexes for common queries (include frequently selected columns)
CREATE INDEX IF NOT EXISTS idx_countries_covering ON countries(id, name, iso2, emoji) WHERE flag = 1;
CREATE INDEX IF NOT EXISTS idx_states_covering ON states(id, name, country_id, country_code) WHERE flag = 1;
CREATE INDEX IF NOT EXISTS idx_cities_covering ON cities(id, name, state_id, country_id, latitude, longitude) WHERE flag = 1;

-- Additional performance indexes for specific use cases
CREATE INDEX IF NOT EXISTS idx_countries_capital ON countries(capital) WHERE capital IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_countries_coordinates ON countries(latitude, longitude) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_states_coordinates ON states(latitude, longitude) WHERE latitude IS NOT NULL AND longitude IS NOT NULL;

-- Indexes for WikiData integration
CREATE INDEX IF NOT EXISTS idx_countries_wikidata ON countries("wikiDataId") WHERE "wikiDataId" IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_regions_wikidata ON regions("wikiDataId") WHERE "wikiDataId" IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_subregions_wikidata ON subregions("wikiDataId") WHERE "wikiDataId" IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_states_wikidata ON states("wikiDataId") WHERE "wikiDataId" IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_cities_wikidata ON cities("wikiDataId") WHERE "wikiDataId" IS NOT NULL;

-- Statistics update for better query planning
ANALYZE countries;
ANALYZE regions;
ANALYZE subregions;
ANALYZE states;
ANALYZE cities;
