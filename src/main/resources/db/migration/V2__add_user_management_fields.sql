-- Add user management fields to users table
-- V2__add_user_management_fields.sql

-- Add active column
ALTER TABLE users ADD COLUMN active BOOLEAN DEFAULT TRUE;

-- Add roles_string column for admin operations
ALTER TABLE users ADD COLUMN roles_string VARCHAR(255);

-- Add timestamp columns
ALTER TABLE users ADD COLUMN created_at TIMESTAMP;
ALTER TABLE users ADD COLUMN updated_at TIMESTAMP;
ALTER TABLE users ADD COLUMN last_login_at TIMESTAMP;

-- Update existing users with default values
UPDATE users SET active = TRUE WHERE active IS NULL;
UPDATE users SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL;
UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL;

-- Set roles_string based on existing roles
-- This assumes you have a user_roles table with role mappings
UPDATE users SET roles_string = (
    SELECT STRING_AGG(ur.roles, ',')
    FROM user_roles ur 
    WHERE ur.user_id = users.id
) WHERE roles_string IS NULL;

-- For users without roles, set default USER role
UPDATE users SET roles_string = 'USER' WHERE roles_string IS NULL OR roles_string = '';

-- Add NOT NULL constraints after setting default values
ALTER TABLE users ALTER COLUMN active SET NOT NULL;
