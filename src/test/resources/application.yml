spring:
  profiles:
    active: test
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: password
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
  h2:
    console:
      enabled: true

# Disable config server for tests
spring.cloud.config.enabled: false

# Disable service discovery for tests
eureka:
  client:
    enabled: false

# Test logging
logging:
  level:
    com.lookforx: DEBUG
    org.springframework.web: DEBUG
