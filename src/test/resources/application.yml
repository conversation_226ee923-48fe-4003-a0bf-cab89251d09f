eureka:
  client:
    enabled: false
spring:
  cloud:
    discovery:
      enabled: false
    config:
      enabled: false
      server:
        git:
          uri: https://github.com/aliturgutbozkurt/lookforx-config/
          default-label: main
          username: aliturgutbozkurt
          password: ****************************************
          clone-on-start: true

  # ─── H2 IN-MEMORY DATASOURCE ──────────────────────────────────────────────
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

  h2:
    console:
      enabled: true
      path: /h2-console