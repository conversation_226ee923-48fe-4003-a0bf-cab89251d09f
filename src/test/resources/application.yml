spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true

  h2:
    console:
      enabled: true
      path: /h2-console

  redis:
    host: localhost
    port: 6379

  data:
    mongodb:
      uri: mongodb://localhost:27017/lookforx_test
      auto-index-creation: true

  elasticsearch:
    uris: http://localhost:9200

  kafka:
    bootstrap-servers: localhost:9092

  # Spring Cloud Config ve Discovery kapalı
  config:
    import: ""
  cloud:
    discovery:
      enabled: false
    config:
      enabled: false

# Eureka kapalı
eureka:
  client:
    enabled: false
    register-with-eureka: false
    fetch-registry: false

logging:
  level:
    root: INFO
    org.springframework.web: DEBUG