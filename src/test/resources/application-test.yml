spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
  data:
    redis:
      host: localhost
      port: 6379
  cloud:
    config:
      enabled: false
  cache:
    type: simple

eureka:
  client:
    enabled: false

management:
  endpoints:
    web:
      exposure:
        include: health

logging:
  level:
    com.lookforx.locationservice: DEBUG
    org.springframework.cache: DEBUG
