spring:
  application:
    name: lookforx-customer-support-service-test
  
  # Database configuration for testing
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  # JPA configuration for testing
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        cache:
          use_second_level_cache: false
  
  # Redis configuration for testing (embedded)
  data:
    redis:
      host: localhost
      port: 6370  # Different port for testing
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  # Kafka configuration for testing (disabled)
  kafka:
    bootstrap-servers: localhost:9093  # Different port for testing
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      group-id: support-service-test
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
  
  # Cloud configuration for testing
  cloud:
    config:
      enabled: false
    discovery:
      enabled: false

# Eureka configuration for testing (disabled)
eureka:
  client:
    enabled: false

# Management endpoints for testing
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: always

# Logging configuration for testing
logging:
  level:
    com.lookforx.support: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
