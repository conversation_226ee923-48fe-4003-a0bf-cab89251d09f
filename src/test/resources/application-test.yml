spring:
  datasource:
    # TestContainers will override these values
    url: *************************************
    username: test
    password: test
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    defer-datasource-initialization: true
  
  sql:
    init:
      mode: never
  
  cache:
    type: simple
    cache-names:
      - exception-messages
  
  # Disable Redis for tests
  data:
    redis:
      repositories:
        enabled: false

logging:
  level:
    com.lookforx.exceptionservice: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.testcontainers: INFO
    com.github.dockerjava: WARN
  
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
