spring:
  application:
    name: notification-service-test

  # Disable Spring Cloud Config for tests
  cloud:
    config:
      enabled: false
  config:
    import: optional:configserver:

  # H2 Test Database
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:
    driver-class-name: org.h2.Driver

  # JPA Configuration for Tests
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: false
        
  # Disable all external integrations for tests
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration

# Notification Configuration for Tests
notification:
  email:
    enabled: false
    from: <EMAIL>
    from-name: LookForX Test
  sms:
    enabled: false
    provider:
      url: http://mock-sms-provider.com
      api-key: test-api-key
  push:
    enabled: false
    fcm:
      server-key: test-fcm-key

# Disable Eureka for tests
eureka:
  client:
    enabled: false

# Logging Configuration for Tests
logging:
  level:
    com.lookforx.notificationservice: DEBUG
    org.springframework.kafka: OFF
    org.hibernate.SQL: OFF
    org.springframework.web: DEBUG
