spring:
  datasource:
    # <PERSON><PERSON><PERSON><PERSON> will override these values
    url: *******************************************
    driver-class-name: org.postgresql.Driver
    username: test
    password: test

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect

  redis:
    host: localhost
    port: 6379

  data:
    mongodb:
      uri: mongodb://localhost:27017/lookforx_test
      auto-index-creation: true

  elasticsearch:
    uris: http://localhost:9200

  kafka:
    bootstrap-servers: localhost:9092

  # Spring Cloud Config ve Discovery kapalı
  config:
    import: ""
  cloud:
    discovery:
      enabled: false
    config:
      enabled: false

# Eureka kapalı
eureka:
  client:
    enabled: false
    register-with-eureka: false
    fetch-registry: false

logging:
  level:
    root: INFO
    org.springframework.web: DEBUG