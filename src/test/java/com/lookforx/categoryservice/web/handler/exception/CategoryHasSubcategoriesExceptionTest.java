package com.lookforx.categoryservice.web.handler.exception;

import com.lookforx.common.exception.BusinessException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * Comprehensive tests for CategoryHasSubcategoriesException.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("CategoryHasSubcategoriesException Tests")
class CategoryHasSubcategoriesExceptionTest {

    @Test
    @DisplayName("Should create CategoryHasSubcategoriesException with valid category ID")
    void testCategoryHasSubcategoriesException_ValidId() {
        // Given
        Long categoryId = 123L;

        // When
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);

        // Then
        assertThat(exception).isNotNull();
        assertThat(exception.getCategoryId()).isEqualTo(categoryId);
        assertThat(exception.getMessage()).isEqualTo(
                "Category with ID 123 has subcategories and cannot be deleted. Please delete all subcategories first."
        );
        assertThat(exception).isInstanceOf(BusinessException.class);
    }

    @Test
    @DisplayName("Should throw NullPointerException when category ID is null")
    void testCategoryHasSubcategoriesException_NullId_ThrowsException() {
        // When & Then
        assertThatThrownBy(() -> new CategoryHasSubcategoriesException(null))
                .isInstanceOf(NullPointerException.class)
                .hasMessage("Category ID cannot be null");
    }

    @Test
    @DisplayName("Should handle zero category ID")
    void testCategoryHasSubcategoriesException_ZeroId() {
        // Given
        Long categoryId = 0L;

        // When
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);

        // Then
        assertThat(exception.getCategoryId()).isEqualTo(0L);
        assertThat(exception.getMessage()).contains("Category with ID 0 has subcategories");
    }

    @Test
    @DisplayName("Should handle negative category ID")
    void testCategoryHasSubcategoriesException_NegativeId() {
        // Given
        Long categoryId = -1L;

        // When
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);

        // Then
        assertThat(exception.getCategoryId()).isEqualTo(-1L);
        assertThat(exception.getMessage()).contains("Category with ID -1 has subcategories");
    }

    @Test
    @DisplayName("Should handle large category ID")
    void testCategoryHasSubcategoriesException_LargeId() {
        // Given
        Long categoryId = Long.MAX_VALUE;

        // When
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);

        // Then
        assertThat(exception.getCategoryId()).isEqualTo(Long.MAX_VALUE);
        assertThat(exception.getMessage()).contains(String.valueOf(Long.MAX_VALUE));
    }

    @Test
    @DisplayName("Should be throwable")
    void testCategoryHasSubcategoriesException_Throwable() {
        // Given
        Long categoryId = 456L;

        // When & Then
        assertThatThrownBy(() -> {
            throw new CategoryHasSubcategoriesException(categoryId);
        })
                .isInstanceOf(CategoryHasSubcategoriesException.class)
                .hasMessage("Category with ID 456 has subcategories and cannot be deleted. Please delete all subcategories first.")
                .satisfies(ex -> {
                    CategoryHasSubcategoriesException chse = (CategoryHasSubcategoriesException) ex;
                    assertThat(chse.getCategoryId()).isEqualTo(456L);
                });
    }

    @Test
    @DisplayName("Should extend BusinessException")
    void testCategoryHasSubcategoriesException_ExtendsBusinessException() {
        // Given
        Long categoryId = 789L;

        // When
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);

        // Then
        assertThat(exception).isInstanceOf(BusinessException.class);
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(exception).isInstanceOf(Exception.class);
        assertThat(exception).isInstanceOf(Throwable.class);
    }

    @Test
    @DisplayName("Should have consistent message format")
    void testCategoryHasSubcategoriesException_MessageFormat() {
        // Given
        Long[] testIds = {1L, 42L, 999L, 1000000L};

        for (Long id : testIds) {
            // When
            CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(id);

            // Then
            assertThat(exception.getMessage())
                    .startsWith("Category with ID ")
                    .contains(id.toString())
                    .contains(" has subcategories and cannot be deleted")
                    .endsWith("Please delete all subcategories first.");
        }
    }

    @Test
    @DisplayName("Should maintain category ID immutability")
    void testCategoryHasSubcategoriesException_CategoryIdImmutable() {
        // Given
        Long originalId = 123L;
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(originalId);

        // When
        Long retrievedId = exception.getCategoryId();

        // Then
        assertThat(retrievedId).isEqualTo(originalId);
        assertThat(retrievedId).isSameAs(originalId); // Same reference for Long values in cache
    }

    @Test
    @DisplayName("Should have proper toString representation")
    void testCategoryHasSubcategoriesException_ToString() {
        // Given
        Long categoryId = 555L;
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);

        // When
        String toString = exception.toString();

        // Then
        assertThat(toString).contains("CategoryHasSubcategoriesException");
        assertThat(toString).contains("Category with ID 555 has subcategories");
    }

    @Test
    @DisplayName("Should support exception chaining")
    void testCategoryHasSubcategoriesException_ExceptionChaining() {
        // Given
        Long categoryId = 777L;
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);
        RuntimeException cause = new RuntimeException("Database constraint violation");

        // When
        exception.initCause(cause);

        // Then
        assertThat(exception.getCause()).isEqualTo(cause);
        assertThat(exception.getCategoryId()).isEqualTo(categoryId);
        assertThat(exception.getMessage()).contains("Category with ID 777 has subcategories");
    }

    @Test
    @DisplayName("Should have proper stack trace")
    void testCategoryHasSubcategoriesException_StackTrace() {
        // Given
        Long categoryId = 888L;

        // When
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);

        // Then
        assertThat(exception.getStackTrace()).isNotEmpty();
        assertThat(exception.getStackTrace()[0].getClassName())
                .isEqualTo(CategoryHasSubcategoriesExceptionTest.class.getName());
        assertThat(exception.getStackTrace()[0].getMethodName())
                .isEqualTo("testCategoryHasSubcategoriesException_StackTrace");
    }

    @Test
    @DisplayName("Should be serializable")
    void testCategoryHasSubcategoriesException_Serializable() {
        // Given
        Long categoryId = 999L;
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);

        // Then
        assertThat(exception).isInstanceOf(java.io.Serializable.class);
    }

    @Test
    @DisplayName("Should handle concurrent access to category ID")
    void testCategoryHasSubcategoriesException_ConcurrentAccess() throws InterruptedException {
        // Given
        Long categoryId = 1001L;
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);
        Long[] results = new Long[2];

        // When
        Thread thread1 = new Thread(() -> results[0] = exception.getCategoryId());
        Thread thread2 = new Thread(() -> results[1] = exception.getCategoryId());

        thread1.start();
        thread2.start();

        thread1.join();
        thread2.join();

        // Then
        assertThat(results[0]).isEqualTo(categoryId);
        assertThat(results[1]).isEqualTo(categoryId);
        assertThat(results[0]).isEqualTo(results[1]);
    }

    @Test
    @DisplayName("Should have consistent error code")
    void testCategoryHasSubcategoriesException_ErrorCode() {
        // Given
        Long categoryId = 1234L;

        // When
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);

        // Then
        // Note: Error code is handled by BusinessException parent class
        // We can verify the exception type and message consistency
        assertThat(exception).isInstanceOf(BusinessException.class);
        assertThat(exception.getMessage()).contains("has subcategories and cannot be deleted");
    }

    @Test
    @DisplayName("Should provide helpful user message")
    void testCategoryHasSubcategoriesException_UserFriendlyMessage() {
        // Given
        Long categoryId = 5678L;

        // When
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);

        // Then
        assertThat(exception.getMessage())
                .contains("cannot be deleted")
                .contains("Please delete all subcategories first")
                .doesNotContain("null")
                .doesNotContain("error")
                .doesNotContain("failed");
    }

    @Test
    @DisplayName("Should differentiate from other category exceptions")
    void testCategoryHasSubcategoriesException_DifferentFromOtherExceptions() {
        // Given
        Long id = 123L;
        CategoryHasSubcategoriesException subcategoriesException = new CategoryHasSubcategoriesException(id);
        CategoryNotFoundException notFoundException = new CategoryNotFoundException(id);
        ParentCategoryNotFoundException parentNotFound = new ParentCategoryNotFoundException(id);

        // Then
        assertThat(subcategoriesException).isNotInstanceOf(CategoryNotFoundException.class);
        assertThat(subcategoriesException).isNotInstanceOf(ParentCategoryNotFoundException.class);
        assertThat(subcategoriesException.getMessage()).isNotEqualTo(notFoundException.getMessage());
        assertThat(subcategoriesException.getMessage()).isNotEqualTo(parentNotFound.getMessage());
        assertThat(subcategoriesException.getMessage()).contains("has subcategories");
        assertThat(notFoundException.getMessage()).contains("was not found");
        assertThat(parentNotFound.getMessage()).contains("Parent category");
    }
}
