package com.lookforx.categoryservice.web.handler;

import com.lookforx.categoryservice.web.handler.exception.CategoryHasSubcategoriesException;
import com.lookforx.categoryservice.web.handler.exception.CategoryNotFoundException;
import com.lookforx.categoryservice.web.handler.exception.ParentCategoryNotFoundException;
import com.lookforx.common.dto.ErrorResponse;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

/**
 * Comprehensive tests for ApplicationExceptionHandler.
 * Following Effective Java principles for thorough testing.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ApplicationExceptionHandler Tests")
class ApplicationExceptionHandlerTest {

    @Mock
    private HttpServletRequest request;

    @InjectMocks
    private ApplicationExceptionHandler exceptionHandler;

    @BeforeEach
    void setUp() {
        // Common setup for request mocking - using lenient to avoid unnecessary stubbing errors
        lenient().when(request.getRequestURI()).thenReturn("/api/v1/categories");
        lenient().when(request.getMethod()).thenReturn("GET");
        lenient().when(request.getHeader("Accept-Language")).thenReturn("en");
    }

    @Test
    @DisplayName("Should handle CategoryNotFoundException correctly")
    void testHandleCategoryNotFound() {
        // Given
        Long categoryId = 123L;
        CategoryNotFoundException exception = new CategoryNotFoundException(categoryId);

        // When
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleCategoryNotFound(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(404);
        assertThat(response.getBody().getTimestamp()).isNotNull();
    }

    @Test
    @DisplayName("Should handle ParentCategoryNotFoundException correctly")
    void testHandleParentCategoryNotFound() {
        // Given
        Long parentId = 456L;
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(parentId);

        // When
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleParentCategoryNotFound(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.NOT_FOUND);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(404);
    }

    @Test
    @DisplayName("Should handle CategoryHasSubcategoriesException correctly")
    void testHandleCategoryHasSubcategories() {
        // Given
        Long categoryId = 789L;
        CategoryHasSubcategoriesException exception = new CategoryHasSubcategoriesException(categoryId);

        // When
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleCategoryHasSubcategories(exception, request);

        // Then
        assertThat(response.getStatusCode()).isEqualTo(HttpStatus.CONFLICT);
        assertThat(response.getBody()).isNotNull();
        assertThat(response.getBody().getStatus()).isEqualTo(409);
    }

    @Test
    @DisplayName("Should be annotated with @RestControllerAdvice")
    void testRestControllerAdviceAnnotation() {
        // Then
        assertThat(ApplicationExceptionHandler.class.isAnnotationPresent(
                org.springframework.web.bind.annotation.RestControllerAdvice.class)).isTrue();
    }

    @Test
    @DisplayName("Should be annotated with @Profile")
    void testProfileAnnotation() {
        // Then
        assertThat(ApplicationExceptionHandler.class.isAnnotationPresent(
                org.springframework.context.annotation.Profile.class)).isTrue();
        
        org.springframework.context.annotation.Profile profileAnnotation = 
                ApplicationExceptionHandler.class.getAnnotation(org.springframework.context.annotation.Profile.class);
        assertThat(profileAnnotation.value()).containsExactly("!test");
    }

    @Test
    @DisplayName("Should have proper exception handler methods")
    void testExceptionHandlerMethods() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method categoryNotFoundMethod = ApplicationExceptionHandler.class
                .getMethod("handleCategoryNotFound", CategoryNotFoundException.class, HttpServletRequest.class);
        java.lang.reflect.Method parentNotFoundMethod = ApplicationExceptionHandler.class
                .getMethod("handleParentCategoryNotFound", ParentCategoryNotFoundException.class, HttpServletRequest.class);
        java.lang.reflect.Method hasSubcategoriesMethod = ApplicationExceptionHandler.class
                .getMethod("handleCategoryHasSubcategories", CategoryHasSubcategoriesException.class, HttpServletRequest.class);

        // Then
        assertThat(categoryNotFoundMethod.isAnnotationPresent(
                org.springframework.web.bind.annotation.ExceptionHandler.class)).isTrue();
        assertThat(parentNotFoundMethod.isAnnotationPresent(
                org.springframework.web.bind.annotation.ExceptionHandler.class)).isTrue();
        assertThat(hasSubcategoriesMethod.isAnnotationPresent(
                org.springframework.web.bind.annotation.ExceptionHandler.class)).isTrue();
    }

    @Test
    @DisplayName("Should return ResponseEntity with ErrorResponse")
    void testReturnTypes() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method[] methods = {
                ApplicationExceptionHandler.class.getMethod("handleCategoryNotFound", 
                        CategoryNotFoundException.class, HttpServletRequest.class),
                ApplicationExceptionHandler.class.getMethod("handleParentCategoryNotFound", 
                        ParentCategoryNotFoundException.class, HttpServletRequest.class),
                ApplicationExceptionHandler.class.getMethod("handleCategoryHasSubcategories", 
                        CategoryHasSubcategoriesException.class, HttpServletRequest.class)
        };

        // Then
        for (java.lang.reflect.Method method : methods) {
            assertThat(method.getReturnType()).isEqualTo(ResponseEntity.class);
        }
    }

    @Test
    @DisplayName("Should handle null request gracefully")
    void testHandleNullRequest() {
        // Given
        CategoryNotFoundException exception = new CategoryNotFoundException(123L);
        HttpServletRequest nullRequest = null;

        // When & Then - This will throw NPE due to ExceptionMessageUtil.resolveLanguage
        // We test that the method exists and can be called, but NPE is expected behavior
        try {
            exceptionHandler.handleCategoryNotFound(exception, nullRequest);
        } catch (NullPointerException e) {
            // Expected behavior when request is null
            assertThat(e.getMessage()).contains("request");
        }
    }

    @Test
    @DisplayName("Should create error response with timestamp")
    void testErrorResponseTimestamp() {
        // Given
        CategoryNotFoundException exception = new CategoryNotFoundException(123L);

        // When
        ResponseEntity<ErrorResponse> response = exceptionHandler.handleCategoryNotFound(exception, request);

        // Then
        assertThat(response.getBody().getTimestamp()).isNotNull();
        assertThat(response.getBody().getTimestamp()).isBeforeOrEqualTo(java.time.LocalDateTime.now());
    }
}
