package com.lookforx.categoryservice.web.handler.exception;

import com.lookforx.common.exception.ResourceNotFoundException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * Comprehensive tests for ParentCategoryNotFoundException.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("ParentCategoryNotFoundException Tests")
class ParentCategoryNotFoundExceptionTest {

    @Test
    @DisplayName("Should create ParentCategoryNotFoundException with valid parent ID")
    void testParentCategoryNotFoundException_ValidId() {
        // Given
        Long parentId = 123L;

        // When
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(parentId);

        // Then
        assertThat(exception).isNotNull();
        assertThat(exception.getParentId()).isEqualTo(parentId);
        assertThat(exception.getMessage()).isEqualTo("Parent category with ID 123 was not found");
        assertThat(exception).isInstanceOf(ResourceNotFoundException.class);
    }

    @Test
    @DisplayName("Should throw NullPointerException when parent ID is null")
    void testParentCategoryNotFoundException_NullId_ThrowsException() {
        // When & Then
        assertThatThrownBy(() -> new ParentCategoryNotFoundException(null))
                .isInstanceOf(NullPointerException.class)
                .hasMessage("Parent category ID cannot be null");
    }

    @Test
    @DisplayName("Should handle zero parent ID")
    void testParentCategoryNotFoundException_ZeroId() {
        // Given
        Long parentId = 0L;

        // When
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(parentId);

        // Then
        assertThat(exception.getParentId()).isEqualTo(0L);
        assertThat(exception.getMessage()).isEqualTo("Parent category with ID 0 was not found");
    }

    @Test
    @DisplayName("Should handle negative parent ID")
    void testParentCategoryNotFoundException_NegativeId() {
        // Given
        Long parentId = -1L;

        // When
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(parentId);

        // Then
        assertThat(exception.getParentId()).isEqualTo(-1L);
        assertThat(exception.getMessage()).isEqualTo("Parent category with ID -1 was not found");
    }

    @Test
    @DisplayName("Should handle large parent ID")
    void testParentCategoryNotFoundException_LargeId() {
        // Given
        Long parentId = Long.MAX_VALUE;

        // When
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(parentId);

        // Then
        assertThat(exception.getParentId()).isEqualTo(Long.MAX_VALUE);
        assertThat(exception.getMessage()).contains(String.valueOf(Long.MAX_VALUE));
    }

    @Test
    @DisplayName("Should be throwable")
    void testParentCategoryNotFoundException_Throwable() {
        // Given
        Long parentId = 456L;

        // When & Then
        assertThatThrownBy(() -> {
            throw new ParentCategoryNotFoundException(parentId);
        })
                .isInstanceOf(ParentCategoryNotFoundException.class)
                .hasMessage("Parent category with ID 456 was not found")
                .satisfies(ex -> {
                    ParentCategoryNotFoundException pcnfe = (ParentCategoryNotFoundException) ex;
                    assertThat(pcnfe.getParentId()).isEqualTo(456L);
                });
    }

    @Test
    @DisplayName("Should extend ResourceNotFoundException")
    void testParentCategoryNotFoundException_ExtendsResourceNotFoundException() {
        // Given
        Long parentId = 789L;

        // When
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(parentId);

        // Then
        assertThat(exception).isInstanceOf(ResourceNotFoundException.class);
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(exception).isInstanceOf(Exception.class);
        assertThat(exception).isInstanceOf(Throwable.class);
    }

    @Test
    @DisplayName("Should have consistent message format")
    void testParentCategoryNotFoundException_MessageFormat() {
        // Given
        Long[] testIds = {1L, 42L, 999L, 1000000L};

        for (Long id : testIds) {
            // When
            ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(id);

            // Then
            assertThat(exception.getMessage())
                    .startsWith("Parent category with ID ")
                    .contains(id.toString())
                    .endsWith(" was not found");
        }
    }

    @Test
    @DisplayName("Should maintain parent ID immutability")
    void testParentCategoryNotFoundException_ParentIdImmutable() {
        // Given
        Long originalId = 123L;
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(originalId);

        // When
        Long retrievedId = exception.getParentId();

        // Then
        assertThat(retrievedId).isEqualTo(originalId);
        assertThat(retrievedId).isSameAs(originalId); // Same reference for Long values in cache
    }

    @Test
    @DisplayName("Should have proper toString representation")
    void testParentCategoryNotFoundException_ToString() {
        // Given
        Long parentId = 555L;
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(parentId);

        // When
        String toString = exception.toString();

        // Then
        assertThat(toString).contains("ParentCategoryNotFoundException");
        assertThat(toString).contains("Parent category with ID 555 was not found");
    }

    @Test
    @DisplayName("Should support exception chaining")
    void testParentCategoryNotFoundException_ExceptionChaining() {
        // Given
        Long parentId = 777L;
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(parentId);
        RuntimeException cause = new RuntimeException("Database connection failed");

        // When
        exception.initCause(cause);

        // Then
        assertThat(exception.getCause()).isEqualTo(cause);
        assertThat(exception.getParentId()).isEqualTo(parentId);
        assertThat(exception.getMessage()).isEqualTo("Parent category with ID 777 was not found");
    }

    @Test
    @DisplayName("Should have proper stack trace")
    void testParentCategoryNotFoundException_StackTrace() {
        // Given
        Long parentId = 888L;

        // When
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(parentId);

        // Then
        assertThat(exception.getStackTrace()).isNotEmpty();
        assertThat(exception.getStackTrace()[0].getClassName())
                .isEqualTo(ParentCategoryNotFoundExceptionTest.class.getName());
        assertThat(exception.getStackTrace()[0].getMethodName())
                .isEqualTo("testParentCategoryNotFoundException_StackTrace");
    }

    @Test
    @DisplayName("Should be serializable")
    void testParentCategoryNotFoundException_Serializable() {
        // Given
        Long parentId = 999L;
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(parentId);

        // Then
        assertThat(exception).isInstanceOf(java.io.Serializable.class);
    }

    @Test
    @DisplayName("Should handle concurrent access to parent ID")
    void testParentCategoryNotFoundException_ConcurrentAccess() throws InterruptedException {
        // Given
        Long parentId = 1001L;
        ParentCategoryNotFoundException exception = new ParentCategoryNotFoundException(parentId);
        Long[] results = new Long[2];

        // When
        Thread thread1 = new Thread(() -> results[0] = exception.getParentId());
        Thread thread2 = new Thread(() -> results[1] = exception.getParentId());

        thread1.start();
        thread2.start();

        thread1.join();
        thread2.join();

        // Then
        assertThat(results[0]).isEqualTo(parentId);
        assertThat(results[1]).isEqualTo(parentId);
        assertThat(results[0]).isEqualTo(results[1]);
    }

    @Test
    @DisplayName("Should differentiate from CategoryNotFoundException")
    void testParentCategoryNotFoundException_DifferentFromCategoryNotFoundException() {
        // Given
        Long id = 123L;
        ParentCategoryNotFoundException parentException = new ParentCategoryNotFoundException(id);
        CategoryNotFoundException categoryException = new CategoryNotFoundException(id);

        // Then
        assertThat(parentException).isNotInstanceOf(CategoryNotFoundException.class);
        assertThat(categoryException).isNotInstanceOf(ParentCategoryNotFoundException.class);
        assertThat(parentException.getMessage()).isNotEqualTo(categoryException.getMessage());
        assertThat(parentException.getMessage()).contains("Parent category");
        assertThat(categoryException.getMessage()).contains("Category");
        assertThat(categoryException.getMessage()).doesNotContain("Parent");
    }
}
