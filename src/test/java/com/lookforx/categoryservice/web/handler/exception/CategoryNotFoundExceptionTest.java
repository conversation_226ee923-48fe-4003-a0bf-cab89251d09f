package com.lookforx.categoryservice.web.handler.exception;

import com.lookforx.common.exception.ResourceNotFoundException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * Comprehensive tests for CategoryNotFoundException.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("CategoryNotFoundException Tests")
class CategoryNotFoundExceptionTest {

    @Test
    @DisplayName("Should create CategoryNotFoundException with valid category ID")
    void testCategoryNotFoundException_ValidId() {
        // Given
        Long categoryId = 123L;

        // When
        CategoryNotFoundException exception = new CategoryNotFoundException(categoryId);

        // Then
        assertThat(exception).isNotNull();
        assertThat(exception.getCategoryId()).isEqualTo(categoryId);
        assertThat(exception.getMessage()).isEqualTo("Category with ID 123 was not found");
        assertThat(exception).isInstanceOf(ResourceNotFoundException.class);
    }

    @Test
    @DisplayName("Should throw IllegalArgumentException when category ID is null")
    void testCategoryNotFoundException_NullId_ThrowsException() {
        // When & Then
        assertThatThrownBy(() -> new CategoryNotFoundException(null))
                .isInstanceOf(NullPointerException.class)
                .hasMessage("Category ID cannot be null");
    }

    @Test
    @DisplayName("Should handle zero category ID")
    void testCategoryNotFoundException_ZeroId() {
        // Given
        Long categoryId = 0L;

        // When
        CategoryNotFoundException exception = new CategoryNotFoundException(categoryId);

        // Then
        assertThat(exception.getCategoryId()).isEqualTo(0L);
        assertThat(exception.getMessage()).isEqualTo("Category with ID 0 was not found");
    }

    @Test
    @DisplayName("Should handle negative category ID")
    void testCategoryNotFoundException_NegativeId() {
        // Given
        Long categoryId = -1L;

        // When
        CategoryNotFoundException exception = new CategoryNotFoundException(categoryId);

        // Then
        assertThat(exception.getCategoryId()).isEqualTo(-1L);
        assertThat(exception.getMessage()).isEqualTo("Category with ID -1 was not found");
    }

    @Test
    @DisplayName("Should handle large category ID")
    void testCategoryNotFoundException_LargeId() {
        // Given
        Long categoryId = Long.MAX_VALUE;

        // When
        CategoryNotFoundException exception = new CategoryNotFoundException(categoryId);

        // Then
        assertThat(exception.getCategoryId()).isEqualTo(Long.MAX_VALUE);
        assertThat(exception.getMessage()).contains(String.valueOf(Long.MAX_VALUE));
    }

    @Test
    @DisplayName("Should be throwable")
    void testCategoryNotFoundException_Throwable() {
        // Given
        Long categoryId = 456L;

        // When & Then
        assertThatThrownBy(() -> {
            throw new CategoryNotFoundException(categoryId);
        })
                .isInstanceOf(CategoryNotFoundException.class)
                .hasMessage("Category with ID 456 was not found")
                .satisfies(ex -> {
                    CategoryNotFoundException cnfe = (CategoryNotFoundException) ex;
                    assertThat(cnfe.getCategoryId()).isEqualTo(456L);
                });
    }

    @Test
    @DisplayName("Should extend ResourceNotFoundException")
    void testCategoryNotFoundException_ExtendsResourceNotFoundException() {
        // Given
        Long categoryId = 789L;

        // When
        CategoryNotFoundException exception = new CategoryNotFoundException(categoryId);

        // Then
        assertThat(exception).isInstanceOf(ResourceNotFoundException.class);
        assertThat(exception).isInstanceOf(RuntimeException.class);
        assertThat(exception).isInstanceOf(Exception.class);
        assertThat(exception).isInstanceOf(Throwable.class);
    }

    @Test
    @DisplayName("Should have consistent message format")
    void testCategoryNotFoundException_MessageFormat() {
        // Given
        Long[] testIds = {1L, 42L, 999L, 1000000L};

        for (Long id : testIds) {
            // When
            CategoryNotFoundException exception = new CategoryNotFoundException(id);

            // Then
            assertThat(exception.getMessage())
                    .startsWith("Category with ID ")
                    .contains(id.toString())
                    .endsWith(" was not found");
        }
    }

    @Test
    @DisplayName("Should maintain category ID immutability")
    void testCategoryNotFoundException_CategoryIdImmutable() {
        // Given
        Long originalId = 123L;
        CategoryNotFoundException exception = new CategoryNotFoundException(originalId);

        // When
        Long retrievedId = exception.getCategoryId();

        // Then
        assertThat(retrievedId).isEqualTo(originalId);
        assertThat(retrievedId).isSameAs(originalId); // Same reference for Long values in cache
    }

    @Test
    @DisplayName("Should have proper toString representation")
    void testCategoryNotFoundException_ToString() {
        // Given
        Long categoryId = 555L;
        CategoryNotFoundException exception = new CategoryNotFoundException(categoryId);

        // When
        String toString = exception.toString();

        // Then
        assertThat(toString).contains("CategoryNotFoundException");
        assertThat(toString).contains("Category with ID 555 was not found");
    }

    @Test
    @DisplayName("Should support exception chaining")
    void testCategoryNotFoundException_ExceptionChaining() {
        // Given
        Long categoryId = 777L;
        CategoryNotFoundException exception = new CategoryNotFoundException(categoryId);
        RuntimeException cause = new RuntimeException("Database connection failed");

        // When
        exception.initCause(cause);

        // Then
        assertThat(exception.getCause()).isEqualTo(cause);
        assertThat(exception.getCategoryId()).isEqualTo(categoryId);
        assertThat(exception.getMessage()).isEqualTo("Category with ID 777 was not found");
    }

    @Test
    @DisplayName("Should have proper stack trace")
    void testCategoryNotFoundException_StackTrace() {
        // Given
        Long categoryId = 888L;

        // When
        CategoryNotFoundException exception = new CategoryNotFoundException(categoryId);

        // Then
        assertThat(exception.getStackTrace()).isNotEmpty();
        assertThat(exception.getStackTrace()[0].getClassName())
                .isEqualTo(CategoryNotFoundExceptionTest.class.getName());
        assertThat(exception.getStackTrace()[0].getMethodName())
                .isEqualTo("testCategoryNotFoundException_StackTrace");
    }

    @Test
    @DisplayName("Should be serializable")
    void testCategoryNotFoundException_Serializable() {
        // Given
        Long categoryId = 999L;
        CategoryNotFoundException exception = new CategoryNotFoundException(categoryId);

        // Then
        assertThat(exception).isInstanceOf(java.io.Serializable.class);
    }

    @Test
    @DisplayName("Should handle concurrent access to category ID")
    void testCategoryNotFoundException_ConcurrentAccess() throws InterruptedException {
        // Given
        Long categoryId = 1001L;
        CategoryNotFoundException exception = new CategoryNotFoundException(categoryId);
        Long[] results = new Long[2];

        // When
        Thread thread1 = new Thread(() -> results[0] = exception.getCategoryId());
        Thread thread2 = new Thread(() -> results[1] = exception.getCategoryId());

        thread1.start();
        thread2.start();

        thread1.join();
        thread2.join();

        // Then
        assertThat(results[0]).isEqualTo(categoryId);
        assertThat(results[1]).isEqualTo(categoryId);
        assertThat(results[0]).isEqualTo(results[1]);
    }
}
