package com.lookforx.categoryservice.web;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.categoryservice.domain.api.request.CreateCategoryRequest;
import com.lookforx.categoryservice.domain.api.request.UpdateCategoryRequest;
import com.lookforx.categoryservice.domain.api.response.CategoryResponse;
import com.lookforx.categoryservice.domain.dto.CategoryDTO;
import com.lookforx.categoryservice.domain.mapper.CategoryMapper;
import com.lookforx.categoryservice.service.CategoryService;
import com.lookforx.categoryservice.web.client.SearchClient;
import com.lookforx.categoryservice.web.handler.exception.CategoryHasSubcategoriesException;
import com.lookforx.categoryservice.web.handler.exception.CategoryNotFoundException;
import com.lookforx.categoryservice.web.handler.exception.ParentCategoryNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
class CategoryControllerTest {

    private MockMvc mockMvc;

    private ObjectMapper objectMapper;

    @Mock
    private CategoryService categoryService;

    @Mock
    private CategoryMapper categoryMapper;

    @Mock
    private ExceptionServiceClient exceptionServiceClient;

    @Mock
    private SearchClient searchClient;

    @InjectMocks
    private CategoryController categoryController;

    private CategoryDTO categoryDTO;
    private CategoryResponse categoryResponse;
    private CreateCategoryRequest createRequest;
    private UpdateCategoryRequest updateRequest;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        mockMvc = MockMvcBuilders.standaloneSetup(categoryController).build();

        Map<LanguageCode, String> translations = new HashMap<>();
        translations.put(LanguageCode.EN, "Electronics");
        translations.put(LanguageCode.TR, "Elektronik");

        categoryDTO = CategoryDTO.builder()
                .id(1L)
                .parentId(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategoryDTOs(new HashSet<>())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        categoryResponse = CategoryResponse.builder()
                .id(1L)
                .parentId(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategories(new HashSet<>())
                .createdAt(LocalDateTime.now())
                .build();

        createRequest = new CreateCategoryRequest(null, translations, CategoryType.PRODUCT, 1);
        updateRequest = new UpdateCategoryRequest(null, translations, CategoryType.PRODUCT, 1);
    }

    @Test
    void testCreateCategory_Success() throws Exception {
        // Given
        when(categoryService.createCategory(any(CreateCategoryRequest.class))).thenReturn(categoryDTO);
        when(categoryMapper.toResponse(categoryDTO)).thenReturn(categoryResponse);

        // When & Then
        mockMvc.perform(post("/api/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createRequest)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(1L))
                .andExpect(jsonPath("$.translations.EN").value("Electronics"))
                .andExpect(jsonPath("$.translations.TR").value("Elektronik"))
                .andExpect(jsonPath("$.type").value("PRODUCT"))
                .andExpect(jsonPath("$.level").value(1));

        verify(categoryService).createCategory(any(CreateCategoryRequest.class));
        verify(categoryMapper).toResponse(categoryDTO);
    }

    @Test
    void testCreateCategory_InvalidRequest_BadRequest() throws Exception {
        // Given - Invalid request with missing required fields
        CreateCategoryRequest invalidRequest = new CreateCategoryRequest(null, null, null, null);

        // When & Then
        mockMvc.perform(post("/api/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());

        verify(categoryService, never()).createCategory(any());
    }

    @Test
    void testCreateCategory_ParentNotFound_NotFound() throws Exception {
        // Given
        when(categoryService.createCategory(any(CreateCategoryRequest.class)))
                .thenThrow(new ParentCategoryNotFoundException(999L));

        // When & Then
        try {
            mockMvc.perform(post("/api/categories")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(createRequest)));
        } catch (Exception e) {
            // Expected exception due to disabled exception handler in test
            assertTrue(e.getCause() instanceof ParentCategoryNotFoundException);
        }

        verify(categoryService).createCategory(any(CreateCategoryRequest.class));
    }

    @Test
    void testUpdateCategory_Success() throws Exception {
        // Given
        when(categoryService.updateCategory(eq(1L), any(UpdateCategoryRequest.class))).thenReturn(categoryDTO);
        when(categoryMapper.toResponse(categoryDTO)).thenReturn(categoryResponse);

        // When & Then
        mockMvc.perform(put("/api/categories/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(1L))
                .andExpect(jsonPath("$.translations.EN").value("Electronics"));

        verify(categoryService).updateCategory(eq(1L), any(UpdateCategoryRequest.class));
        verify(categoryMapper).toResponse(categoryDTO);
    }

    @Test
    void testUpdateCategory_CategoryNotFound_NotFound() throws Exception {
        // Given
        when(categoryService.updateCategory(eq(999L), any(UpdateCategoryRequest.class)))
                .thenThrow(new CategoryNotFoundException(999L));

        // When & Then
        try {
            mockMvc.perform(put("/api/categories/999")
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(updateRequest)));
        } catch (Exception e) {
            // Expected exception due to disabled exception handler in test
            assertTrue(e.getCause() instanceof CategoryNotFoundException);
        }

        verify(categoryService).updateCategory(eq(999L), any(UpdateCategoryRequest.class));
    }

    @Test
    void testGetAllCategories_Success() throws Exception {
        // Given
        List<CategoryDTO> categories = Arrays.asList(categoryDTO);
        
        when(categoryService.getAllCategories()).thenReturn(categories);
        when(categoryMapper.toResponse(categoryDTO)).thenReturn(categoryResponse);

        // When & Then
        mockMvc.perform(get("/api/categories"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").value(1L))
                .andExpect(jsonPath("$[0].translations.EN").value("Electronics"));

        verify(categoryService).getAllCategories();
        verify(categoryMapper).toResponse(categoryDTO);
    }

    @Test
    void testGetCategoryById_Success() throws Exception {
        // Given
        when(categoryService.getCategoryById(1L)).thenReturn(categoryDTO);
        when(categoryMapper.toResponse(categoryDTO)).thenReturn(categoryResponse);

        // When & Then
        mockMvc.perform(get("/api/categories/1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(1L))
                .andExpect(jsonPath("$.translations.EN").value("Electronics"));

        verify(categoryService).getCategoryById(1L);
        verify(categoryMapper).toResponse(categoryDTO);
    }

    @Test
    void testGetCategoryById_NotFound() throws Exception {
        // Given
        when(categoryService.getCategoryById(999L)).thenThrow(new CategoryNotFoundException(999L));

        // When & Then
        try {
            mockMvc.perform(get("/api/categories/999"));
        } catch (Exception e) {
            // Expected exception due to disabled exception handler in test
            assertTrue(e.getCause() instanceof CategoryNotFoundException);
        }

        verify(categoryService).getCategoryById(999L);
    }

    @Test
    void testGetByParentAndType_WithParent_Success() throws Exception {
        // Given
        List<CategoryDTO> categories = Arrays.asList(categoryDTO);
        when(categoryService.getCategoriesByParentAndType(1L, CategoryType.PRODUCT)).thenReturn(categories);
        when(categoryMapper.toResponse(categoryDTO)).thenReturn(categoryResponse);

        // When & Then
        mockMvc.perform(get("/api/categories/by-parent-and-type")
                        .param("parentId", "1")
                        .param("type", "PRODUCT"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").value(1L));

        verify(categoryService).getCategoriesByParentAndType(1L, CategoryType.PRODUCT);
        verify(categoryMapper).toResponse(categoryDTO);
    }

    @Test
    void testGetByParentAndType_WithoutParent_Success() throws Exception {
        // Given
        List<CategoryDTO> categories = Arrays.asList(categoryDTO);
        when(categoryService.getCategoriesByParentAndType(null, CategoryType.PRODUCT)).thenReturn(categories);
        when(categoryMapper.toResponse(categoryDTO)).thenReturn(categoryResponse);

        // When & Then
        mockMvc.perform(get("/api/categories/by-parent-and-type")
                        .param("type", "PRODUCT"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").value(1L));

        verify(categoryService).getCategoriesByParentAndType(null, CategoryType.PRODUCT);
        verify(categoryMapper).toResponse(categoryDTO);
    }

    @Test
    void testGetByParentAndType_MissingTypeParameter_BadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/categories/by-parent-and-type")
                        .param("parentId", "1"))
                .andExpect(status().isBadRequest());

        verify(categoryService, never()).getCategoriesByParentAndType(any(), any());
    }

    @Test
    void testDeleteCategory_Success() throws Exception {
        // Given
        doNothing().when(categoryService).deleteCategory(1L);

        // When & Then
        mockMvc.perform(delete("/api/categories/1"))
                .andExpect(status().isNoContent());

        verify(categoryService).deleteCategory(1L);
    }

    @Test
    void testDeleteCategory_NotFound() throws Exception {
        // Given
        doThrow(new CategoryNotFoundException(999L)).when(categoryService).deleteCategory(999L);

        // When & Then
        try {
            mockMvc.perform(delete("/api/categories/999"));
        } catch (Exception e) {
            // Expected exception due to disabled exception handler in test
            assertTrue(e.getCause() instanceof CategoryNotFoundException);
        }

        verify(categoryService).deleteCategory(999L);
    }

    @Test
    void testDeleteCategory_HasSubcategories_Conflict() throws Exception {
        // Given
        doThrow(new CategoryHasSubcategoriesException(1L)).when(categoryService).deleteCategory(1L);

        // When & Then
        try {
            mockMvc.perform(delete("/api/categories/1"));
        } catch (Exception e) {
            // Expected exception due to disabled exception handler in test
            assertTrue(e.getCause() instanceof CategoryHasSubcategoriesException);
        }

        verify(categoryService).deleteCategory(1L);
    }
}
