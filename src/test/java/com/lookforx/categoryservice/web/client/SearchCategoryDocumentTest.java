package com.lookforx.categoryservice.web.client;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Comprehensive tests for SearchCategoryDocument.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("SearchCategoryDocument Tests")
class SearchCategoryDocumentTest {

    private Map<String, String> translations;
    private LocalDate now;

    @BeforeEach
    void setUp() {
        translations = new HashMap<>();
        translations.put("EN", "Electronics");
        translations.put("TR", "Elektronik");
        now = LocalDate.now();
    }

    @Test
    @DisplayName("Should create SearchCategoryDocument with builder pattern")
    void testSearchCategoryDocumentBuilder_Success() {
        // When
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id("1")
                .parentId(null)
                .translations(translations)
                .type("PRODUCT")
                .level(1)
                .createdAt(now)
                .updatedAt(now.plusDays(1))
                .createdBy("testUser")
                .build();

        // Then
        assertThat(document).isNotNull();
        assertThat(document.getId()).isEqualTo("1");
        assertThat(document.getParentId()).isNull();
        assertThat(document.getTranslations()).isEqualTo(translations);
        assertThat(document.getType()).isEqualTo("PRODUCT");
        assertThat(document.getLevel()).isEqualTo(1);
        assertThat(document.getCreatedAt()).isEqualTo(now);
        assertThat(document.getUpdatedAt()).isEqualTo(now.plusDays(1));
        assertThat(document.getCreatedBy()).isEqualTo("testUser");
    }

    @Test
    @DisplayName("Should create SearchCategoryDocument with all fields")
    void testSearchCategoryDocumentBuilder_AllFields() {
        // When
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id("2")
                .parentId(1L)
                .translations(translations)
                .type("SERVICE")
                .level(2)
                .createdAt(now)
                .updatedAt(now.plusDays(5))
                .createdBy("adminUser")
                .build();

        // Then
        assertThat(document.getId()).isEqualTo("2");
        assertThat(document.getParentId()).isEqualTo(1L);
        assertThat(document.getTranslations()).isEqualTo(translations);
        assertThat(document.getType()).isEqualTo("SERVICE");
        assertThat(document.getLevel()).isEqualTo(2);
        assertThat(document.getCreatedAt()).isEqualTo(now);
        assertThat(document.getUpdatedAt()).isEqualTo(now.plusDays(5));
        assertThat(document.getCreatedBy()).isEqualTo("adminUser");
    }

    @Test
    @DisplayName("Should handle null values")
    void testSearchCategoryDocumentBuilder_NullValues() {
        // When
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id(null)
                .parentId(null)
                .translations(null)
                .type(null)
                .level(null)
                .createdAt(null)
                .updatedAt(null)
                .createdBy(null)
                .build();

        // Then
        assertThat(document.getId()).isNull();
        assertThat(document.getParentId()).isNull();
        assertThat(document.getTranslations()).isNull();
        assertThat(document.getType()).isNull();
        assertThat(document.getLevel()).isNull();
        assertThat(document.getCreatedAt()).isNull();
        assertThat(document.getUpdatedAt()).isNull();
        assertThat(document.getCreatedBy()).isNull();
    }

    @Test
    @DisplayName("Should handle empty translations")
    void testSearchCategoryDocumentBuilder_EmptyTranslations() {
        // When
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id("1")
                .translations(new HashMap<>())
                .type("PRODUCT")
                .level(1)
                .build();

        // Then
        assertThat(document.getTranslations()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should implement equals and hashCode correctly")
    void testEquals_HashCode() {
        // Given
        SearchCategoryDocument document1 = SearchCategoryDocument.builder()
                .id("1")
                .parentId(null)
                .translations(translations)
                .type("PRODUCT")
                .level(1)
                .createdAt(now)
                .createdBy("testUser")
                .build();

        SearchCategoryDocument document2 = SearchCategoryDocument.builder()
                .id("1")
                .parentId(null)
                .translations(new HashMap<>(translations))
                .type("PRODUCT")
                .level(1)
                .createdAt(now)
                .createdBy("testUser")
                .build();

        SearchCategoryDocument document3 = SearchCategoryDocument.builder()
                .id("2")
                .parentId(null)
                .translations(translations)
                .type("PRODUCT")
                .level(1)
                .createdAt(now)
                .createdBy("testUser")
                .build();

        // Then
        assertThat(document1).isEqualTo(document2);
        assertThat(document1.hashCode()).isEqualTo(document2.hashCode());
        assertThat(document1).isNotEqualTo(document3);
    }

    @Test
    @DisplayName("Should handle null in equals")
    void testEquals_Null() {
        // Given
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id("1")
                .type("PRODUCT")
                .level(1)
                .build();

        // Then
        assertThat(document).isNotEqualTo(null);
    }

    @Test
    @DisplayName("Should handle different class in equals")
    void testEquals_DifferentClass() {
        // Given
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id("1")
                .type("PRODUCT")
                .level(1)
                .build();

        // Then
        assertThat(document).isNotEqualTo("not a SearchCategoryDocument");
    }

    @Test
    @DisplayName("Should implement toString correctly")
    void testToString() {
        // Given
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id("1")
                .translations(translations)
                .type("PRODUCT")
                .level(1)
                .createdBy("testUser")
                .build();

        // When
        String toString = document.toString();

        // Then
        assertThat(toString).contains("SearchCategoryDocument");
        assertThat(toString).contains("id=1");
        assertThat(toString).contains("type=PRODUCT");
        assertThat(toString).contains("level=1");
        assertThat(toString).contains("createdBy=testUser");
    }

    @Test
    @DisplayName("Should support no-args constructor")
    void testNoArgsConstructor() {
        // When
        SearchCategoryDocument document = new SearchCategoryDocument();

        // Then
        assertThat(document).isNotNull();
        assertThat(document.getId()).isNull();
        assertThat(document.getParentId()).isNull();
        assertThat(document.getTranslations()).isNull();
        assertThat(document.getType()).isNull();
        assertThat(document.getLevel()).isNull();
        assertThat(document.getCreatedAt()).isNull();
        assertThat(document.getUpdatedAt()).isNull();
        assertThat(document.getCreatedBy()).isNull();
    }

    @Test
    @DisplayName("Should support all-args constructor")
    void testAllArgsConstructor() {
        // When
        SearchCategoryDocument document = new SearchCategoryDocument(
                "1", null, translations, "PRODUCT", 1, now, now.plusDays(1), "testUser"
        );

        // Then
        assertThat(document.getId()).isEqualTo("1");
        assertThat(document.getParentId()).isNull();
        assertThat(document.getTranslations()).isEqualTo(translations);
        assertThat(document.getType()).isEqualTo("PRODUCT");
        assertThat(document.getLevel()).isEqualTo(1);
        assertThat(document.getCreatedAt()).isEqualTo(now);
        assertThat(document.getUpdatedAt()).isEqualTo(now.plusDays(1));
        assertThat(document.getCreatedBy()).isEqualTo("testUser");
    }

    @Test
    @DisplayName("Should support setter methods")
    void testSetters() {
        // Given
        SearchCategoryDocument document = new SearchCategoryDocument();

        // When
        document.setId("1");
        document.setParentId(2L);
        document.setTranslations(translations);
        document.setType("SERVICE");
        document.setLevel(3);
        document.setCreatedAt(now);
        document.setUpdatedAt(now.plusDays(2));
        document.setCreatedBy("adminUser");

        // Then
        assertThat(document.getId()).isEqualTo("1");
        assertThat(document.getParentId()).isEqualTo(2L);
        assertThat(document.getTranslations()).isEqualTo(translations);
        assertThat(document.getType()).isEqualTo("SERVICE");
        assertThat(document.getLevel()).isEqualTo(3);
        assertThat(document.getCreatedAt()).isEqualTo(now);
        assertThat(document.getUpdatedAt()).isEqualTo(now.plusDays(2));
        assertThat(document.getCreatedBy()).isEqualTo("adminUser");
    }

    @Test
    @DisplayName("Should handle zero and negative values")
    void testZeroAndNegativeValues() {
        // When
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id("0")
                .parentId(-1L)
                .level(-5)
                .build();

        // Then
        assertThat(document.getId()).isEqualTo("0");
        assertThat(document.getParentId()).isEqualTo(-1L);
        assertThat(document.getLevel()).isEqualTo(-5);
    }

    @Test
    @DisplayName("Should handle large values")
    void testLargeValues() {
        // When
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id(String.valueOf(Long.MAX_VALUE))
                .parentId(Long.MAX_VALUE)
                .level(Integer.MAX_VALUE)
                .build();

        // Then
        assertThat(document.getId()).isEqualTo(String.valueOf(Long.MAX_VALUE));
        assertThat(document.getParentId()).isEqualTo(Long.MAX_VALUE);
        assertThat(document.getLevel()).isEqualTo(Integer.MAX_VALUE);
    }

    @Test
    @DisplayName("Should handle multiple language translations")
    void testMultipleLanguageTranslations() {
        // Given
        Map<String, String> multiLangTranslations = new HashMap<>();
        multiLangTranslations.put("EN", "Electronics");
        multiLangTranslations.put("TR", "Elektronik");
        multiLangTranslations.put("DE", "Elektronik");
        multiLangTranslations.put("FR", "Électronique");

        // When
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id("1")
                .translations(multiLangTranslations)
                .type("PRODUCT")
                .level(1)
                .build();

        // Then
        assertThat(document.getTranslations()).hasSize(4);
        assertThat(document.getTranslations().get("EN")).isEqualTo("Electronics");
        assertThat(document.getTranslations().get("TR")).isEqualTo("Elektronik");
        assertThat(document.getTranslations().get("DE")).isEqualTo("Elektronik");
        assertThat(document.getTranslations().get("FR")).isEqualTo("Électronique");
    }

    @Test
    @DisplayName("Should handle date edge cases")
    void testDateEdgeCases() {
        // Given
        LocalDate minDate = LocalDate.MIN;
        LocalDate maxDate = LocalDate.MAX;

        // When
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id("1")
                .createdAt(minDate)
                .updatedAt(maxDate)
                .build();

        // Then
        assertThat(document.getCreatedAt()).isEqualTo(minDate);
        assertThat(document.getUpdatedAt()).isEqualTo(maxDate);
    }

    @Test
    @DisplayName("Should handle empty and whitespace strings")
    void testEmptyAndWhitespaceStrings() {
        // When
        SearchCategoryDocument document = SearchCategoryDocument.builder()
                .id("")
                .type("   ")
                .createdBy("\t\n")
                .build();

        // Then
        assertThat(document.getId()).isEmpty();
        assertThat(document.getType()).isEqualTo("   ");
        assertThat(document.getCreatedBy()).isEqualTo("\t\n");
    }
}
