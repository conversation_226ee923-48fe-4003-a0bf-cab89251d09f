package com.lookforx.categoryservice.web.client;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.client.RestClientTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.test.web.client.MockRestServiceServer;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.client.match.MockRestRequestMatchers.*;
import static org.springframework.test.web.client.response.MockRestResponseCreators.*;

/**
 * Comprehensive tests for SearchClient.
 * Following Effective Java principles for thorough testing.
 * 
 * Note: This test focuses on the interface structure and annotations
 * since Feign clients are typically tested through integration tests.
 */
@DisplayName("SearchClient Tests")
class SearchClientTest {

    private SearchCategoryDocument document;
    private List<SearchCategoryDocument> documents;

    @BeforeEach
    void setUp() {
        Map<String, String> translations = new HashMap<>();
        translations.put("EN", "Electronics");
        translations.put("TR", "Elektronik");

        document = SearchCategoryDocument.builder()
                .id("1")
                .parentId(null)
                .translations(translations)
                .type("PRODUCT")
                .level(1)
                .createdAt(LocalDate.now())
                .updatedAt(LocalDate.now())
                .createdBy("testUser")
                .build();

        SearchCategoryDocument document2 = SearchCategoryDocument.builder()
                .id("2")
                .parentId(1L)
                .translations(Map.of("EN", "Smartphones"))
                .type("PRODUCT")
                .level(2)
                .createdAt(LocalDate.now())
                .updatedAt(LocalDate.now())
                .createdBy("testUser")
                .build();

        documents = Arrays.asList(document, document2);
    }

    @Test
    @DisplayName("Should be annotated with @FeignClient")
    void testSearchClient_HasFeignClientAnnotation() {
        // Then
        assertThat(SearchClient.class.isAnnotationPresent(FeignClient.class)).isTrue();
        
        FeignClient annotation = SearchClient.class.getAnnotation(FeignClient.class);
        assertThat(annotation.name()).isEqualTo("search-microservice");
    }

    @Test
    @DisplayName("Should have indexCategory method with correct annotations")
    void testIndexCategory_MethodAnnotations() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method method = SearchClient.class.getMethod("indexCategory", SearchCategoryDocument.class);

        // Then
        assertThat(method.isAnnotationPresent(org.springframework.web.bind.annotation.PostMapping.class)).isTrue();
        
        org.springframework.web.bind.annotation.PostMapping postMapping = 
                method.getAnnotation(org.springframework.web.bind.annotation.PostMapping.class);
        assertThat(postMapping.value()).containsExactly("/api/v1/categories");
    }

    @Test
    @DisplayName("Should have updateCategory method with correct annotations")
    void testUpdateCategory_MethodAnnotations() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method method = SearchClient.class.getMethod("updateCategory", String.class, SearchCategoryDocument.class);

        // Then
        assertThat(method.isAnnotationPresent(org.springframework.web.bind.annotation.PutMapping.class)).isTrue();
        
        org.springframework.web.bind.annotation.PutMapping putMapping = 
                method.getAnnotation(org.springframework.web.bind.annotation.PutMapping.class);
        assertThat(putMapping.value()).containsExactly("/api/v1/categories/{id}");
    }

    @Test
    @DisplayName("Should have deleteCategory method with correct annotations")
    void testDeleteCategory_MethodAnnotations() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method method = SearchClient.class.getMethod("deleteCategory", String.class);

        // Then
        assertThat(method.isAnnotationPresent(org.springframework.web.bind.annotation.DeleteMapping.class)).isTrue();
        
        org.springframework.web.bind.annotation.DeleteMapping deleteMapping = 
                method.getAnnotation(org.springframework.web.bind.annotation.DeleteMapping.class);
        assertThat(deleteMapping.value()).containsExactly("/api/v1/categories/{id}");
    }

    @Test
    @DisplayName("Should have clearAllCategories method with correct annotations")
    void testClearAllCategories_MethodAnnotations() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method method = SearchClient.class.getMethod("clearAllCategories");

        // Then
        assertThat(method.isAnnotationPresent(org.springframework.web.bind.annotation.DeleteMapping.class)).isTrue();
        
        org.springframework.web.bind.annotation.DeleteMapping deleteMapping = 
                method.getAnnotation(org.springframework.web.bind.annotation.DeleteMapping.class);
        assertThat(deleteMapping.value()).containsExactly("/api/v1/categories/clear-all");
    }

    @Test
    @DisplayName("Should have bulkIndexCategories method with correct annotations")
    void testBulkIndexCategories_MethodAnnotations() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method method = SearchClient.class.getMethod("bulkIndexCategories", java.util.List.class);

        // Then
        assertThat(method.isAnnotationPresent(org.springframework.web.bind.annotation.PostMapping.class)).isTrue();
        
        org.springframework.web.bind.annotation.PostMapping postMapping = 
                method.getAnnotation(org.springframework.web.bind.annotation.PostMapping.class);
        assertThat(postMapping.value()).containsExactly("/api/v1/categories/bulk-index");
    }

    @Test
    @DisplayName("Should have correct parameter annotations for indexCategory")
    void testIndexCategory_ParameterAnnotations() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method method = SearchClient.class.getMethod("indexCategory", SearchCategoryDocument.class);
        java.lang.reflect.Parameter parameter = method.getParameters()[0];

        // Then
        assertThat(parameter.isAnnotationPresent(org.springframework.web.bind.annotation.RequestBody.class)).isTrue();
    }

    @Test
    @DisplayName("Should have correct parameter annotations for updateCategory")
    void testUpdateCategory_ParameterAnnotations() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method method = SearchClient.class.getMethod("updateCategory", String.class, SearchCategoryDocument.class);
        java.lang.reflect.Parameter[] parameters = method.getParameters();

        // Then
        assertThat(parameters[0].isAnnotationPresent(org.springframework.web.bind.annotation.PathVariable.class)).isTrue();
        assertThat(parameters[1].isAnnotationPresent(org.springframework.web.bind.annotation.RequestBody.class)).isTrue();
        
        org.springframework.web.bind.annotation.PathVariable pathVariable = 
                parameters[0].getAnnotation(org.springframework.web.bind.annotation.PathVariable.class);
        assertThat(pathVariable.value()).isEqualTo("id");
    }

    @Test
    @DisplayName("Should have correct parameter annotations for deleteCategory")
    void testDeleteCategory_ParameterAnnotations() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method method = SearchClient.class.getMethod("deleteCategory", String.class);
        java.lang.reflect.Parameter parameter = method.getParameters()[0];

        // Then
        assertThat(parameter.isAnnotationPresent(org.springframework.web.bind.annotation.PathVariable.class)).isTrue();
        
        org.springframework.web.bind.annotation.PathVariable pathVariable = 
                parameter.getAnnotation(org.springframework.web.bind.annotation.PathVariable.class);
        assertThat(pathVariable.value()).isEqualTo("id");
    }

    @Test
    @DisplayName("Should have correct parameter annotations for bulkIndexCategories")
    void testBulkIndexCategories_ParameterAnnotations() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method method = SearchClient.class.getMethod("bulkIndexCategories", java.util.List.class);
        java.lang.reflect.Parameter parameter = method.getParameters()[0];

        // Then
        assertThat(parameter.isAnnotationPresent(org.springframework.web.bind.annotation.RequestBody.class)).isTrue();
    }

    @Test
    @DisplayName("Should have void return types for all methods")
    void testAllMethods_VoidReturnType() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method[] methods = {
                SearchClient.class.getMethod("indexCategory", SearchCategoryDocument.class),
                SearchClient.class.getMethod("updateCategory", String.class, SearchCategoryDocument.class),
                SearchClient.class.getMethod("deleteCategory", String.class),
                SearchClient.class.getMethod("clearAllCategories"),
                SearchClient.class.getMethod("bulkIndexCategories", java.util.List.class)
        };

        // Then
        for (java.lang.reflect.Method method : methods) {
            assertThat(method.getReturnType()).isEqualTo(void.class);
        }
    }

    @Test
    @DisplayName("Should be an interface")
    void testSearchClient_IsInterface() {
        // Then
        assertThat(SearchClient.class.isInterface()).isTrue();
        assertThat(SearchClient.class.isAnnotation()).isFalse();
        assertThat(SearchClient.class.isEnum()).isFalse();
    }

    @Test
    @DisplayName("Should have exactly 5 methods")
    void testSearchClient_MethodCount() {
        // When
        java.lang.reflect.Method[] methods = SearchClient.class.getDeclaredMethods();

        // Then
        assertThat(methods).hasSize(5);
    }

    @Test
    @DisplayName("Should have correct method names")
    void testSearchClient_MethodNames() {
        // When
        java.lang.reflect.Method[] methods = SearchClient.class.getDeclaredMethods();
        String[] methodNames = java.util.Arrays.stream(methods)
                .map(java.lang.reflect.Method::getName)
                .sorted()
                .toArray(String[]::new);

        // Then
        assertThat(methodNames).containsExactly(
                "bulkIndexCategories",
                "clearAllCategories", 
                "deleteCategory",
                "indexCategory",
                "updateCategory"
        );
    }

    @Test
    @DisplayName("Should follow REST API conventions")
    void testSearchClient_RESTConventions() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method indexMethod = SearchClient.class.getMethod("indexCategory", SearchCategoryDocument.class);
        java.lang.reflect.Method updateMethod = SearchClient.class.getMethod("updateCategory", String.class, SearchCategoryDocument.class);
        java.lang.reflect.Method deleteMethod = SearchClient.class.getMethod("deleteCategory", String.class);
        java.lang.reflect.Method clearMethod = SearchClient.class.getMethod("clearAllCategories");
        java.lang.reflect.Method bulkMethod = SearchClient.class.getMethod("bulkIndexCategories", java.util.List.class);

        // Then - Check HTTP methods follow REST conventions
        assertThat(indexMethod.isAnnotationPresent(org.springframework.web.bind.annotation.PostMapping.class)).isTrue(); // POST for create
        assertThat(updateMethod.isAnnotationPresent(org.springframework.web.bind.annotation.PutMapping.class)).isTrue(); // PUT for update
        assertThat(deleteMethod.isAnnotationPresent(org.springframework.web.bind.annotation.DeleteMapping.class)).isTrue(); // DELETE for delete
        assertThat(clearMethod.isAnnotationPresent(org.springframework.web.bind.annotation.DeleteMapping.class)).isTrue(); // DELETE for clear
        assertThat(bulkMethod.isAnnotationPresent(org.springframework.web.bind.annotation.PostMapping.class)).isTrue(); // POST for bulk create
    }

    @Test
    @DisplayName("Should have consistent API path structure")
    void testSearchClient_ConsistentAPIPath() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method[] methods = SearchClient.class.getDeclaredMethods();

        // Then
        for (java.lang.reflect.Method method : methods) {
            String path = getPathFromMethod(method);
            assertThat(path).startsWith("/api/v1/categories");
        }
    }

    // Helper method to extract path from method annotations
    private String getPathFromMethod(java.lang.reflect.Method method) {
        if (method.isAnnotationPresent(org.springframework.web.bind.annotation.PostMapping.class)) {
            return method.getAnnotation(org.springframework.web.bind.annotation.PostMapping.class).value()[0];
        } else if (method.isAnnotationPresent(org.springframework.web.bind.annotation.PutMapping.class)) {
            return method.getAnnotation(org.springframework.web.bind.annotation.PutMapping.class).value()[0];
        } else if (method.isAnnotationPresent(org.springframework.web.bind.annotation.DeleteMapping.class)) {
            return method.getAnnotation(org.springframework.web.bind.annotation.DeleteMapping.class).value()[0];
        }
        return "";
    }
}
