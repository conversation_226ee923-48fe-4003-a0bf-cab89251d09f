package com.lookforx.categoryservice.config.audit;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.AuditorAware;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

/**
 * Comprehensive tests for AuditorAwareImpl.
 * Following Effective Java principles for thorough testing.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuditorAwareImpl Tests")
class AuditorAwareImplTest {

    @Mock
    private SecurityContext securityContext;

    @Mock
    private Authentication authentication;

    private AuditorAwareImpl auditorAware;
    private MockedStatic<SecurityContextHolder> securityContextHolderMock;

    @BeforeEach
    void setUp() {
        auditorAware = new AuditorAwareImpl();
        securityContextHolderMock = mockStatic(SecurityContextHolder.class);
        securityContextHolderMock.when(SecurityContextHolder::getContext).thenReturn(securityContext);
    }

    @AfterEach
    void tearDown() {
        securityContextHolderMock.close();
    }

    @Test
    @DisplayName("Should create AuditorAwareImpl instance")
    void testAuditorAwareImpl_Creation() {
        // Then
        assertThat(auditorAware).isNotNull();
        assertThat(auditorAware).isInstanceOf(AuditorAware.class);
    }

    @Test
    @DisplayName("Should be annotated with @Component")
    void testAuditorAwareImpl_HasComponentAnnotation() {
        // Then
        assertThat(auditorAware.getClass().isAnnotationPresent(Component.class)).isTrue();
        
        Component annotation = auditorAware.getClass().getAnnotation(Component.class);
        assertThat(annotation.value()).isEqualTo("auditorProvider");
    }

    @Test
    @DisplayName("Should return anonymousUser when authentication is null")
    void testGetCurrentAuditor_NullAuthentication_ReturnsAnonymousUser() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(null);

        // When
        Optional<String> result = auditorAware.getCurrentAuditor();

        // Then
        assertThat(result).isPresent();
        assertThat(result.get()).isEqualTo("anonymousUser");
    }

    @Test
    @DisplayName("Should return anonymousUser when authentication is not authenticated")
    void testGetCurrentAuditor_NotAuthenticated_ReturnsAnonymousUser() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.isAuthenticated()).thenReturn(false);

        // When
        Optional<String> result = auditorAware.getCurrentAuditor();

        // Then
        assertThat(result).isPresent();
        assertThat(result.get()).isEqualTo("anonymousUser");
    }

    @Test
    @DisplayName("Should return anonymousUser when authentication is anonymous")
    void testGetCurrentAuditor_AnonymousAuthentication_ReturnsAnonymousUser() {
        // Given
        AnonymousAuthenticationToken anonymousAuth = new AnonymousAuthenticationToken(
                "key", "anonymous", Collections.singletonList(new SimpleGrantedAuthority("ROLE_ANONYMOUS")));
        when(securityContext.getAuthentication()).thenReturn(anonymousAuth);

        // When
        Optional<String> result = auditorAware.getCurrentAuditor();

        // Then
        assertThat(result).isPresent();
        assertThat(result.get()).isEqualTo("anonymousUser");
    }

    @Test
    @DisplayName("Should return username when authentication is valid")
    void testGetCurrentAuditor_ValidAuthentication_ReturnsUsername() {
        // Given
        String expectedUsername = "testuser";
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.isAuthenticated()).thenReturn(true);
        when(authentication.getName()).thenReturn(expectedUsername);

        // When
        Optional<String> result = auditorAware.getCurrentAuditor();

        // Then
        assertThat(result).isPresent();
        assertThat(result.get()).isEqualTo(expectedUsername);
    }

    @Test
    @DisplayName("Should return username for UsernamePasswordAuthenticationToken")
    void testGetCurrentAuditor_UsernamePasswordToken_ReturnsUsername() {
        // Given
        String expectedUsername = "admin";
        UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                expectedUsername, "password", Collections.singletonList(new SimpleGrantedAuthority("ROLE_ADMIN")));
        when(securityContext.getAuthentication()).thenReturn(authToken);

        // When
        Optional<String> result = auditorAware.getCurrentAuditor();

        // Then
        assertThat(result).isPresent();
        assertThat(result.get()).isEqualTo(expectedUsername);
    }

    @Test
    @DisplayName("Should handle empty username gracefully")
    void testGetCurrentAuditor_EmptyUsername_ReturnsEmptyString() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.isAuthenticated()).thenReturn(true);
        when(authentication.getName()).thenReturn("");

        // When
        Optional<String> result = auditorAware.getCurrentAuditor();

        // Then
        assertThat(result).isPresent();
        assertThat(result.get()).isEmpty();
    }

    @Test
    @DisplayName("Should handle null username gracefully")
    void testGetCurrentAuditor_NullUsername_ThrowsException() {
        // Given
        when(securityContext.getAuthentication()).thenReturn(authentication);
        when(authentication.isAuthenticated()).thenReturn(true);
        when(authentication.getName()).thenReturn(null);

        // When & Then - Optional.of(null) throws NullPointerException
        assertThatThrownBy(() -> auditorAware.getCurrentAuditor())
                .isInstanceOf(NullPointerException.class);
    }

    @Test
    @DisplayName("Should always return non-empty Optional")
    void testGetCurrentAuditor_AlwaysReturnsNonEmptyOptional() {
        // Given - various scenarios
        when(securityContext.getAuthentication()).thenReturn(null);

        // When
        Optional<String> result = auditorAware.getCurrentAuditor();

        // Then
        assertThat(result).isNotNull();
        assertThat(result).isPresent();
    }
}
