package com.lookforx.categoryservice.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Comprehensive tests for KafkaConfig.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("KafkaConfig Tests")
class KafkaConfigTest {

    private KafkaConfig kafkaConfig;

    @BeforeEach
    void setUp() {
        kafkaConfig = new KafkaConfig();
    }

    @Test
    @DisplayName("Should create KafkaConfig instance")
    void testKafkaConfig_Creation() {
        // Then
        assertThat(kafkaConfig).isNotNull();
    }

    @Test
    @DisplayName("Should be annotated with @Configuration")
    void testKafkaConfig_HasConfigurationAnnotation() {
        // Then
        assertThat(kafkaConfig.getClass().isAnnotationPresent(Configuration.class)).isTrue();
    }

    @Test
    @DisplayName("Should be annotated with @EnableKafka")
    void testKafkaConfig_HasEnableKafkaAnnotation() {
        // Then
        assertThat(kafkaConfig.getClass().isAnnotationPresent(EnableKafka.class)).isTrue();
    }

    @Test
    @DisplayName("Should be annotated with @ComponentScan")
    void testKafkaConfig_HasComponentScanAnnotation() {
        // Then
        assertThat(kafkaConfig.getClass().isAnnotationPresent(ComponentScan.class)).isTrue();
    }

    @Test
    @DisplayName("Should have correct basePackages in @ComponentScan")
    void testKafkaConfig_ComponentScanBasePackages() {
        // Given
        ComponentScan annotation = kafkaConfig.getClass().getAnnotation(ComponentScan.class);

        // Then
        assertThat(annotation).isNotNull();
        assertThat(annotation.basePackages()).containsExactly("com.lookforx.common.kafka");
    }

    @Test
    @DisplayName("Should be a simple configuration class without business methods")
    void testKafkaConfig_NoBusinessMethods() {
        // Then - Filter out JaCoCo generated methods
        long businessMethods = java.util.Arrays.stream(kafkaConfig.getClass().getDeclaredMethods())
                .filter(method -> !method.getName().contains("jacoco"))
                .count();
        assertThat(businessMethods).isEqualTo(0);
    }
}
