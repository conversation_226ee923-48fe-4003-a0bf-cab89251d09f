package com.lookforx.categoryservice.config;

import com.lookforx.common.kafka.LogEventPublisher;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.Mockito.*;

/**
 * Comprehensive tests for LoggingConfig.
 * Following Effective Java principles for thorough testing.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("LoggingConfig Tests")
class LoggingConfigTest {

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private LogEventPublisher logEventPublisher;

    private LoggingConfig loggingConfig;

    @BeforeEach
    void setUp() {
        loggingConfig = new LoggingConfig();
        // Use reflection to set the private field
        try {
            java.lang.reflect.Field field = LoggingConfig.class.getDeclaredField("applicationContext");
            field.setAccessible(true);
            field.set(loggingConfig, applicationContext);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    @DisplayName("Should create LoggingConfig instance")
    void testLoggingConfig_Creation() {
        // Then
        assertThat(loggingConfig).isNotNull();
    }

    @Test
    @DisplayName("Should be annotated with @Configuration")
    void testLoggingConfig_HasConfigurationAnnotation() {
        // Then
        assertThat(loggingConfig.getClass().isAnnotationPresent(Configuration.class)).isTrue();
    }

    @Test
    @DisplayName("Should be annotated with @Slf4j")
    void testLoggingConfig_HasSlf4jAnnotation() {
        // Then - Check if the class has a log field (generated by @Slf4j)
        boolean hasLogField = java.util.Arrays.stream(loggingConfig.getClass().getDeclaredFields())
                .anyMatch(field -> field.getName().equals("log"));
        assertThat(hasLogField).isTrue();
    }

    @Test
    @DisplayName("Should create CommandLineRunner bean")
    void testDebugLogEventPublisher_CreateBean() {
        // When
        CommandLineRunner runner = loggingConfig.debugLogEventPublisher();

        // Then
        assertThat(runner).isNotNull();
    }

    @Test
    @DisplayName("Should handle successful LogEventPublisher bean lookup")
    void testDebugLogEventPublisher_SuccessfulLookup() throws Exception {
        // Given
        when(applicationContext.getBean(LogEventPublisher.class)).thenReturn(logEventPublisher);
        CommandLineRunner runner = loggingConfig.debugLogEventPublisher();

        // When & Then
        assertDoesNotThrow(() -> runner.run());
        verify(applicationContext).getBean(LogEventPublisher.class);
    }

    @Test
    @DisplayName("Should handle LogEventPublisher bean not found")
    void testDebugLogEventPublisher_BeanNotFound() throws Exception {
        // Given
        when(applicationContext.getBean(LogEventPublisher.class))
                .thenThrow(new RuntimeException("Bean not found"));
        when(applicationContext.getBeanDefinitionNames())
                .thenReturn(new String[]{"logService", "kafkaTemplate", "otherBean"});
        when(applicationContext.getBean("logService")).thenReturn(new Object());
        when(applicationContext.getBean("kafkaTemplate")).thenReturn(new Object());
        // Don't mock otherBean since it won't be accessed
        
        CommandLineRunner runner = loggingConfig.debugLogEventPublisher();

        // When & Then
        assertDoesNotThrow(() -> runner.run());
        verify(applicationContext).getBean(LogEventPublisher.class);
        verify(applicationContext).getBeanDefinitionNames();
    }

    @Test
    @DisplayName("Should filter beans containing 'log' or 'kafka'")
    void testDebugLogEventPublisher_FilterBeans() throws Exception {
        // Given
        when(applicationContext.getBean(LogEventPublisher.class))
                .thenThrow(new RuntimeException("Bean not found"));
        when(applicationContext.getBeanDefinitionNames())
                .thenReturn(new String[]{"logService", "kafkaTemplate", "userService", "loggerBean", "kafkaConsumer"});
        
        // Mock the beans
        when(applicationContext.getBean("logService")).thenReturn(new Object());
        when(applicationContext.getBean("kafkaTemplate")).thenReturn(new Object());
        when(applicationContext.getBean("loggerBean")).thenReturn(new Object());
        when(applicationContext.getBean("kafkaConsumer")).thenReturn(new Object());
        
        CommandLineRunner runner = loggingConfig.debugLogEventPublisher();

        // When & Then
        assertDoesNotThrow(() -> runner.run());
        
        // Verify that only log/kafka beans are accessed
        verify(applicationContext).getBean("logService");
        verify(applicationContext).getBean("kafkaTemplate");
        verify(applicationContext).getBean("loggerBean");
        verify(applicationContext).getBean("kafkaConsumer");
        verify(applicationContext, never()).getBean("userService");
    }

    @Test
    @DisplayName("Should handle empty bean names array")
    void testDebugLogEventPublisher_EmptyBeanNames() throws Exception {
        // Given
        when(applicationContext.getBean(LogEventPublisher.class))
                .thenThrow(new RuntimeException("Bean not found"));
        when(applicationContext.getBeanDefinitionNames()).thenReturn(new String[]{});
        
        CommandLineRunner runner = loggingConfig.debugLogEventPublisher();

        // When & Then
        assertDoesNotThrow(() -> runner.run());
        verify(applicationContext).getBeanDefinitionNames();
    }
}
