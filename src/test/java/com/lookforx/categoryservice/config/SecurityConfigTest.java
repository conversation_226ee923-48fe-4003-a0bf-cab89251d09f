package com.lookforx.categoryservice.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.when;

/**
 * Comprehensive tests for SecurityConfig.
 * Following Effective Java principles for thorough testing.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SecurityConfig Tests")
class SecurityConfigTest {

    @Mock
    private HttpSecurity httpSecurity;

    private SecurityConfig securityConfig;

    @BeforeEach
    void setUp() {
        securityConfig = new SecurityConfig();
    }

    @Test
    @DisplayName("Should throw exception when HttpSecurity is null")
    void testSecurityFilterChain_NullHttpSecurity_ThrowsException() {
        // When & Then
        assertThatThrownBy(() -> securityConfig.securityFilterChain(null))
                .isInstanceOf(NullPointerException.class)
                .hasMessage("HttpSecurity cannot be null");
    }

    @Test
    @DisplayName("Should create SecurityConfig instance")
    void testSecurityConfig_Creation() {
        // Then
        assertThat(securityConfig).isNotNull();
    }

    // Note: Testing the actual SecurityFilterChain configuration requires
    // a more complex setup with Spring Security test framework.
    // For comprehensive testing, we would need integration tests.

    @Test
    @DisplayName("Should be a valid Spring configuration class")
    void testSecurityConfig_IsConfigurationClass() {
        // Then
        assertThat(securityConfig.getClass().isAnnotationPresent(
                org.springframework.context.annotation.Configuration.class)).isTrue();
        assertThat(securityConfig.getClass().isAnnotationPresent(
                org.springframework.security.config.annotation.web.configuration.EnableWebSecurity.class)).isTrue();
    }

    @Test
    @DisplayName("Should have proper logging configuration")
    void testSecurityConfig_HasLogging() {
        // Check if the class has a log field (generated by @Slf4j)
        boolean hasLogField = java.util.Arrays.stream(securityConfig.getClass().getDeclaredFields())
                .anyMatch(field -> field.getName().equals("log"));
        assertThat(hasLogField).isTrue();
    }
}
