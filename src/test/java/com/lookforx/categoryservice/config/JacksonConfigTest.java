package com.lookforx.categoryservice.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive tests for JacksonConfig.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("JacksonConfig Tests")
class JacksonConfigTest {

    private JacksonConfig jacksonConfig;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        jacksonConfig = new JacksonConfig();
        objectMapper = jacksonConfig.objectMapper();
    }

    @Test
    @DisplayName("Should create ObjectMapper with proper configuration")
    void testObjectMapper_Configuration() {
        // Then
        assertThat(objectMapper).isNotNull();
        assertThat(objectMapper.getRegisteredModuleIds()).contains("jackson-datatype-jsr310");
        
        // Check serialization features
        assertThat(objectMapper.isEnabled(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)).isFalse();
        assertThat(objectMapper.isEnabled(SerializationFeature.FAIL_ON_EMPTY_BEANS)).isFalse();
        assertThat(objectMapper.isEnabled(SerializationFeature.WRITE_ENUMS_USING_TO_STRING)).isTrue();
        
        // Check deserialization features
        assertThat(objectMapper.isEnabled(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)).isFalse();
        assertThat(objectMapper.isEnabled(DeserializationFeature.READ_ENUMS_USING_TO_STRING)).isTrue();
        assertThat(objectMapper.isEnabled(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT)).isTrue();
    }

    @Test
    @DisplayName("Should serialize LocalDateTime properly")
    void testLocalDateTime_Serialization() throws JsonProcessingException {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2023, 12, 25, 10, 30, 45);
        TestObject testObject = new TestObject(dateTime);

        // When
        String json = objectMapper.writeValueAsString(testObject);

        // Then
        assertThat(json).contains("2023-12-25T10:30:45");
        assertThat(json).doesNotContain("timestamp"); // Should not be timestamp format
    }

    @Test
    @DisplayName("Should deserialize LocalDateTime properly")
    void testLocalDateTime_Deserialization() throws JsonProcessingException {
        // Given
        String json = "{\"dateTime\":\"2023-12-25T10:30:45\"}";

        // When
        TestObject result = objectMapper.readValue(json, TestObject.class);

        // Then
        assertThat(result.getDateTime()).isEqualTo(LocalDateTime.of(2023, 12, 25, 10, 30, 45));
    }

    @Test
    @DisplayName("Should handle unknown properties gracefully")
    void testUnknownProperties_Ignored() throws JsonProcessingException {
        // Given
        String json = "{\"dateTime\":\"2023-12-25T10:30:45\",\"unknownProperty\":\"value\"}";

        // When & Then
        assertDoesNotThrow(() -> {
            TestObject result = objectMapper.readValue(json, TestObject.class);
            assertThat(result.getDateTime()).isEqualTo(LocalDateTime.of(2023, 12, 25, 10, 30, 45));
        });
    }

    @Test
    @DisplayName("Should handle empty beans without failing")
    void testEmptyBeans_NoFailure() throws JsonProcessingException {
        // Given
        EmptyBean emptyBean = new EmptyBean();

        // When & Then
        assertDoesNotThrow(() -> {
            String json = objectMapper.writeValueAsString(emptyBean);
            assertThat(json).isEqualTo("{}");
        });
    }

    @Test
    @DisplayName("Should handle null values in empty strings")
    void testEmptyStringAsNull() throws JsonProcessingException {
        // Given
        String json = "{\"value\":\"\"}";

        // When
        Map<String, Object> result = objectMapper.readValue(json, Map.class);

        // Then
        assertThat(result).containsKey("value");
        // Empty string handling depends on specific configuration
    }

    @Test
    @DisplayName("Should serialize enums using toString")
    void testEnum_SerializationUsingToString() throws JsonProcessingException {
        // Given
        TestEnum testEnum = TestEnum.VALUE_ONE;
        EnumContainer container = new EnumContainer(testEnum);

        // When
        String json = objectMapper.writeValueAsString(container);

        // Then
        assertThat(json).contains("VALUE_ONE");
    }

    @Test
    @DisplayName("Should deserialize enums using toString")
    void testEnum_DeserializationUsingToString() throws JsonProcessingException {
        // Given
        String json = "{\"enumValue\":\"VALUE_ONE\"}";

        // When
        EnumContainer result = objectMapper.readValue(json, EnumContainer.class);

        // Then
        assertThat(result.getEnumValue()).isEqualTo(TestEnum.VALUE_ONE);
    }

    @Test
    @DisplayName("Should have JavaTimeModule registered")
    void testJavaTimeModule_Registered() {
        // Then
        assertThat(objectMapper.getRegisteredModuleIds())
                .contains("jackson-datatype-jsr310");
    }

    // Test helper classes
    private static class TestObject {
        private LocalDateTime dateTime;

        public TestObject() {}

        public TestObject(LocalDateTime dateTime) {
            this.dateTime = dateTime;
        }

        public LocalDateTime getDateTime() { return dateTime; }
        public void setDateTime(LocalDateTime dateTime) { this.dateTime = dateTime; }
    }

    private static class EmptyBean {
        // Empty class for testing FAIL_ON_EMPTY_BEANS feature
    }

    private enum TestEnum {
        VALUE_ONE, VALUE_TWO
    }

    private static class EnumContainer {
        private TestEnum enumValue;

        public EnumContainer() {}

        public EnumContainer(TestEnum enumValue) {
            this.enumValue = enumValue;
        }

        public TestEnum getEnumValue() { return enumValue; }
        public void setEnumValue(TestEnum enumValue) { this.enumValue = enumValue; }
    }
}
