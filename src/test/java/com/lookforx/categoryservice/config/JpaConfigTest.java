package com.lookforx.categoryservice.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Comprehensive tests for JpaConfig.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("JpaConfig Tests")
class JpaConfigTest {

    private JpaConfig jpaConfig;

    @BeforeEach
    void setUp() {
        jpaConfig = new JpaConfig();
    }

    @Test
    @DisplayName("Should create JpaConfig instance")
    void testJpaConfig_Creation() {
        // Then
        assertThat(jpaConfig).isNotNull();
    }

    @Test
    @DisplayName("Should be annotated with @Configuration")
    void testJpaConfig_HasConfigurationAnnotation() {
        // Then
        assertThat(jpaConfig.getClass().isAnnotationPresent(Configuration.class)).isTrue();
    }

    @Test
    @DisplayName("Should be annotated with @EnableJpaAuditing")
    void testJpaConfig_HasEnableJpaAuditingAnnotation() {
        // Then
        assertThat(jpaConfig.getClass().isAnnotationPresent(EnableJpaAuditing.class)).isTrue();
    }

    @Test
    @DisplayName("Should have correct auditorAwareRef in @EnableJpaAuditing")
    void testJpaConfig_AuditorAwareRef() {
        // Given
        EnableJpaAuditing annotation = jpaConfig.getClass().getAnnotation(EnableJpaAuditing.class);

        // Then
        assertThat(annotation).isNotNull();
        assertThat(annotation.auditorAwareRef()).isEqualTo("auditorProvider");
    }

    @Test
    @DisplayName("Should be a simple configuration class without business methods")
    void testJpaConfig_NoBusinessMethods() {
        // Then - Filter out JaCoCo generated methods
        long businessMethods = java.util.Arrays.stream(jpaConfig.getClass().getDeclaredMethods())
                .filter(method -> !method.getName().contains("jacoco"))
                .count();
        assertThat(businessMethods).isEqualTo(0);
    }
}
