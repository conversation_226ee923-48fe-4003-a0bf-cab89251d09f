package com.lookforx.categoryservice.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.util.ReflectionTestUtils;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive tests for CacheConfig.
 * Following Effective Java principles for thorough testing.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("CacheConfig Tests")
class CacheConfigTest {

    @Mock
    private RedisConnectionFactory redisConnectionFactory;

    private CacheConfig cacheConfig;

    @BeforeEach
    void setUp() {
        cacheConfig = new CacheConfig();
        ReflectionTestUtils.setField(cacheConfig, "cacheTimeoutSeconds", 1800L);
    }

    @Test
    @DisplayName("Should create RedisTemplate with proper configuration")
    void testRedisTemplate_Success() {
        // When
        RedisTemplate<String, Object> redisTemplate = cacheConfig.redisTemplate(redisConnectionFactory);

        // Then
        assertThat(redisTemplate).isNotNull();
        assertThat(redisTemplate.getConnectionFactory()).isEqualTo(redisConnectionFactory);
        assertThat(redisTemplate.getKeySerializer()).isNotNull();
        assertThat(redisTemplate.getValueSerializer()).isNotNull();
        assertThat(redisTemplate.getHashKeySerializer()).isNotNull();
        assertThat(redisTemplate.getHashValueSerializer()).isNotNull();
        assertThat(redisTemplate.getDefaultSerializer()).isNotNull();
    }

    @Test
    @DisplayName("Should throw exception when RedisConnectionFactory is null")
    void testRedisTemplate_NullFactory_ThrowsException() {
        // When & Then
        assertThatThrownBy(() -> cacheConfig.redisTemplate(null))
                .isInstanceOf(NullPointerException.class)
                .hasMessage("RedisConnectionFactory cannot be null");
    }

    @Test
    @DisplayName("Should create CacheManager with proper configuration")
    void testCacheManager_Success() {
        // When
        CacheManager cacheManager = cacheConfig.cacheManager(redisConnectionFactory);

        // Then
        assertThat(cacheManager).isNotNull();
        assertThat(cacheManager).isInstanceOf(RedisCacheManager.class);
    }

    @Test
    @DisplayName("Should throw exception when CacheManager factory is null")
    void testCacheManager_NullFactory_ThrowsException() {
        // When & Then
        assertThatThrownBy(() -> cacheConfig.cacheManager(null))
                .isInstanceOf(NullPointerException.class)
                .hasMessage("RedisConnectionFactory cannot be null");
    }

    @Test
    @DisplayName("Should use minimum cache timeout when configured value is too low")
    void testCacheManager_MinimumTimeout() {
        // Given
        ReflectionTestUtils.setField(cacheConfig, "cacheTimeoutSeconds", 30L); // Less than minimum

        // When
        CacheManager cacheManager = cacheConfig.cacheManager(redisConnectionFactory);

        // Then
        assertThat(cacheManager).isNotNull();
        // The actual timeout validation is internal to the configuration
    }

    @Test
    @DisplayName("Should handle large cache timeout values")
    void testCacheManager_LargeTimeout() {
        // Given
        ReflectionTestUtils.setField(cacheConfig, "cacheTimeoutSeconds", 86400L); // 24 hours

        // When
        CacheManager cacheManager = cacheConfig.cacheManager(redisConnectionFactory);

        // Then
        assertThat(cacheManager).isNotNull();
    }

    @Test
    @DisplayName("Should create RedisTemplate with consistent serializers")
    void testRedisTemplate_SerializerConsistency() {
        // When
        RedisTemplate<String, Object> redisTemplate = cacheConfig.redisTemplate(redisConnectionFactory);

        // Then
        assertThat(redisTemplate.getKeySerializer())
                .isEqualTo(redisTemplate.getHashKeySerializer());
        assertThat(redisTemplate.getValueSerializer())
                .isEqualTo(redisTemplate.getHashValueSerializer());
        assertThat(redisTemplate.getDefaultSerializer())
                .isEqualTo(redisTemplate.getValueSerializer());
    }

    @Test
    @DisplayName("Should configure RedisTemplate as afterPropertiesSet")
    void testRedisTemplate_AfterPropertiesSet() {
        // When
        RedisTemplate<String, Object> redisTemplate = cacheConfig.redisTemplate(redisConnectionFactory);

        // Then
        // The template should be properly initialized
        assertThat(redisTemplate.getConnectionFactory()).isNotNull();
        assertThat(redisTemplate.isExposeConnection()).isFalse(); // Default value
    }
}
