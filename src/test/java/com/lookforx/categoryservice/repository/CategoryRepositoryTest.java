package com.lookforx.categoryservice.repository;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Comprehensive tests for CategoryRepository.
 * Following Effective Java principles for thorough testing.
 */
@DataJpaTest
@ActiveProfiles("test")
@DisplayName("CategoryRepository Tests")
class CategoryRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private CategoryRepository categoryRepository;

    private Category parentCategory;
    private Category childCategory1;
    private Category childCategory2;
    private Category serviceCategory;

    @BeforeEach
    void setUp() {
        // Create parent category
        Map<LanguageCode, String> parentTranslations = new HashMap<>();
        parentTranslations.put(LanguageCode.EN, "Electronics");
        parentTranslations.put(LanguageCode.TR, "Elektronik");

        parentCategory = Category.builder()
                .parent(null)
                .translations(parentTranslations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategories(new HashSet<>())
                .build();

        // Create child categories
        Map<LanguageCode, String> child1Translations = new HashMap<>();
        child1Translations.put(LanguageCode.EN, "Smartphones");
        child1Translations.put(LanguageCode.TR, "Akıllı Telefonlar");

        childCategory1 = Category.builder()
                .parent(parentCategory)
                .translations(child1Translations)
                .type(CategoryType.PRODUCT)
                .level(2)
                .subcategories(new HashSet<>())
                .build();

        Map<LanguageCode, String> child2Translations = new HashMap<>();
        child2Translations.put(LanguageCode.EN, "Laptops");
        child2Translations.put(LanguageCode.TR, "Dizüstü Bilgisayarlar");

        childCategory2 = Category.builder()
                .parent(parentCategory)
                .translations(child2Translations)
                .type(CategoryType.PRODUCT)
                .level(2)
                .subcategories(new HashSet<>())
                .build();

        // Create service category
        Map<LanguageCode, String> serviceTranslations = new HashMap<>();
        serviceTranslations.put(LanguageCode.EN, "Consulting");
        serviceTranslations.put(LanguageCode.TR, "Danışmanlık");

        serviceCategory = Category.builder()
                .parent(null)
                .translations(serviceTranslations)
                .type(CategoryType.SERVICE)
                .level(1)
                .subcategories(new HashSet<>())
                .build();

        // Persist entities
        entityManager.persistAndFlush(parentCategory);
        entityManager.persistAndFlush(childCategory1);
        entityManager.persistAndFlush(childCategory2);
        entityManager.persistAndFlush(serviceCategory);
    }

    @Test
    @DisplayName("Should find categories by parent ID and type")
    void testFindByParentIdAndType() {
        // When
        List<Category> result = categoryRepository.findByParentIdAndType(
                parentCategory.getId(), 
                CategoryType.PRODUCT, 
                Sort.by("level")
        );

        // Then
        assertThat(result).hasSize(2);
        assertThat(result).extracting(Category::getType).containsOnly(CategoryType.PRODUCT);
        assertThat(result).extracting(Category::getLevel).containsOnly(2);
    }

    @Test
    @DisplayName("Should find root categories by type")
    void testFindByParentIsNullAndType() {
        // When
        List<Category> productRoots = categoryRepository.findByParentIsNullAndType(
                CategoryType.PRODUCT, 
                Sort.by("level")
        );
        List<Category> serviceRoots = categoryRepository.findByParentIsNullAndType(
                CategoryType.SERVICE, 
                Sort.by("level")
        );

        // Then
        assertThat(productRoots).hasSize(1);
        assertThat(productRoots.get(0)).isEqualTo(parentCategory);
        assertThat(serviceRoots).hasSize(1);
        assertThat(serviceRoots.get(0)).isEqualTo(serviceCategory);
    }

    @Test
    @DisplayName("Should find categories by type")
    void testFindByType() {
        // When
        List<Category> productCategories = categoryRepository.findByType(CategoryType.PRODUCT);
        List<Category> serviceCategories = categoryRepository.findByType(CategoryType.SERVICE);

        // Then
        assertThat(productCategories).hasSize(3); // parent + 2 children
        assertThat(serviceCategories).hasSize(1);
        assertThat(productCategories).extracting(Category::getType).containsOnly(CategoryType.PRODUCT);
        assertThat(serviceCategories).extracting(Category::getType).containsOnly(CategoryType.SERVICE);
    }

    @Test
    @DisplayName("Should find categories by level")
    void testFindByLevel() {
        // When
        List<Category> level1Categories = categoryRepository.findByLevel(1);
        List<Category> level2Categories = categoryRepository.findByLevel(2);

        // Then
        assertThat(level1Categories).hasSize(2); // parent + service
        assertThat(level2Categories).hasSize(2); // 2 children
        assertThat(level1Categories).extracting(Category::getLevel).containsOnly(1);
        assertThat(level2Categories).extracting(Category::getLevel).containsOnly(2);
    }

    @Test
    @DisplayName("Should find categories by parent ID")
    void testFindByParentId() {
        // When
        List<Category> children = categoryRepository.findByParentId(parentCategory.getId());
        List<Category> rootCategories = categoryRepository.findByParentId(null);

        // Then
        assertThat(children).hasSize(2);
        assertThat(children).containsExactlyInAnyOrder(childCategory1, childCategory2);
        assertThat(rootCategories).hasSize(2); // parent + service
        assertThat(rootCategories).containsExactlyInAnyOrder(parentCategory, serviceCategory);
    }

    @Test
    @DisplayName("Should check if category exists by ID")
    void testExistsById() {
        // When & Then
        assertThat(categoryRepository.existsById(parentCategory.getId())).isTrue();
        assertThat(categoryRepository.existsById(childCategory1.getId())).isTrue();
        assertThat(categoryRepository.existsById(999L)).isFalse();
    }

    @Test
    @DisplayName("Should count categories by type")
    void testCountByType() {
        // When
        long productCount = categoryRepository.countByType(CategoryType.PRODUCT);
        long serviceCount = categoryRepository.countByType(CategoryType.SERVICE);

        // Then
        assertThat(productCount).isEqualTo(3); // parent + 2 children
        assertThat(serviceCount).isEqualTo(1);
    }

    @Test
    @DisplayName("Should find categories by type with subcategories")
    void testFindByTypeWithSubcategories() {
        // When
        List<Category> result = categoryRepository.findByTypeWithSubcategories(CategoryType.PRODUCT);

        // Then
        assertThat(result).hasSize(3);
        
        // Find parent category in result
        Category foundParent = result.stream()
                .filter(c -> c.getId().equals(parentCategory.getId()))
                .findFirst()
                .orElse(null);
        
        assertThat(foundParent).isNotNull();
        // Note: Subcategories might not be loaded due to lazy loading in test context
    }

    @Test
    @DisplayName("Should find category by ID with relationships")
    void testFindByIdWithRelationships() {
        // When
        Optional<Category> result = categoryRepository.findByIdWithRelationships(parentCategory.getId());

        // Then
        assertThat(result).isPresent();
        Category foundCategory = result.get();
        assertThat(foundCategory.getId()).isEqualTo(parentCategory.getId());
        assertThat(foundCategory.getType()).isEqualTo(CategoryType.PRODUCT);
    }

    @Test
    @DisplayName("Should return empty when finding non-existent category with relationships")
    void testFindByIdWithRelationships_NotFound() {
        // When
        Optional<Category> result = categoryRepository.findByIdWithRelationships(999L);

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    @DisplayName("Should handle empty results for non-matching criteria")
    void testEmptyResults() {
        // When
        List<Category> nonExistentParent = categoryRepository.findByParentId(999L);
        List<Category> nonExistentLevel = categoryRepository.findByLevel(999);
        long nonExistentTypeCount = categoryRepository.countByType(CategoryType.PRODUCT);

        // Then
        assertThat(nonExistentParent).isEmpty();
        assertThat(nonExistentLevel).isEmpty();
        // Note: countByType will return actual count, not 0 for existing type
    }

    @Test
    @DisplayName("Should handle sorting in findByParentIdAndType")
    void testFindByParentIdAndType_Sorting() {
        // When
        List<Category> ascendingSort = categoryRepository.findByParentIdAndType(
                parentCategory.getId(), 
                CategoryType.PRODUCT, 
                Sort.by(Sort.Direction.ASC, "id")
        );
        
        List<Category> descendingSort = categoryRepository.findByParentIdAndType(
                parentCategory.getId(), 
                CategoryType.PRODUCT, 
                Sort.by(Sort.Direction.DESC, "id")
        );

        // Then
        assertThat(ascendingSort).hasSize(2);
        assertThat(descendingSort).hasSize(2);
        
        if (ascendingSort.size() == 2 && descendingSort.size() == 2) {
            assertThat(ascendingSort.get(0).getId()).isLessThan(ascendingSort.get(1).getId());
            assertThat(descendingSort.get(0).getId()).isGreaterThan(descendingSort.get(1).getId());
        }
    }

    @Test
    @DisplayName("Should handle sorting in findByParentIsNullAndType")
    void testFindByParentIsNullAndType_Sorting() {
        // When
        List<Category> result = categoryRepository.findByParentIsNullAndType(
                CategoryType.PRODUCT, 
                Sort.by(Sort.Direction.ASC, "level")
        );

        // Then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getLevel()).isEqualTo(1);
    }

    @Test
    @DisplayName("Should persist and retrieve categories correctly")
    void testPersistAndRetrieve() {
        // Given
        Map<LanguageCode, String> newTranslations = new HashMap<>();
        newTranslations.put(LanguageCode.EN, "New Category");
        
        Category newCategory = Category.builder()
                .parent(null)
                .translations(newTranslations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategories(new HashSet<>())
                .build();

        // When
        Category saved = categoryRepository.save(newCategory);
        Optional<Category> retrieved = categoryRepository.findById(saved.getId());

        // Then
        assertThat(saved.getId()).isNotNull();
        assertThat(retrieved).isPresent();
        assertThat(retrieved.get().getTranslations()).isEqualTo(newTranslations);
        assertThat(retrieved.get().getType()).isEqualTo(CategoryType.PRODUCT);
        assertThat(retrieved.get().getLevel()).isEqualTo(1);
    }

    @Test
    @DisplayName("Should delete categories correctly")
    void testDeleteCategory() {
        // Given
        Long categoryId = childCategory1.getId();
        assertThat(categoryRepository.existsById(categoryId)).isTrue();

        // When
        categoryRepository.deleteById(categoryId);
        entityManager.flush();

        // Then
        assertThat(categoryRepository.existsById(categoryId)).isFalse();
        assertThat(categoryRepository.findById(categoryId)).isEmpty();
    }
}
