package com.lookforx.categoryservice.domain.entity;

import com.lookforx.categoryservice.domain.CategoryType;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Comprehensive tests for CategoryType enum.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("CategoryType Enum Tests")
class CategoryTypeTest {

    @Test
    @DisplayName("Should have all expected enum values")
    void testCategoryType_AllValues() {
        // Given
        CategoryType[] expectedValues = {CategoryType.PRODUCT, CategoryType.SERVICE};

        // When
        CategoryType[] actualValues = CategoryType.values();

        // Then
        assertThat(actualValues).containsExactlyInAnyOrder(expectedValues);
        assertThat(actualValues).hasSize(2);
    }

    @Test
    @DisplayName("Should have PRODUCT enum value")
    void testCategoryType_ProductValue() {
        // When
        CategoryType productType = CategoryType.PRODUCT;

        // Then
        assertThat(productType).isNotNull();
        assertThat(productType.name()).isEqualTo("PRODUCT");
        assertThat(productType.toString()).isEqualTo("PRODUCT");
    }

    @Test
    @DisplayName("Should have SERVICE enum value")
    void testCategoryType_ServiceValue() {
        // When
        CategoryType serviceType = CategoryType.SERVICE;

        // Then
        assertThat(serviceType).isNotNull();
        assertThat(serviceType.name()).isEqualTo("SERVICE");
        assertThat(serviceType.toString()).isEqualTo("SERVICE");
    }

    @Test
    @DisplayName("Should support valueOf method")
    void testCategoryType_ValueOf() {
        // When & Then
        assertThat(CategoryType.valueOf("PRODUCT")).isEqualTo(CategoryType.PRODUCT);
        assertThat(CategoryType.valueOf("SERVICE")).isEqualTo(CategoryType.SERVICE);
    }

    @Test
    @DisplayName("Should throw exception for invalid valueOf")
    void testCategoryType_ValueOf_Invalid() {
        // When & Then
        org.junit.jupiter.api.Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> CategoryType.valueOf("INVALID")
        );
    }

    @Test
    @DisplayName("Should maintain enum equality")
    void testCategoryType_Equality() {
        // When
        CategoryType product1 = CategoryType.PRODUCT;
        CategoryType product2 = CategoryType.valueOf("PRODUCT");

        // Then
        assertThat(product1).isEqualTo(product2);
        assertThat(product1).isSameAs(product2); // Enum instances are singletons
        assertThat(product1.hashCode()).isEqualTo(product2.hashCode());
    }

    @Test
    @DisplayName("Should maintain enum inequality")
    void testCategoryType_Inequality() {
        // When
        CategoryType product = CategoryType.PRODUCT;
        CategoryType service = CategoryType.SERVICE;

        // Then
        assertThat(product).isNotEqualTo(service);
        assertThat(product.hashCode()).isNotEqualTo(service.hashCode());
    }

    @Test
    @DisplayName("Should support ordinal values")
    void testCategoryType_Ordinals() {
        // Then
        assertThat(CategoryType.PRODUCT.ordinal()).isEqualTo(0);
        assertThat(CategoryType.SERVICE.ordinal()).isEqualTo(1);
    }

    @Test
    @DisplayName("Should support compareTo")
    void testCategoryType_CompareTo() {
        // When & Then
        assertThat(CategoryType.PRODUCT.compareTo(CategoryType.SERVICE)).isLessThan(0);
        assertThat(CategoryType.SERVICE.compareTo(CategoryType.PRODUCT)).isGreaterThan(0);
        assertThat(CategoryType.PRODUCT.compareTo(CategoryType.PRODUCT)).isEqualTo(0);
    }

    @Test
    @DisplayName("Should be serializable")
    void testCategoryType_Serializable() {
        // Then - Enums are serializable by default
        assertThat(CategoryType.PRODUCT).isInstanceOf(java.io.Serializable.class);
        assertThat(CategoryType.SERVICE).isInstanceOf(java.io.Serializable.class);
    }

    @Test
    @DisplayName("Should work in switch statements")
    void testCategoryType_SwitchStatement() {
        // When & Then
        String result1 = getTypeDescription(CategoryType.PRODUCT);
        String result2 = getTypeDescription(CategoryType.SERVICE);

        assertThat(result1).isEqualTo("Product Category");
        assertThat(result2).isEqualTo("Service Category");
    }

    @Test
    @DisplayName("Should work with EnumSet")
    void testCategoryType_EnumSet() {
        // When
        java.util.EnumSet<CategoryType> allTypes = java.util.EnumSet.allOf(CategoryType.class);
        java.util.EnumSet<CategoryType> productOnly = java.util.EnumSet.of(CategoryType.PRODUCT);

        // Then
        assertThat(allTypes).hasSize(2);
        assertThat(allTypes).contains(CategoryType.PRODUCT, CategoryType.SERVICE);
        assertThat(productOnly).hasSize(1);
        assertThat(productOnly).contains(CategoryType.PRODUCT);
    }

    @Test
    @DisplayName("Should work with EnumMap")
    void testCategoryType_EnumMap() {
        // When
        java.util.EnumMap<CategoryType, String> typeDescriptions = new java.util.EnumMap<>(CategoryType.class);
        typeDescriptions.put(CategoryType.PRODUCT, "Product Category");
        typeDescriptions.put(CategoryType.SERVICE, "Service Category");

        // Then
        assertThat(typeDescriptions).hasSize(2);
        assertThat(typeDescriptions.get(CategoryType.PRODUCT)).isEqualTo("Product Category");
        assertThat(typeDescriptions.get(CategoryType.SERVICE)).isEqualTo("Service Category");
    }

    // Helper method for switch statement test
    private String getTypeDescription(CategoryType type) {
        return switch (type) {
            case PRODUCT -> "Product Category";
            case SERVICE -> "Service Category";
        };
    }
}
