package com.lookforx.categoryservice.domain.entity;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive tests for Category entity.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("Category Entity Tests")
class CategoryTest {

    private Map<LanguageCode, String> translations;
    private Category parentCategory;
    private Category childCategory;

    @BeforeEach
    void setUp() {
        translations = new HashMap<>();
        translations.put(LanguageCode.EN, "Electronics");
        translations.put(LanguageCode.TR, "Elektronik");

        parentCategory = Category.builder()
                .parent(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        Map<LanguageCode, String> childTranslations = new HashMap<>();
        childTranslations.put(LanguageCode.EN, "Smartphones");
        childTranslations.put(LanguageCode.TR, "Akıllı Telefonlar");

        childCategory = Category.builder()
                .parent(parentCategory)
                .translations(childTranslations)
                .type(CategoryType.PRODUCT)
                .level(2)
                .build();
    }

    @Test
    @DisplayName("Should create Category with builder pattern")
    void testCategoryBuilder_Success() {
        // When
        Category category = Category.builder()
                .parent(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        // Then
        assertThat(category).isNotNull();
        assertThat(category.getParent()).isNull();
        assertThat(category.getTranslations()).isEqualTo(translations);
        assertThat(category.getType()).isEqualTo(CategoryType.PRODUCT);
        assertThat(category.getLevel()).isEqualTo(1);
        assertThat(category.getSubcategories()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should create Category with subcategories")
    void testCategoryBuilder_WithSubcategories() {
        // Given
        Set<Category> subcategories = new HashSet<>();
        subcategories.add(childCategory);

        // When
        Category category = Category.builder()
                .parent(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategories(subcategories)
                .build();

        // Then
        assertThat(category.getSubcategories()).hasSize(1);
        assertThat(category.getSubcategories()).contains(childCategory);
    }

    @Test
    @DisplayName("Should handle null translations gracefully")
    void testCategoryBuilder_NullTranslations() {
        // When
        Category category = Category.builder()
                .parent(null)
                .translations(null)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        // Then
        assertThat(category.getTranslations()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should handle empty translations")
    void testCategoryBuilder_EmptyTranslations() {
        // When
        Category category = Category.builder()
                .parent(null)
                .translations(new HashMap<>())
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        // Then
        assertThat(category.getTranslations()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should allow negative level (no validation in current implementation)")
    void testCategoryBuilder_NegativeLevel_Allowed() {
        // When
        Category category = Category.builder()
                .parent(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(-1)
                .build();

        // Then
        assertThat(category.getLevel()).isEqualTo(-1);
    }

    @Test
    @DisplayName("Should allow zero level")
    void testCategoryBuilder_ZeroLevel_Success() {
        // When
        Category category = Category.builder()
                .parent(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(0)
                .build();

        // Then
        assertThat(category.getLevel()).isEqualTo(0);
    }

    @Test
    @DisplayName("Should implement equals correctly - different instances are not equal")
    void testEquals_DifferentInstances() {
        // Given
        Category category1 = Category.builder()
                .parent(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        Category category2 = Category.builder()
                .parent(null)
                .translations(new HashMap<>(translations))
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        // Set same ID using reflection
        setId(category1, 1L);
        setId(category2, 1L);

        // Then - Since BaseEntity doesn't override equals, different instances are not equal
        // even with same content (this is the current behavior)
        assertThat(category1.getId()).isEqualTo(category2.getId());
        assertThat(category1.getType()).isEqualTo(category2.getType());
        assertThat(category1.getLevel()).isEqualTo(category2.getLevel());
        assertThat(category1).isNotEqualTo(category2); // Different object references
    }

    @Test
    @DisplayName("Should implement equals correctly for different IDs")
    void testEquals_DifferentIds() {
        // Given
        Category category1 = Category.builder()
                .parent(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        Category category2 = Category.builder()
                .parent(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        // Set different IDs
        setId(category1, 1L);
        setId(category2, 2L);

        // Then
        assertThat(category1).isNotEqualTo(category2);
    }

    @Test
    @DisplayName("Should handle null in equals")
    void testEquals_Null() {
        // Then
        assertThat(parentCategory).isNotEqualTo(null);
    }

    @Test
    @DisplayName("Should handle different class in equals")
    void testEquals_DifferentClass() {
        // Then
        assertThat(parentCategory).isNotEqualTo("not a category");
    }

    @Test
    @DisplayName("Should implement toString correctly")
    void testToString() {
        // When
        String toString = parentCategory.toString();

        // Then - Default Object.toString() format
        assertThat(toString).contains("Category");
        assertThat(toString).contains("@"); // Object hash code
    }

    @Test
    @DisplayName("Should handle translations map (no defensive copying in builder)")
    void testTranslations_MapHandling() {
        // Given
        Map<LanguageCode, String> originalTranslations = new HashMap<>();
        originalTranslations.put(LanguageCode.EN, "Original");

        Category category = Category.builder()
                .translations(originalTranslations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        // When - Modify original map
        originalTranslations.put(LanguageCode.TR, "Modified");

        // Then - Category may be affected (no defensive copying in builder)
        assertThat(category.getTranslations()).containsKey(LanguageCode.EN);
        // The actual behavior depends on implementation
    }

    @Test
    @DisplayName("Should handle subcategories set (no defensive copying in builder)")
    void testSubcategories_SetHandling() {
        // Given
        Set<Category> originalSubcategories = new HashSet<>();
        originalSubcategories.add(childCategory);

        Category category = Category.builder()
                .subcategories(originalSubcategories)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        // When - Modify original set
        Category anotherChild = Category.builder()
                .type(CategoryType.PRODUCT)
                .level(2)
                .build();
        originalSubcategories.add(anotherChild);

        // Then - Category may be affected (no defensive copying in builder)
        assertThat(category.getSubcategories()).contains(childCategory);
        // The actual behavior depends on implementation
    }

    @Test
    @DisplayName("Should handle auditing fields")
    void testAuditingFields() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        
        // When - Set auditing fields using reflection
        setCreatedBy(parentCategory, "testUser");
        setCreatedAt(parentCategory, now);
        setUpdatedAt(parentCategory, now.plusMinutes(5));

        // Then
        assertThat(parentCategory.getCreatedBy()).isEqualTo("testUser");
        assertThat(parentCategory.getCreatedAt()).isEqualTo(now);
        assertThat(parentCategory.getUpdatedAt()).isEqualTo(now.plusMinutes(5));
    }

    // Helper methods for reflection
    private void setId(Category category, Long id) {
        try {
            java.lang.reflect.Field field = category.getClass().getSuperclass().getDeclaredField("id");
            field.setAccessible(true);
            field.set(category, id);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void setCreatedBy(Category category, String createdBy) {
        try {
            java.lang.reflect.Field field = category.getClass().getSuperclass().getDeclaredField("createdBy");
            field.setAccessible(true);
            field.set(category, createdBy);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void setCreatedAt(Category category, LocalDateTime createdAt) {
        try {
            java.lang.reflect.Field field = category.getClass().getSuperclass().getDeclaredField("createdAt");
            field.setAccessible(true);
            field.set(category, createdAt);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void setUpdatedAt(Category category, LocalDateTime updatedAt) {
        try {
            java.lang.reflect.Field field = category.getClass().getSuperclass().getDeclaredField("updatedAt");
            field.setAccessible(true);
            field.set(category, updatedAt);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
