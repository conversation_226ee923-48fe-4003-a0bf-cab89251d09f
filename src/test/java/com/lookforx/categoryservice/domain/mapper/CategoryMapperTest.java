package com.lookforx.categoryservice.domain.mapper;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.categoryservice.domain.api.request.CreateCategoryRequest;
import com.lookforx.categoryservice.domain.api.request.UpdateCategoryRequest;
import com.lookforx.categoryservice.domain.api.response.CategoryResponse;
import com.lookforx.categoryservice.domain.dto.CategoryDTO;
import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * Comprehensive tests for CategoryMapper.
 * Following Effective Java principles for thorough testing.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("CategoryMapper Tests")
class CategoryMapperTest {

    @InjectMocks
    private CategoryMapper categoryMapper;

    private Map<LanguageCode, String> translations;
    private CreateCategoryRequest createRequest;
    private UpdateCategoryRequest updateRequest;
    private Category parentCategory;
    private Category childCategory;
    private CategoryDTO categoryDTO;
    private LocalDateTime now;

    @BeforeEach
    void setUp() {
        translations = new HashMap<>();
        translations.put(LanguageCode.EN, "Electronics");
        translations.put(LanguageCode.TR, "Elektronik");

        createRequest = CreateCategoryRequest.builder()
                .parentId(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        updateRequest = UpdateCategoryRequest.builder()
                .parentId(null)
                .translations(translations)
                .type(CategoryType.SERVICE)
                .level(2)
                .build();

        parentCategory = Category.builder()
                .parent(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategories(new HashSet<>())
                .build();

        Map<LanguageCode, String> childTranslations = new HashMap<>();
        childTranslations.put(LanguageCode.EN, "Smartphones");

        childCategory = Category.builder()
                .parent(parentCategory)
                .translations(childTranslations)
                .type(CategoryType.PRODUCT)
                .level(2)
                .subcategories(new HashSet<>())
                .build();

        now = LocalDateTime.now();

        categoryDTO = CategoryDTO.builder()
                .id(1L)
                .parentId(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategoryDTOs(new HashSet<>())
                .createdBy("testUser")
                .createdAt(now)
                .updatedAt(now.plusMinutes(5))
                .build();

        // Set IDs using reflection
        setId(parentCategory, 1L);
        setId(childCategory, 2L);
    }

    @Test
    @DisplayName("Should convert CreateCategoryRequest to Category entity")
    void testToEntity_Success() {
        // When
        Category result = categoryMapper.toEntity(createRequest, null);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getParent()).isNull();
        assertThat(result.getTranslations()).isEqualTo(translations);
        assertThat(result.getType()).isEqualTo(CategoryType.PRODUCT);
        assertThat(result.getLevel()).isEqualTo(1);
        assertThat(result.getSubcategories()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should convert CreateCategoryRequest to Category entity with parent")
    void testToEntity_WithParent() {
        // When
        Category result = categoryMapper.toEntity(createRequest, parentCategory);

        // Then
        assertThat(result.getParent()).isEqualTo(parentCategory);
        assertThat(result.getTranslations()).isEqualTo(translations);
        assertThat(result.getType()).isEqualTo(CategoryType.PRODUCT);
        assertThat(result.getLevel()).isEqualTo(1);
    }

    @Test
    @DisplayName("Should throw exception when CreateCategoryRequest is null")
    void testToEntity_NullRequest_ThrowsException() {
        // When & Then
        assertThatThrownBy(() -> categoryMapper.toEntity(null, parentCategory))
                .isInstanceOf(NullPointerException.class)
                .hasMessage("CreateCategoryRequest cannot be null");
    }

    @Test
    @DisplayName("Should handle null translations in CreateCategoryRequest")
    void testToEntity_NullTranslations() {
        // Given
        CreateCategoryRequest requestWithNullTranslations = CreateCategoryRequest.builder()
                .translations(null)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        // When
        Category result = categoryMapper.toEntity(requestWithNullTranslations, null);

        // Then
        assertThat(result.getTranslations()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should handle empty translations in CreateCategoryRequest")
    void testToEntity_EmptyTranslations() {
        // Given
        CreateCategoryRequest requestWithEmptyTranslations = CreateCategoryRequest.builder()
                .translations(new HashMap<>())
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        // When
        Category result = categoryMapper.toEntity(requestWithEmptyTranslations, null);

        // Then
        assertThat(result.getTranslations()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should update existing Category entity")
    void testUpdateEntity_Success() {
        // Given - Use mutable HashMap instead of immutable Map.of()
        Map<LanguageCode, String> oldTranslations = new HashMap<>();
        oldTranslations.put(LanguageCode.EN, "Old Name");

        Category existingCategory = Category.builder()
                .parent(null)
                .translations(oldTranslations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        // When
        categoryMapper.updateEntity(updateRequest, existingCategory, parentCategory);

        // Then
        assertThat(existingCategory.getParent()).isEqualTo(parentCategory);
        assertThat(existingCategory.getTranslations()).isEqualTo(translations);
        assertThat(existingCategory.getType()).isEqualTo(CategoryType.SERVICE);
        assertThat(existingCategory.getLevel()).isEqualTo(2);
    }

    @Test
    @DisplayName("Should throw exception when UpdateCategoryRequest is null")
    void testUpdateEntity_NullRequest_ThrowsException() {
        // When & Then
        assertThatThrownBy(() -> categoryMapper.updateEntity(null, parentCategory, null))
                .isInstanceOf(NullPointerException.class)
                .hasMessage("UpdateCategoryRequest cannot be null");
    }

    @Test
    @DisplayName("Should throw exception when existing Category is null")
    void testUpdateEntity_NullExisting_ThrowsException() {
        // When & Then
        assertThatThrownBy(() -> categoryMapper.updateEntity(updateRequest, null, null))
                .isInstanceOf(NullPointerException.class)
                .hasMessage("Existing category cannot be null");
    }

    @Test
    @DisplayName("Should convert CategoryDTO to CategoryResponse")
    void testToResponse_Success() {
        // When
        CategoryResponse result = categoryMapper.toResponse(categoryDTO);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getParentId()).isNull();
        assertThat(result.getTranslations()).isEqualTo(translations);
        assertThat(result.getType()).isEqualTo(CategoryType.PRODUCT);
        assertThat(result.getLevel()).isEqualTo(1);
        assertThat(result.getSubcategories()).isNotNull().isEmpty();
        assertThat(result.getCreatedBy()).isEqualTo("testUser");
        assertThat(result.getCreatedAt()).isEqualTo(now);
        assertThat(result.getRevisedAt()).isEqualTo(now.plusMinutes(5));
    }

    @Test
    @DisplayName("Should throw exception when CategoryDTO is null")
    void testToResponse_NullDTO_ThrowsException() {
        // When & Then
        assertThatThrownBy(() -> categoryMapper.toResponse(null))
                .isInstanceOf(NullPointerException.class)
                .hasMessage("CategoryDTO cannot be null");
    }

    @Test
    @DisplayName("Should convert Category entity to CategoryDTO")
    void testToDTO_Success() {
        // When
        CategoryDTO result = categoryMapper.toDTO(parentCategory);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getParentId()).isNull();
        assertThat(result.getTranslations()).isEqualTo(translations);
        assertThat(result.getType()).isEqualTo(CategoryType.PRODUCT);
        assertThat(result.getLevel()).isEqualTo(1);
        assertThat(result.getSubcategoryDTOs()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should convert Category entity with parent to CategoryDTO")
    void testToDTO_WithParent() {
        // When
        CategoryDTO result = categoryMapper.toDTO(childCategory);

        // Then
        assertThat(result.getParentId()).isEqualTo(1L);
        assertThat(result.getLevel()).isEqualTo(2);
    }

    @Test
    @DisplayName("Should throw exception when Category entity is null")
    void testToDTO_NullEntity_ThrowsException() {
        // When & Then
        assertThatThrownBy(() -> categoryMapper.toDTO(null))
                .isInstanceOf(NullPointerException.class)
                .hasMessage("Category entity cannot be null");
    }

    @Test
    @DisplayName("Should handle null subcategories in entity")
    void testToDTO_NullSubcategories() {
        // Given
        Category categoryWithNullSubcategories = Category.builder()
                .parent(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategories(null)
                .build();

        // When
        CategoryDTO result = categoryMapper.toDTO(categoryWithNullSubcategories);

        // Then
        assertThat(result.getSubcategoryDTOs()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should handle empty subcategories in entity")
    void testToDTO_EmptySubcategories() {
        // Given
        Category categoryWithEmptySubcategories = Category.builder()
                .parent(null)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategories(new HashSet<>())
                .build();

        // When
        CategoryDTO result = categoryMapper.toDTO(categoryWithEmptySubcategories);

        // Then
        assertThat(result.getSubcategoryDTOs()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should handle null translations in entity")
    void testToDTO_NullTranslations() {
        // Given
        Category categoryWithNullTranslations = Category.builder()
                .parent(null)
                .translations(null)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategories(new HashSet<>())
                .build();

        // When
        CategoryDTO result = categoryMapper.toDTO(categoryWithNullTranslations);

        // Then
        assertThat(result.getTranslations()).isNotNull().isEmpty();
    }

    @Test
    @DisplayName("Should handle defensive copying of translations")
    void testDefensiveCopying_Translations() {
        // Given
        Map<LanguageCode, String> originalTranslations = new HashMap<>();
        originalTranslations.put(LanguageCode.EN, "Original");

        CreateCategoryRequest request = CreateCategoryRequest.builder()
                .translations(originalTranslations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        // When
        Category result = categoryMapper.toEntity(request, null);

        // Modify original map
        originalTranslations.put(LanguageCode.TR, "Modified");

        // Then - Result should not be affected by modification
        assertThat(result.getTranslations()).hasSize(1);
        assertThat(result.getTranslations()).containsKey(LanguageCode.EN);
        assertThat(result.getTranslations()).doesNotContainKey(LanguageCode.TR);
    }

    // Helper method for reflection
    private void setId(Category category, Long id) {
        try {
            java.lang.reflect.Field field = category.getClass().getSuperclass().getDeclaredField("id");
            field.setAccessible(true);
            field.set(category, id);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
