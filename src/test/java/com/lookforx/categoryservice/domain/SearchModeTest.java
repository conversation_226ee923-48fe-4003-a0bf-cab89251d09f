package com.lookforx.categoryservice.domain;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Comprehensive tests for SearchMode enum.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("SearchMode Enum Tests")
class SearchModeTest {

    @Test
    @DisplayName("Should have all expected enum values")
    void testSearchMode_AllValues() {
        // Given
        SearchMode[] expectedValues = {SearchMode.FULL_TEXT, SearchMode.FUZZY, SearchMode.PHRASE};

        // When
        SearchMode[] actualValues = SearchMode.values();

        // Then
        assertThat(actualValues).containsExactlyInAnyOrder(expectedValues);
        assertThat(actualValues).hasSize(3);
    }

    @Test
    @DisplayName("Should have FULL_TEXT enum value")
    void testSearchMode_FullTextValue() {
        // When
        SearchMode fullTextMode = SearchMode.FULL_TEXT;

        // Then
        assertThat(fullTextMode).isNotNull();
        assertThat(fullTextMode.name()).isEqualTo("FULL_TEXT");
        assertThat(fullTextMode.toString()).isEqualTo("FULL_TEXT");
    }

    @Test
    @DisplayName("Should have FUZZY enum value")
    void testSearchMode_FuzzyValue() {
        // When
        SearchMode fuzzyMode = SearchMode.FUZZY;

        // Then
        assertThat(fuzzyMode).isNotNull();
        assertThat(fuzzyMode.name()).isEqualTo("FUZZY");
        assertThat(fuzzyMode.toString()).isEqualTo("FUZZY");
    }

    @Test
    @DisplayName("Should have PHRASE enum value")
    void testSearchMode_PhraseValue() {
        // When
        SearchMode phraseMode = SearchMode.PHRASE;

        // Then
        assertThat(phraseMode).isNotNull();
        assertThat(phraseMode.name()).isEqualTo("PHRASE");
        assertThat(phraseMode.toString()).isEqualTo("PHRASE");
    }

    @Test
    @DisplayName("Should support valueOf method")
    void testSearchMode_ValueOf() {
        // When & Then
        assertThat(SearchMode.valueOf("FULL_TEXT")).isEqualTo(SearchMode.FULL_TEXT);
        assertThat(SearchMode.valueOf("FUZZY")).isEqualTo(SearchMode.FUZZY);
        assertThat(SearchMode.valueOf("PHRASE")).isEqualTo(SearchMode.PHRASE);
    }

    @Test
    @DisplayName("Should throw exception for invalid valueOf")
    void testSearchMode_ValueOf_Invalid() {
        // When & Then
        org.junit.jupiter.api.Assertions.assertThrows(
                IllegalArgumentException.class,
                () -> SearchMode.valueOf("INVALID")
        );
    }

    @Test
    @DisplayName("Should maintain enum equality")
    void testSearchMode_Equality() {
        // When
        SearchMode fullText1 = SearchMode.FULL_TEXT;
        SearchMode fullText2 = SearchMode.valueOf("FULL_TEXT");

        // Then
        assertThat(fullText1).isEqualTo(fullText2);
        assertThat(fullText1).isSameAs(fullText2); // Enum instances are singletons
        assertThat(fullText1.hashCode()).isEqualTo(fullText2.hashCode());
    }

    @Test
    @DisplayName("Should maintain enum inequality")
    void testSearchMode_Inequality() {
        // When
        SearchMode fullText = SearchMode.FULL_TEXT;
        SearchMode fuzzy = SearchMode.FUZZY;
        SearchMode phrase = SearchMode.PHRASE;

        // Then
        assertThat(fullText).isNotEqualTo(fuzzy);
        assertThat(fullText).isNotEqualTo(phrase);
        assertThat(fuzzy).isNotEqualTo(phrase);
    }

    @Test
    @DisplayName("Should support ordinal values")
    void testSearchMode_Ordinals() {
        // Then
        assertThat(SearchMode.FULL_TEXT.ordinal()).isEqualTo(0);
        assertThat(SearchMode.FUZZY.ordinal()).isEqualTo(1);
        assertThat(SearchMode.PHRASE.ordinal()).isEqualTo(2);
    }

    @Test
    @DisplayName("Should support compareTo")
    void testSearchMode_CompareTo() {
        // When & Then
        assertThat(SearchMode.FULL_TEXT.compareTo(SearchMode.FUZZY)).isLessThan(0);
        assertThat(SearchMode.FUZZY.compareTo(SearchMode.PHRASE)).isLessThan(0);
        assertThat(SearchMode.PHRASE.compareTo(SearchMode.FULL_TEXT)).isGreaterThan(0);
        assertThat(SearchMode.FULL_TEXT.compareTo(SearchMode.FULL_TEXT)).isEqualTo(0);
    }

    @Test
    @DisplayName("Should be serializable")
    void testSearchMode_Serializable() {
        // Then - Enums are serializable by default
        assertThat(SearchMode.FULL_TEXT).isInstanceOf(java.io.Serializable.class);
        assertThat(SearchMode.FUZZY).isInstanceOf(java.io.Serializable.class);
        assertThat(SearchMode.PHRASE).isInstanceOf(java.io.Serializable.class);
    }

    @Test
    @DisplayName("Should work in switch statements")
    void testSearchMode_SwitchStatement() {
        // When & Then
        String result1 = getModeDescription(SearchMode.FULL_TEXT);
        String result2 = getModeDescription(SearchMode.FUZZY);
        String result3 = getModeDescription(SearchMode.PHRASE);

        assertThat(result1).isEqualTo("Full Text Search");
        assertThat(result2).isEqualTo("Fuzzy Search");
        assertThat(result3).isEqualTo("Phrase Search");
    }

    @Test
    @DisplayName("Should work with EnumSet")
    void testSearchMode_EnumSet() {
        // When
        java.util.EnumSet<SearchMode> allModes = java.util.EnumSet.allOf(SearchMode.class);
        java.util.EnumSet<SearchMode> textModes = java.util.EnumSet.of(SearchMode.FULL_TEXT, SearchMode.PHRASE);

        // Then
        assertThat(allModes).hasSize(3);
        assertThat(allModes).contains(SearchMode.FULL_TEXT, SearchMode.FUZZY, SearchMode.PHRASE);
        assertThat(textModes).hasSize(2);
        assertThat(textModes).contains(SearchMode.FULL_TEXT, SearchMode.PHRASE);
    }

    @Test
    @DisplayName("Should work with EnumMap")
    void testSearchMode_EnumMap() {
        // When
        java.util.EnumMap<SearchMode, String> modeDescriptions = new java.util.EnumMap<>(SearchMode.class);
        modeDescriptions.put(SearchMode.FULL_TEXT, "Full Text Search");
        modeDescriptions.put(SearchMode.FUZZY, "Fuzzy Search");
        modeDescriptions.put(SearchMode.PHRASE, "Phrase Search");

        // Then
        assertThat(modeDescriptions).hasSize(3);
        assertThat(modeDescriptions.get(SearchMode.FULL_TEXT)).isEqualTo("Full Text Search");
        assertThat(modeDescriptions.get(SearchMode.FUZZY)).isEqualTo("Fuzzy Search");
        assertThat(modeDescriptions.get(SearchMode.PHRASE)).isEqualTo("Phrase Search");
    }

    @Test
    @DisplayName("Should provide meaningful string representation")
    void testSearchMode_StringRepresentation() {
        // Then
        assertThat(SearchMode.FULL_TEXT.toString()).isEqualTo("FULL_TEXT");
        assertThat(SearchMode.FUZZY.toString()).isEqualTo("FUZZY");
        assertThat(SearchMode.PHRASE.toString()).isEqualTo("PHRASE");
    }

    // Helper method for switch statement test
    private String getModeDescription(SearchMode mode) {
        return switch (mode) {
            case FULL_TEXT -> "Full Text Search";
            case FUZZY -> "Fuzzy Search";
            case PHRASE -> "Phrase Search";
        };
    }
}
