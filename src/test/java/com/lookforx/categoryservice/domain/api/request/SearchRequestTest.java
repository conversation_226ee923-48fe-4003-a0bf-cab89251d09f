package com.lookforx.categoryservice.domain.api.request;

import com.lookforx.categoryservice.domain.SearchMode;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Comprehensive tests for SearchRequest.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("SearchRequest Tests")
class SearchRequestTest {

    @Test
    @DisplayName("Should create SearchRequest with no-args constructor")
    void testSearchRequest_NoArgsConstructor() {
        // When
        SearchRequest request = new SearchRequest();

        // Then
        assertThat(request).isNotNull();
        assertThat(request.getQuery()).isNull();
        assertThat(request.getMode()).isNull();
    }

    @Test
    @DisplayName("Should create SearchRequest with all-args constructor")
    void testSearchRequest_AllArgsConstructor() {
        // When
        SearchRequest request = new SearchRequest("electronics", SearchMode.FULL_TEXT);

        // Then
        assertThat(request.getQuery()).isEqualTo("electronics");
        assertThat(request.getMode()).isEqualTo(SearchMode.FULL_TEXT);
    }

    @Test
    @DisplayName("Should support setter and getter methods")
    void testSearchRequest_SettersGetters() {
        // Given
        SearchRequest request = new SearchRequest();

        // When
        request.setQuery("smartphones");
        request.setMode(SearchMode.FUZZY);

        // Then
        assertThat(request.getQuery()).isEqualTo("smartphones");
        assertThat(request.getMode()).isEqualTo(SearchMode.FUZZY);
    }

    @Test
    @DisplayName("Should handle null query")
    void testSearchRequest_NullQuery() {
        // When
        SearchRequest request = new SearchRequest(null, SearchMode.PHRASE);

        // Then
        assertThat(request.getQuery()).isNull();
        assertThat(request.getMode()).isEqualTo(SearchMode.PHRASE);
    }

    @Test
    @DisplayName("Should handle empty query")
    void testSearchRequest_EmptyQuery() {
        // When
        SearchRequest request = new SearchRequest("", SearchMode.FULL_TEXT);

        // Then
        assertThat(request.getQuery()).isEmpty();
        assertThat(request.getMode()).isEqualTo(SearchMode.FULL_TEXT);
    }

    @Test
    @DisplayName("Should handle null mode")
    void testSearchRequest_NullMode() {
        // When
        SearchRequest request = new SearchRequest("test query", null);

        // Then
        assertThat(request.getQuery()).isEqualTo("test query");
        assertThat(request.getMode()).isNull();
    }

    @Test
    @DisplayName("Should handle whitespace query")
    void testSearchRequest_WhitespaceQuery() {
        // When
        SearchRequest request = new SearchRequest("   ", SearchMode.FUZZY);

        // Then
        assertThat(request.getQuery()).isEqualTo("   ");
        assertThat(request.getMode()).isEqualTo(SearchMode.FUZZY);
    }

    @Test
    @DisplayName("Should handle long query")
    void testSearchRequest_LongQuery() {
        // Given
        String longQuery = "a".repeat(1000);

        // When
        SearchRequest request = new SearchRequest(longQuery, SearchMode.PHRASE);

        // Then
        assertThat(request.getQuery()).isEqualTo(longQuery);
        assertThat(request.getQuery()).hasSize(1000);
        assertThat(request.getMode()).isEqualTo(SearchMode.PHRASE);
    }

    @Test
    @DisplayName("Should handle special characters in query")
    void testSearchRequest_SpecialCharacters() {
        // When
        SearchRequest request = new SearchRequest("test@#$%^&*()query", SearchMode.FULL_TEXT);

        // Then
        assertThat(request.getQuery()).isEqualTo("test@#$%^&*()query");
        assertThat(request.getMode()).isEqualTo(SearchMode.FULL_TEXT);
    }

    @Test
    @DisplayName("Should handle Unicode characters in query")
    void testSearchRequest_UnicodeCharacters() {
        // When
        SearchRequest request = new SearchRequest("elektronik çözüm", SearchMode.FUZZY);

        // Then
        assertThat(request.getQuery()).isEqualTo("elektronik çözüm");
        assertThat(request.getMode()).isEqualTo(SearchMode.FUZZY);
    }

    @Test
    @DisplayName("Should test all SearchMode values")
    void testSearchRequest_AllSearchModes() {
        // When
        SearchRequest fullTextRequest = new SearchRequest("query", SearchMode.FULL_TEXT);
        SearchRequest fuzzyRequest = new SearchRequest("query", SearchMode.FUZZY);
        SearchRequest phraseRequest = new SearchRequest("query", SearchMode.PHRASE);

        // Then
        assertThat(fullTextRequest.getMode()).isEqualTo(SearchMode.FULL_TEXT);
        assertThat(fuzzyRequest.getMode()).isEqualTo(SearchMode.FUZZY);
        assertThat(phraseRequest.getMode()).isEqualTo(SearchMode.PHRASE);
    }

    @Test
    @DisplayName("Should implement equals and hashCode correctly")
    void testEquals_HashCode() {
        // Given
        SearchRequest request1 = new SearchRequest("electronics", SearchMode.FULL_TEXT);
        SearchRequest request2 = new SearchRequest("electronics", SearchMode.FULL_TEXT);
        SearchRequest request3 = new SearchRequest("smartphones", SearchMode.FULL_TEXT);
        SearchRequest request4 = new SearchRequest("electronics", SearchMode.FUZZY);

        // Then
        assertThat(request1).isEqualTo(request2);
        assertThat(request1.hashCode()).isEqualTo(request2.hashCode());
        assertThat(request1).isNotEqualTo(request3);
        assertThat(request1).isNotEqualTo(request4);
    }

    @Test
    @DisplayName("Should handle null in equals")
    void testEquals_Null() {
        // Given
        SearchRequest request = new SearchRequest("query", SearchMode.FULL_TEXT);

        // Then
        assertThat(request).isNotEqualTo(null);
    }

    @Test
    @DisplayName("Should handle different class in equals")
    void testEquals_DifferentClass() {
        // Given
        SearchRequest request = new SearchRequest("query", SearchMode.FULL_TEXT);

        // Then
        assertThat(request).isNotEqualTo("not a SearchRequest");
    }

    @Test
    @DisplayName("Should implement toString correctly")
    void testToString() {
        // Given
        SearchRequest request = new SearchRequest("electronics", SearchMode.FULL_TEXT);

        // When
        String toString = request.toString();

        // Then
        assertThat(toString).contains("SearchRequest");
        assertThat(toString).contains("query=electronics");
        assertThat(toString).contains("mode=FULL_TEXT");
    }

    @Test
    @DisplayName("Should handle equals with null fields")
    void testEquals_NullFields() {
        // Given
        SearchRequest request1 = new SearchRequest(null, null);
        SearchRequest request2 = new SearchRequest(null, null);
        SearchRequest request3 = new SearchRequest("query", null);
        SearchRequest request4 = new SearchRequest(null, SearchMode.FULL_TEXT);

        // Then
        assertThat(request1).isEqualTo(request2);
        assertThat(request1.hashCode()).isEqualTo(request2.hashCode());
        assertThat(request1).isNotEqualTo(request3);
        assertThat(request1).isNotEqualTo(request4);
    }

    @Test
    @DisplayName("Should handle case sensitive queries")
    void testSearchRequest_CaseSensitive() {
        // Given
        SearchRequest upperCaseRequest = new SearchRequest("ELECTRONICS", SearchMode.FULL_TEXT);
        SearchRequest lowerCaseRequest = new SearchRequest("electronics", SearchMode.FULL_TEXT);

        // Then
        assertThat(upperCaseRequest).isNotEqualTo(lowerCaseRequest);
        assertThat(upperCaseRequest.getQuery()).isEqualTo("ELECTRONICS");
        assertThat(lowerCaseRequest.getQuery()).isEqualTo("electronics");
    }

    @Test
    @DisplayName("Should handle numeric queries")
    void testSearchRequest_NumericQuery() {
        // When
        SearchRequest request = new SearchRequest("12345", SearchMode.PHRASE);

        // Then
        assertThat(request.getQuery()).isEqualTo("12345");
        assertThat(request.getMode()).isEqualTo(SearchMode.PHRASE);
    }

    @Test
    @DisplayName("Should handle mixed alphanumeric queries")
    void testSearchRequest_MixedAlphanumeric() {
        // When
        SearchRequest request = new SearchRequest("iPhone13Pro", SearchMode.FUZZY);

        // Then
        assertThat(request.getQuery()).isEqualTo("iPhone13Pro");
        assertThat(request.getMode()).isEqualTo(SearchMode.FUZZY);
    }
}
