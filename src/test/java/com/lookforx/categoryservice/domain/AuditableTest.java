package com.lookforx.categoryservice.domain;

import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Comprehensive tests for Auditable base class.
 * Following Effective Java principles for thorough testing.
 */
@DisplayName("Auditable Base Class Tests")
class AuditableTest {

    private TestAuditableEntity entity;

    @BeforeEach
    void setUp() {
        entity = TestAuditableEntity.builder()
                .name("Test Entity")
                .build();
    }

    @Test
    @DisplayName("Should be annotated with @MappedSuperclass")
    void testAuditable_HasMappedSuperclassAnnotation() {
        // Then
        assertThat(Auditable.class.isAnnotationPresent(MappedSuperclass.class)).isTrue();
    }

    @Test
    @DisplayName("Should be annotated with @EntityListeners")
    void testAuditable_HasEntityListenersAnnotation() {
        // Given
        EntityListeners annotation = Auditable.class.getAnnotation(EntityListeners.class);

        // Then
        assertThat(annotation).isNotNull();
        assertThat(annotation.value()).containsExactly(AuditingEntityListener.class);
    }

    @Test
    @DisplayName("Should have createdBy field with proper annotations")
    void testAuditable_CreatedByField() throws NoSuchFieldException {
        // Given
        java.lang.reflect.Field createdByField = Auditable.class.getDeclaredField("createdBy");

        // Then
        assertThat(createdByField.isAnnotationPresent(CreatedBy.class)).isTrue();
        assertThat(createdByField.isAnnotationPresent(Column.class)).isTrue();
        
        Column columnAnnotation = createdByField.getAnnotation(Column.class);
        assertThat(columnAnnotation.name()).isEqualTo("created_by");
        assertThat(columnAnnotation.updatable()).isFalse();
    }

    @Test
    @DisplayName("Should have createdAt field with proper annotations")
    void testAuditable_CreatedAtField() throws NoSuchFieldException {
        // Given
        java.lang.reflect.Field createdAtField = Auditable.class.getDeclaredField("createdAt");

        // Then
        assertThat(createdAtField.isAnnotationPresent(CreatedDate.class)).isTrue();
        assertThat(createdAtField.isAnnotationPresent(Column.class)).isTrue();
        
        Column columnAnnotation = createdAtField.getAnnotation(Column.class);
        assertThat(columnAnnotation.name()).isEqualTo("created_at");
        assertThat(columnAnnotation.updatable()).isFalse();
        assertThat(createdAtField.getType()).isEqualTo(LocalDateTime.class);
    }

    @Test
    @DisplayName("Should have lastModifiedBy field with proper annotations")
    void testAuditable_LastModifiedByField() throws NoSuchFieldException {
        // Given
        java.lang.reflect.Field lastModifiedByField = Auditable.class.getDeclaredField("lastModifiedBy");

        // Then
        assertThat(lastModifiedByField.isAnnotationPresent(LastModifiedBy.class)).isTrue();
        assertThat(lastModifiedByField.isAnnotationPresent(Column.class)).isTrue();
        
        Column columnAnnotation = lastModifiedByField.getAnnotation(Column.class);
        assertThat(columnAnnotation.name()).isEqualTo("updated_by");
        assertThat(columnAnnotation.updatable()).isTrue(); // Default value
    }

    @Test
    @DisplayName("Should have updatedAt field with proper annotations")
    void testAuditable_UpdatedAtField() throws NoSuchFieldException {
        // Given
        java.lang.reflect.Field updatedAtField = Auditable.class.getDeclaredField("updatedAt");

        // Then
        assertThat(updatedAtField.isAnnotationPresent(LastModifiedDate.class)).isTrue();
        assertThat(updatedAtField.isAnnotationPresent(Column.class)).isTrue();
        
        Column columnAnnotation = updatedAtField.getAnnotation(Column.class);
        assertThat(columnAnnotation.name()).isEqualTo("updated_at");
        assertThat(updatedAtField.getType()).isEqualTo(LocalDateTime.class);
    }

    @Test
    @DisplayName("Should support generic type parameter")
    void testAuditable_GenericTypeParameter() {
        // Given
        TestAuditableEntity stringEntity = new TestAuditableEntity();
        TestAuditableEntityWithLongUser longEntity = new TestAuditableEntityWithLongUser();

        // When
        stringEntity.setCreatedBy("testUser");
        longEntity.setCreatedBy(123L);

        // Then
        assertThat(stringEntity.getCreatedBy()).isEqualTo("testUser");
        assertThat(longEntity.getCreatedBy()).isEqualTo(123L);
    }

    @Test
    @DisplayName("Should set and get createdBy")
    void testAuditable_CreatedBy() {
        // When
        entity.setCreatedBy("testUser");

        // Then
        assertThat(entity.getCreatedBy()).isEqualTo("testUser");
    }

    @Test
    @DisplayName("Should set and get createdAt")
    void testAuditable_CreatedAt() {
        // Given
        LocalDateTime now = LocalDateTime.now();

        // When
        entity.setCreatedAt(now);

        // Then
        assertThat(entity.getCreatedAt()).isEqualTo(now);
    }

    @Test
    @DisplayName("Should set and get lastModifiedBy")
    void testAuditable_LastModifiedBy() {
        // When
        entity.setLastModifiedBy("modifiedUser");

        // Then
        assertThat(entity.getLastModifiedBy()).isEqualTo("modifiedUser");
    }

    @Test
    @DisplayName("Should set and get updatedAt")
    void testAuditable_UpdatedAt() {
        // Given
        LocalDateTime now = LocalDateTime.now();

        // When
        entity.setUpdatedAt(now);

        // Then
        assertThat(entity.getUpdatedAt()).isEqualTo(now);
    }

    @Test
    @DisplayName("Should handle null values")
    void testAuditable_NullValues() {
        // When
        entity.setCreatedBy(null);
        entity.setCreatedAt(null);
        entity.setLastModifiedBy(null);
        entity.setUpdatedAt(null);

        // Then
        assertThat(entity.getCreatedBy()).isNull();
        assertThat(entity.getCreatedAt()).isNull();
        assertThat(entity.getLastModifiedBy()).isNull();
        assertThat(entity.getUpdatedAt()).isNull();
    }

    @Test
    @DisplayName("Should support builder pattern")
    void testAuditable_BuilderPattern() {
        // Given
        LocalDateTime now = LocalDateTime.now();

        // When
        TestAuditableEntity builtEntity = TestAuditableEntity.builder()
                .name("Built Entity")
                .createdBy("builder")
                .createdAt(now)
                .lastModifiedBy("modifier")
                .updatedAt(now.plusMinutes(5))
                .build();

        // Then
        assertThat(builtEntity.getName()).isEqualTo("Built Entity");
        assertThat(builtEntity.getCreatedBy()).isEqualTo("builder");
        assertThat(builtEntity.getCreatedAt()).isEqualTo(now);
        assertThat(builtEntity.getLastModifiedBy()).isEqualTo("modifier");
        assertThat(builtEntity.getUpdatedAt()).isEqualTo(now.plusMinutes(5));
    }

    @Test
    @DisplayName("Should be abstract class")
    void testAuditable_IsAbstract() {
        // Then
        assertThat(java.lang.reflect.Modifier.isAbstract(Auditable.class.getModifiers())).isTrue();
    }

    @Test
    @DisplayName("Should have protected fields")
    void testAuditable_ProtectedFields() throws NoSuchFieldException {
        // Given
        java.lang.reflect.Field createdByField = Auditable.class.getDeclaredField("createdBy");
        java.lang.reflect.Field createdAtField = Auditable.class.getDeclaredField("createdAt");
        java.lang.reflect.Field lastModifiedByField = Auditable.class.getDeclaredField("lastModifiedBy");
        java.lang.reflect.Field updatedAtField = Auditable.class.getDeclaredField("updatedAt");

        // Then
        assertThat(java.lang.reflect.Modifier.isProtected(createdByField.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isProtected(createdAtField.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isProtected(lastModifiedByField.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isProtected(updatedAtField.getModifiers())).isTrue();
    }

    // Test helper classes
    private static class TestAuditableEntity extends Auditable<String> {
        private String name;

        public TestAuditableEntity() {
            super();
        }

        public TestAuditableEntity(String name) {
            this.name = name;
        }

        public static TestAuditableEntityBuilder builder() {
            return new TestAuditableEntityBuilder();
        }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public static class TestAuditableEntityBuilder {
            private String name;
            private String createdBy;
            private LocalDateTime createdAt;
            private String lastModifiedBy;
            private LocalDateTime updatedAt;

            public TestAuditableEntityBuilder name(String name) {
                this.name = name;
                return this;
            }

            public TestAuditableEntityBuilder createdBy(String createdBy) {
                this.createdBy = createdBy;
                return this;
            }

            public TestAuditableEntityBuilder createdAt(LocalDateTime createdAt) {
                this.createdAt = createdAt;
                return this;
            }

            public TestAuditableEntityBuilder lastModifiedBy(String lastModifiedBy) {
                this.lastModifiedBy = lastModifiedBy;
                return this;
            }

            public TestAuditableEntityBuilder updatedAt(LocalDateTime updatedAt) {
                this.updatedAt = updatedAt;
                return this;
            }

            public TestAuditableEntity build() {
                TestAuditableEntity entity = new TestAuditableEntity(name);
                entity.setCreatedBy(createdBy);
                entity.setCreatedAt(createdAt);
                entity.setLastModifiedBy(lastModifiedBy);
                entity.setUpdatedAt(updatedAt);
                return entity;
            }
        }
    }

    private static class TestAuditableEntityWithLongUser extends Auditable<Long> {
        // Test entity with Long user type
    }
}
