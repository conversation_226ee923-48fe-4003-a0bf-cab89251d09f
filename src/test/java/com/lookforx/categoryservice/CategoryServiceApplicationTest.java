package com.lookforx.categoryservice;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Comprehensive tests for CategoryServiceApplication.
 * Following Effective Java principles for thorough testing.
 *
 * Note: These are unit tests for the application class structure and annotations.
 * Integration tests for context loading should be separate.
 */
@DisplayName("CategoryServiceApplication Tests")
class CategoryServiceApplicationTest {

    @Test
    @DisplayName("Should be annotated with @SpringBootApplication")
    void testSpringBootApplication_Annotation() {
        // Then
        assertThat(CategoryServiceApplication.class.isAnnotationPresent(SpringBootApplication.class)).isTrue();
        
        SpringBootApplication annotation = CategoryServiceApplication.class.getAnnotation(SpringBootApplication.class);
        assertThat(annotation.scanBasePackages()).containsExactlyInAnyOrder(
                "com.lookforx.categoryservice",
                "com.lookforx.common"
        );
    }

    @Test
    @DisplayName("Should be annotated with @EnableDiscoveryClient")
    void testEnableDiscoveryClient_Annotation() {
        // Then
        assertThat(CategoryServiceApplication.class.isAnnotationPresent(EnableDiscoveryClient.class)).isTrue();
    }

    @Test
    @DisplayName("Should be annotated with @EnableFeignClients")
    void testEnableFeignClients_Annotation() {
        // Then
        assertThat(CategoryServiceApplication.class.isAnnotationPresent(EnableFeignClients.class)).isTrue();
        
        EnableFeignClients annotation = CategoryServiceApplication.class.getAnnotation(EnableFeignClients.class);
        assertThat(annotation.basePackages()).containsExactlyInAnyOrder(
                "com.lookforx.common.client",
                "com.lookforx.categoryservice.web"
        );
    }

    @Test
    @DisplayName("Should have main method with correct signature")
    void testMainMethod_Signature() throws NoSuchMethodException {
        // Given
        java.lang.reflect.Method mainMethod = CategoryServiceApplication.class.getMethod("main", String[].class);

        // Then
        assertThat(mainMethod).isNotNull();
        assertThat(mainMethod.getReturnType()).isEqualTo(void.class);
        assertThat(java.lang.reflect.Modifier.isStatic(mainMethod.getModifiers())).isTrue();
        assertThat(java.lang.reflect.Modifier.isPublic(mainMethod.getModifiers())).isTrue();
    }

    @Test
    @DisplayName("Should be a public class")
    void testClass_IsPublic() {
        // Then
        assertThat(java.lang.reflect.Modifier.isPublic(CategoryServiceApplication.class.getModifiers())).isTrue();
        assertThat(CategoryServiceApplication.class.isInterface()).isFalse();
        assertThat(CategoryServiceApplication.class.isEnum()).isFalse();
        assertThat(CategoryServiceApplication.class.isAnnotation()).isFalse();
    }

    @Test
    @DisplayName("Should have correct package name")
    void testClass_PackageName() {
        // Then
        assertThat(CategoryServiceApplication.class.getPackage().getName())
                .isEqualTo("com.lookforx.categoryservice");
    }

    @Test
    @DisplayName("Should have correct class name")
    void testClass_Name() {
        // Then
        assertThat(CategoryServiceApplication.class.getSimpleName())
                .isEqualTo("CategoryServiceApplication");
    }

    @Test
    @DisplayName("Should have no declared fields")
    void testClass_NoFields() {
        // Then
        assertThat(CategoryServiceApplication.class.getDeclaredFields()).isEmpty();
    }

    @Test
    @DisplayName("Should have only main method and default constructor")
    void testClass_Methods() {
        // Given
        java.lang.reflect.Method[] declaredMethods = CategoryServiceApplication.class.getDeclaredMethods();

        // Filter out JaCoCo generated methods
        java.lang.reflect.Method[] businessMethods = java.util.Arrays.stream(declaredMethods)
                .filter(method -> !method.getName().contains("jacoco"))
                .toArray(java.lang.reflect.Method[]::new);

        // Then
        assertThat(businessMethods).hasSize(1);
        assertThat(businessMethods[0].getName()).isEqualTo("main");
    }

    @Test
    @DisplayName("Should have default constructor")
    void testClass_DefaultConstructor() {
        // Given
        java.lang.reflect.Constructor<?>[] constructors = CategoryServiceApplication.class.getDeclaredConstructors();

        // Then
        assertThat(constructors).hasSize(1);
        assertThat(constructors[0].getParameterCount()).isEqualTo(0);
        assertThat(java.lang.reflect.Modifier.isPublic(constructors[0].getModifiers())).isTrue();
    }

    @Test
    @DisplayName("Should be instantiable")
    void testClass_Instantiable() {
        // When & Then
        try {
            CategoryServiceApplication instance = CategoryServiceApplication.class.getDeclaredConstructor().newInstance();
            assertThat(instance).isNotNull();
        } catch (Exception e) {
            throw new RuntimeException("Failed to instantiate CategoryServiceApplication", e);
        }
    }

    @Test
    @DisplayName("Should extend Object class")
    void testClass_Inheritance() {
        // Then
        assertThat(CategoryServiceApplication.class.getSuperclass()).isEqualTo(Object.class);
        assertThat(CategoryServiceApplication.class.getInterfaces()).isEmpty();
    }

    @Test
    @DisplayName("Should have correct annotation configuration for microservice")
    void testMicroservice_Configuration() {
        // Given
        SpringBootApplication springBootApp = CategoryServiceApplication.class.getAnnotation(SpringBootApplication.class);
        EnableDiscoveryClient discoveryClient = CategoryServiceApplication.class.getAnnotation(EnableDiscoveryClient.class);
        EnableFeignClients feignClients = CategoryServiceApplication.class.getAnnotation(EnableFeignClients.class);

        // Then - Verify microservice configuration
        assertThat(springBootApp).isNotNull();
        assertThat(discoveryClient).isNotNull();
        assertThat(feignClients).isNotNull();

        // Verify scan base packages include common package for shared components
        assertThat(springBootApp.scanBasePackages()).contains("com.lookforx.common");
        
        // Verify Feign clients are configured for both common and service-specific packages
        assertThat(feignClients.basePackages()).contains("com.lookforx.common.client");
        assertThat(feignClients.basePackages()).contains("com.lookforx.categoryservice.web");
    }

    @Test
    @DisplayName("Should follow Spring Boot application naming convention")
    void testNamingConvention() {
        // Then
        assertThat(CategoryServiceApplication.class.getSimpleName())
                .endsWith("Application")
                .startsWith("CategoryService");
    }

    @Test
    @DisplayName("Should be in correct package structure")
    void testPackageStructure() {
        // Then
        String packageName = CategoryServiceApplication.class.getPackage().getName();
        assertThat(packageName)
                .startsWith("com.lookforx")
                .contains("categoryservice")
                .doesNotContain("test");
    }

    @Test
    @DisplayName("Should have proper annotation order")
    void testAnnotationOrder() {
        // Given
        java.lang.annotation.Annotation[] annotations = CategoryServiceApplication.class.getAnnotations();

        // Then - Verify annotations are present (order may vary)
        assertThat(annotations).hasSize(3);
        
        boolean hasSpringBootApp = false;
        boolean hasDiscoveryClient = false;
        boolean hasFeignClients = false;
        
        for (java.lang.annotation.Annotation annotation : annotations) {
            if (annotation instanceof SpringBootApplication) hasSpringBootApp = true;
            if (annotation instanceof EnableDiscoveryClient) hasDiscoveryClient = true;
            if (annotation instanceof EnableFeignClients) hasFeignClients = true;
        }
        
        assertThat(hasSpringBootApp).isTrue();
        assertThat(hasDiscoveryClient).isTrue();
        assertThat(hasFeignClients).isTrue();
    }

    @Test
    @DisplayName("Should be thread-safe for main method")
    void testMainMethod_ThreadSafety() {
        // Given
        java.lang.reflect.Method mainMethod;
        try {
            mainMethod = CategoryServiceApplication.class.getMethod("main", String[].class);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }

        // Then - Static methods are inherently thread-safe for the method itself
        assertThat(java.lang.reflect.Modifier.isStatic(mainMethod.getModifiers())).isTrue();
        assertThat(mainMethod.getParameterCount()).isEqualTo(1);
        assertThat(mainMethod.getParameterTypes()[0]).isEqualTo(String[].class);
    }
}
