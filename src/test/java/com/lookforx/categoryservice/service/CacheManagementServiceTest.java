package com.lookforx.categoryservice.service;

import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.categoryservice.service.impl.CacheManagementServiceImpl;
import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * Unit tests for CacheManagementServiceImpl.
 * 
 * Following best practices:
 * - Mock-based testing approach
 * - Comprehensive test coverage
 * - Clean test structure
 */
@ExtendWith(MockitoExtension.class)
class CacheManagementServiceTest {

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private CacheManager cacheManager;

    @Mock
    private Cache simpleCategoriesCache;

    @Mock
    private Cache categoriesByParentTypeCache;

    @InjectMocks
    private CacheManagementServiceImpl cacheManagementService;

    @BeforeEach
    void setUp() {
        // Setup cache manager mocks with lenient stubbing
        lenient().when(cacheManager.getCache("simpleCategories")).thenReturn(simpleCategoriesCache);
        lenient().when(cacheManager.getCache("categoriesByParentAndType")).thenReturn(categoriesByParentTypeCache);
        lenient().when(cacheManager.getCacheNames()).thenReturn(Arrays.asList("simpleCategories", "categoriesByParentAndType"));
    }

    @Test
    void testInvalidateAllCategoryCache_Success() {
        // When
        cacheManagementService.invalidateAllCategoryCache();

        // Then
        verify(redisTemplate).delete("all_categories");
        verify(redisTemplate).delete("all_categories_flat");
        verify(redisTemplate).delete("root_categories_PRODUCT");
        verify(redisTemplate).delete("root_categories_SERVICE");
        verify(simpleCategoriesCache).clear();
        verify(categoriesByParentTypeCache).clear();
    }

    @Test
    void testInvalidateAllCategoryCache_WithException() {
        // Given
        doThrow(new RuntimeException("Redis error")).when(redisTemplate).delete(anyString());

        // When & Then
        assertThrows(RuntimeException.class, () -> 
            cacheManagementService.invalidateAllCategoryCache()
        );
    }

    @Test
    void testInvalidateCacheByType_Success() {
        // Given
        CategoryType type = CategoryType.PRODUCT;

        // When
        cacheManagementService.invalidateCacheByType(type);

        // Then
        verify(redisTemplate).delete("root_categories_PRODUCT");
        verify(redisTemplate).delete("all_categories");
        verify(redisTemplate).delete("all_categories_flat");
        verify(simpleCategoriesCache).clear();
        verify(categoriesByParentTypeCache).clear();
    }

    @Test
    void testInvalidateCacheByType_WithException() {
        // Given
        CategoryType type = CategoryType.SERVICE;
        doThrow(new RuntimeException("Redis error")).when(redisTemplate).delete(anyString());

        // When & Then
        assertThrows(RuntimeException.class, () -> 
            cacheManagementService.invalidateCacheByType(type)
        );
    }

    @Test
    void testInvalidateSimpleCategoriesCache_Success() {
        // Given
        LanguageCode language = LanguageCode.EN;
        CategoryType type = CategoryType.PRODUCT;
        String search = "electronics";

        // When
        cacheManagementService.invalidateSimpleCategoriesCache(language, type, search);

        // Then
        verify(simpleCategoriesCache).evict("EN_PRODUCT_electronics");
    }

    @Test
    void testInvalidateSimpleCategoriesCache_WithNullValues() {
        // Given
        LanguageCode language = LanguageCode.TR;

        // When
        cacheManagementService.invalidateSimpleCategoriesCache(language, null, null);

        // Then
        verify(simpleCategoriesCache).evict("TR_ALL_");
    }

    @Test
    void testInvalidateSimpleCategoriesCache_WithException() {
        // Given
        when(cacheManager.getCache("simpleCategories")).thenReturn(null);

        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> 
            cacheManagementService.invalidateSimpleCategoriesCache(LanguageCode.EN, CategoryType.PRODUCT, "test")
        );
    }

    @Test
    void testClearAllCache_Success() {
        // Given
        Set<String> keys = new HashSet<>(Arrays.asList("key1", "key2", "key3"));
        when(redisTemplate.keys("*")).thenReturn(keys);

        // When
        cacheManagementService.clearAllCache();

        // Then
        verify(redisTemplate).keys("*");
        verify(redisTemplate).delete(keys);
        verify(simpleCategoriesCache).clear();
        verify(categoriesByParentTypeCache).clear();
    }

    @Test
    void testClearAllCache_WithEmptyKeys() {
        // Given
        when(redisTemplate.keys("*")).thenReturn(new HashSet<>());

        // When
        cacheManagementService.clearAllCache();

        // Then
        verify(redisTemplate).keys("*");
        verify(redisTemplate, never()).delete(any(Set.class));
        verify(simpleCategoriesCache).clear();
        verify(categoriesByParentTypeCache).clear();
    }

    @Test
    void testClearAllCache_WithNullKeys() {
        // Given
        when(redisTemplate.keys("*")).thenReturn(null);

        // When
        cacheManagementService.clearAllCache();

        // Then
        verify(redisTemplate).keys("*");
        verify(redisTemplate, never()).delete(any(Set.class));
        verify(simpleCategoriesCache).clear();
        verify(categoriesByParentTypeCache).clear();
    }

    @Test
    void testClearAllCache_WithException() {
        // Given
        doThrow(new RuntimeException("Redis error")).when(redisTemplate).keys(anyString());

        // When & Then
        assertThrows(RuntimeException.class, () -> 
            cacheManagementService.clearAllCache()
        );
    }

    @Test
    void testWarmUpCache_Success() {
        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> cacheManagementService.warmUpCache());
    }

    @Test
    void testGetCacheStatistics_Success() {
        // Given
        Set<String> keys = new HashSet<>(Arrays.asList("key1", "key2"));
        when(redisTemplate.keys("*")).thenReturn(keys);

        // When
        String result = cacheManagementService.getCacheStatistics();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("Total Redis keys: 2"));
        assertTrue(result.contains("simpleCategories"));
        assertTrue(result.contains("categoriesByParentAndType"));
    }

    @Test
    void testGetCacheStatistics_WithException() {
        // Given
        doThrow(new RuntimeException("Redis error")).when(redisTemplate).keys(anyString());

        // When
        String result = cacheManagementService.getCacheStatistics();

        // Then
        assertEquals("Cache statistics unavailable", result);
    }
}
