package com.lookforx.categoryservice.service.strategy.impl;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for DefaultCategoryNameResolver.
 * 
 * Following Clean Code and Design Patterns testing principles:
 * - Strategy Pattern testing
 * - Comprehensive test coverage
 * - Meaningful test names
 */
@ExtendWith(MockitoExtension.class)
class DefaultCategoryNameResolverTest {

    @InjectMocks
    private DefaultCategoryNameResolver nameResolver;

    private Category testCategory;
    private Map<LanguageCode, String> translations;

    @BeforeEach
    void setUp() {
        translations = new HashMap<>();
        translations.put(LanguageCode.EN, "Electronics");
        translations.put(LanguageCode.TR, "Elektronik");
        translations.put(LanguageCode.DE, "Elektronik");

        testCategory = Category.builder()
                .translations(translations)
                .build();

        // Set ID using reflection
        try {
            java.lang.reflect.Field idField = testCategory.getClass().getSuperclass().getDeclaredField("id");
            idField.setAccessible(true);
            idField.set(testCategory, 1L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testResolveCategoryName_WithRequestedLanguageAvailable_ShouldReturnRequestedLanguage() {
        // When
        String result = nameResolver.resolveCategoryName(testCategory, LanguageCode.TR);

        // Then
        assertEquals("Elektronik", result);
    }

    @Test
    void testResolveCategoryName_WithRequestedLanguageUnavailable_ShouldFallbackToEnglish() {
        // Given
        LanguageCode unavailableLanguage = LanguageCode.FR;

        // When
        String result = nameResolver.resolveCategoryName(testCategory, unavailableLanguage);

        // Then
        assertEquals("Electronics", result);
    }

    @Test
    void testResolveCategoryName_WithNoEnglishAvailable_ShouldReturnFirstAvailable() {
        // Given
        Map<LanguageCode, String> translationsWithoutEnglish = new HashMap<>();
        translationsWithoutEnglish.put(LanguageCode.TR, "Elektronik");
        translationsWithoutEnglish.put(LanguageCode.DE, "Elektronik");
        
        Category categoryWithoutEnglish = Category.builder()
                .translations(translationsWithoutEnglish)
                .build();

        // When
        String result = nameResolver.resolveCategoryName(categoryWithoutEnglish, LanguageCode.FR);

        // Then
        assertTrue(result.equals("Elektronik")); // Should return one of the available translations
    }

    @Test
    void testResolveCategoryName_WithEmptyTranslations_ShouldReturnFallback() {
        // Given
        Category categoryWithEmptyTranslations = Category.builder()
                .translations(new HashMap<>())
                .build();

        // When
        String result = nameResolver.resolveCategoryName(categoryWithEmptyTranslations, LanguageCode.EN);

        // Then
        assertEquals("Unknown Category", result);
    }

    @Test
    void testResolveCategoryName_WithNullTranslations_ShouldReturnFallback() {
        // Given
        Category categoryWithNullTranslations = Category.builder()
                .translations(null)
                .build();

        // When
        String result = nameResolver.resolveCategoryName(categoryWithNullTranslations, LanguageCode.EN);

        // Then
        assertEquals("Unknown Category", result);
    }

    @Test
    void testResolveCategoryName_WithNullCategory_ShouldReturnFallback() {
        // When
        String result = nameResolver.resolveCategoryName(null, LanguageCode.EN);

        // Then
        assertEquals("Unknown Category", result);
    }

    @Test
    void testResolveCategoryName_WithEmptyStringTranslations_ShouldFallbackProperly() {
        // Given
        Map<LanguageCode, String> translationsWithEmptyStrings = new HashMap<>();
        translationsWithEmptyStrings.put(LanguageCode.TR, "");
        translationsWithEmptyStrings.put(LanguageCode.EN, "   "); // Whitespace only
        translationsWithEmptyStrings.put(LanguageCode.DE, "Elektronik");
        
        Category categoryWithEmptyStrings = Category.builder()
                .translations(translationsWithEmptyStrings)
                .build();

        // When
        String result = nameResolver.resolveCategoryName(categoryWithEmptyStrings, LanguageCode.TR);

        // Then
        assertEquals("Elektronik", result); // Should fallback to first valid translation
    }

    @Test
    void testCanResolve_WithValidCategory_ShouldReturnTrue() {
        // When
        boolean result = nameResolver.canResolve(testCategory, LanguageCode.EN);

        // Then
        assertTrue(result);
    }

    @Test
    void testCanResolve_WithNullCategory_ShouldReturnFalse() {
        // When
        boolean result = nameResolver.canResolve(null, LanguageCode.EN);

        // Then
        assertFalse(result);
    }

    @Test
    void testCanResolve_WithNullTranslations_ShouldReturnFalse() {
        // Given
        Category categoryWithNullTranslations = Category.builder()
                .translations(null)
                .build();

        // When
        boolean result = nameResolver.canResolve(categoryWithNullTranslations, LanguageCode.EN);

        // Then
        assertFalse(result);
    }

    @Test
    void testCanResolve_WithEmptyTranslations_ShouldReturnFalse() {
        // Given
        Category categoryWithEmptyTranslations = Category.builder()
                .translations(new HashMap<>())
                .build();

        // When
        boolean result = nameResolver.canResolve(categoryWithEmptyTranslations, LanguageCode.EN);

        // Then
        assertFalse(result);
    }

    @Test
    void testResolveCategoryName_WithAllTranslationsEmpty_ShouldReturnFallback() {
        // Given
        Map<LanguageCode, String> emptyTranslations = new HashMap<>();
        emptyTranslations.put(LanguageCode.EN, "");
        emptyTranslations.put(LanguageCode.TR, "   ");
        emptyTranslations.put(LanguageCode.DE, null);
        
        Category categoryWithAllEmptyTranslations = Category.builder()
                .translations(emptyTranslations)
                .build();

        // When
        String result = nameResolver.resolveCategoryName(categoryWithAllEmptyTranslations, LanguageCode.EN);

        // Then
        assertEquals("Unknown Category", result);
    }
}
