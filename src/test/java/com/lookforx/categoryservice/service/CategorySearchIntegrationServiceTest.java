package com.lookforx.categoryservice.service;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.categoryservice.domain.dto.CategoryDTO;
import com.lookforx.categoryservice.domain.mapper.CategoryMapper;
import com.lookforx.categoryservice.repository.CategoryRepository;
import com.lookforx.categoryservice.service.impl.CategorySearchIntegrationServiceImpl;
import com.lookforx.categoryservice.web.client.SearchCategoryDocument;
import com.lookforx.categoryservice.web.client.SearchClient;
import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CategorySearchIntegrationServiceImpl.
 * 
 * Following best practices:
 * - Mock-based testing approach
 * - Comprehensive test coverage
 * - Clean test structure
 */
@ExtendWith(MockitoExtension.class)
class CategorySearchIntegrationServiceTest {

    @Mock
    private SearchClient searchClient;

    @Mock
    private CategoryMapper categoryMapper;

    @Mock
    private CategoryRepository categoryRepository;

    @InjectMocks
    private CategorySearchIntegrationServiceImpl searchIntegrationService;

    private Category testCategory;
    private CategoryDTO testCategoryDTO;
    private Map<LanguageCode, String> translations;

    @BeforeEach
    void setUp() {
        translations = new HashMap<>();
        translations.put(LanguageCode.EN, "Electronics");
        translations.put(LanguageCode.TR, "Elektronik");

        testCategory = Category.builder()
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();
        
        // Set ID using reflection
        try {
            java.lang.reflect.Field idField = testCategory.getClass().getSuperclass().getDeclaredField("id");
            idField.setAccessible(true);
            idField.set(testCategory, 1L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        testCategoryDTO = CategoryDTO.builder()
                .id(1L)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();
    }

    @Test
    void testIndexCategory_WithCategory_Success() {
        // When
        searchIntegrationService.indexCategory(testCategory);

        // Then
        verify(searchClient).indexCategory(any(SearchCategoryDocument.class));
    }

    @Test
    void testIndexCategory_WithNullCategory_ShouldNotThrow() {
        // When & Then
        assertDoesNotThrow(() -> searchIntegrationService.indexCategory((Category) null));
        verify(searchClient, never()).indexCategory(any());
    }

    @Test
    void testIndexCategory_WithCategoryDTO_Success() {
        // When
        searchIntegrationService.indexCategory(testCategoryDTO);

        // Then
        verify(searchClient).indexCategory(any(SearchCategoryDocument.class));
    }

    @Test
    void testIndexCategory_WithNullCategoryDTO_ShouldNotThrow() {
        // When & Then
        assertDoesNotThrow(() -> searchIntegrationService.indexCategory((CategoryDTO) null));
        verify(searchClient, never()).indexCategory(any());
    }

    @Test
    void testIndexCategory_WithException_ShouldUseCircuitBreaker() {
        // Given
        doThrow(new RuntimeException("Search service error")).when(searchClient).indexCategory(any());

        // When & Then - Should throw exception (resilience4j will handle it)
        assertThrows(RuntimeException.class, () ->
            searchIntegrationService.indexCategory(testCategory)
        );
    }

    @Test
    void testUpdateCategoryIndex_Success() {
        // When
        searchIntegrationService.updateCategoryIndex(testCategory);

        // Then
        verify(searchClient).updateCategory(eq("1"), any(SearchCategoryDocument.class));
    }

    @Test
    void testUpdateCategoryIndex_WithNullCategory_ShouldNotThrow() {
        // When & Then
        assertDoesNotThrow(() -> searchIntegrationService.updateCategoryIndex(null));
        verify(searchClient, never()).updateCategory(anyString(), any());
    }

    @Test
    void testUpdateCategoryIndex_WithException_ShouldUseCircuitBreaker() {
        // Given
        doThrow(new RuntimeException("Search service error")).when(searchClient).updateCategory(anyString(), any());

        // When & Then - Should throw exception (resilience4j will handle it)
        assertThrows(RuntimeException.class, () ->
            searchIntegrationService.updateCategoryIndex(testCategory)
        );
    }

    @Test
    void testRemoveCategoryFromIndex_Success() {
        // When
        searchIntegrationService.removeCategoryFromIndex(1L);

        // Then
        verify(searchClient).deleteCategory("1");
    }

    @Test
    void testRemoveCategoryFromIndex_WithNullId_ShouldNotThrow() {
        // When & Then
        assertDoesNotThrow(() -> searchIntegrationService.removeCategoryFromIndex(null));
        verify(searchClient, never()).deleteCategory(anyString());
    }

    @Test
    void testRemoveCategoryFromIndex_WithException_ShouldNotThrow() {
        // Given
        doThrow(new RuntimeException("Search service error")).when(searchClient).deleteCategory(anyString());

        // When & Then
        assertDoesNotThrow(() -> searchIntegrationService.removeCategoryFromIndex(1L));
    }

    @Test
    void testBulkIndexCategories_Success() {
        // Given
        List<Category> categories = Arrays.asList(testCategory);

        // When
        searchIntegrationService.bulkIndexCategories(categories);

        // Then
        verify(searchClient).bulkIndexCategories(any());
    }

    @Test
    void testBulkIndexCategories_WithEmptyList_ShouldNotCallClient() {
        // Given
        List<Category> categories = new ArrayList<>();

        // When
        searchIntegrationService.bulkIndexCategories(categories);

        // Then
        verify(searchClient, never()).bulkIndexCategories(any());
    }

    @Test
    void testBulkIndexCategories_WithNullList_ShouldNotCallClient() {
        // When
        searchIntegrationService.bulkIndexCategories(null);

        // Then
        verify(searchClient, never()).bulkIndexCategories(any());
    }

    @Test
    void testBulkIndexCategories_WithException_ShouldThrow() {
        // Given
        List<Category> categories = Arrays.asList(testCategory);
        doThrow(new RuntimeException("Search service error")).when(searchClient).bulkIndexCategories(any());

        // When & Then
        assertThrows(RuntimeException.class, () -> 
            searchIntegrationService.bulkIndexCategories(categories)
        );
    }

    @Test
    void testSyncAllCategoriesToSearch_Success() {
        // Given
        List<Category> categories = Arrays.asList(testCategory);
        when(categoryRepository.findAll()).thenReturn(categories);

        // When
        searchIntegrationService.syncAllCategoriesToSearch();

        // Then
        verify(categoryRepository).findAll();
        verify(searchClient).bulkIndexCategories(any());
    }

    @Test
    void testSyncAllCategoriesToSearch_WithEmptyCategories() {
        // Given
        when(categoryRepository.findAll()).thenReturn(new ArrayList<>());

        // When
        searchIntegrationService.syncAllCategoriesToSearch();

        // Then
        verify(categoryRepository).findAll();
        verify(searchClient, never()).bulkIndexCategories(any());
    }

    @Test
    void testSyncAllCategoriesToSearch_WithException_ShouldThrow() {
        // Given
        doThrow(new RuntimeException("Database error")).when(categoryRepository).findAll();

        // When & Then
        assertThrows(RuntimeException.class, () -> 
            searchIntegrationService.syncAllCategoriesToSearch()
        );
    }

    @Test
    void testClearSearchIndex_Success() {
        // When
        searchIntegrationService.clearSearchIndex();

        // Then
        verify(searchClient).clearAllCategories();
    }

    @Test
    void testClearSearchIndex_WithException_ShouldThrow() {
        // Given
        doThrow(new RuntimeException("Search service error")).when(searchClient).clearAllCategories();

        // When & Then
        assertThrows(RuntimeException.class, () -> 
            searchIntegrationService.clearSearchIndex()
        );
    }

    @Test
    void testIsSearchServiceHealthy_Success() {
        // When
        boolean result = searchIntegrationService.isSearchServiceHealthy();

        // Then
        assertTrue(result);
    }

    @Test
    void testIsSearchServiceHealthy_WithException_ShouldReturnFalse() {
        // This test verifies the placeholder implementation
        // When actual health check is implemented, this test should be updated
        
        // When
        boolean result = searchIntegrationService.isSearchServiceHealthy();

        // Then
        assertTrue(result); // Current placeholder returns true
    }

    @Test
    void testGetSearchServiceStatistics_Success() {
        // When
        String result = searchIntegrationService.getSearchServiceStatistics();

        // Then
        assertNotNull(result);
        assertTrue(result.contains("Search Service Status"));
    }
}
