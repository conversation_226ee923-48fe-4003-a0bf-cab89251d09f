package com.lookforx.categoryservice.service.factory;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.service.strategy.CategoryNameResolver;
import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CategoryNameResolverFactory.
 * 
 * Following Clean Code and Design Patterns testing principles:
 * - Factory Pattern testing
 * - Chain of Responsibility testing
 * - Comprehensive test coverage
 * - Mock-based testing approach
 */
@ExtendWith(MockitoExtension.class)
class CategoryNameResolverFactoryTest {

    @Mock
    private CategoryNameResolver firstResolver;

    @Mock
    private CategoryNameResolver secondResolver;

    @InjectMocks
    private CategoryNameResolverFactory resolverFactory;

    private Category testCategory;

    @BeforeEach
    void setUp() {
        testCategory = Category.builder()
                .translations(new HashMap<>())
                .build();

        // Set ID using reflection
        try {
            java.lang.reflect.Field idField = testCategory.getClass().getSuperclass().getDeclaredField("id");
            idField.setAccessible(true);
            idField.set(testCategory, 1L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // Setup factory with mock resolvers
        List<CategoryNameResolver> resolvers = Arrays.asList(firstResolver, secondResolver);
        try {
            java.lang.reflect.Field resolversField = resolverFactory.getClass().getDeclaredField("availableResolvers");
            resolversField.setAccessible(true);
            resolversField.set(resolverFactory, resolvers);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    void testCreateResolverFor_WithFirstResolverCanHandle_ShouldReturnFirstResolver() {
        // Given
        when(firstResolver.canResolve(testCategory, LanguageCode.EN)).thenReturn(true);

        // When
        CategoryNameResolver result = resolverFactory.createResolverFor(testCategory, LanguageCode.EN);

        // Then
        assertEquals(firstResolver, result);
        verify(firstResolver).canResolve(testCategory, LanguageCode.EN);
        verify(secondResolver, never()).canResolve(any(), any());
    }

    @Test
    void testCreateResolverFor_WithFirstResolverCannotHandle_ShouldReturnSecondResolver() {
        // Given
        when(firstResolver.canResolve(testCategory, LanguageCode.EN)).thenReturn(false);
        when(secondResolver.canResolve(testCategory, LanguageCode.EN)).thenReturn(true);

        // When
        CategoryNameResolver result = resolverFactory.createResolverFor(testCategory, LanguageCode.EN);

        // Then
        assertEquals(secondResolver, result);
        verify(firstResolver).canResolve(testCategory, LanguageCode.EN);
        verify(secondResolver).canResolve(testCategory, LanguageCode.EN);
    }

    @Test
    void testCreateResolverFor_WithNoResolverCanHandle_ShouldThrowException() {
        // Given
        when(firstResolver.canResolve(testCategory, LanguageCode.EN)).thenReturn(false);
        when(secondResolver.canResolve(testCategory, LanguageCode.EN)).thenReturn(false);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () ->
                resolverFactory.createResolverFor(testCategory, LanguageCode.EN)
        );

        assertTrue(exception.getMessage().contains("No suitable CategoryNameResolver found"));
        verify(firstResolver).canResolve(testCategory, LanguageCode.EN);
        verify(secondResolver).canResolve(testCategory, LanguageCode.EN);
    }

    @Test
    void testCreateResolverFor_WithNullCategory_ShouldThrowException() {
        // Given
        when(firstResolver.canResolve(null, LanguageCode.EN)).thenReturn(false);
        when(secondResolver.canResolve(null, LanguageCode.EN)).thenReturn(false);

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () ->
                resolverFactory.createResolverFor(null, LanguageCode.EN)
        );

        assertTrue(exception.getMessage().contains("No suitable CategoryNameResolver found"));
        assertTrue(exception.getMessage().contains("category ID: null"));
    }

    @Test
    void testCreateResolverFor_WithEmptyResolversList_ShouldThrowException() {
        // Given - Setup factory with empty resolvers list
        try {
            java.lang.reflect.Field resolversField = resolverFactory.getClass().getDeclaredField("availableResolvers");
            resolversField.setAccessible(true);
            resolversField.set(resolverFactory, Collections.emptyList());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class, () ->
                resolverFactory.createResolverFor(testCategory, LanguageCode.EN)
        );

        assertTrue(exception.getMessage().contains("No suitable CategoryNameResolver found"));
    }

    @Test
    void testGetAvailableResolverCount_ShouldReturnCorrectCount() {
        // When
        int count = resolverFactory.getAvailableResolverCount();

        // Then
        assertEquals(2, count);
    }

    @Test
    void testGetAvailableResolverCount_WithEmptyList_ShouldReturnZero() {
        // Given - Setup factory with empty resolvers list
        try {
            java.lang.reflect.Field resolversField = resolverFactory.getClass().getDeclaredField("availableResolvers");
            resolversField.setAccessible(true);
            resolversField.set(resolverFactory, Collections.emptyList());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // When
        int count = resolverFactory.getAvailableResolverCount();

        // Then
        assertEquals(0, count);
    }

    @Test
    void testGetAvailableResolverNames_ShouldReturnCorrectNames() {
        // When
        List<String> names = resolverFactory.getAvailableResolverNames();

        // Then
        assertEquals(2, names.size());
        // Mock objects have generated class names, so just check the size and that it's not empty
        assertFalse(names.isEmpty());
        names.forEach(name -> assertNotNull(name));
    }

    @Test
    void testGetAvailableResolverNames_WithEmptyList_ShouldReturnEmptyList() {
        // Given - Setup factory with empty resolvers list
        try {
            java.lang.reflect.Field resolversField = resolverFactory.getClass().getDeclaredField("availableResolvers");
            resolversField.setAccessible(true);
            resolversField.set(resolverFactory, Collections.emptyList());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // When
        List<String> names = resolverFactory.getAvailableResolverNames();

        // Then
        assertTrue(names.isEmpty());
    }

    @Test
    void testCreateResolverFor_ChainOfResponsibilityPattern_ShouldStopAtFirstMatch() {
        // Given
        when(firstResolver.canResolve(testCategory, LanguageCode.TR)).thenReturn(true);
        // Don't stub secondResolver since it shouldn't be called

        // When
        CategoryNameResolver result = resolverFactory.createResolverFor(testCategory, LanguageCode.TR);

        // Then
        assertEquals(firstResolver, result);
        verify(firstResolver).canResolve(testCategory, LanguageCode.TR);
        verify(secondResolver, never()).canResolve(any(), any()); // Should not be called due to short-circuiting
    }

    @Test
    void testCreateResolverFor_WithDifferentLanguages_ShouldWorkCorrectly() {
        // Given
        when(firstResolver.canResolve(testCategory, LanguageCode.DE)).thenReturn(false);
        when(secondResolver.canResolve(testCategory, LanguageCode.DE)).thenReturn(true);

        // When
        CategoryNameResolver result = resolverFactory.createResolverFor(testCategory, LanguageCode.DE);

        // Then
        assertEquals(secondResolver, result);
        verify(firstResolver).canResolve(testCategory, LanguageCode.DE);
        verify(secondResolver).canResolve(testCategory, LanguageCode.DE);
    }
}
