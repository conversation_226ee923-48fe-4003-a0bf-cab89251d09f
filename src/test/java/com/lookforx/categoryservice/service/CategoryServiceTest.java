package com.lookforx.categoryservice.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.lookforx.categoryservice.base.AbstractBaseServiceTest;
import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.categoryservice.domain.api.request.CreateCategoryRequest;
import com.lookforx.categoryservice.domain.api.request.UpdateCategoryRequest;
import com.lookforx.categoryservice.domain.dto.CategoryDTO;
import com.lookforx.categoryservice.domain.mapper.CategoryMapper;
import com.lookforx.categoryservice.repository.CategoryRepository;
import com.lookforx.categoryservice.service.CacheManagementService;
import com.lookforx.categoryservice.service.CategorySearchIntegrationService;
import com.lookforx.categoryservice.service.CategoryValidationService;
import com.lookforx.categoryservice.service.impl.CategoryServiceImpl;
import com.lookforx.categoryservice.web.client.SearchCategoryDocument;
import com.lookforx.categoryservice.web.client.SearchClient;
import com.lookforx.categoryservice.web.handler.exception.CategoryHasSubcategoriesException;
import com.lookforx.categoryservice.web.handler.exception.CategoryNotFoundException;
import com.lookforx.categoryservice.web.handler.exception.ParentCategoryNotFoundException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;
import org.testcontainers.shaded.com.fasterxml.jackson.core.type.TypeReference;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class CategoryServiceTest extends AbstractBaseServiceTest {

    @Mock
    private CategoryRepository categoryRepository;

    @Mock
    private CategoryMapper categoryMapper;

    @Mock
    private CacheManagementService cacheManagementService;

    @Mock
    private CategorySearchIntegrationService searchIntegrationService;

    @Mock
    private CategoryValidationService validationService;

    @Mock
    private SearchClient searchClient;

    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;


    @InjectMocks
    private CategoryServiceImpl categoryService;

    private Category parentCategory;
    private Category childCategory;
    private CategoryDTO parentCategoryDTO;
    private CategoryDTO childCategoryDTO;
    private CreateCategoryRequest createRequest;
    private UpdateCategoryRequest updateRequest;

    @BeforeEach
    void setUp() {

        ReflectionTestUtils.setField(categoryService, "cacheTimeout", 3600L);

        ObjectMapper realMapper = new ObjectMapper();
        realMapper.registerModule(new JavaTimeModule());
        realMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        ReflectionTestUtils.setField(categoryService, "objectMapper", realMapper);

        when(redisTemplate.opsForValue()).thenReturn(valueOperations);

        // Setup validation service mocks - no-op for successful cases
        doNothing().when(validationService).validateCreateRequest(any());
        doNothing().when(validationService).validateUpdateRequest(any(), any());
        doNothing().when(validationService).validateDeleteOperation(any());

        // Setup cache management service mocks - no-op
        doNothing().when(cacheManagementService).invalidateAllCategoryCache();

        // Setup search integration service mocks - no-op
        doNothing().when(searchIntegrationService).indexCategory(any(Category.class));
        doNothing().when(searchIntegrationService).updateCategoryIndex(any(Category.class));
        doNothing().when(searchIntegrationService).removeCategoryFromIndex(any());

        // Setup parent category
        Map<LanguageCode, String> parentTranslations = new HashMap<>();
        parentTranslations.put(LanguageCode.EN, "Electronics");
        parentTranslations.put(LanguageCode.TR, "Elektronik");

        parentCategory = Category.builder()
                .parent(null)
                .translations(parentTranslations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategories(new HashSet<>())
                .build();
        // Set ID using reflection since it's inherited from BaseEntity
        try {
            java.lang.reflect.Field idField = parentCategory.getClass().getSuperclass().getDeclaredField("id");
            idField.setAccessible(true);
            idField.set(parentCategory, 1L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        parentCategoryDTO = CategoryDTO.builder()
                .id(1L)
                .parentId(null)
                .translations(parentTranslations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .subcategoryDTOs(new HashSet<>())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // Setup child category
        Map<LanguageCode, String> childTranslations = new HashMap<>();
        childTranslations.put(LanguageCode.EN, "Computers");
        childTranslations.put(LanguageCode.TR, "Bilgisayarlar");

        childCategory = Category.builder()
                .parent(parentCategory)
                .translations(childTranslations)
                .type(CategoryType.PRODUCT)
                .level(2)
                .subcategories(new HashSet<>())
                .build();
        // Set ID using reflection since it's inherited from BaseEntity
        try {
            java.lang.reflect.Field idField = childCategory.getClass().getSuperclass().getDeclaredField("id");
            idField.setAccessible(true);
            idField.set(childCategory, 2L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        childCategoryDTO = CategoryDTO.builder()
                .id(2L)
                .parentId(1L)
                .translations(childTranslations)
                .type(CategoryType.PRODUCT)
                .level(2)
                .subcategoryDTOs(new HashSet<>())
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // Setup requests
        createRequest = new CreateCategoryRequest(1L, childTranslations, CategoryType.PRODUCT, 2);
        updateRequest = new UpdateCategoryRequest(1L, childTranslations, CategoryType.PRODUCT, 2);
    }

    @Test
    void testCreateCategory_WithParent_Success() {
        when(categoryRepository.findById(1L)).thenReturn(Optional.of(parentCategory));
        when(categoryMapper.toEntity(createRequest, parentCategory)).thenReturn(childCategory);
        when(categoryRepository.save(childCategory)).thenReturn(childCategory);
        when(categoryMapper.toDTO(childCategory)).thenReturn(childCategoryDTO);

        CategoryDTO result = categoryService.createCategory(createRequest);

        assertEquals(childCategoryDTO, result);
        verify(validationService).validateCreateRequest(createRequest);
        verify(categoryRepository).findById(1L);
        verify(categoryRepository).save(childCategory);
        verify(cacheManagementService).invalidateAllCategoryCache();
        verify(searchIntegrationService).indexCategory(childCategory);
    }

    @Test
    void testCreateCategory_WithoutParent_Success() {
        CreateCategoryRequest rootRequest = new CreateCategoryRequest(
                null, parentCategory.getTranslations(), CategoryType.PRODUCT, 1
        );
        when(categoryMapper.toEntity(rootRequest, null)).thenReturn(parentCategory);
        when(categoryRepository.save(parentCategory)).thenReturn(parentCategory);
        when(categoryMapper.toDTO(parentCategory)).thenReturn(parentCategoryDTO);

        CategoryDTO result = categoryService.createCategory(rootRequest);

        assertEquals(parentCategoryDTO, result);
        verify(validationService).validateCreateRequest(rootRequest);
        verify(categoryRepository, never()).findById(any());
        verify(categoryRepository).save(parentCategory);
        verify(cacheManagementService).invalidateAllCategoryCache();
        verify(searchIntegrationService).indexCategory(parentCategory);
    }

    @Test
    void testCreateCategory_ParentNotFound_ThrowsException() {
        // Setup validation service to throw exception
        doThrow(new ParentCategoryNotFoundException(1L))
                .when(validationService).validateCreateRequest(createRequest);

        assertThrows(ParentCategoryNotFoundException.class, () ->
                categoryService.createCategory(createRequest)
        );
        verify(validationService).validateCreateRequest(createRequest);
        verify(categoryRepository, never()).save(any());
        verify(searchIntegrationService, never()).indexCategory(any(Category.class));
    }

    @Test
    void testUpdateCategory_Success() {
        when(categoryRepository.findById(2L)).thenReturn(Optional.of(childCategory));
        when(categoryRepository.findById(1L)).thenReturn(Optional.of(parentCategory));
        doNothing().when(categoryMapper).updateEntity(updateRequest, childCategory, parentCategory);
        when(categoryRepository.save(childCategory)).thenReturn(childCategory);
        when(categoryMapper.toDTO(childCategory)).thenReturn(childCategoryDTO);

        CategoryDTO result = categoryService.updateCategory(2L, updateRequest);

        assertEquals(childCategoryDTO, result);
        verify(validationService).validateUpdateRequest(2L, updateRequest);
        verify(categoryRepository).findById(2L);
        verify(categoryRepository).findById(1L);
        verify(categoryMapper).updateEntity(updateRequest, childCategory, parentCategory);
        verify(categoryRepository).save(childCategory);
        verify(cacheManagementService).invalidateAllCategoryCache();
        verify(searchIntegrationService).updateCategoryIndex(childCategory);
    }

    @Test
    void testUpdateCategory_CategoryNotFound_ThrowsException() {
        // Setup validation service to throw exception
        doThrow(new CategoryNotFoundException(2L))
                .when(validationService).validateUpdateRequest(2L, updateRequest);

        assertThrows(CategoryNotFoundException.class, () ->
                categoryService.updateCategory(2L, updateRequest)
        );
        verify(validationService).validateUpdateRequest(2L, updateRequest);
        verify(categoryRepository, never()).save(any());
        verify(searchIntegrationService, never()).updateCategoryIndex(any(Category.class));
    }

//    @Test
//    void testGetCategoryById_FromDb_CachesAndReturns() throws JsonProcessingException {
//        when(valueOperations.get("category_1")).thenReturn(null);
//        when(categoryRepository.findById(1L)).thenReturn(Optional.of(parentCategory));
//        when(categoryMapper.toDTO(parentCategory)).thenReturn(parentCategoryDTO);
//        when(objectMapper.writeValueAsString(parentCategoryDTO)).thenReturn("{}");
//
//        CategoryDTO result = categoryService.getCategoryById(1L);
//
//        assertEquals(parentCategoryDTO, result);
//        verify(categoryRepository).findById(1L);
//        verify(valueOperations).set(eq("category_1"), eq("{}"), eq(Duration.ofHours(1)));
//    }

    @Test
    void testGetCategoryById_FromCache_ReturnsWithoutDb() throws Exception {
        // 1) Let Redis give back the JSON for our DTO
        ObjectMapper realMapper = (ObjectMapper)
                ReflectionTestUtils.getField(categoryService, "objectMapper");
        String json = realMapper.writeValueAsString(parentCategoryDTO);
        when(valueOperations.get("category_1")).thenReturn(json);

        // 2) Call the service
        CategoryDTO result = categoryService.getCategoryById(1L);

        // 3) It should parse that JSON back into the same DTO
        assertEquals(parentCategoryDTO.getParentId(), result.getParentId());
        assertEquals(parentCategoryDTO.getId(), result.getId());

        // 4) And never hit the repository
        verify(categoryRepository, never()).findById(any());
    }

    @Test
    void testGetCategoriesByParentAndType_WithParent() {
        Sort sort = Sort.by(Sort.Direction.ASC, "level", "id");
        List<Category> list = List.of(childCategory);
        when(categoryRepository.findByParentIdAndType(1L, CategoryType.PRODUCT, sort)).thenReturn(list);
        when(categoryMapper.toDTO(childCategory)).thenReturn(childCategoryDTO);

        List<CategoryDTO> result = categoryService.getCategoriesByParentAndType(1L, CategoryType.PRODUCT);

        assertEquals(1, result.size());
        assertEquals(childCategoryDTO, result.get(0));
        verify(categoryRepository).findByParentIdAndType(1L, CategoryType.PRODUCT, sort);
    }

    @Test
    void testGetCategoriesByParentAndType_WithoutParent() {
        Sort sort = Sort.by(Sort.Direction.ASC, "level", "id");
        List<Category> list = List.of(parentCategory);
        when(categoryRepository.findByParentIsNullAndType(CategoryType.PRODUCT, sort)).thenReturn(list);
        when(categoryMapper.toDTO(parentCategory)).thenReturn(parentCategoryDTO);

        List<CategoryDTO> result = categoryService.getCategoriesByParentAndType(null, CategoryType.PRODUCT);

        assertEquals(1, result.size());
        assertEquals(parentCategoryDTO, result.get(0));
        verify(categoryRepository).findByParentIsNullAndType(CategoryType.PRODUCT, sort);
    }

    @Test
    void testDeleteCategory_Success() {
        Category leaf = Category.builder().subcategories(new HashSet<>()).build();
        // Set ID using reflection since it's inherited from BaseEntity
        try {
            java.lang.reflect.Field idField = leaf.getClass().getSuperclass().getDeclaredField("id");
            idField.setAccessible(true);
            idField.set(leaf, 3L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        when(categoryRepository.findById(3L)).thenReturn(Optional.of(leaf));

        categoryService.deleteCategory(3L);

        verify(validationService).validateDeleteOperation(3L);
        verify(categoryRepository).findById(3L);
        verify(categoryRepository).delete(leaf);
        verify(cacheManagementService).invalidateAllCategoryCache();
        verify(searchIntegrationService).removeCategoryFromIndex(3L);
    }

    @Test
    void testDeleteCategory_HasChildren_ThrowsException() {
        // Setup validation service to throw exception
        doThrow(new CategoryHasSubcategoriesException(1L))
                .when(validationService).validateDeleteOperation(1L);

        assertThrows(CategoryHasSubcategoriesException.class, () ->
                categoryService.deleteCategory(1L)
        );
        verify(validationService).validateDeleteOperation(1L);
        verify(categoryRepository, never()).delete(any());
    }

    @Test
    void testDeleteCategory_NotFound_ThrowsException() {
        // Setup validation service to throw exception
        doThrow(new CategoryNotFoundException(1L))
                .when(validationService).validateDeleteOperation(1L);

        assertThrows(CategoryNotFoundException.class, () ->
                categoryService.deleteCategory(1L)
        );
        verify(validationService).validateDeleteOperation(1L);
        verify(categoryRepository, never()).delete(any());
        verify(searchIntegrationService, never()).removeCategoryFromIndex(any());
    }

//    @Test
//    void testGetAllCategories_ReturnsAndCaches() throws JsonProcessingException {
//        List<Category> list = List.of(parentCategory, childCategory);
//        when(valueOperations.get("all_categories")).thenReturn(null);
//        when(categoryRepository.findAll()).thenReturn(list);
//        when(categoryMapper.toDTO(parentCategory)).thenReturn(parentCategoryDTO);
//        when(categoryMapper.toDTO(childCategory)).thenReturn(childCategoryDTO);
//        when(objectMapper.writeValueAsString(anyList())).thenReturn("[]");
//
//        List<CategoryDTO> result = categoryService.getAllCategories();
//
//        assertEquals(2, result.size());
//        verify(valueOperations).set(eq("all_categories"), eq("[]"), eq(Duration.ofHours(1)));
//    }

    @Test
    void testGetAllCategories_FromCache() throws Exception {
        // Use real ObjectMapper for JSON parsing
        ObjectMapper realMapper = new ObjectMapper();
        realMapper.registerModule(new JavaTimeModule());
        realMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        ReflectionTestUtils.setField(categoryService, "objectMapper", realMapper);

        // Prepare cached list and JSON
        List<CategoryDTO> cachedList = List.of(parentCategoryDTO, childCategoryDTO);
        String json = realMapper.writeValueAsString(cachedList);

        // Stub Redis to return JSON
        when(valueOperations.get("all_categories")).thenReturn(json);

        // Execute
        List<CategoryDTO> result = categoryService.getAllCategories();

        // Verify
        assertEquals(cachedList.get(0).getLevel(), result.get(0).getLevel());
        verify(categoryRepository, never()).findAll();

    }

//    @Test
//    void testGetAllCategoriesFlat_CachesWithTimeout() throws JsonProcessingException {
//        // Ensure no cache hit
//        when(valueOperations.get("all_categories_flat")).thenReturn(null);
//        List<Category> list = List.of(parentCategory, childCategory);
//        when(categoryRepository.findAll()).thenReturn(list);
//        when(categoryMapper.toDTO(any())).thenReturn(parentCategoryDTO, childCategoryDTO);
//        when(objectMapper.writeValueAsString(anyList())).thenReturn("[]");
//
//        // Extract the private cacheTimeout via reflection
//        Long timeoutObj = (Long) ReflectionTestUtils.getField(categoryService, "cacheTimeout");
//        long timeout = timeoutObj != null ? timeoutObj : 300L;
//
//        // When
//        List<CategoryDTO> result = categoryService.getAllCategoriesFlat();
//
//        // Then
//        assertEquals(2, result.size());
//        verify(valueOperations).set(eq("all_categories_flat"), eq("[]"), eq(Duration.ofSeconds(timeout)));
//    }

//    @Test
//    void testGetRootCategoriesByType_CachesAndReturns() throws JsonProcessingException {
//        // Ensure no cache hit for root categories
//        when(valueOperations.get("root_categories_PRODUCT")).thenReturn(null);
//        when(categoryRepository.findByParentIsNullAndType(eq(CategoryType.PRODUCT), any(Sort.class)))
//                .thenReturn(List.of(parentCategory));
//        when(objectMapper.writeValueAsString(anyList())).thenReturn("[]");
//
//        // Extract the private cacheTimeout via reflection
//        Long timeoutObj = (Long) ReflectionTestUtils.getField(categoryService, "cacheTimeout");
//        long timeout = timeoutObj != null ? timeoutObj : 300L;
//
//        // When
//        List<CategoryDTO> result = categoryService.getRootCategoriesByType(CategoryType.PRODUCT);
//
//        // Then
//        assertEquals(1, result.size());
//        verify(valueOperations).set(eq("root_categories_PRODUCT"), eq("[]"), eq(Duration.ofSeconds(timeout)));
//    }

}
