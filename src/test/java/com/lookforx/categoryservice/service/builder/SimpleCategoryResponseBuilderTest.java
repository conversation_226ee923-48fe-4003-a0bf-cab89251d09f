package com.lookforx.categoryservice.service.builder;

import com.lookforx.categoryservice.domain.Category;
import com.lookforx.categoryservice.domain.CategoryType;
import com.lookforx.categoryservice.domain.api.response.SimpleCategoryResponse;
import com.lookforx.categoryservice.service.factory.CategoryNameResolverFactory;
import com.lookforx.categoryservice.service.strategy.CategoryNameResolver;
import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * Unit tests for SimpleCategoryResponseBuilder.
 * 
 * Following Clean Code and Design Patterns testing principles:
 * - Builder Pattern testing
 * - Comprehensive test coverage
 * - Mock-based testing approach
 * - Meaningful test names
 */
@ExtendWith(MockitoExtension.class)
class SimpleCategoryResponseBuilderTest {

    @Mock
    private CategoryNameResolverFactory nameResolverFactory;

    @Mock
    private CategoryNameResolver nameResolver;

    @InjectMocks
    private SimpleCategoryResponseBuilder responseBuilder;

    private Category testCategory;
    private Category parentCategory;
    private Map<LanguageCode, String> translations;

    @BeforeEach
    void setUp() {
        translations = new HashMap<>();
        translations.put(LanguageCode.EN, "Electronics");
        translations.put(LanguageCode.TR, "Elektronik");

        parentCategory = Category.builder()
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        testCategory = Category.builder()
                .parent(parentCategory)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(2)
                .build();

        // Set IDs using reflection
        try {
            java.lang.reflect.Field idField = parentCategory.getClass().getSuperclass().getDeclaredField("id");
            idField.setAccessible(true);
            idField.set(parentCategory, 1L);
            idField.set(testCategory, 2L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // Setup mocks with lenient stubbing
        lenient().when(nameResolverFactory.createResolverFor(any(Category.class), any(LanguageCode.class)))
                .thenReturn(nameResolver);
        lenient().when(nameResolver.resolveCategoryName(any(Category.class), any(LanguageCode.class)))
                .thenReturn("Electronics");
    }

    @Test
    void testBuildFrom_WithValidCategory_ShouldReturnCorrectResponse() {
        // When
        SimpleCategoryResponse result = responseBuilder.buildFrom(testCategory, LanguageCode.EN);

        // Then
        assertNotNull(result);
        assertEquals(2L, result.getId());
        assertEquals("Electronics", result.getName());
        assertEquals(CategoryType.PRODUCT, result.getType());
        assertEquals(2, result.getLevel());
        assertNotNull(result.getFullPath());
        
        verify(nameResolverFactory).createResolverFor(testCategory, LanguageCode.EN);
        verify(nameResolver, atLeastOnce()).resolveCategoryName(any(Category.class), eq(LanguageCode.EN));
    }

    @Test
    void testBuildFrom_WithNullCategory_ShouldThrowException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                responseBuilder.buildFrom(null, LanguageCode.EN)
        );

        assertEquals("Category cannot be null", exception.getMessage());
        verify(nameResolverFactory, never()).createResolverFor(any(), any());
    }

    @Test
    void testBuildFrom_WithNullLanguage_ShouldThrowException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () ->
                responseBuilder.buildFrom(testCategory, null)
        );

        assertEquals("Language cannot be null", exception.getMessage());
        verify(nameResolverFactory, never()).createResolverFor(any(), any());
    }

    @Test
    void testBuildFrom_WithRootCategory_ShouldBuildCorrectPath() {
        // Given - Root category without parent
        Category rootCategory = Category.builder()
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        try {
            java.lang.reflect.Field idField = rootCategory.getClass().getSuperclass().getDeclaredField("id");
            idField.setAccessible(true);
            idField.set(rootCategory, 1L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        lenient().when(nameResolver.resolveCategoryName(rootCategory, LanguageCode.EN))
                .thenReturn("Root Electronics");

        // When
        SimpleCategoryResponse result = responseBuilder.buildFrom(rootCategory, LanguageCode.EN);

        // Then
        assertNotNull(result);
        assertEquals("Root Electronics", result.getFullPath());
        verify(nameResolver, atLeastOnce()).resolveCategoryName(any(Category.class), eq(LanguageCode.EN));
    }

    @Test
    void testBuildFrom_WithCategoryHierarchy_ShouldBuildCorrectFullPath() {
        // Given - Setup hierarchy: Root > Parent > Child
        Category grandParent = Category.builder()
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(1)
                .build();

        Category parent = Category.builder()
                .parent(grandParent)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(2)
                .build();

        Category child = Category.builder()
                .parent(parent)
                .translations(translations)
                .type(CategoryType.PRODUCT)
                .level(3)
                .build();

        try {
            java.lang.reflect.Field idField = grandParent.getClass().getSuperclass().getDeclaredField("id");
            idField.setAccessible(true);
            idField.set(grandParent, 1L);
            idField.set(parent, 2L);
            idField.set(child, 3L);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        lenient().when(nameResolver.resolveCategoryName(grandParent, LanguageCode.EN))
                .thenReturn("Electronics");
        lenient().when(nameResolver.resolveCategoryName(parent, LanguageCode.EN))
                .thenReturn("Computers");
        lenient().when(nameResolver.resolveCategoryName(child, LanguageCode.EN))
                .thenReturn("Laptops");

        // When
        SimpleCategoryResponse result = responseBuilder.buildFrom(child, LanguageCode.EN);

        // Then
        assertNotNull(result);
        assertEquals("Electronics > Computers > Laptops", result.getFullPath());
    }

    @Test
    void testBuildListFrom_WithValidCategories_ShouldReturnCorrectList() {
        // Given
        List<Category> categories = Arrays.asList(testCategory, parentCategory);

        lenient().when(nameResolver.resolveCategoryName(parentCategory, LanguageCode.TR))
                .thenReturn("Elektronik");

        // When
        List<SimpleCategoryResponse> result = responseBuilder.buildListFrom(categories, LanguageCode.TR);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        
        verify(nameResolverFactory, times(2)).createResolverFor(any(Category.class), eq(LanguageCode.TR));
    }

    @Test
    void testBuildListFrom_WithNullCategories_ShouldReturnEmptyList() {
        // When
        List<SimpleCategoryResponse> result = responseBuilder.buildListFrom(null, LanguageCode.EN);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(nameResolverFactory, never()).createResolverFor(any(), any());
    }

    @Test
    void testBuildListFrom_WithEmptyCategories_ShouldReturnEmptyList() {
        // When
        List<SimpleCategoryResponse> result = responseBuilder.buildListFrom(Arrays.asList(), LanguageCode.EN);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(nameResolverFactory, never()).createResolverFor(any(), any());
    }

    @Test
    void testBuildFrom_WithDifferentLanguages_ShouldUseCorrectLanguage() {
        // Given
        lenient().when(nameResolver.resolveCategoryName(testCategory, LanguageCode.TR))
                .thenReturn("Elektronik");

        // When
        SimpleCategoryResponse result = responseBuilder.buildFrom(testCategory, LanguageCode.TR);

        // Then
        assertNotNull(result);
        assertEquals("Elektronik", result.getName());
        verify(nameResolverFactory).createResolverFor(testCategory, LanguageCode.TR);
        verify(nameResolver, atLeastOnce()).resolveCategoryName(any(Category.class), eq(LanguageCode.TR));
    }

    @Test
    void testBuildFrom_WithNameResolverException_ShouldPropagateException() {
        // Given
        when(nameResolverFactory.createResolverFor(testCategory, LanguageCode.EN))
                .thenThrow(new RuntimeException("Resolver error"));

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                responseBuilder.buildFrom(testCategory, LanguageCode.EN)
        );

        assertEquals("Resolver error", exception.getMessage());
    }

    @Test
    void testBuildListFrom_WithMixedValidAndInvalidCategories_ShouldHandleGracefully() {
        // Given
        Category validCategory = testCategory;
        Category categoryThatCausesException = parentCategory;
        
        when(nameResolverFactory.createResolverFor(categoryThatCausesException, LanguageCode.EN))
                .thenThrow(new RuntimeException("Resolver error"));

        List<Category> categories = Arrays.asList(validCategory, categoryThatCausesException);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                responseBuilder.buildListFrom(categories, LanguageCode.EN)
        );

        assertEquals("Resolver error", exception.getMessage());
    }
}
