package com.lookforx.exceptionservice.observer;

import com.lookforx.exceptionservice.domain.ExceptionEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ExceptionEvent classes.
 * Tests the Observer Pattern implementation for exception events.
 */
@DisplayName("Exception Event Tests")
class ExceptionEventTest {

    private ExceptionEntity testEntity;
    private Object testSource;

    @BeforeEach
    void setUp() {
        testEntity = new ExceptionEntity();
        testEntity.setId(1L);
        testEntity.setExceptionCode("TEST_ERROR");
        
        testSource = new Object();
    }

    @Test
    @DisplayName("Should create ExceptionCreatedEvent successfully")
    void exceptionCreatedEvent_Creation() {
        // When
        ExceptionEvent.ExceptionCreatedEvent event = 
                new ExceptionEvent.ExceptionCreatedEvent(testSource, testEntity);

        // Then
        assertNotNull(event);
        assertEquals(testSource, event.getSource());
        assertEquals(testEntity, event.getExceptionEntity());
        assertEquals("EXCEPTION_CREATED", event.getEventType());
        assertNotNull(event.getEventTimestamp());
    }

    @Test
    @DisplayName("Should create ExceptionUpdatedEvent successfully")
    void exceptionUpdatedEvent_Creation() {
        // Given
        ExceptionEntity previousState = new ExceptionEntity();
        previousState.setId(1L);
        previousState.setExceptionCode("OLD_ERROR");

        // When
        ExceptionEvent.ExceptionUpdatedEvent event = 
                new ExceptionEvent.ExceptionUpdatedEvent(testSource, testEntity, previousState);

        // Then
        assertNotNull(event);
        assertEquals(testSource, event.getSource());
        assertEquals(testEntity, event.getExceptionEntity());
        assertEquals(previousState, event.getPreviousState());
        assertEquals("EXCEPTION_UPDATED", event.getEventType());
        assertNotNull(event.getEventTimestamp());
    }

    @Test
    @DisplayName("Should create ExceptionDeletedEvent successfully")
    void exceptionDeletedEvent_Creation() {
        // When
        ExceptionEvent.ExceptionDeletedEvent event = 
                new ExceptionEvent.ExceptionDeletedEvent(testSource, testEntity);

        // Then
        assertNotNull(event);
        assertEquals(testSource, event.getSource());
        assertEquals(testEntity, event.getExceptionEntity());
        assertEquals("EXCEPTION_DELETED", event.getEventType());
        assertNotNull(event.getEventTimestamp());
    }

    @Test
    @DisplayName("Should create ExceptionMessageRetrievedEvent successfully")
    void exceptionMessageRetrievedEvent_Creation() {
        // Given
        String languageCode = "EN";
        String retrievedMessage = "Test message";
        String strategyUsed = "Database Strategy";

        // When
        ExceptionEvent.ExceptionMessageRetrievedEvent event = 
                new ExceptionEvent.ExceptionMessageRetrievedEvent(
                        testSource, testEntity, languageCode, retrievedMessage, strategyUsed);

        // Then
        assertNotNull(event);
        assertEquals(testSource, event.getSource());
        assertEquals(testEntity, event.getExceptionEntity());
        assertEquals(languageCode, event.getLanguageCode());
        assertEquals(retrievedMessage, event.getRetrievedMessage());
        assertEquals(strategyUsed, event.getStrategyUsed());
        assertEquals("EXCEPTION_MESSAGE_RETRIEVED", event.getEventType());
        assertNotNull(event.getEventTimestamp());
    }

    @Test
    @DisplayName("Should handle null previous state in updated event")
    void exceptionUpdatedEvent_NullPreviousState() {
        // When
        ExceptionEvent.ExceptionUpdatedEvent event = 
                new ExceptionEvent.ExceptionUpdatedEvent(testSource, testEntity, null);

        // Then
        assertNotNull(event);
        assertEquals(testEntity, event.getExceptionEntity());
        assertNull(event.getPreviousState());
        assertEquals("EXCEPTION_UPDATED", event.getEventType());
    }

    @Test
    @DisplayName("Should handle null values in message retrieved event")
    void exceptionMessageRetrievedEvent_NullValues() {
        // When
        ExceptionEvent.ExceptionMessageRetrievedEvent event = 
                new ExceptionEvent.ExceptionMessageRetrievedEvent(
                        testSource, testEntity, null, null, null);

        // Then
        assertNotNull(event);
        assertEquals(testEntity, event.getExceptionEntity());
        assertNull(event.getLanguageCode());
        assertNull(event.getRetrievedMessage());
        assertNull(event.getStrategyUsed());
        assertEquals("EXCEPTION_MESSAGE_RETRIEVED", event.getEventType());
    }

    @Test
    @DisplayName("Should maintain timestamp consistency")
    void events_TimestampConsistency() {
        // Given
        long beforeTime = System.currentTimeMillis();

        // When
        ExceptionEvent.ExceptionCreatedEvent event1 = 
                new ExceptionEvent.ExceptionCreatedEvent(testSource, testEntity);
        
        ExceptionEvent.ExceptionDeletedEvent event2 = 
                new ExceptionEvent.ExceptionDeletedEvent(testSource, testEntity);

        long afterTime = System.currentTimeMillis();

        // Then
        assertNotNull(event1.getEventTimestamp());
        assertNotNull(event2.getEventTimestamp());
        
        // Timestamps should be within reasonable range
        assertTrue(event1.getEventTimestamp().isAfter(
                java.time.LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochMilli(beforeTime - 1000), 
                        java.time.ZoneId.systemDefault())));
        assertTrue(event1.getEventTimestamp().isBefore(
                java.time.LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochMilli(afterTime + 1000), 
                        java.time.ZoneId.systemDefault())));
    }

    @Test
    @DisplayName("Should handle different event types correctly")
    void events_DifferentTypes() {
        // When
        ExceptionEvent.ExceptionCreatedEvent createdEvent = 
                new ExceptionEvent.ExceptionCreatedEvent(testSource, testEntity);
        ExceptionEvent.ExceptionUpdatedEvent updatedEvent = 
                new ExceptionEvent.ExceptionUpdatedEvent(testSource, testEntity, null);
        ExceptionEvent.ExceptionDeletedEvent deletedEvent = 
                new ExceptionEvent.ExceptionDeletedEvent(testSource, testEntity);
        ExceptionEvent.ExceptionMessageRetrievedEvent messageEvent = 
                new ExceptionEvent.ExceptionMessageRetrievedEvent(
                        testSource, testEntity, "EN", "Message", "Strategy");

        // Then
        assertEquals("EXCEPTION_CREATED", createdEvent.getEventType());
        assertEquals("EXCEPTION_UPDATED", updatedEvent.getEventType());
        assertEquals("EXCEPTION_DELETED", deletedEvent.getEventType());
        assertEquals("EXCEPTION_MESSAGE_RETRIEVED", messageEvent.getEventType());
    }

    @Test
    @DisplayName("Should maintain entity reference integrity")
    void events_EntityReferenceIntegrity() {
        // When
        ExceptionEvent.ExceptionCreatedEvent event = 
                new ExceptionEvent.ExceptionCreatedEvent(testSource, testEntity);

        // Then
        assertSame(testEntity, event.getExceptionEntity());
        assertEquals(testEntity.getId(), event.getExceptionEntity().getId());
        assertEquals(testEntity.getExceptionCode(), event.getExceptionEntity().getExceptionCode());
    }

    @Test
    @DisplayName("Should handle source object correctly")
    void events_SourceObjectHandling() {
        // Given
        String customSource = "Custom Source";

        // When
        ExceptionEvent.ExceptionCreatedEvent event = 
                new ExceptionEvent.ExceptionCreatedEvent(customSource, testEntity);

        // Then
        assertEquals(customSource, event.getSource());
        assertNotEquals(testSource, event.getSource());
    }

    @Test
    @DisplayName("Should create events with different entities")
    void events_DifferentEntities() {
        // Given
        ExceptionEntity entity1 = new ExceptionEntity();
        entity1.setId(1L);
        entity1.setExceptionCode("ERROR_1");

        ExceptionEntity entity2 = new ExceptionEntity();
        entity2.setId(2L);
        entity2.setExceptionCode("ERROR_2");

        // When
        ExceptionEvent.ExceptionCreatedEvent event1 = 
                new ExceptionEvent.ExceptionCreatedEvent(testSource, entity1);
        ExceptionEvent.ExceptionCreatedEvent event2 = 
                new ExceptionEvent.ExceptionCreatedEvent(testSource, entity2);

        // Then
        assertNotSame(event1.getExceptionEntity(), event2.getExceptionEntity());
        assertEquals("ERROR_1", event1.getExceptionEntity().getExceptionCode());
        assertEquals("ERROR_2", event2.getExceptionEntity().getExceptionCode());
    }

    @Test
    @DisplayName("Should handle empty string values in message retrieved event")
    void exceptionMessageRetrievedEvent_EmptyStrings() {
        // When
        ExceptionEvent.ExceptionMessageRetrievedEvent event = 
                new ExceptionEvent.ExceptionMessageRetrievedEvent(
                        testSource, testEntity, "", "", "");

        // Then
        assertNotNull(event);
        assertEquals("", event.getLanguageCode());
        assertEquals("", event.getRetrievedMessage());
        assertEquals("", event.getStrategyUsed());
        assertEquals("EXCEPTION_MESSAGE_RETRIEVED", event.getEventType());
    }
}
