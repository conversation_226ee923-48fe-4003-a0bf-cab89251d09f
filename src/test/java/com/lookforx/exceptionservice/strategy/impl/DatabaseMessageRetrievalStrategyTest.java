package com.lookforx.exceptionservice.strategy.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for DatabaseMessageRetrievalStrategy.
 * Tests the Strategy Pattern implementation for database-based message retrieval.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Database Message Retrieval Strategy Tests")
class DatabaseMessageRetrievalStrategyTest {

    @Mock
    private ExceptionRepository exceptionRepository;

    private DatabaseMessageRetrievalStrategy strategy;

    @BeforeEach
    void setUp() {
        strategy = new DatabaseMessageRetrievalStrategy(exceptionRepository);
    }

    @Test
    @DisplayName("Should retrieve message successfully from database")
    void retrieveMessage_Success() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;
        String expectedMessage = "User not found";

        when(exceptionRepository.findMessageWithFallback(exceptionCode, languageCode, LanguageCode.EN))
                .thenReturn(Optional.of(expectedMessage));

        // When
        String result = strategy.retrieveMessage(exceptionCode, languageCode);

        // Then
        assertEquals(expectedMessage, result);
        verify(exceptionRepository).findMessageWithFallback(exceptionCode, languageCode, LanguageCode.EN);
    }

    @Test
    @DisplayName("Should return null when message not found in database")
    void retrieveMessage_NotFound() {
        // Given
        String exceptionCode = "UNKNOWN_ERROR";
        LanguageCode languageCode = LanguageCode.TR;

        when(exceptionRepository.findMessageWithFallback(exceptionCode, languageCode, LanguageCode.EN))
                .thenReturn(Optional.empty());

        // When
        String result = strategy.retrieveMessage(exceptionCode, languageCode);

        // Then
        assertNull(result);
        verify(exceptionRepository).findMessageWithFallback(exceptionCode, languageCode, LanguageCode.EN);
    }

    @Test
    @DisplayName("Should throw exception when exception code is null")
    void retrieveMessage_NullExceptionCode() {
        // Given
        String exceptionCode = null;
        LanguageCode languageCode = LanguageCode.EN;

        // When & Then
        assertThrows(NullPointerException.class, 
                () -> strategy.retrieveMessage(exceptionCode, languageCode));
        
        verifyNoInteractions(exceptionRepository);
    }

    @Test
    @DisplayName("Should throw exception when language code is null")
    void retrieveMessage_NullLanguageCode() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = null;

        // When & Then
        assertThrows(NullPointerException.class, 
                () -> strategy.retrieveMessage(exceptionCode, languageCode));
        
        verifyNoInteractions(exceptionRepository);
    }

    @Test
    @DisplayName("Should return correct priority")
    void getPriority_ReturnsCorrectValue() {
        // When
        int priority = strategy.getPriority();

        // Then
        assertEquals(1, priority);
    }

    @Test
    @DisplayName("Should handle valid parameters")
    void canHandle_ValidParameters() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;

        // When
        boolean result = strategy.canHandle(exceptionCode, languageCode);

        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("Should not handle null exception code")
    void canHandle_NullExceptionCode() {
        // Given
        String exceptionCode = null;
        LanguageCode languageCode = LanguageCode.EN;

        // When
        boolean result = strategy.canHandle(exceptionCode, languageCode);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("Should not handle empty exception code")
    void canHandle_EmptyExceptionCode() {
        // Given
        String exceptionCode = "";
        LanguageCode languageCode = LanguageCode.EN;

        // When
        boolean result = strategy.canHandle(exceptionCode, languageCode);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("Should not handle whitespace-only exception code")
    void canHandle_WhitespaceExceptionCode() {
        // Given
        String exceptionCode = "   ";
        LanguageCode languageCode = LanguageCode.EN;

        // When
        boolean result = strategy.canHandle(exceptionCode, languageCode);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("Should not handle null language code")
    void canHandle_NullLanguageCode() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = null;

        // When
        boolean result = strategy.canHandle(exceptionCode, languageCode);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("Should return correct strategy name")
    void getStrategyName_ReturnsCorrectName() {
        // When
        String strategyName = strategy.getStrategyName();

        // Then
        assertEquals("Database Message Retrieval Strategy", strategyName);
    }

    @Test
    @DisplayName("Should handle repository exception gracefully")
    void retrieveMessage_RepositoryException() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;

        when(exceptionRepository.findMessageWithFallback(exceptionCode, languageCode, LanguageCode.EN))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        assertThrows(RuntimeException.class, 
                () -> strategy.retrieveMessage(exceptionCode, languageCode));
        
        verify(exceptionRepository).findMessageWithFallback(exceptionCode, languageCode, LanguageCode.EN);
    }
}
