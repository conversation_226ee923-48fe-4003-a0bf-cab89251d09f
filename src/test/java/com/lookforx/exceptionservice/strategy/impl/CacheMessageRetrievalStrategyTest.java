package com.lookforx.exceptionservice.strategy.impl;

import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CacheMessageRetrievalStrategy.
 * Tests the Strategy Pattern implementation for cache-based message retrieval.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Cache Message Retrieval Strategy Tests")
class CacheMessageRetrievalStrategyTest {

    @Mock
    private CacheManager cacheManager;

    @Mock
    private Cache cache;

    @Mock
    private Cache.ValueWrapper valueWrapper;

    private CacheMessageRetrievalStrategy strategy;

    @BeforeEach
    void setUp() {
        strategy = new CacheMessageRetrievalStrategy(cacheManager);
    }

    @Test
    @DisplayName("Should retrieve message successfully from cache")
    void retrieveMessage_Success() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;
        String expectedMessage = "User not found";
        String cacheKey = "USER_NOT_FOUND:EN";

        when(cacheManager.getCache("exception-messages")).thenReturn(cache);
        when(cache.get(cacheKey)).thenReturn(valueWrapper);
        when(valueWrapper.get()).thenReturn(expectedMessage);

        // When
        String result = strategy.retrieveMessage(exceptionCode, languageCode);

        // Then
        assertEquals(expectedMessage, result);
        verify(cacheManager).getCache("exception-messages");
        verify(cache).get(cacheKey);
        verify(valueWrapper).get();
    }

    @Test
    @DisplayName("Should return null when message not found in cache")
    void retrieveMessage_NotFoundInCache() {
        // Given
        String exceptionCode = "UNKNOWN_ERROR";
        LanguageCode languageCode = LanguageCode.TR;
        String cacheKey = "UNKNOWN_ERROR:TR";

        when(cacheManager.getCache("exception-messages")).thenReturn(cache);
        when(cache.get(cacheKey)).thenReturn(null);

        // When
        String result = strategy.retrieveMessage(exceptionCode, languageCode);

        // Then
        assertNull(result);
        verify(cacheManager).getCache("exception-messages");
        verify(cache).get(cacheKey);
    }

    @Test
    @DisplayName("Should return null when cache is not available")
    void retrieveMessage_CacheNotAvailable() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;

        when(cacheManager.getCache("exception-messages")).thenReturn(null);

        // When
        String result = strategy.retrieveMessage(exceptionCode, languageCode);

        // Then
        assertNull(result);
        verify(cacheManager).getCache("exception-messages");
    }

    @Test
    @DisplayName("Should throw exception when exception code is null")
    void retrieveMessage_NullExceptionCode() {
        // Given
        String exceptionCode = null;
        LanguageCode languageCode = LanguageCode.EN;

        // When & Then
        assertThrows(NullPointerException.class, 
                () -> strategy.retrieveMessage(exceptionCode, languageCode));
        
        verifyNoInteractions(cacheManager);
    }

    @Test
    @DisplayName("Should throw exception when language code is null")
    void retrieveMessage_NullLanguageCode() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = null;

        // When & Then
        assertThrows(NullPointerException.class, 
                () -> strategy.retrieveMessage(exceptionCode, languageCode));
        
        verifyNoInteractions(cacheManager);
    }

    @Test
    @DisplayName("Should return correct priority")
    void getPriority_ReturnsCorrectValue() {
        // When
        int priority = strategy.getPriority();

        // Then
        assertEquals(0, priority);
    }

    @Test
    @DisplayName("Should handle valid parameters when cache is available")
    void canHandle_ValidParametersWithCache() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;

        when(cacheManager.getCache("exception-messages")).thenReturn(cache);

        // When
        boolean result = strategy.canHandle(exceptionCode, languageCode);

        // Then
        assertTrue(result);
        verify(cacheManager).getCache("exception-messages");
    }

    @Test
    @DisplayName("Should not handle when cache is not available")
    void canHandle_CacheNotAvailable() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;

        when(cacheManager.getCache("exception-messages")).thenReturn(null);

        // When
        boolean result = strategy.canHandle(exceptionCode, languageCode);

        // Then
        assertFalse(result);
        verify(cacheManager).getCache("exception-messages");
    }

    @Test
    @DisplayName("Should return correct strategy name")
    void getStrategyName_ReturnsCorrectName() {
        // When
        String strategyName = strategy.getStrategyName();

        // Then
        assertEquals("Cache Message Retrieval Strategy", strategyName);
    }

    @Test
    @DisplayName("Should cache message successfully")
    void cacheMessage_Success() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;
        String message = "User not found";
        String cacheKey = "USER_NOT_FOUND:EN";

        when(cacheManager.getCache("exception-messages")).thenReturn(cache);

        // When
        strategy.cacheMessage(exceptionCode, languageCode, message);

        // Then
        verify(cacheManager).getCache("exception-messages");
        verify(cache).put(cacheKey, message);
    }

    @Test
    @DisplayName("Should not cache when cache is not available")
    void cacheMessage_CacheNotAvailable() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;
        String message = "User not found";

        when(cacheManager.getCache("exception-messages")).thenReturn(null);

        // When
        strategy.cacheMessage(exceptionCode, languageCode, message);

        // Then
        verify(cacheManager).getCache("exception-messages");
        verifyNoMoreInteractions(cacheManager);
    }

    @Test
    @DisplayName("Should not cache null message")
    void cacheMessage_NullMessage() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;
        String message = null;

        when(cacheManager.getCache("exception-messages")).thenReturn(cache);

        // When
        strategy.cacheMessage(exceptionCode, languageCode, message);

        // Then
        verify(cacheManager).getCache("exception-messages");
        verifyNoInteractions(cache);
    }
}
