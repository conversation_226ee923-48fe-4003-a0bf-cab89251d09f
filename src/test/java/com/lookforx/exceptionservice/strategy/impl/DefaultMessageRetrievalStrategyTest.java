package com.lookforx.exceptionservice.strategy.impl;

import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for DefaultMessageRetrievalStrategy.
 * Tests the Strategy Pattern implementation for default fallback message retrieval.
 */
@DisplayName("Default Message Retrieval Strategy Tests")
class DefaultMessageRetrievalStrategyTest {

    private DefaultMessageRetrievalStrategy strategy;

    @BeforeEach
    void setUp() {
        strategy = new DefaultMessageRetrievalStrategy();
    }

    @Test
    @DisplayName("Should retrieve English message successfully")
    void retrieveMessage_EnglishMessage() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;

        // When
        String result = strategy.retrieveMessage(exceptionCode, languageCode);

        // Then
        assertNotNull(result);
        assertEquals("An error occurred. Please contact support team.", result);
    }

    @Test
    @DisplayName("Should retrieve Turkish message successfully")
    void retrieveMessage_TurkishMessage() {
        // Given
        String exceptionCode = "VALIDATION_ERROR";
        LanguageCode languageCode = LanguageCode.TR;

        // When
        String result = strategy.retrieveMessage(exceptionCode, languageCode);

        // Then
        assertNotNull(result);
        assertEquals("Bir hata oluştu. Lütfen destek ekibiyle iletişime geçin.", result);
    }

    @ParameterizedTest
    @EnumSource(LanguageCode.class)
    @DisplayName("Should retrieve message for all supported languages")
    void retrieveMessage_AllSupportedLanguages(LanguageCode languageCode) {
        // Given
        String exceptionCode = "GENERIC_ERROR";

        // When
        String result = strategy.retrieveMessage(exceptionCode, languageCode);

        // Then
        assertNotNull(result);
        assertFalse(result.trim().isEmpty());
    }

    @Test
    @DisplayName("Should throw exception when exception code is null")
    void retrieveMessage_NullExceptionCode() {
        // Given
        String exceptionCode = null;
        LanguageCode languageCode = LanguageCode.EN;

        // When & Then
        assertThrows(NullPointerException.class, 
                () -> strategy.retrieveMessage(exceptionCode, languageCode));
    }

    @Test
    @DisplayName("Should throw exception when language code is null")
    void retrieveMessage_NullLanguageCode() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = null;

        // When & Then
        assertThrows(NullPointerException.class, 
                () -> strategy.retrieveMessage(exceptionCode, languageCode));
    }

    @Test
    @DisplayName("Should return correct priority")
    void getPriority_ReturnsCorrectValue() {
        // When
        int priority = strategy.getPriority();

        // Then
        assertEquals(999, priority);
    }

    @Test
    @DisplayName("Should handle valid parameters")
    void canHandle_ValidParameters() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;

        // When
        boolean result = strategy.canHandle(exceptionCode, languageCode);

        // Then
        assertTrue(result);
    }

    @Test
    @DisplayName("Should not handle null exception code")
    void canHandle_NullExceptionCode() {
        // Given
        String exceptionCode = null;
        LanguageCode languageCode = LanguageCode.EN;

        // When
        boolean result = strategy.canHandle(exceptionCode, languageCode);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("Should not handle null language code")
    void canHandle_NullLanguageCode() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = null;

        // When
        boolean result = strategy.canHandle(exceptionCode, languageCode);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("Should return correct strategy name")
    void getStrategyName_ReturnsCorrectName() {
        // When
        String strategyName = strategy.getStrategyName();

        // Then
        assertEquals("Default Message Retrieval Strategy", strategyName);
    }

    @Test
    @DisplayName("Should handle empty exception code")
    void retrieveMessage_EmptyExceptionCode() {
        // Given
        String exceptionCode = "";
        LanguageCode languageCode = LanguageCode.EN;

        // When
        String result = strategy.retrieveMessage(exceptionCode, languageCode);

        // Then
        assertNotNull(result);
        assertEquals("An error occurred. Please contact support team.", result);
    }

    @Test
    @DisplayName("Should provide consistent messages for same language")
    void retrieveMessage_ConsistentMessages() {
        // Given
        String exceptionCode1 = "ERROR_1";
        String exceptionCode2 = "ERROR_2";
        LanguageCode languageCode = LanguageCode.FR;

        // When
        String result1 = strategy.retrieveMessage(exceptionCode1, languageCode);
        String result2 = strategy.retrieveMessage(exceptionCode2, languageCode);

        // Then
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(result1, result2); // Should be same default message for same language
    }
}
