package com.lookforx.exceptionservice.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.dto.ExceptionMessageResponse;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import com.lookforx.exceptionservice.service.ExceptionMessageService;
import com.lookforx.exceptionservice.service.ExceptionManagementService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Exception Controller Unit Tests")
class ExceptionControllerTest {

    private MockMvc mockMvc;

    private ObjectMapper objectMapper;

    @Mock
    private ExceptionMessageService messageService;

    @Mock
    private ExceptionManagementService managementService;

    @InjectMocks
    private ExceptionController exceptionController;

    private static final String BASE = "/api/v1/exceptions";

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        mockMvc = MockMvcBuilders.standaloneSetup(exceptionController).build();
    }

    @Test
    @DisplayName("GET  /api/v1/exceptions → 200 & full ExceptionResponse list")
    void testGetAllExceptions() throws Exception {

        // Given
        ExceptionResponse resp = ExceptionResponse.builder()
                .id(1L)
                .exceptionCode("E001")
                .messages(Map.of(LanguageCode.EN, "Something went wrong"))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        List<ExceptionResponse> list = List.of(resp);

        // When
        when(managementService.getAllExceptions()).thenReturn(list);

        // Then
        mockMvc.perform(get(BASE).param("unpaged", "true"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        // Verify SOLID: Controller delegates to specialized service
        verify(managementService, times(1)).getAllExceptions();
        verifyNoInteractions(messageService);

    }

    @Test
    @DisplayName("POST /api/v1/exceptions → 201 & created ExceptionResponse")
    void testCreateException() throws Exception {

        // Given
        ExceptionRequest req = ExceptionRequest.builder()
                .exceptionCode("E002")
                .messages(Map.of(LanguageCode.TR, "Bir hata oluştu"))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        ExceptionResponse created = ExceptionResponse.builder()
                .id(2L)
                .exceptionCode(req.getExceptionCode())
                .messages(req.getMessages())
                .httpStatus(req.getHttpStatus())
                .build();

        // When
        when(managementService.createException(any(ExceptionRequest.class)))
                .thenReturn(created);

        // Then
        mockMvc.perform(post(BASE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(req)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(created.getId()))
                .andExpect(jsonPath("$.exceptionCode").value(created.getExceptionCode()))
                .andExpect(jsonPath("$.httpStatus").value(created.getHttpStatus().name()));

        // Verify SOLID: Controller delegates to specialized service
        verify(managementService, times(1)).createException(any(ExceptionRequest.class));
        verifyNoInteractions(messageService);

    }

    @Test
    @DisplayName("PUT  /api/v1/exceptions/{id} → 200 & updated ExceptionResponse")
    void testUpdateException() throws Exception {

        // Given
        Long id = 3L;
        ExceptionRequest req = ExceptionRequest.builder()
                .exceptionCode("E003")
                .messages(Map.of(LanguageCode.EN, "Updated message"))
                .httpStatus(HttpStatus.CONFLICT)
                .build();

        ExceptionResponse updated = ExceptionResponse.builder()
                .id(id)
                .exceptionCode(req.getExceptionCode())
                .messages(req.getMessages())
                .httpStatus(req.getHttpStatus())
                .build();

        // When
        when(managementService.updateException(eq(id), any(ExceptionRequest.class)))
                .thenReturn(updated);

        // Then
        mockMvc.perform(put(BASE + "/{id}", id)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(req)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(updated.getId()))
                .andExpect(jsonPath("$.exceptionCode").value(updated.getExceptionCode()))
                .andExpect(jsonPath("$.httpStatus").value(updated.getHttpStatus().name()));

        // Verify SOLID: Controller delegates to specialized service
        verify(managementService, times(1)).updateException(eq(id), any(ExceptionRequest.class));
        verifyNoInteractions(messageService);

    }

    @Test
    @DisplayName("DELETE /api/v1/exceptions/{id} → 204 No Content")
    void testDeleteException() throws Exception {

        // Given
        Long id = 4L;

        // When & Then
        mockMvc.perform(delete(BASE + "/{id}", id))
                .andExpect(status().isNoContent());

        // Verify SOLID: Controller delegates to specialized service
        verify(managementService, times(1)).deleteException(eq(id));
        verifyNoInteractions(messageService);


    }

    @Test
    @DisplayName("GET /api/v1/exceptions/{id} → 200 & ExceptionResponse")
    void testGetExceptionById() throws Exception {
        // Given
        Long id = 1L;
        ExceptionResponse response = ExceptionResponse.builder()
                .id(id)
                .exceptionCode("USER_NOT_FOUND")
                .messages(Map.of(LanguageCode.EN, "User not found"))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        // When
        when(managementService.getExceptionById(id)).thenReturn(response);

        // Then
        mockMvc.perform(get(BASE + "/{id}", id))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(id))
                .andExpect(jsonPath("$.exceptionCode").value("USER_NOT_FOUND"));

        // Verify SOLID: Controller delegates to specialized service
        verify(managementService, times(1)).getExceptionById(id);
        verifyNoInteractions(messageService);
    }

    @Test
    @DisplayName("GET /api/v1/exceptions with pagination → 200 & Page<ExceptionResponse>")
    void testGetAllExceptionsWithPagination() throws Exception {
        // Given
        ExceptionResponse response = ExceptionResponse.builder()
                .id(1L)
                .exceptionCode("VALIDATION_ERROR")
                .messages(Map.of(LanguageCode.EN, "Validation failed"))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        org.springframework.data.domain.Pageable pageable =
                org.springframework.data.domain.PageRequest.of(0, 10);
        org.springframework.data.domain.Page<ExceptionResponse> page =
                new org.springframework.data.domain.PageImpl<>(List.of(response), pageable, 1);

        // When
        when(managementService.getAllExceptions(any(org.springframework.data.domain.Pageable.class), eq(null)))
                .thenReturn(page);

        // Then
        mockMvc.perform(get(BASE)
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].id").value(1L));

        // Verify SOLID: Controller delegates to specialized service
        verify(managementService, times(1)).getAllExceptions(any(org.springframework.data.domain.Pageable.class), eq(null));
        verifyNoInteractions(messageService);
    }

    @Test
    @DisplayName("GET /api/v1/exceptions with search → 200 & Page<ExceptionResponse>")
    void testGetAllExceptionsWithSearch() throws Exception {
        // Given
        String search = "USER";
        ExceptionResponse response = ExceptionResponse.builder()
                .id(1L)
                .exceptionCode("USER_NOT_FOUND")
                .messages(Map.of(LanguageCode.EN, "User not found"))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        org.springframework.data.domain.Pageable pageable =
                org.springframework.data.domain.PageRequest.of(0, 10);
        org.springframework.data.domain.Page<ExceptionResponse> page =
                new org.springframework.data.domain.PageImpl<>(List.of(response), pageable, 1);

        // When
        when(managementService.getAllExceptions(any(org.springframework.data.domain.Pageable.class), eq(search)))
                .thenReturn(page);

        // Then
        mockMvc.perform(get(BASE)
                        .param("page", "0")
                        .param("size", "10")
                        .param("search", search))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].exceptionCode").value("USER_NOT_FOUND"));

        // Verify SOLID: Controller delegates to specialized service
        verify(managementService, times(1)).getAllExceptions(any(org.springframework.data.domain.Pageable.class), eq(search));
        verifyNoInteractions(messageService);
    }

    @Test
    @DisplayName("GET  /api/v1/exceptions/exception-message → 200 & ExceptionMessageResponse")
    void testGetExceptionMessage() throws Exception {

        // Given
        String code = "E004";
        LanguageCode lang = LanguageCode.TR;
        String msg = "Özel hata mesajı";

        ExceptionMessageResponse resp = ExceptionMessageResponse.builder()
                .exceptionCode(code)
                .languageCode(lang)
                .message(msg)
                .build();

        // When
        when(messageService.retrieveLocalizedMessage(eq(code), eq(lang)))
                .thenReturn(msg);

        // Then
        mockMvc.perform(get(BASE + "/exception-message")
                        .param("exceptionCode", code)
                        .param("languageCode", lang.name()))
                .andExpect(status().isOk())
                .andExpect(content().json(objectMapper.writeValueAsString(resp)));

        // Verify SOLID: Controller delegates to specialized service
        verify(messageService, times(1)).retrieveLocalizedMessage(eq(code), eq(lang));
        verifyNoInteractions(managementService);

    }

}