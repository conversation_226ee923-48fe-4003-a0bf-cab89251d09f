package com.lookforx.exceptionservice.exception;

import com.lookforx.common.dto.ErrorResponse;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {

    @InjectMocks
    private GlobalExceptionHandler handler;

    @Test
    void handleExceptionNotFound_ShouldReturnNotFoundStatus() {

        // Given
        ExceptionNotFoundException ex = mock(ExceptionNotFoundException.class);

        // When
        when(ex.getMessage()).thenReturn("Resource missing");

        // Then
        ResponseEntity<ErrorResponse> response = handler.handleExceptionNotFound(ex);

        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNotNull(response.getBody());

        //  Verify
        verify(ex).getMessage();

    }

    @Test
    void handleDuplicateExceptionCode_ShouldReturnConflictStatus() {

        // Given
        DuplicateExceptionCode ex = mock(DuplicateExceptionCode.class);

        // When
        when(ex.getMessage()).thenReturn("Code exists");

        // Then
        ResponseEntity<ErrorResponse> response = handler.handleDuplicateExceptionCode(ex);

        assertEquals(HttpStatus.CONFLICT, response.getStatusCode());
        assertNotNull(response.getBody());

        // Verify
        verify(ex).getMessage();

    }

    @Test
    void handleValidationExceptions_ShouldReturnBadRequestWithErrors() {

        // Given
        MethodArgumentNotValidException ex = mock(MethodArgumentNotValidException.class);
        BindingResult bindingResult = mock(BindingResult.class);
        FieldError fe1 = new FieldError("obj", "field1", "Invalid value 1");
        FieldError fe2 = new FieldError("obj", "field2", "Invalid value 2");
        List<FieldError> fieldErrors = Arrays.asList(fe1, fe2);

        // When
        when(ex.getBindingResult()).thenReturn(bindingResult);
        when(bindingResult.getFieldErrors()).thenReturn(fieldErrors);

        // Then
        ResponseEntity<ErrorResponse> response = handler.handleValidationExceptions(ex);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(HttpStatus.BAD_REQUEST.value(), errorResponse.getStatus());
        assertEquals("Validation failed for one or more fields", errorResponse.getMessage());

        Map<String, String> errors = errorResponse.getFieldErrors();
        assertNotNull(errors);
        assertEquals(2, errors.size());
        assertEquals("Invalid value 1", errors.get("field1"));
        assertEquals("Invalid value 2", errors.get("field2"));

        // Verify
        verify(ex).getBindingResult();
        verify(bindingResult).getFieldErrors();

    }

    @Test
    void handleAllUncaught_ShouldReturnInternalServerErrorStatus() {

        // Given
        Exception ex = mock(Exception.class);

        // When
        when(ex.getMessage()).thenReturn("Unexpected error");

        // Then
        ResponseEntity<ErrorResponse> response = handler.handleAllUncaught(ex);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());

        // Verify
        verify(ex).getMessage();

    }

}