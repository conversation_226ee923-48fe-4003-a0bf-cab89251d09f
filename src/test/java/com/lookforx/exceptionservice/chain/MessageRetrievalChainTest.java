package com.lookforx.exceptionservice.chain;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.strategy.MessageRetrievalStrategy;
import com.lookforx.exceptionservice.strategy.impl.CacheMessageRetrievalStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

/**
 * Unit tests for MessageRetrievalChain.
 * Tests the Chain of Responsibility Pattern implementation for message retrieval.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Message Retrieval Chain Tests")
class MessageRetrievalChainTest {

    @Mock
    private MessageRetrievalStrategy highPriorityStrategy;

    @Mock
    private MessageRetrievalStrategy mediumPriorityStrategy;

    @Mock
    private MessageRetrievalStrategy lowPriorityStrategy;

    @Mock
    private CacheMessageRetrievalStrategy cacheStrategy;

    private MessageRetrievalChain chain;

    @BeforeEach
    void setUp() {
        // Setup strategy priorities - use lenient to avoid unnecessary stubbing errors
        lenient().when(highPriorityStrategy.getPriority()).thenReturn(1);
        lenient().when(mediumPriorityStrategy.getPriority()).thenReturn(5);
        lenient().when(lowPriorityStrategy.getPriority()).thenReturn(10);

        // Setup strategy names
        lenient().when(highPriorityStrategy.getStrategyName()).thenReturn("High Priority Strategy");
        lenient().when(mediumPriorityStrategy.getStrategyName()).thenReturn("Medium Priority Strategy");
        lenient().when(lowPriorityStrategy.getStrategyName()).thenReturn("Low Priority Strategy");

        List<MessageRetrievalStrategy> strategies = Arrays.asList(
                lowPriorityStrategy, highPriorityStrategy, mediumPriorityStrategy
        );

        chain = new MessageRetrievalChain(strategies, cacheStrategy);
    }

    @Test
    @DisplayName("Should process request with first successful strategy")
    void processRequest_FirstStrategySucceeds() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;
        String expectedMessage = "User not found";

        when(highPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);
        when(mediumPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);
        when(lowPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);

        when(highPriorityStrategy.retrieveMessage(exceptionCode, languageCode)).thenReturn(expectedMessage);

        // When
        String result = chain.processRequest(exceptionCode, languageCode);

        // Then
        assertEquals(expectedMessage, result);
        verify(highPriorityStrategy).retrieveMessage(exceptionCode, languageCode);
        verify(cacheStrategy).cacheMessage(exceptionCode, languageCode, expectedMessage);
        
        // Should not call retrieveMessage on other strategies since first one succeeded
        verify(mediumPriorityStrategy, never()).retrieveMessage(any(), any());
        verify(lowPriorityStrategy, never()).retrieveMessage(any(), any());
    }

    @Test
    @DisplayName("Should fallback to next strategy when first fails")
    void processRequest_FallbackToNextStrategy() {
        // Given
        String exceptionCode = "VALIDATION_ERROR";
        LanguageCode languageCode = LanguageCode.TR;
        String expectedMessage = "Doğrulama hatası";

        when(highPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);
        when(mediumPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);
        when(lowPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);

        when(highPriorityStrategy.retrieveMessage(exceptionCode, languageCode)).thenReturn(null);
        when(mediumPriorityStrategy.retrieveMessage(exceptionCode, languageCode)).thenReturn(expectedMessage);

        // When
        String result = chain.processRequest(exceptionCode, languageCode);

        // Then
        assertEquals(expectedMessage, result);
        verify(highPriorityStrategy).retrieveMessage(exceptionCode, languageCode);
        verify(mediumPriorityStrategy).retrieveMessage(exceptionCode, languageCode);
        verify(cacheStrategy).cacheMessage(exceptionCode, languageCode, expectedMessage);
        
        // Should not call low priority strategy since medium succeeded
        verify(lowPriorityStrategy, never()).retrieveMessage(any(), any());
    }

    @Test
    @DisplayName("Should process through all strategies when all return null")
    void processRequest_AllStrategiesReturnNull() {
        // Given
        String exceptionCode = "UNKNOWN_ERROR";
        LanguageCode languageCode = LanguageCode.DE;

        when(highPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);
        when(mediumPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);
        when(lowPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);

        when(highPriorityStrategy.retrieveMessage(exceptionCode, languageCode)).thenReturn(null);
        when(mediumPriorityStrategy.retrieveMessage(exceptionCode, languageCode)).thenReturn(null);
        when(lowPriorityStrategy.retrieveMessage(exceptionCode, languageCode)).thenReturn(null);

        // When
        String result = chain.processRequest(exceptionCode, languageCode);

        // Then
        assertNull(result);
        verify(highPriorityStrategy).retrieveMessage(exceptionCode, languageCode);
        verify(mediumPriorityStrategy).retrieveMessage(exceptionCode, languageCode);
        verify(lowPriorityStrategy).retrieveMessage(exceptionCode, languageCode);
        
        // Should not cache null result
        verifyNoInteractions(cacheStrategy);
    }

    @Test
    @DisplayName("Should skip strategies that cannot handle request")
    void processRequest_SkipIncompatibleStrategies() {
        // Given
        String exceptionCode = "ACCESS_DENIED";
        LanguageCode languageCode = LanguageCode.FR;
        String expectedMessage = "Accès refusé";

        when(highPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(false);
        when(mediumPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);
        when(lowPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);

        when(mediumPriorityStrategy.retrieveMessage(exceptionCode, languageCode)).thenReturn(expectedMessage);

        // When
        String result = chain.processRequest(exceptionCode, languageCode);

        // Then
        assertEquals(expectedMessage, result);
        verify(mediumPriorityStrategy).retrieveMessage(exceptionCode, languageCode);
        verify(cacheStrategy).cacheMessage(exceptionCode, languageCode, expectedMessage);
        
        // Should not call high priority strategy since it can't handle
        verify(highPriorityStrategy, never()).retrieveMessage(any(), any());
        verify(lowPriorityStrategy, never()).retrieveMessage(any(), any());
    }

    @Test
    @DisplayName("Should handle strategy exception gracefully")
    void processRequest_StrategyThrowsException() {
        // Given
        String exceptionCode = "DATABASE_ERROR";
        LanguageCode languageCode = LanguageCode.ES;
        String expectedMessage = "Error de base de datos";

        when(highPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);
        when(mediumPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);

        when(highPriorityStrategy.retrieveMessage(exceptionCode, languageCode))
                .thenThrow(new RuntimeException("Database connection failed"));
        when(mediumPriorityStrategy.retrieveMessage(exceptionCode, languageCode)).thenReturn(expectedMessage);

        // When
        String result = chain.processRequest(exceptionCode, languageCode);

        // Then
        assertEquals(expectedMessage, result);
        verify(highPriorityStrategy).retrieveMessage(exceptionCode, languageCode);
        verify(mediumPriorityStrategy).retrieveMessage(exceptionCode, languageCode);
        verify(cacheStrategy).cacheMessage(exceptionCode, languageCode, expectedMessage);
    }

    @Test
    @DisplayName("Should not cache when strategy is CacheMessageRetrievalStrategy")
    void processRequest_DoNotCacheFromCacheStrategy() {
        // Given
        String exceptionCode = "CACHE_ERROR";
        LanguageCode languageCode = LanguageCode.IT;
        String expectedMessage = "Errore cache";

        // Create a mock that is instance of CacheMessageRetrievalStrategy
        CacheMessageRetrievalStrategy mockCacheStrategy = mock(CacheMessageRetrievalStrategy.class);
        lenient().when(mockCacheStrategy.getPriority()).thenReturn(0);
        lenient().when(mockCacheStrategy.getStrategyName()).thenReturn("Cache Strategy");
        when(mockCacheStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);
        when(mockCacheStrategy.retrieveMessage(exceptionCode, languageCode)).thenReturn(expectedMessage);

        List<MessageRetrievalStrategy> strategies = Arrays.asList(mockCacheStrategy);
        MessageRetrievalChain testChain = new MessageRetrievalChain(strategies, cacheStrategy);

        // When
        String result = testChain.processRequest(exceptionCode, languageCode);

        // Then
        assertEquals(expectedMessage, result);
        verify(mockCacheStrategy).retrieveMessage(exceptionCode, languageCode);
        
        // Should not cache when result comes from cache strategy
        verifyNoInteractions(cacheStrategy);
    }

    @Test
    @DisplayName("Should throw exception when exception code is null")
    void processRequest_NullExceptionCode() {
        // Given
        String exceptionCode = null;
        LanguageCode languageCode = LanguageCode.EN;

        // When & Then
        assertThrows(NullPointerException.class, 
                () -> chain.processRequest(exceptionCode, languageCode));
    }

    @Test
    @DisplayName("Should throw exception when language code is null")
    void processRequest_NullLanguageCode() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = null;

        // When & Then
        assertThrows(NullPointerException.class, 
                () -> chain.processRequest(exceptionCode, languageCode));
    }

    @Test
    @DisplayName("Should handle empty strategy list")
    void processRequest_EmptyStrategyList() {
        // Given
        List<MessageRetrievalStrategy> emptyStrategies = Collections.emptyList();
        MessageRetrievalChain emptyChain = new MessageRetrievalChain(emptyStrategies, cacheStrategy);

        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;

        // When
        String result = emptyChain.processRequest(exceptionCode, languageCode);

        // Then
        assertNull(result);
        verifyNoInteractions(cacheStrategy);
    }

    @Test
    @DisplayName("Should return chain info with correct priority order")
    void getChainInfo_ReturnsCorrectOrder() {
        // When
        List<String> chainInfo = chain.getChainInfo();

        // Then
        assertEquals(3, chainInfo.size());
        assertEquals("Priority 1: High Priority Strategy", chainInfo.get(0));
        assertEquals("Priority 5: Medium Priority Strategy", chainInfo.get(1));
        assertEquals("Priority 10: Low Priority Strategy", chainInfo.get(2));
    }

    @Test
    @DisplayName("Should handle empty message gracefully")
    void processRequest_EmptyMessage() {
        // Given
        String exceptionCode = "EMPTY_ERROR";
        LanguageCode languageCode = LanguageCode.PT;
        String emptyMessage = "";

        when(highPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);
        when(highPriorityStrategy.retrieveMessage(exceptionCode, languageCode)).thenReturn(emptyMessage);

        // When
        String result = chain.processRequest(exceptionCode, languageCode);

        // Then
        assertNull(result); // Empty message should be treated as null
        verify(highPriorityStrategy).retrieveMessage(exceptionCode, languageCode);
        verifyNoInteractions(cacheStrategy);
    }

    @Test
    @DisplayName("Should handle whitespace-only message gracefully")
    void processRequest_WhitespaceMessage() {
        // Given
        String exceptionCode = "WHITESPACE_ERROR";
        LanguageCode languageCode = LanguageCode.RU;
        String whitespaceMessage = "   ";

        when(highPriorityStrategy.canHandle(exceptionCode, languageCode)).thenReturn(true);
        when(highPriorityStrategy.retrieveMessage(exceptionCode, languageCode)).thenReturn(whitespaceMessage);

        // When
        String result = chain.processRequest(exceptionCode, languageCode);

        // Then
        assertNull(result); // Whitespace-only message should be treated as null
        verify(highPriorityStrategy).retrieveMessage(exceptionCode, languageCode);
        verifyNoInteractions(cacheStrategy);
    }
}
