package com.lookforx.exceptionservice.integration;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for ExceptionRepository with PostgreSQL using TestContainers.
 * Tests repository layer with real database interactions.
 */
@DataJpaTest
@Testcontainers
@ActiveProfiles("test")
@DisplayName("Exception Repository Integration Tests")
class ExceptionRepositoryIntegrationTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("exception_repo_test")
            .withUsername("test")
            .withPassword("test");

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
    }

    @Autowired
    private ExceptionRepository exceptionRepository;

    @BeforeEach
    void setUp() {
        exceptionRepository.deleteAll();
    }

    @Test
    @DisplayName("Should save and find exception by code")
    void saveAndFindByExceptionCode_Success() {
        // Given
        ExceptionEntity entity = new ExceptionEntity();
        entity.setExceptionCode("USER_NOT_FOUND");
        entity.setTranslations(Map.of(
                LanguageCode.EN, "User not found",
                LanguageCode.TR, "Kullanıcı bulunamadı"
        ));
        entity.setHttpStatus(HttpStatus.NOT_FOUND);

        // When
        ExceptionEntity saved = exceptionRepository.save(entity);
        Optional<ExceptionEntity> found = exceptionRepository.findByExceptionCode("USER_NOT_FOUND");

        // Then
        assertNotNull(saved.getId());
        assertTrue(found.isPresent());
        assertEquals("USER_NOT_FOUND", found.get().getExceptionCode());
        assertEquals(2, found.get().getTranslations().size());
        assertEquals(HttpStatus.NOT_FOUND, found.get().getHttpStatus());
    }

    @Test
    @DisplayName("Should find message by exception code and language code")
    void findMessageByExceptionCodeAndLanguageCode_Success() {
        // Given
        ExceptionEntity entity = new ExceptionEntity();
        entity.setExceptionCode("VALIDATION_ERROR");
        entity.setTranslations(Map.of(
                LanguageCode.EN, "Validation failed",
                LanguageCode.TR, "Doğrulama başarısız",
                LanguageCode.DE, "Validierung fehlgeschlagen"
        ));
        entity.setHttpStatus(HttpStatus.BAD_REQUEST);

        exceptionRepository.save(entity);

        // When
        Optional<String> englishMessage = exceptionRepository
                .findMessageByExceptionCodeAndLanguageCode("VALIDATION_ERROR", LanguageCode.EN);
        Optional<String> turkishMessage = exceptionRepository
                .findMessageByExceptionCodeAndLanguageCode("VALIDATION_ERROR", LanguageCode.TR);
        Optional<String> germanMessage = exceptionRepository
                .findMessageByExceptionCodeAndLanguageCode("VALIDATION_ERROR", LanguageCode.DE);
        Optional<String> frenchMessage = exceptionRepository
                .findMessageByExceptionCodeAndLanguageCode("VALIDATION_ERROR", LanguageCode.FR);

        // Then
        assertTrue(englishMessage.isPresent());
        assertEquals("Validation failed", englishMessage.get());

        assertTrue(turkishMessage.isPresent());
        assertEquals("Doğrulama başarısız", turkishMessage.get());

        assertTrue(germanMessage.isPresent());
        assertEquals("Validierung fehlgeschlagen", germanMessage.get());

        assertFalse(frenchMessage.isPresent());
    }

    @Test
    @DisplayName("Should find message with fallback to English")
    void findMessageWithFallback_Success() {
        // Given
        ExceptionEntity entity = new ExceptionEntity();
        entity.setExceptionCode("ACCESS_DENIED");
        entity.setTranslations(Map.of(
                LanguageCode.EN, "Access denied",
                LanguageCode.TR, "Erişim reddedildi"
        ));
        entity.setHttpStatus(HttpStatus.FORBIDDEN);

        exceptionRepository.save(entity);

        // When - Request existing language
        Optional<String> turkishMessage = exceptionRepository
                .findMessageWithFallback("ACCESS_DENIED", LanguageCode.TR, LanguageCode.EN);

        // When - Request non-existing language (should fallback to English)
        Optional<String> frenchMessage = exceptionRepository
                .findMessageWithFallback("ACCESS_DENIED", LanguageCode.FR, LanguageCode.EN);

        // When - Request non-existing exception
        Optional<String> nonExistingMessage = exceptionRepository
                .findMessageWithFallback("NON_EXISTING", LanguageCode.EN, LanguageCode.EN);

        // Then
        assertTrue(turkishMessage.isPresent());
        assertEquals("Erişim reddedildi", turkishMessage.get());

        assertTrue(frenchMessage.isPresent());
        assertEquals("Access denied", frenchMessage.get()); // Fallback to English

        assertFalse(nonExistingMessage.isPresent());
    }

    @Test
    @DisplayName("Should find exceptions by code containing ignore case with pagination")
    void findByExceptionCodeContainingIgnoreCase_Success() {
        // Given
        createTestException("USER_NOT_FOUND", "User not found");
        createTestException("USER_ALREADY_EXISTS", "User already exists");
        createTestException("VALIDATION_ERROR", "Validation failed");
        createTestException("ACCESS_DENIED", "Access denied");

        Pageable pageable = PageRequest.of(0, 10);

        // When - Search for "USER"
        Page<ExceptionEntity> userExceptions = exceptionRepository
                .findByExceptionCodeContainingIgnoreCase("USER", pageable);

        // When - Search for "error" (case insensitive)
        Page<ExceptionEntity> errorExceptions = exceptionRepository
                .findByExceptionCodeContainingIgnoreCase("error", pageable);

        // When - Search for non-existing pattern
        Page<ExceptionEntity> nonExisting = exceptionRepository
                .findByExceptionCodeContainingIgnoreCase("NONEXISTING", pageable);

        // Then
        assertEquals(2, userExceptions.getTotalElements());
        assertTrue(userExceptions.getContent().stream()
                .allMatch(entity -> entity.getExceptionCode().contains("USER")));

        assertEquals(1, errorExceptions.getTotalElements());
        assertEquals("VALIDATION_ERROR", errorExceptions.getContent().get(0).getExceptionCode());

        assertEquals(0, nonExisting.getTotalElements());
    }

    @Test
    @DisplayName("Should handle pagination correctly")
    void pagination_Success() {
        // Given - Create 15 test exceptions
        for (int i = 1; i <= 15; i++) {
            createTestException("ERROR_" + String.format("%02d", i), "Error message " + i);
        }

        // When - Request first page (5 items)
        Pageable firstPage = PageRequest.of(0, 5);
        Page<ExceptionEntity> firstResult = exceptionRepository.findAll(firstPage);

        // When - Request second page (5 items)
        Pageable secondPage = PageRequest.of(1, 5);
        Page<ExceptionEntity> secondResult = exceptionRepository.findAll(secondPage);

        // When - Request third page (5 items)
        Pageable thirdPage = PageRequest.of(2, 5);
        Page<ExceptionEntity> thirdResult = exceptionRepository.findAll(thirdPage);

        // Then
        assertEquals(15, firstResult.getTotalElements());
        assertEquals(3, firstResult.getTotalPages());
        assertEquals(5, firstResult.getContent().size());

        assertEquals(15, secondResult.getTotalElements());
        assertEquals(5, secondResult.getContent().size());

        assertEquals(15, thirdResult.getTotalElements());
        assertEquals(5, thirdResult.getContent().size());

        // Verify no overlap between pages
        assertNotEquals(firstResult.getContent().get(0).getId(), 
                       secondResult.getContent().get(0).getId());
    }

    private void createTestException(String code, String message) {
        ExceptionEntity entity = new ExceptionEntity();
        entity.setExceptionCode(code);
        entity.setTranslations(Map.of(LanguageCode.EN, message));
        entity.setHttpStatus(HttpStatus.BAD_REQUEST);
        exceptionRepository.save(entity);
    }
}
