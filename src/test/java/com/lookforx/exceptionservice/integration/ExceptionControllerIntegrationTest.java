package com.lookforx.exceptionservice.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for ExceptionController with PostgreSQL using TestContainers.
 * Tests full HTTP request/response cycle with real database interactions.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Testcontainers
@ActiveProfiles("test")
@Transactional
@DisplayName("Exception Controller Integration Tests")
class ExceptionControllerIntegrationTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("exception_controller_test")
            .withUsername("test")
            .withPassword("test");

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
    }

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ExceptionRepository exceptionRepository;

    private static final String BASE_URL = "/api/v1/exceptions";

    @BeforeEach
    void setUp() {
        exceptionRepository.deleteAll();
    }

    @Test
    @DisplayName("Should create exception via POST request")
    void createException_Success() throws Exception {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("USER_NOT_FOUND")
                .messages(Map.of(
                        LanguageCode.EN, "User not found",
                        LanguageCode.TR, "Kullanıcı bulunamadı"
                ))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        // When & Then
        mockMvc.perform(post(BASE_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.exceptionCode").value("USER_NOT_FOUND"))
                .andExpect(jsonPath("$.httpStatus").value("NOT_FOUND"))
                .andExpect(jsonPath("$.messages.EN").value("User not found"))
                .andExpect(jsonPath("$.messages.TR").value("Kullanıcı bulunamadı"));
    }

    @Test
    @DisplayName("Should get all exceptions via GET request")
    void getAllExceptions_Success() throws Exception {
        // Given - Create test exceptions
        createTestException("ERROR_001", "First error");
        createTestException("ERROR_002", "Second error");

        // When & Then
        mockMvc.perform(get(BASE_URL).param("unpaged", "true"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.length()").value(2))
                .andExpect(jsonPath("$[0].exceptionCode").exists())
                .andExpect(jsonPath("$[1].exceptionCode").exists());
    }

    @Test
    @DisplayName("Should get paginated exceptions via GET request")
    void getAllExceptions_WithPagination_Success() throws Exception {
        // Given - Create test exceptions
        for (int i = 1; i <= 15; i++) {
            createTestException("ERROR_" + String.format("%03d", i), "Error " + i);
        }

        // When & Then - First page
        mockMvc.perform(get(BASE_URL)
                        .param("page", "0")
                        .param("size", "5"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.content.length()").value(5))
                .andExpect(jsonPath("$.totalElements").value(15))
                .andExpect(jsonPath("$.totalPages").value(3))
                .andExpect(jsonPath("$.first").value(true))
                .andExpect(jsonPath("$.last").value(false));

        // When & Then - Last page
        mockMvc.perform(get(BASE_URL)
                        .param("page", "2")
                        .param("size", "5"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content.length()").value(5))
                .andExpect(jsonPath("$.first").value(false))
                .andExpect(jsonPath("$.last").value(true));
    }

    @Test
    @DisplayName("Should search exceptions via GET request with search parameter")
    void getAllExceptions_WithSearch_Success() throws Exception {
        // Given
        createTestException("USER_NOT_FOUND", "User not found");
        createTestException("USER_ALREADY_EXISTS", "User already exists");
        createTestException("VALIDATION_ERROR", "Validation failed");

        // When & Then - Search for "USER"
        mockMvc.perform(get(BASE_URL)
                        .param("search", "USER")
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.content.length()").value(2))
                .andExpect(jsonPath("$.totalElements").value(2));
    }

    @Test
    @DisplayName("Should get exception by ID via GET request")
    void getExceptionById_Success() throws Exception {
        // Given
        Long exceptionId = createTestException("ACCESS_DENIED", "Access denied");

        // When & Then
        mockMvc.perform(get(BASE_URL + "/" + exceptionId))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(exceptionId))
                .andExpect(jsonPath("$.exceptionCode").value("ACCESS_DENIED"))
                .andExpect(jsonPath("$.messages.EN").value("Access denied"));
    }

    @Test
    @DisplayName("Should update exception via PUT request")
    void updateException_Success() throws Exception {
        // Given
        Long exceptionId = createTestException("OLD_ERROR", "Old message");

        ExceptionRequest updateRequest = ExceptionRequest.builder()
                .exceptionCode("UPDATED_ERROR")
                .messages(Map.of(
                        LanguageCode.EN, "Updated message",
                        LanguageCode.TR, "Güncellenmiş mesaj"
                ))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        // When & Then
        mockMvc.perform(put(BASE_URL + "/" + exceptionId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(exceptionId))
                .andExpect(jsonPath("$.messages.EN").value("Updated message"))
                .andExpect(jsonPath("$.messages.TR").value("Güncellenmiş mesaj"))
                .andExpect(jsonPath("$.httpStatus").value("BAD_REQUEST"));
    }

    @Test
    @DisplayName("Should delete exception via DELETE request")
    void deleteException_Success() throws Exception {
        // Given
        Long exceptionId = createTestException("TO_DELETE", "To be deleted");

        // When & Then
        mockMvc.perform(delete(BASE_URL + "/" + exceptionId))
                .andExpect(status().isNoContent());

        // Verify deletion
        mockMvc.perform(get(BASE_URL + "/" + exceptionId))
                .andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("Should get localized exception message via GET request")
    void getExceptionMessage_Success() throws Exception {
        // Given
        createTestException("VALIDATION_ERROR", Map.of(
                LanguageCode.EN, "Validation failed",
                LanguageCode.TR, "Doğrulama başarısız",
                LanguageCode.DE, "Validierung fehlgeschlagen"
        ));

        // When & Then - English message
        mockMvc.perform(get(BASE_URL + "/exception-message")
                        .param("exceptionCode", "VALIDATION_ERROR")
                        .param("languageCode", "EN"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.exceptionCode").value("VALIDATION_ERROR"))
                .andExpect(jsonPath("$.languageCode").value("EN"))
                .andExpect(jsonPath("$.message").value("Validation failed"));

        // When & Then - Turkish message
        mockMvc.perform(get(BASE_URL + "/exception-message")
                        .param("exceptionCode", "VALIDATION_ERROR")
                        .param("languageCode", "TR"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.languageCode").value("TR"))
                .andExpect(jsonPath("$.message").value("Doğrulama başarısız"));

        // When & Then - Fallback to English for non-existing language
        mockMvc.perform(get(BASE_URL + "/exception-message")
                        .param("exceptionCode", "VALIDATION_ERROR")
                        .param("languageCode", "FR"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.languageCode").value("FR"))
                .andExpect(jsonPath("$.message").value("Validation failed"));
    }

    @Test
    @DisplayName("Should return 404 for non-existing exception")
    void getExceptionById_NotFound() throws Exception {
        // When & Then
        mockMvc.perform(get(BASE_URL + "/999"))
                .andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("Should return 400 for invalid request")
    void createException_InvalidRequest() throws Exception {
        // Given - Invalid request (missing required fields)
        ExceptionRequest invalidRequest = ExceptionRequest.builder()
                .exceptionCode("") // Empty code
                .messages(Map.of())
                .build();

        // When & Then
        mockMvc.perform(post(BASE_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }

    private Long createTestException(String code, String message) {
        return createTestException(code, Map.of(LanguageCode.EN, message));
    }

    private Long createTestException(String code, Map<LanguageCode, String> messages) {
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode(code)
                .messages(messages)
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        try {
            String response = mockMvc.perform(post(BASE_URL)
                            .contentType(MediaType.APPLICATION_JSON)
                            .content(objectMapper.writeValueAsString(request)))
                    .andExpect(status().isCreated())
                    .andReturn()
                    .getResponse()
                    .getContentAsString();

            return objectMapper.readTree(response).get("id").asLong();
        } catch (Exception e) {
            throw new RuntimeException("Failed to create test exception", e);
        }
    }
}
