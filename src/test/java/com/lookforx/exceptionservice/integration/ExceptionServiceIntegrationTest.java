package com.lookforx.exceptionservice.integration;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import com.lookforx.exceptionservice.service.ExceptionManagementService;
import com.lookforx.exceptionservice.service.ExceptionMessageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for Exception Service with PostgreSQL using TestContainers.
 * Tests SOLID principles implementation with real database interactions.
 */
@SpringBootTest
@Testcontainers
@ActiveProfiles("test")
@Transactional
@DisplayName("Exception Service Integration Tests")
class ExceptionServiceIntegrationTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>("postgres:15-alpine")
            .withDatabaseName("exception_service_test")
            .withUsername("test")
            .withPassword("test");

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "create-drop");
    }

    @Autowired
    private ExceptionMessageService messageService;

    @Autowired
    private ExceptionManagementService managementService;

    @Autowired
    private ExceptionRepository exceptionRepository;

    @BeforeEach
    void setUp() {
        exceptionRepository.deleteAll();
    }

    @Test
    @DisplayName("Should create and retrieve exception with localized messages")
    void createAndRetrieveException_Success() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("USER_NOT_FOUND")
                .messages(Map.of(
                        LanguageCode.EN, "User not found",
                        LanguageCode.TR, "Kullanıcı bulunamadı",
                        LanguageCode.DE, "Benutzer nicht gefunden"
                ))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        // When - Create exception
        ExceptionResponse createdResponse = managementService.createException(request);

        // Then - Verify creation
        assertNotNull(createdResponse);
        assertNotNull(createdResponse.getId());
        assertEquals("USER_NOT_FOUND", createdResponse.getExceptionCode());
        assertEquals(HttpStatus.NOT_FOUND, createdResponse.getHttpStatus());
        assertEquals(3, createdResponse.getMessages().size());

        // When - Retrieve messages in different languages
        String englishMessage = messageService.retrieveLocalizedMessage("USER_NOT_FOUND", LanguageCode.EN);
        String turkishMessage = messageService.retrieveLocalizedMessage("USER_NOT_FOUND", LanguageCode.TR);
        String germanMessage = messageService.retrieveLocalizedMessage("USER_NOT_FOUND", LanguageCode.DE);

        // Then - Verify localized messages
        assertEquals("User not found", englishMessage);
        assertEquals("Kullanıcı bulunamadı", turkishMessage);
        assertEquals("Benutzer nicht gefunden", germanMessage);
    }

    @Test
    @DisplayName("Should fallback to English when requested language not available")
    void retrieveMessage_FallbackToEnglish() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("VALIDATION_ERROR")
                .messages(Map.of(
                        LanguageCode.EN, "Validation failed",
                        LanguageCode.TR, "Doğrulama başarısız"
                ))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        managementService.createException(request);

        // When - Request message in language not available (French)
        String message = messageService.retrieveLocalizedMessage("VALIDATION_ERROR", LanguageCode.FR);

        // Then - Should return English fallback
        assertEquals("Validation failed", message);
    }

    @Test
    @DisplayName("Should return default message when no translations available")
    void retrieveMessage_DefaultMessage() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("UNKNOWN_ERROR")
                .messages(Map.of()) // No translations
                .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .build();

        managementService.createException(request);

        // When - Request message
        String message = messageService.retrieveLocalizedMessage("UNKNOWN_ERROR", LanguageCode.TR);

        // Then - Should return default Turkish message
        assertNotNull(message);
        assertTrue(message.contains("hata")); // Turkish default message
    }

    @Test
    @DisplayName("Should update exception successfully")
    void updateException_Success() {
        // Given - Create initial exception
        ExceptionRequest initialRequest = ExceptionRequest.builder()
                .exceptionCode("INITIAL_ERROR")
                .messages(Map.of(LanguageCode.EN, "Initial message"))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        ExceptionResponse created = managementService.createException(initialRequest);

        // When - Update exception
        ExceptionRequest updateRequest = ExceptionRequest.builder()
                .exceptionCode("UPDATED_ERROR")
                .messages(Map.of(
                        LanguageCode.EN, "Updated message",
                        LanguageCode.TR, "Güncellenmiş mesaj"
                ))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        ExceptionResponse updated = managementService.updateException(created.getId(), updateRequest);

        // Then - Verify update
        assertEquals(created.getId(), updated.getId());
        assertEquals(2, updated.getMessages().size());
        assertEquals(HttpStatus.NOT_FOUND, updated.getHttpStatus());

        // Verify messages are updated
        String englishMessage = messageService.retrieveLocalizedMessage("UPDATED_ERROR", LanguageCode.EN);
        String turkishMessage = messageService.retrieveLocalizedMessage("UPDATED_ERROR", LanguageCode.TR);

        assertEquals("Updated message", englishMessage);
        assertEquals("Güncellenmiş mesaj", turkishMessage);
    }

    @Test
    @DisplayName("Should delete exception successfully")
    void deleteException_Success() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("TO_DELETE")
                .messages(Map.of(LanguageCode.EN, "To be deleted"))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        ExceptionResponse created = managementService.createException(request);

        // When
        managementService.deleteException(created.getId());

        // Then - Verify deletion
        assertFalse(exceptionRepository.existsById(created.getId()));
    }

    @Test
    @DisplayName("Should retrieve paginated exceptions with search")
    void getAllExceptions_WithPaginationAndSearch() {
        // Given - Create multiple exceptions
        createTestException("USER_NOT_FOUND", "User not found");
        createTestException("USER_ALREADY_EXISTS", "User already exists");
        createTestException("VALIDATION_ERROR", "Validation failed");

        // When - Search for exceptions containing "USER"
        Pageable pageable = PageRequest.of(0, 10);
        Page<ExceptionResponse> result = managementService.getAllExceptions(pageable, "USER");

        // Then
        assertEquals(2, result.getTotalElements());
        assertTrue(result.getContent().stream()
                .allMatch(response -> response.getExceptionCode().contains("USER")));
    }

    @Test
    @DisplayName("Should handle concurrent access correctly")
    void concurrentAccess_Success() throws InterruptedException {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("CONCURRENT_TEST")
                .messages(Map.of(LanguageCode.EN, "Concurrent test"))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        ExceptionResponse created = managementService.createException(request);

        // When - Multiple threads access the same exception
        Thread[] threads = new Thread[5];
        String[] results = new String[5];

        for (int i = 0; i < 5; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                results[index] = messageService.retrieveLocalizedMessage("CONCURRENT_TEST", LanguageCode.EN);
            });
            threads[i].start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join();
        }

        // Then - All threads should get the same result
        for (String result : results) {
            assertEquals("Concurrent test", result);
        }
    }

    private void createTestException(String code, String message) {
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode(code)
                .messages(Map.of(LanguageCode.EN, message))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();
        managementService.createException(request);
    }
}
