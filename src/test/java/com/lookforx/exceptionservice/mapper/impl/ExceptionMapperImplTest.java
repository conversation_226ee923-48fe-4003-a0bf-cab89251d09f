package com.lookforx.exceptionservice.mapper.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ExceptionMapperImpl.
 * Tests SOLID principles implementation with focused mapping responsibilities.
 */
@DisplayName("ExceptionMapperImpl Unit Tests")
class ExceptionMapperImplTest {

    private ExceptionMapperImpl mapper;
    
    @BeforeEach
    void setUp() {
        mapper = new ExceptionMapperImpl();
    }
    
    @Test
    @DisplayName("Should map entity to response successfully")
    void toResponse_Success() {
        // Given
        ExceptionEntity entity = new ExceptionEntity();
        entity.setId(1L);
        entity.setExceptionCode("USER_NOT_FOUND");
        entity.setTranslations(Map.of(
                LanguageCode.EN, "User not found",
                LanguageCode.TR, "Kullanıcı bulunamadı"
        ));
        entity.setHttpStatus(HttpStatus.NOT_FOUND);
        entity.setCreatedAt(LocalDateTime.now());
        entity.setUpdatedAt(LocalDateTime.now());
        
        // When
        ExceptionResponse response = mapper.toResponse(entity);
        
        // Then
        assertNotNull(response);
        assertEquals(entity.getId(), response.getId());
        assertEquals(entity.getExceptionCode(), response.getExceptionCode());
        assertEquals(entity.getTranslations().size(), response.getMessages().size());
        assertEquals(entity.getHttpStatus(), response.getHttpStatus());
        assertEquals(entity.getCreatedAt(), response.getCreatedAt());
        assertEquals(entity.getUpdatedAt(), response.getUpdatedAt());
        
        // Verify defensive copying
        assertNotSame(entity.getTranslations(), response.getMessages());
    }
    
    @Test
    @DisplayName("Should handle null HTTP status with default")
    void toResponse_NullHttpStatus() {
        // Given
        ExceptionEntity entity = new ExceptionEntity();
        entity.setId(1L);
        entity.setExceptionCode("TEST_ERROR");
        entity.setTranslations(Map.of(LanguageCode.EN, "Test error"));
        entity.setHttpStatus(null);
        
        // When
        ExceptionResponse response = mapper.toResponse(entity);
        
        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getHttpStatus());
    }
    
    @Test
    @DisplayName("Should throw exception for null entity")
    void toResponse_NullEntity() {
        // When & Then
        NullPointerException exception = assertThrows(NullPointerException.class,
                () -> mapper.toResponse(null));

        assertEquals("Exception entity cannot be null", exception.getMessage());
    }
    
    @Test
    @DisplayName("Should map request to entity successfully")
    void toEntity_Success() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("  validation_error  ")
                .messages(Map.of(
                        LanguageCode.EN, "Validation failed",
                        LanguageCode.TR, "Doğrulama başarısız"
                ))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();
        
        // When
        ExceptionEntity entity = mapper.toEntity(request);
        
        // Then
        assertNotNull(entity);
        assertEquals("VALİDATİON_ERROR", entity.getExceptionCode()); // Normalized (Turkish characters preserved)
        assertEquals(request.getMessages().size(), entity.getTranslations().size());
        assertEquals(request.getHttpStatus(), entity.getHttpStatus());
        
        // Verify defensive copying
        assertNotSame(request.getMessages(), entity.getTranslations());
    }
    
    @Test
    @DisplayName("Should handle null HTTP status with default in entity mapping")
    void toEntity_NullHttpStatus() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("TEST_ERROR")
                .messages(Map.of(LanguageCode.EN, "Test error"))
                .httpStatus(null)
                .build();
        
        // When
        ExceptionEntity entity = mapper.toEntity(request);
        
        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, entity.getHttpStatus());
    }
    
    @Test
    @DisplayName("Should throw exception for null request")
    void toEntity_NullRequest() {
        // When & Then
        NullPointerException exception = assertThrows(NullPointerException.class,
                () -> mapper.toEntity(null));

        assertEquals("Exception request cannot be null", exception.getMessage());
    }
    
    @Test
    @DisplayName("Should update entity successfully")
    void updateEntity_Success() {
        // Given
        ExceptionEntity existingEntity = new ExceptionEntity();
        existingEntity.setId(1L);
        existingEntity.setExceptionCode("OLD_CODE");
        existingEntity.setTranslations(Map.of(LanguageCode.EN, "Old message"));
        existingEntity.setHttpStatus(HttpStatus.BAD_REQUEST);
        existingEntity.setCreatedAt(LocalDateTime.now().minusDays(1));
        existingEntity.setUpdatedAt(LocalDateTime.now().minusDays(1));
        
        ExceptionRequest updateRequest = ExceptionRequest.builder()
                .exceptionCode("NEW_CODE")
                .messages(Map.of(
                        LanguageCode.EN, "New message",
                        LanguageCode.TR, "Yeni mesaj"
                ))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();
        
        LocalDateTime beforeUpdate = LocalDateTime.now();
        
        // When
        ExceptionEntity updatedEntity = mapper.updateEntity(existingEntity, updateRequest);
        
        // Then
        assertSame(existingEntity, updatedEntity); // Same instance
        assertEquals(updateRequest.getMessages().size(), updatedEntity.getTranslations().size());
        assertEquals(updateRequest.getHttpStatus(), updatedEntity.getHttpStatus());
        // updatedAt is managed by JPA @PreUpdate, so we just verify it exists
        assertNotNull(updatedEntity.getUpdatedAt());
        
        // Verify defensive copying
        assertNotSame(updateRequest.getMessages(), updatedEntity.getTranslations());
    }
    
    @Test
    @DisplayName("Should throw exception for null entity in update")
    void updateEntity_NullEntity() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("TEST")
                .messages(Map.of(LanguageCode.EN, "Test"))
                .build();
        
        // When & Then
        NullPointerException exception = assertThrows(NullPointerException.class,
                () -> mapper.updateEntity(null, request));

        assertEquals("Exception entity cannot be null", exception.getMessage());
    }
    
    @Test
    @DisplayName("Should throw exception for null request in update")
    void updateEntity_NullRequest() {
        // Given
        ExceptionEntity entity = new ExceptionEntity();
        
        // When & Then
        NullPointerException exception = assertThrows(NullPointerException.class,
                () -> mapper.updateEntity(entity, null));

        assertEquals("Exception request cannot be null", exception.getMessage());
    }
    
    @Test
    @DisplayName("Should handle null translations gracefully")
    void toEntity_NullTranslations() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("TEST_ERROR")
                .messages(null)
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();
        
        // When
        ExceptionEntity entity = mapper.toEntity(request);
        
        // Then
        assertNotNull(entity.getTranslations());
        assertTrue(entity.getTranslations().isEmpty());
    }
    
    @Test
    @DisplayName("Should filter out null and empty translations")
    void createDefensiveCopyOfTranslations_FilterInvalid() {
        // Given
        Map<LanguageCode, String> originalTranslations = new HashMap<>();
        originalTranslations.put(LanguageCode.EN, "Valid message");
        originalTranslations.put(LanguageCode.TR, null);
        originalTranslations.put(LanguageCode.DE, "");
        originalTranslations.put(LanguageCode.FR, "   ");
        originalTranslations.put(null, "Invalid key");
        
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("TEST")
                .messages(originalTranslations)
                .build();
        
        // When
        ExceptionEntity entity = mapper.toEntity(request);
        
        // Then
        assertEquals(1, entity.getTranslations().size());
        assertTrue(entity.getTranslations().containsKey(LanguageCode.EN));
        assertEquals("Valid message", entity.getTranslations().get(LanguageCode.EN));
    }
    
    @Test
    @DisplayName("Should trim translation values")
    void createDefensiveCopyOfTranslations_TrimValues() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("TEST")
                .messages(Map.of(LanguageCode.EN, "  Trimmed message  "))
                .build();
        
        // When
        ExceptionEntity entity = mapper.toEntity(request);
        
        // Then
        assertEquals("Trimmed message", entity.getTranslations().get(LanguageCode.EN));
    }
}
