package com.lookforx.exceptionservice.template.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.domain.factory.ExceptionEntityFactory;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.exception.ExceptionNotFoundException;
import com.lookforx.exceptionservice.observer.ExceptionEvent;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import com.lookforx.exceptionservice.service.ExceptionValidationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for UpdateExceptionProcessor.
 * Tests the Template Method Pattern implementation for exception updates.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Update Exception Processor Tests")
class UpdateExceptionProcessorTest {

    @Mock
    private ExceptionValidationService validationService;

    @Mock
    private ExceptionEntityFactory entityFactory;

    @Mock
    private ExceptionRepository exceptionRepository;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    private UpdateExceptionProcessor processor;

    @BeforeEach
    void setUp() {
        processor = new UpdateExceptionProcessor(validationService, entityFactory, 
                                               exceptionRepository, eventPublisher);
    }

    @Test
    @DisplayName("Should process exception update successfully")
    void processException_Success() {
        // Given
        Long entityId = 1L;
        processor.setEntityId(entityId);

        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "Updated user not found");
        messages.put(LanguageCode.TR, "Güncellenmiş kullanıcı bulunamadı");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("USER_NOT_FOUND_UPDATED")
                .messages(messages)
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        ExceptionEntity existingEntity = new ExceptionEntity();
        existingEntity.setId(entityId);
        existingEntity.setExceptionCode("USER_NOT_FOUND");

        ExceptionEntity updatedEntity = new ExceptionEntity();
        updatedEntity.setId(entityId);
        updatedEntity.setExceptionCode("USER_NOT_FOUND_UPDATED");

        ExceptionEntity savedEntity = new ExceptionEntity();
        savedEntity.setId(entityId);
        savedEntity.setExceptionCode("USER_NOT_FOUND_UPDATED");
        savedEntity.setTranslations(messages);

        when(exceptionRepository.findById(entityId)).thenReturn(Optional.of(existingEntity));
        when(entityFactory.updateFromRequest(existingEntity, request)).thenReturn(updatedEntity);
        when(exceptionRepository.save(updatedEntity)).thenReturn(savedEntity);

        // When
        ExceptionEntity result = processor.processException(request);

        // Then
        assertNotNull(result);
        assertEquals(entityId, result.getId());
        assertEquals("USER_NOT_FOUND_UPDATED", result.getExceptionCode());

        // Verify template method steps
        verify(validationService).validateExceptionCreationRequest(request);
        verify(exceptionRepository).findById(entityId);
        verify(entityFactory).updateFromRequest(existingEntity, request);
        verify(exceptionRepository).save(updatedEntity);

        // Verify event publishing
        ArgumentCaptor<ExceptionEvent.ExceptionUpdatedEvent> eventCaptor = 
                ArgumentCaptor.forClass(ExceptionEvent.ExceptionUpdatedEvent.class);
        verify(eventPublisher).publishEvent(eventCaptor.capture());
        
        ExceptionEvent.ExceptionUpdatedEvent publishedEvent = eventCaptor.getValue();
        assertEquals(savedEntity, publishedEvent.getExceptionEntity());
        assertEquals("EXCEPTION_UPDATED", publishedEvent.getEventType());
        assertNotNull(publishedEvent.getPreviousState());
    }

    @Test
    @DisplayName("Should throw exception when entity ID is not set")
    void processException_EntityIdNotSet() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("TEST_ERROR")
                .messages(Map.of(LanguageCode.EN, "Test message"))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        // When & Then
        assertThrows(IllegalArgumentException.class, 
                () -> processor.processException(request));

        verifyNoInteractions(validationService);
        verifyNoInteractions(exceptionRepository);
        verifyNoInteractions(entityFactory);
        verifyNoInteractions(eventPublisher);
    }

    @Test
    @DisplayName("Should throw exception when entity not found")
    void processException_EntityNotFound() {
        // Given
        Long entityId = 999L;
        processor.setEntityId(entityId);

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("NOT_FOUND_ERROR")
                .messages(Map.of(LanguageCode.EN, "Not found message"))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        when(exceptionRepository.findById(entityId)).thenReturn(Optional.empty());

        // When & Then
        ExceptionNotFoundException exception = assertThrows(ExceptionNotFoundException.class, 
                () -> processor.processException(request));
        
        assertTrue(exception.getMessage().contains("Exception not found with ID: 999"));

        verify(validationService).validateExceptionCreationRequest(request);
        verify(exceptionRepository).findById(entityId);
        verifyNoInteractions(entityFactory);
        verifyNoInteractions(eventPublisher);
    }

    @Test
    @DisplayName("Should handle validation failure")
    void processException_ValidationFailure() {
        // Given
        Long entityId = 1L;
        processor.setEntityId(entityId);

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("INVALID_CODE")
                .messages(new HashMap<>())
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        doThrow(new IllegalArgumentException("Invalid request"))
                .when(validationService).validateExceptionCreationRequest(request);

        // When & Then
        assertThrows(IllegalArgumentException.class, 
                () -> processor.processException(request));

        verify(validationService).validateExceptionCreationRequest(request);
        verifyNoInteractions(exceptionRepository);
        verifyNoInteractions(entityFactory);
        verifyNoInteractions(eventPublisher);
    }

    @Test
    @DisplayName("Should handle entity factory failure")
    void processException_EntityFactoryFailure() {
        // Given
        Long entityId = 1L;
        processor.setEntityId(entityId);

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("FACTORY_ERROR")
                .messages(Map.of(LanguageCode.EN, "Factory error"))
                .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .build();

        ExceptionEntity existingEntity = new ExceptionEntity();
        existingEntity.setId(entityId);

        when(exceptionRepository.findById(entityId)).thenReturn(Optional.of(existingEntity));
        when(entityFactory.updateFromRequest(existingEntity, request))
                .thenThrow(new RuntimeException("Factory update failed"));

        // When & Then
        assertThrows(RuntimeException.class, 
                () -> processor.processException(request));

        verify(validationService).validateExceptionCreationRequest(request);
        verify(exceptionRepository).findById(entityId);
        verify(entityFactory).updateFromRequest(existingEntity, request);
        verifyNoInteractions(eventPublisher);
    }

    @Test
    @DisplayName("Should handle repository save failure")
    void processException_RepositorySaveFailure() {
        // Given
        Long entityId = 1L;
        processor.setEntityId(entityId);

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("SAVE_ERROR")
                .messages(Map.of(LanguageCode.EN, "Save error"))
                .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .build();

        ExceptionEntity existingEntity = new ExceptionEntity();
        ExceptionEntity updatedEntity = new ExceptionEntity();

        when(exceptionRepository.findById(entityId)).thenReturn(Optional.of(existingEntity));
        when(entityFactory.updateFromRequest(existingEntity, request)).thenReturn(updatedEntity);
        when(exceptionRepository.save(updatedEntity))
                .thenThrow(new RuntimeException("Database save failed"));

        // When & Then
        assertThrows(RuntimeException.class, 
                () -> processor.processException(request));

        verify(validationService).validateExceptionCreationRequest(request);
        verify(exceptionRepository).findById(entityId);
        verify(entityFactory).updateFromRequest(existingEntity, request);
        verify(exceptionRepository).save(updatedEntity);
        verifyNoInteractions(eventPublisher);
    }

    @Test
    @DisplayName("Should handle ExceptionNotFoundException specifically")
    void processException_ExceptionNotFoundHandling() {
        // Given
        Long entityId = 1L;
        processor.setEntityId(entityId);

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("NOT_FOUND_TEST")
                .messages(Map.of(LanguageCode.EN, "Test message"))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        when(exceptionRepository.findById(entityId)).thenReturn(Optional.empty());

        // When & Then
        ExceptionNotFoundException exception = assertThrows(ExceptionNotFoundException.class, 
                () -> processor.processException(request));
        
        assertNotNull(exception);
        assertTrue(exception instanceof RuntimeException);
    }

    @Test
    @DisplayName("Should publish event with previous state")
    void processException_EventPublishingWithPreviousState() {
        // Given
        Long entityId = 1L;
        processor.setEntityId(entityId);

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("EVENT_TEST")
                .messages(Map.of(LanguageCode.EN, "Event test"))
                .httpStatus(HttpStatus.OK)
                .build();

        ExceptionEntity existingEntity = new ExceptionEntity();
        existingEntity.setId(entityId);
        existingEntity.setExceptionCode("OLD_CODE");

        ExceptionEntity updatedEntity = new ExceptionEntity();
        ExceptionEntity savedEntity = new ExceptionEntity();
        savedEntity.setId(entityId);
        savedEntity.setExceptionCode("EVENT_TEST");

        when(exceptionRepository.findById(entityId)).thenReturn(Optional.of(existingEntity));
        when(entityFactory.updateFromRequest(existingEntity, request)).thenReturn(updatedEntity);
        when(exceptionRepository.save(updatedEntity)).thenReturn(savedEntity);

        // When
        processor.processException(request);

        // Then
        ArgumentCaptor<ExceptionEvent.ExceptionUpdatedEvent> eventCaptor = 
                ArgumentCaptor.forClass(ExceptionEvent.ExceptionUpdatedEvent.class);
        verify(eventPublisher).publishEvent(eventCaptor.capture());
        
        ExceptionEvent.ExceptionUpdatedEvent event = eventCaptor.getValue();
        assertNotNull(event);
        assertEquals(savedEntity, event.getExceptionEntity());
        assertEquals("EXCEPTION_UPDATED", event.getEventType());
        assertNotNull(event.getPreviousState());
        assertEquals("OLD_CODE", event.getPreviousState().getExceptionCode());
        assertNotNull(event.getEventTimestamp());
    }

    @Test
    @DisplayName("Should handle null request")
    void processException_NullRequest() {
        // Given
        Long entityId = 1L;
        processor.setEntityId(entityId);

        // When & Then
        assertThrows(NullPointerException.class, 
                () -> processor.processException(null));

        verifyNoInteractions(validationService);
        verifyNoInteractions(exceptionRepository);
        verifyNoInteractions(entityFactory);
        verifyNoInteractions(eventPublisher);
    }
}
