package com.lookforx.exceptionservice.template.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.domain.factory.ExceptionEntityFactory;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.observer.ExceptionEvent;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import com.lookforx.exceptionservice.service.ExceptionValidationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for CreateExceptionProcessor.
 * Tests the Template Method Pattern implementation for exception creation.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Create Exception Processor Tests")
class CreateExceptionProcessorTest {

    @Mock
    private ExceptionValidationService validationService;

    @Mock
    private ExceptionEntityFactory entityFactory;

    @Mock
    private ExceptionRepository exceptionRepository;

    @Mock
    private ApplicationEventPublisher eventPublisher;

    private CreateExceptionProcessor processor;

    @BeforeEach
    void setUp() {
        processor = new CreateExceptionProcessor(validationService, entityFactory, 
                                               exceptionRepository, eventPublisher);
    }

    @Test
    @DisplayName("Should process exception creation successfully")
    void processException_Success() {
        // Given
        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "User not found");
        messages.put(LanguageCode.TR, "Kullanıcı bulunamadı");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("USER_NOT_FOUND")
                .messages(messages)
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        ExceptionEntity createdEntity = new ExceptionEntity();
        createdEntity.setExceptionCode("USER_NOT_FOUND");
        createdEntity.setTranslations(messages);
        createdEntity.setHttpStatus(HttpStatus.NOT_FOUND);

        ExceptionEntity savedEntity = new ExceptionEntity();
        savedEntity.setId(1L);
        savedEntity.setExceptionCode("USER_NOT_FOUND");
        savedEntity.setTranslations(messages);
        savedEntity.setHttpStatus(HttpStatus.NOT_FOUND);

        when(validationService.normalizeExceptionCode("USER_NOT_FOUND")).thenReturn("USER_NOT_FOUND");
        when(entityFactory.createFromRequest(request)).thenReturn(createdEntity);
        when(exceptionRepository.save(createdEntity)).thenReturn(savedEntity);

        // When
        ExceptionEntity result = processor.processException(request);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("USER_NOT_FOUND", result.getExceptionCode());

        // Verify template method steps
        verify(validationService).validateExceptionCreationRequest(request);
        verify(validationService).normalizeExceptionCode("USER_NOT_FOUND");
        verify(validationService).validateExceptionCodeUniqueness("USER_NOT_FOUND");
        verify(entityFactory).createFromRequest(request);
        verify(exceptionRepository).save(createdEntity);

        // Verify event publishing
        ArgumentCaptor<ExceptionEvent.ExceptionCreatedEvent> eventCaptor = 
                ArgumentCaptor.forClass(ExceptionEvent.ExceptionCreatedEvent.class);
        verify(eventPublisher).publishEvent(eventCaptor.capture());
        
        ExceptionEvent.ExceptionCreatedEvent publishedEvent = eventCaptor.getValue();
        assertEquals(savedEntity, publishedEvent.getExceptionEntity());
        assertEquals("EXCEPTION_CREATED", publishedEvent.getEventType());
    }

    @Test
    @DisplayName("Should handle validation failure")
    void processException_ValidationFailure() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("INVALID_CODE")
                .messages(new HashMap<>())
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        doThrow(new IllegalArgumentException("Invalid exception code"))
                .when(validationService).validateExceptionCreationRequest(request);

        // When & Then
        assertThrows(IllegalArgumentException.class, 
                () -> processor.processException(request));

        verify(validationService).validateExceptionCreationRequest(request);
        verifyNoInteractions(entityFactory);
        verifyNoInteractions(exceptionRepository);
        verifyNoInteractions(eventPublisher);
    }

    @Test
    @DisplayName("Should handle uniqueness validation failure")
    void processException_UniquenessValidationFailure() {
        // Given
        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "Duplicate error");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("DUPLICATE_ERROR")
                .messages(messages)
                .httpStatus(HttpStatus.CONFLICT)
                .build();

        when(validationService.normalizeExceptionCode("DUPLICATE_ERROR")).thenReturn("DUPLICATE_ERROR");
        doThrow(new IllegalArgumentException("Exception code already exists"))
                .when(validationService).validateExceptionCodeUniqueness("DUPLICATE_ERROR");

        // When & Then
        assertThrows(IllegalArgumentException.class, 
                () -> processor.processException(request));

        verify(validationService).validateExceptionCreationRequest(request);
        verify(validationService).normalizeExceptionCode("DUPLICATE_ERROR");
        verify(validationService).validateExceptionCodeUniqueness("DUPLICATE_ERROR");
        verifyNoInteractions(entityFactory);
        verifyNoInteractions(exceptionRepository);
        verifyNoInteractions(eventPublisher);
    }

    @Test
    @DisplayName("Should handle entity factory failure")
    void processException_EntityFactoryFailure() {
        // Given
        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "Factory error");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("FACTORY_ERROR")
                .messages(messages)
                .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .build();

        when(validationService.normalizeExceptionCode("FACTORY_ERROR")).thenReturn("FACTORY_ERROR");
        when(entityFactory.createFromRequest(request))
                .thenThrow(new RuntimeException("Factory creation failed"));

        // When & Then
        assertThrows(RuntimeException.class, 
                () -> processor.processException(request));

        verify(validationService).validateExceptionCreationRequest(request);
        verify(validationService).normalizeExceptionCode("FACTORY_ERROR");
        verify(validationService).validateExceptionCodeUniqueness("FACTORY_ERROR");
        verify(entityFactory).createFromRequest(request);
        verifyNoInteractions(exceptionRepository);
        verifyNoInteractions(eventPublisher);
    }

    @Test
    @DisplayName("Should handle repository save failure")
    void processException_RepositorySaveFailure() {
        // Given
        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "Repository error");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("REPOSITORY_ERROR")
                .messages(messages)
                .httpStatus(HttpStatus.INTERNAL_SERVER_ERROR)
                .build();

        ExceptionEntity createdEntity = new ExceptionEntity();
        createdEntity.setExceptionCode("REPOSITORY_ERROR");

        when(validationService.normalizeExceptionCode("REPOSITORY_ERROR")).thenReturn("REPOSITORY_ERROR");
        when(entityFactory.createFromRequest(request)).thenReturn(createdEntity);
        when(exceptionRepository.save(createdEntity))
                .thenThrow(new RuntimeException("Database save failed"));

        // When & Then
        assertThrows(RuntimeException.class, 
                () -> processor.processException(request));

        verify(validationService).validateExceptionCreationRequest(request);
        verify(validationService).normalizeExceptionCode("REPOSITORY_ERROR");
        verify(validationService).validateExceptionCodeUniqueness("REPOSITORY_ERROR");
        verify(entityFactory).createFromRequest(request);
        verify(exceptionRepository).save(createdEntity);
        verifyNoInteractions(eventPublisher);
    }

    @Test
    @DisplayName("Should handle data integrity violation")
    void processException_DataIntegrityViolation() {
        // Given
        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "Integrity error");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("INTEGRITY_ERROR")
                .messages(messages)
                .httpStatus(HttpStatus.CONFLICT)
                .build();

        ExceptionEntity createdEntity = new ExceptionEntity();
        createdEntity.setExceptionCode("INTEGRITY_ERROR");

        when(validationService.normalizeExceptionCode("INTEGRITY_ERROR")).thenReturn("INTEGRITY_ERROR");
        when(entityFactory.createFromRequest(request)).thenReturn(createdEntity);
        when(exceptionRepository.save(createdEntity))
                .thenThrow(new org.springframework.dao.DataIntegrityViolationException("Duplicate key"));

        // When & Then
        // Should throw the original DataIntegrityViolationException since it's not caught in template method
        assertThrows(org.springframework.dao.DataIntegrityViolationException.class,
                () -> processor.processException(request));

        verify(validationService).validateExceptionCreationRequest(request);
        verify(validationService).normalizeExceptionCode("INTEGRITY_ERROR");
        verify(validationService).validateExceptionCodeUniqueness("INTEGRITY_ERROR");
        verify(entityFactory).createFromRequest(request);
        verify(exceptionRepository).save(createdEntity);
        verifyNoInteractions(eventPublisher);
    }

    @Test
    @DisplayName("Should handle null request")
    void processException_NullRequest() {
        // When & Then
        assertThrows(NullPointerException.class, 
                () -> processor.processException(null));

        verifyNoInteractions(validationService);
        verifyNoInteractions(entityFactory);
        verifyNoInteractions(exceptionRepository);
        verifyNoInteractions(eventPublisher);
    }

    @Test
    @DisplayName("Should publish event after successful creation")
    void processException_EventPublishing() {
        // Given
        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "Event test");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("EVENT_TEST")
                .messages(messages)
                .httpStatus(HttpStatus.OK)
                .build();

        ExceptionEntity createdEntity = new ExceptionEntity();
        ExceptionEntity savedEntity = new ExceptionEntity();
        savedEntity.setId(2L);
        savedEntity.setExceptionCode("EVENT_TEST");

        when(validationService.normalizeExceptionCode("EVENT_TEST")).thenReturn("EVENT_TEST");
        when(entityFactory.createFromRequest(request)).thenReturn(createdEntity);
        when(exceptionRepository.save(createdEntity)).thenReturn(savedEntity);

        // When
        processor.processException(request);

        // Then
        ArgumentCaptor<ExceptionEvent.ExceptionCreatedEvent> eventCaptor = 
                ArgumentCaptor.forClass(ExceptionEvent.ExceptionCreatedEvent.class);
        verify(eventPublisher).publishEvent(eventCaptor.capture());
        
        ExceptionEvent.ExceptionCreatedEvent event = eventCaptor.getValue();
        assertNotNull(event);
        assertEquals(savedEntity, event.getExceptionEntity());
        assertEquals("EXCEPTION_CREATED", event.getEventType());
        assertNotNull(event.getEventTimestamp());
    }

    @Test
    @DisplayName("Should handle event publishing failure gracefully")
    void processException_EventPublishingFailure() {
        // Given
        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "Event failure test");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("EVENT_FAILURE")
                .messages(messages)
                .httpStatus(HttpStatus.OK)
                .build();

        ExceptionEntity createdEntity = new ExceptionEntity();
        ExceptionEntity savedEntity = new ExceptionEntity();
        savedEntity.setId(3L);

        when(validationService.normalizeExceptionCode("EVENT_FAILURE")).thenReturn("EVENT_FAILURE");
        when(entityFactory.createFromRequest(request)).thenReturn(createdEntity);
        when(exceptionRepository.save(createdEntity)).thenReturn(savedEntity);
        doThrow(new RuntimeException("Event publishing failed"))
                .when(eventPublisher).publishEvent(any(ExceptionEvent.ExceptionCreatedEvent.class));

        // When & Then
        // Should throw exception if event publishing fails (current implementation)
        assertThrows(RuntimeException.class, () -> processor.processException(request));

        verify(eventPublisher).publishEvent(any(ExceptionEvent.ExceptionCreatedEvent.class));
    }
}
