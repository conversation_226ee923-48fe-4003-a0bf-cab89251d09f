package com.lookforx.exceptionservice.service.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import com.lookforx.exceptionservice.mapper.ExceptionMapper;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import com.lookforx.exceptionservice.service.ExceptionValidationService;
import com.lookforx.exceptionservice.template.impl.CreateExceptionProcessor;
import com.lookforx.exceptionservice.template.impl.UpdateExceptionProcessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExceptionManagementServiceImpl.
 * Tests SOLID principles implementation with focused CRUD responsibilities.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ExceptionManagementServiceImpl Unit Tests")
class ExceptionManagementServiceImplTest {

    @Mock
    private ExceptionRepository exceptionRepository;
    
    @Mock
    private ExceptionValidationService validationService;
    
    @Mock
    private ExceptionMapper exceptionMapper;

    @Mock
    private CreateExceptionProcessor createProcessor;

    @Mock
    private UpdateExceptionProcessor updateProcessor;

    private ExceptionManagementServiceImpl managementService;

    @BeforeEach
    void setUp() {
        managementService = new ExceptionManagementServiceImpl(
                exceptionRepository, validationService, exceptionMapper,
                createProcessor, updateProcessor);
    }
    
    @Test
    @DisplayName("Should create exception successfully")
    void createException_Success() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("USER_NOT_FOUND")
                .messages(Map.of(LanguageCode.EN, "User not found"))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();
        
        ExceptionEntity createdEntity = new ExceptionEntity();
        createdEntity.setId(1L);
        createdEntity.setExceptionCode("USER_NOT_FOUND");

        ExceptionResponse expectedResponse = ExceptionResponse.builder()
                .id(1L)
                .exceptionCode("USER_NOT_FOUND")
                .messages(request.getMessages())
                .httpStatus(request.getHttpStatus())
                .build();

        // Mock Template Method processor
        when(createProcessor.processException(request)).thenReturn(createdEntity);
        when(exceptionMapper.toResponse(createdEntity)).thenReturn(expectedResponse);
        
        // When
        ExceptionResponse result = managementService.createException(request);
        
        // Then
        assertNotNull(result);
        assertEquals(expectedResponse.getId(), result.getId());
        assertEquals(expectedResponse.getExceptionCode(), result.getExceptionCode());
        
        // Verify Template Method pattern usage
        verify(createProcessor).processException(request);
        verify(exceptionMapper).toResponse(createdEntity);
    }
    
    @Test
    @DisplayName("Should update exception successfully")
    void updateException_Success() {
        // Given
        Long id = 1L;
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("UPDATED_ERROR")
                .messages(Map.of(LanguageCode.EN, "Updated error"))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();
        
        ExceptionEntity updatedEntity = new ExceptionEntity();
        updatedEntity.setId(id);
        updatedEntity.setExceptionCode("UPDATED_ERROR");

        ExceptionResponse expectedResponse = ExceptionResponse.builder()
                .id(id)
                .exceptionCode("UPDATED_ERROR")
                .build();

        // Mock Template Method processor
        when(updateProcessor.processException(request)).thenReturn(updatedEntity);
        when(exceptionMapper.toResponse(updatedEntity)).thenReturn(expectedResponse);
        
        // When
        ExceptionResponse result = managementService.updateException(id, request);
        
        // Then
        assertNotNull(result);
        assertEquals(expectedResponse.getId(), result.getId());
        
        // Verify Template Method pattern usage
        verify(updateProcessor).setEntityId(id);
        verify(updateProcessor).processException(request);
        verify(exceptionMapper).toResponse(updatedEntity);
    }
    
    @Test
    @DisplayName("Should delete exception successfully")
    void deleteException_Success() {
        // Given
        Long id = 1L;
        
        // When
        managementService.deleteException(id);
        
        // Then
        verify(validationService).validateExceptionExistsById(id);
        verify(exceptionRepository).deleteById(id);
    }
    
    @Test
    @DisplayName("Should get all exceptions successfully")
    void getAllExceptions_Success() {
        // Given
        ExceptionEntity entity1 = new ExceptionEntity();
        entity1.setId(1L);
        entity1.setExceptionCode("ERROR_001");
        ExceptionEntity entity2 = new ExceptionEntity();
        entity2.setId(2L);
        entity2.setExceptionCode("ERROR_002");

        List<ExceptionEntity> entities = List.of(entity1, entity2);

        ExceptionResponse response1 = ExceptionResponse.builder()
                .id(1L)
                .exceptionCode("ERROR_001")
                .build();
        ExceptionResponse response2 = ExceptionResponse.builder()
                .id(2L)
                .exceptionCode("ERROR_002")
                .build();
        
        when(exceptionRepository.findAll()).thenReturn(entities);
        when(exceptionMapper.toResponse(entity1)).thenReturn(response1);
        when(exceptionMapper.toResponse(entity2)).thenReturn(response2);
        
        // When
        List<ExceptionResponse> result = managementService.getAllExceptions();
        
        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1L, result.get(0).getId());
        assertEquals(2L, result.get(1).getId());

        // Verify
        verify(exceptionRepository).findAll();
        verify(exceptionMapper).toResponse(entity1);
        verify(exceptionMapper).toResponse(entity2);
    }

    @Test
    @DisplayName("Should get all exceptions with empty list successfully")
    void getAllExceptions_EmptyList_Success() {
        // Given
        List<ExceptionEntity> emptyEntities = List.of();

        when(exceptionRepository.findAll()).thenReturn(emptyEntities);

        // When
        List<ExceptionResponse> result = managementService.getAllExceptions();

        // Then
        assertNotNull(result);
        assertEquals(0, result.size());
        assertTrue(result.isEmpty());

        // Verify SOLID: Single Responsibility - delegates to repository
        verify(exceptionRepository).findAll();
        verifyNoInteractions(exceptionMapper);
    }
    
    @Test
    @DisplayName("Should get paginated exceptions with search successfully")
    void getAllExceptions_WithPaginationAndSearch() {
        // Given
        String search = "USER";
        Pageable pageable = PageRequest.of(0, 10);
        
        ExceptionEntity entity = new ExceptionEntity();
        entity.setId(1L);
        entity.setExceptionCode("USER_NOT_FOUND");

        Page<ExceptionEntity> entityPage = new PageImpl<>(List.of(entity), pageable, 1);
        ExceptionResponse response = ExceptionResponse.builder()
                .id(1L)
                .exceptionCode("USER_NOT_FOUND")
                .build();
        
        when(exceptionRepository.findByExceptionCodeContainingIgnoreCase(search, pageable))
                .thenReturn(entityPage);
        when(exceptionMapper.toResponse(entity)).thenReturn(response);
        
        // When
        Page<ExceptionResponse> result = managementService.getAllExceptions(pageable, search);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1L, result.getContent().get(0).getId());
        
        // Verify
        verify(exceptionRepository).findByExceptionCodeContainingIgnoreCase(search, pageable);
        verify(exceptionMapper).toResponse(entity);
    }
    
    @Test
    @DisplayName("Should get paginated exceptions without search successfully")
    void getAllExceptions_WithPaginationNoSearch() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        
        ExceptionEntity entity = new ExceptionEntity();
        entity.setId(1L);
        entity.setExceptionCode("VALIDATION_ERROR");

        Page<ExceptionEntity> entityPage = new PageImpl<>(List.of(entity), pageable, 1);
        ExceptionResponse response = ExceptionResponse.builder()
                .id(1L)
                .exceptionCode("VALIDATION_ERROR")
                .build();
        
        when(exceptionRepository.findAll(pageable)).thenReturn(entityPage);
        when(exceptionMapper.toResponse(entity)).thenReturn(response);
        
        // When
        Page<ExceptionResponse> result = managementService.getAllExceptions(pageable, null);
        
        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        
        // Verify
        verify(exceptionRepository).findAll(pageable);
        verify(exceptionMapper).toResponse(entity);
        verifyNoMoreInteractions(exceptionRepository);
    }
    
    @Test
    @DisplayName("Should get exception by ID successfully")
    void getExceptionById_Success() {
        // Given
        Long id = 1L;
        ExceptionEntity entity = new ExceptionEntity();
        entity.setId(id);
        entity.setExceptionCode("ACCESS_DENIED");

        ExceptionResponse expectedResponse = ExceptionResponse.builder()
                .id(id)
                .exceptionCode("ACCESS_DENIED")
                .build();
        
        when(exceptionRepository.findById(id)).thenReturn(Optional.of(entity));
        when(exceptionMapper.toResponse(entity)).thenReturn(expectedResponse);
        
        // When
        ExceptionResponse result = managementService.getExceptionById(id);
        
        // Then
        assertNotNull(result);
        assertEquals(id, result.getId());
        
        // Verify SOLID: Single Responsibility - delegates validation
        verify(validationService).validateExceptionExistsById(id);
        verify(exceptionRepository).findById(id);
        verify(exceptionMapper).toResponse(entity);
    }
    
    @Test
    @DisplayName("Should handle null repository gracefully")
    void constructor_NullRepository_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
                () -> new ExceptionManagementServiceImpl(null, validationService, exceptionMapper,
                        createProcessor, updateProcessor));
    }

    @Test
    @DisplayName("Should handle null validation service gracefully")
    void constructor_NullValidationService_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
                () -> new ExceptionManagementServiceImpl(exceptionRepository, null, exceptionMapper,
                        createProcessor, updateProcessor));
    }

    @Test
    @DisplayName("Should handle null mapper gracefully")
    void constructor_NullMapper_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
                () -> new ExceptionManagementServiceImpl(exceptionRepository, validationService, null,
                        createProcessor, updateProcessor));
    }

    @Test
    @DisplayName("Should handle null create processor gracefully")
    void constructor_NullCreateProcessor_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
                () -> new ExceptionManagementServiceImpl(exceptionRepository, validationService, exceptionMapper,
                        null, updateProcessor));
    }

    @Test
    @DisplayName("Should handle null update processor gracefully")
    void constructor_NullUpdateProcessor_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
                () -> new ExceptionManagementServiceImpl(exceptionRepository, validationService, exceptionMapper,
                        createProcessor, null));
    }
}
