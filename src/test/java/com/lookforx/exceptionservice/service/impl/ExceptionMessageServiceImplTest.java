package com.lookforx.exceptionservice.service.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.exception.ExceptionNotFoundException;
import com.lookforx.exceptionservice.chain.MessageRetrievalChain;
import com.lookforx.exceptionservice.service.ExceptionValidationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExceptionMessageServiceImpl.
 * Tests SOLID principles implementation with focused responsibilities.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ExceptionMessageServiceImpl Unit Tests")
class ExceptionMessageServiceImplTest {

    @Mock
    private MessageRetrievalChain messageRetrievalChain;

    @Mock
    private ExceptionValidationService validationService;

    private ExceptionMessageServiceImpl messageService;
    
    @BeforeEach
    void setUp() {
        messageService = new ExceptionMessageServiceImpl(messageRetrievalChain, validationService);
    }
    
    @Test
    @DisplayName("Should retrieve localized message successfully")
    void retrieveLocalizedMessage_Success() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;
        String normalizedCode = "USER_NOT_FOUND";
        String expectedMessage = "User not found";
        
        when(validationService.normalizeExceptionCode(exceptionCode)).thenReturn(normalizedCode);
        when(messageRetrievalChain.processRequest(normalizedCode, languageCode))
                .thenReturn(expectedMessage);

        // When
        String result = messageService.retrieveLocalizedMessage(exceptionCode, languageCode);

        // Then
        assertEquals(expectedMessage, result);

        // Verify SOLID: Single Responsibility - delegates validation
        verify(validationService).validateMessageRetrievalParameters(exceptionCode, languageCode);
        verify(validationService).normalizeExceptionCode(exceptionCode);
        verify(validationService).validateExceptionExists(normalizedCode);
        // Verify Chain of Responsibility pattern usage
        verify(messageRetrievalChain).processRequest(normalizedCode, languageCode);
    }
    
    @Test
    @DisplayName("Should return fallback message when requested language not available")
    void retrieveLocalizedMessage_FallbackToEnglish() {
        // Given
        String exceptionCode = "VALIDATION_ERROR";
        LanguageCode languageCode = LanguageCode.TR;
        String normalizedCode = "VALIDATION_ERROR";
        String englishMessage = "Validation error occurred";
        
        when(validationService.normalizeExceptionCode(exceptionCode)).thenReturn(normalizedCode);
        when(messageRetrievalChain.processRequest(normalizedCode, languageCode))
                .thenReturn(englishMessage);

        // When
        String result = messageService.retrieveLocalizedMessage(exceptionCode, languageCode);

        // Then
        assertEquals(englishMessage, result);

        // Verify
        verify(validationService).validateMessageRetrievalParameters(exceptionCode, languageCode);
        verify(validationService).normalizeExceptionCode(exceptionCode);
        verify(validationService).validateExceptionExists(normalizedCode);
        verify(messageRetrievalChain).processRequest(normalizedCode, languageCode);
    }
    
    @Test
    @DisplayName("Should return default message when no translations available")
    void retrieveLocalizedMessage_DefaultMessage() {
        // Given
        String exceptionCode = "UNKNOWN_ERROR";
        LanguageCode languageCode = LanguageCode.TR;
        String normalizedCode = "UNKNOWN_ERROR";
        
        when(validationService.normalizeExceptionCode(exceptionCode)).thenReturn(normalizedCode);
        when(messageRetrievalChain.processRequest(normalizedCode, languageCode))
                .thenReturn("Bir hata oluştu. Lütfen destek ekibiyle iletişime geçin.");

        // When
        String result = messageService.retrieveLocalizedMessage(exceptionCode, languageCode);

        // Then
        assertNotNull(result);
        assertTrue(result.contains("hata")); // Turkish default message

        // Verify
        verify(validationService).validateMessageRetrievalParameters(exceptionCode, languageCode);
        verify(validationService).normalizeExceptionCode(exceptionCode);
        verify(validationService).validateExceptionExists(normalizedCode);
        verify(messageRetrievalChain).processRequest(normalizedCode, languageCode);
    }
    
    @Test
    @DisplayName("Should throw exception when validation service throws exception")
    void retrieveLocalizedMessage_ValidationException() {
        // Given
        String exceptionCode = "INVALID_CODE";
        LanguageCode languageCode = LanguageCode.EN;
        
        doThrow(new ExceptionNotFoundException(exceptionCode))
                .when(validationService).validateExceptionExists(anyString());
        when(validationService.normalizeExceptionCode(exceptionCode)).thenReturn(exceptionCode);
        
        // When & Then
        assertThrows(ExceptionNotFoundException.class, 
                () -> messageService.retrieveLocalizedMessage(exceptionCode, languageCode));
        
        // Verify
        verify(validationService).validateMessageRetrievalParameters(exceptionCode, languageCode);
        verify(validationService).normalizeExceptionCode(exceptionCode);
        verify(validationService).validateExceptionExists(exceptionCode);
        verifyNoInteractions(messageRetrievalChain);
    }
    
    @Test
    @DisplayName("Should handle null message retrieval chain gracefully")
    void constructor_NullMessageRetrievalChain_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
                () -> new ExceptionMessageServiceImpl(null, validationService));
    }

    @Test
    @DisplayName("Should handle null validation service gracefully")
    void constructor_NullValidationService_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
                () -> new ExceptionMessageServiceImpl(messageRetrievalChain, null));
    }
}
