package com.lookforx.exceptionservice.service.impl;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.exception.DuplicateExceptionCode;
import com.lookforx.exceptionservice.exception.ExceptionNotFoundException;
import com.lookforx.exceptionservice.repository.ExceptionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExceptionValidationServiceImpl.
 * Tests SOLID principles implementation with focused validation responsibilities.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ExceptionValidationServiceImpl Unit Tests")
class ExceptionValidationServiceImplTest {

    @Mock
    private ExceptionRepository exceptionRepository;
    
    private ExceptionValidationServiceImpl validationService;
    
    @BeforeEach
    void setUp() {
        validationService = new ExceptionValidationServiceImpl(exceptionRepository);
    }
    
    @Test
    @DisplayName("Should validate message retrieval parameters successfully")
    void validateMessageRetrievalParameters_Success() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;
        
        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> 
                validationService.validateMessageRetrievalParameters(exceptionCode, languageCode));
    }
    
    @Test
    @DisplayName("Should throw exception for null exception code")
    void validateMessageRetrievalParameters_NullExceptionCode() {
        // Given
        String exceptionCode = null;
        LanguageCode languageCode = LanguageCode.EN;
        
        // When & Then
        NullPointerException exception = assertThrows(NullPointerException.class,
                () -> validationService.validateMessageRetrievalParameters(exceptionCode, languageCode));

        assertEquals("Exception code cannot be null", exception.getMessage());
    }
    
    @Test
    @DisplayName("Should throw exception for null language code")
    void validateMessageRetrievalParameters_NullLanguageCode() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = null;
        
        // When & Then
        NullPointerException exception = assertThrows(NullPointerException.class,
                () -> validationService.validateMessageRetrievalParameters(exceptionCode, languageCode));

        assertEquals("Language code cannot be null", exception.getMessage());
    }
    
    @Test
    @DisplayName("Should throw exception for empty exception code")
    void validateMessageRetrievalParameters_EmptyExceptionCode() {
        // Given
        String exceptionCode = "   ";
        LanguageCode languageCode = LanguageCode.EN;
        
        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class,
                () -> validationService.validateMessageRetrievalParameters(exceptionCode, languageCode));
        
        assertEquals("Exception code cannot be empty", exception.getMessage());
    }
    
    @Test
    @DisplayName("Should validate exception creation request successfully")
    void validateExceptionCreationRequest_Success() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("USER_NOT_FOUND")
                .messages(Map.of(LanguageCode.EN, "User not found"))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();
        
        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> validationService.validateExceptionCreationRequest(request));
    }
    
    @Test
    @DisplayName("Should throw exception for null request")
    void validateExceptionCreationRequest_NullRequest() {
        // Given
        ExceptionRequest request = null;
        
        // When & Then
        NullPointerException exception = assertThrows(NullPointerException.class,
                () -> validationService.validateExceptionCreationRequest(request));

        assertEquals("Exception request cannot be null", exception.getMessage());
    }
    
    @Test
    @DisplayName("Should validate exception code uniqueness successfully")
    void validateExceptionCodeUniqueness_Success() {
        // Given
        String exceptionCode = "NEW_EXCEPTION";
        
        when(exceptionRepository.findByExceptionCode(exceptionCode)).thenReturn(Optional.empty());
        
        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> validationService.validateExceptionCodeUniqueness(exceptionCode));
        
        // Verify
        verify(exceptionRepository).findByExceptionCode(exceptionCode);
    }
    
    @Test
    @DisplayName("Should throw exception for duplicate exception code")
    void validateExceptionCodeUniqueness_DuplicateCode() {
        // Given
        String exceptionCode = "EXISTING_EXCEPTION";
        ExceptionEntity existingEntity = new ExceptionEntity();
        
        when(exceptionRepository.findByExceptionCode(exceptionCode)).thenReturn(Optional.of(existingEntity));
        
        // When & Then
        DuplicateExceptionCode exception = assertThrows(DuplicateExceptionCode.class,
                () -> validationService.validateExceptionCodeUniqueness(exceptionCode));
        
        assertTrue(exception.getMessage().contains(exceptionCode));
        
        // Verify
        verify(exceptionRepository).findByExceptionCode(exceptionCode);
    }
    
    @Test
    @DisplayName("Should validate exception exists successfully")
    void validateExceptionExists_Success() {
        // Given
        String exceptionCode = "EXISTING_EXCEPTION";
        ExceptionEntity existingEntity = new ExceptionEntity();
        
        when(exceptionRepository.findByExceptionCode(exceptionCode)).thenReturn(Optional.of(existingEntity));
        
        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> validationService.validateExceptionExists(exceptionCode));
        
        // Verify
        verify(exceptionRepository).findByExceptionCode(exceptionCode);
    }
    
    @Test
    @DisplayName("Should throw exception when exception does not exist")
    void validateExceptionExists_NotFound() {
        // Given
        String exceptionCode = "NON_EXISTING_EXCEPTION";
        
        when(exceptionRepository.findByExceptionCode(exceptionCode)).thenReturn(Optional.empty());
        
        // When & Then
        ExceptionNotFoundException exception = assertThrows(ExceptionNotFoundException.class,
                () -> validationService.validateExceptionExists(exceptionCode));
        
        assertTrue(exception.getMessage().contains(exceptionCode));
        
        // Verify
        verify(exceptionRepository).findByExceptionCode(exceptionCode);
    }
    
    @Test
    @DisplayName("Should validate exception exists by ID successfully")
    void validateExceptionExistsById_Success() {
        // Given
        Long id = 1L;
        ExceptionEntity existingEntity = new ExceptionEntity();
        
        when(exceptionRepository.findById(id)).thenReturn(Optional.of(existingEntity));
        
        // When & Then - Should not throw exception
        assertDoesNotThrow(() -> validationService.validateExceptionExistsById(id));
        
        // Verify
        verify(exceptionRepository).findById(id);
    }
    
    @Test
    @DisplayName("Should throw exception when exception ID does not exist")
    void validateExceptionExistsById_NotFound() {
        // Given
        Long id = 999L;
        
        when(exceptionRepository.findById(id)).thenReturn(Optional.empty());
        
        // When & Then
        ExceptionNotFoundException exception = assertThrows(ExceptionNotFoundException.class,
                () -> validationService.validateExceptionExistsById(id));
        
        assertTrue(exception.getMessage().contains(id.toString()));
        
        // Verify
        verify(exceptionRepository).findById(id);
    }
    
    @Test
    @DisplayName("Should throw exception for null ID")
    void validateExceptionExistsById_NullId() {
        // Given
        Long id = null;
        
        // When & Then
        NullPointerException exception = assertThrows(NullPointerException.class,
                () -> validationService.validateExceptionExistsById(id));

        assertEquals("Exception ID cannot be null", exception.getMessage());
        
        // Verify
        verifyNoInteractions(exceptionRepository);
    }
    
    @Test
    @DisplayName("Should normalize exception code correctly")
    void normalizeExceptionCode_Success() {
        // Given
        String exceptionCode = "  user_not_found  ";
        
        // When
        String result = validationService.normalizeExceptionCode(exceptionCode);
        
        // Then
        assertEquals("USER_NOT_FOUND", result);
    }
    
    @Test
    @DisplayName("Should handle null exception code in normalization")
    void normalizeExceptionCode_NullCode() {
        // Given
        String exceptionCode = null;
        
        // When
        String result = validationService.normalizeExceptionCode(exceptionCode);
        
        // Then
        assertNull(result);
    }
    
    @Test
    @DisplayName("Should handle null repository gracefully")
    void constructor_NullRepository_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class, 
                () -> new ExceptionValidationServiceImpl(null));
    }
}
