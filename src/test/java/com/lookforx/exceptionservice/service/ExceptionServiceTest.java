package com.lookforx.exceptionservice.service;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import com.lookforx.exceptionservice.dto.ExceptionResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ExceptionService (Facade).
 * Tests SOLID principles implementation with delegation pattern.
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ExceptionService Facade Unit Tests")
class ExceptionServiceTest {

    @Mock
    private ExceptionMessageService messageService;

    @Mock
    private ExceptionManagementService managementService;

    private ExceptionService exceptionService;

    @BeforeEach
    void setUp() {
        exceptionService = new ExceptionService(messageService, managementService);
    }

    @Test
    @DisplayName("Should delegate message retrieval to message service")
    void retrieveLocalizedExceptionMessage_Success() {
        // Given
        String exceptionCode = "USER_NOT_FOUND";
        LanguageCode languageCode = LanguageCode.EN;
        String expectedMessage = "User not found";

        when(messageService.retrieveLocalizedMessage(exceptionCode, languageCode))
                .thenReturn(expectedMessage);

        // When
        String result = exceptionService.retrieveLocalizedExceptionMessage(exceptionCode, languageCode);

        // Then
        assertEquals(expectedMessage, result);

        // Verify SOLID: Single Responsibility - delegates to specialized service
        verify(messageService).retrieveLocalizedMessage(exceptionCode, languageCode);
        verifyNoInteractions(managementService);
    }

    @Test
    @DisplayName("Should delegate exception creation to management service")
    void createException_Success() {
        // Given
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("USER_NOT_FOUND")
                .messages(Map.of(LanguageCode.EN, "User not found"))
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        ExceptionResponse expectedResponse = ExceptionResponse.builder()
                .id(1L)
                .exceptionCode("USER_NOT_FOUND")
                .messages(request.getMessages())
                .httpStatus(request.getHttpStatus())
                .build();

        when(managementService.createException(request)).thenReturn(expectedResponse);

        // When
        ExceptionResponse result = exceptionService.createException(request);

        // Then
        assertEquals(expectedResponse, result);

        // Verify SOLID: Single Responsibility - delegates to specialized service
        verify(managementService).createException(request);
        verifyNoInteractions(messageService);
    }

    @Test
    @DisplayName("Should delegate exception update to management service")
    void updateException_Success() {
        // Given
        Long id = 1L;
        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("UPDATED_ERROR")
                .messages(Map.of(LanguageCode.EN, "Updated error"))
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        ExceptionResponse expectedResponse = ExceptionResponse.builder()
                .id(id)
                .exceptionCode("UPDATED_ERROR")
                .messages(request.getMessages())
                .httpStatus(request.getHttpStatus())
                .build();

        when(managementService.updateException(id, request)).thenReturn(expectedResponse);

        // When
        ExceptionResponse result = exceptionService.updateException(id, request);

        // Then
        assertEquals(expectedResponse, result);

        // Verify SOLID: Single Responsibility - delegates to specialized service
        verify(managementService).updateException(id, request);
        verifyNoInteractions(messageService);
    }

    @Test
    @DisplayName("Should delegate exception deletion to management service")
    void deleteException_Success() {
        // Given
        Long id = 1L;

        // When
        exceptionService.deleteException(id);

        // Then
        // Verify SOLID: Single Responsibility - delegates to specialized service
        verify(managementService).deleteException(id);
        verifyNoInteractions(messageService);
    }

    @Test
    @DisplayName("Should delegate get all exceptions to management service")
    void getAllExceptions_Success() {
        // Given
        List<ExceptionResponse> expectedResponses = List.of(
                ExceptionResponse.builder().id(1L).exceptionCode("CODE1").build(),
                ExceptionResponse.builder().id(2L).exceptionCode("CODE2").build()
        );

        when(managementService.getAllExceptions()).thenReturn(expectedResponses);

        // When
        List<ExceptionResponse> result = exceptionService.getAllExceptions();

        // Then
        assertEquals(expectedResponses, result);
        assertEquals(2, result.size());

        // Verify SOLID: Single Responsibility - delegates to specialized service
        verify(managementService).getAllExceptions();
        verifyNoInteractions(messageService);
    }

    @Test
    @DisplayName("Should delegate get paginated exceptions to management service")
    void getAllExceptions_WithPagination_Success() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        String search = "USER";

        Page<ExceptionResponse> expectedPage = new PageImpl<>(
                List.of(ExceptionResponse.builder().id(1L).exceptionCode("USER_NOT_FOUND").build()),
                pageable, 1
        );

        when(managementService.getAllExceptions(pageable, search)).thenReturn(expectedPage);

        // When
        Page<ExceptionResponse> result = exceptionService.getAllExceptions(pageable, search);

        // Then
        assertEquals(expectedPage, result);
        assertEquals(1, result.getTotalElements());

        // Verify SOLID: Single Responsibility - delegates to specialized service
        verify(managementService).getAllExceptions(pageable, search);
        verifyNoInteractions(messageService);
    }

    @Test
    @DisplayName("Should delegate get exception by ID to management service")
    void getExceptionById_Success() {
        // Given
        Long id = 1L;
        ExceptionResponse expectedResponse = ExceptionResponse.builder()
                .id(id)
                .exceptionCode("USER_NOT_FOUND")
                .build();

        when(managementService.getExceptionById(id)).thenReturn(expectedResponse);

        // When
        ExceptionResponse result = exceptionService.getExceptionById(id);

        // Then
        assertEquals(expectedResponse, result);

        // Verify SOLID: Single Responsibility - delegates to specialized service
        verify(managementService).getExceptionById(id);
        verifyNoInteractions(messageService);
    }

    @Test
    @DisplayName("Should handle null message service gracefully")
    void constructor_NullMessageService_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
                () -> new ExceptionService(null, managementService));
    }

    @Test
    @DisplayName("Should handle null management service gracefully")
    void constructor_NullManagementService_ThrowsException() {
        // When & Then
        assertThrows(NullPointerException.class,
                () -> new ExceptionService(messageService, null));
    }
}