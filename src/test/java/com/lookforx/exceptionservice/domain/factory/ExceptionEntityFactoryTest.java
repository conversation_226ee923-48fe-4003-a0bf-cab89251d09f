package com.lookforx.exceptionservice.domain.factory;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.exceptionservice.domain.ExceptionEntity;
import com.lookforx.exceptionservice.dto.ExceptionRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for ExceptionEntityFactory.
 * Tests the Factory Pattern implementation for creating ExceptionEntity instances.
 */
@DisplayName("Exception Entity Factory Tests")
class ExceptionEntityFactoryTest {

    private ExceptionEntityFactory factory;

    @BeforeEach
    void setUp() {
        factory = new ExceptionEntityFactory();
    }

    @Test
    @DisplayName("Should create entity from request successfully")
    void createFromRequest_Success() {
        // Given
        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "User not found");
        messages.put(LanguageCode.TR, "Kullanıcı bulunamadı");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("user_not_found")
                .messages(messages)
                .httpStatus(HttpStatus.NOT_FOUND)
                .build();

        // When
        ExceptionEntity result = factory.createFromRequest(request);

        // Then
        assertNotNull(result);
        assertEquals("USER_NOT_FOUND", result.getExceptionCode());
        assertEquals(HttpStatus.NOT_FOUND, result.getHttpStatus());
        assertEquals(2, result.getTranslations().size());
        assertEquals("User not found", result.getTranslations().get(LanguageCode.EN));
        assertEquals("Kullanıcı bulunamadı", result.getTranslations().get(LanguageCode.TR));
    }

    @Test
    @DisplayName("Should normalize exception code correctly")
    void createFromRequest_NormalizeExceptionCode() {
        // Given
        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "Validation error");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("  validation error  ")
                .messages(messages)
                .httpStatus(HttpStatus.BAD_REQUEST)
                .build();

        // When
        ExceptionEntity result = factory.createFromRequest(request);

        // Then
        assertEquals("VALIDATION_ERROR", result.getExceptionCode());
    }

    @Test
    @DisplayName("Should use default HTTP status when not provided")
    void createFromRequest_DefaultHttpStatus() {
        // Given
        Map<LanguageCode, String> messages = new HashMap<>();
        messages.put(LanguageCode.EN, "Internal error");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("INTERNAL_ERROR")
                .messages(messages)
                .httpStatus(null)
                .build();

        // When
        ExceptionEntity result = factory.createFromRequest(request);

        // Then
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, result.getHttpStatus());
    }

    @Test
    @DisplayName("Should throw exception when request is null")
    void createFromRequest_NullRequest() {
        // When & Then
        assertThrows(NullPointerException.class, 
                () -> factory.createFromRequest(null));
    }

    @Test
    @DisplayName("Should create default entity successfully")
    void createDefault_Success() {
        // Given
        String exceptionCode = "default_error";

        // When
        ExceptionEntity result = factory.createDefault(exceptionCode);

        // Then
        assertNotNull(result);
        assertEquals("DEFAULT_ERROR", result.getExceptionCode());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, result.getHttpStatus());
        assertEquals(2, result.getTranslations().size());
        assertTrue(result.getTranslations().containsKey(LanguageCode.EN));
        assertTrue(result.getTranslations().containsKey(LanguageCode.TR));
    }

    @Test
    @DisplayName("Should create system exception successfully")
    void createSystemException_Success() {
        // Given
        String exceptionCode = "system_error";
        HttpStatus httpStatus = HttpStatus.SERVICE_UNAVAILABLE;
        String englishMessage = "System is temporarily unavailable";

        // When
        ExceptionEntity result = factory.createSystemException(exceptionCode, httpStatus, englishMessage);

        // Then
        assertNotNull(result);
        assertEquals("SYSTEM_ERROR", result.getExceptionCode());
        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, result.getHttpStatus());
        assertEquals(1, result.getTranslations().size());
        assertEquals(englishMessage, result.getTranslations().get(LanguageCode.EN));
    }

    @Test
    @DisplayName("Should update existing entity from request successfully")
    void updateFromRequest_Success() {
        // Given
        ExceptionEntity existingEntity = new ExceptionEntity();
        existingEntity.setId(1L);
        existingEntity.setExceptionCode("OLD_ERROR");

        Map<LanguageCode, String> newMessages = new HashMap<>();
        newMessages.put(LanguageCode.EN, "Updated error message");
        newMessages.put(LanguageCode.TR, "Güncellenmiş hata mesajı");

        ExceptionRequest request = ExceptionRequest.builder()
                .exceptionCode("updated_error")
                .messages(newMessages)
                .httpStatus(HttpStatus.CONFLICT)
                .build();

        // When
        ExceptionEntity result = factory.updateFromRequest(existingEntity, request);

        // Then
        assertSame(existingEntity, result);
        assertEquals("UPDATED_ERROR", result.getExceptionCode());
        assertEquals(HttpStatus.CONFLICT, result.getHttpStatus());
        assertEquals(2, result.getTranslations().size());
    }
}
