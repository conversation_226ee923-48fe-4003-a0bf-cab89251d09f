package com.lookforx.questionformservice.controller;

import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.questionformservice.base.AbstractRestControllerTest;
import com.lookforx.questionformservice.domain.ElementType;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.questionformservice.dto.FormTemplateDTO;
import com.lookforx.questionformservice.dto.LocalizedFormTemplateDTO;
import com.lookforx.questionformservice.dto.request.CreateFormElementRequest;
import com.lookforx.questionformservice.dto.request.CreateFormTemplateRequest;
import com.lookforx.questionformservice.dto.request.UpdateFormElementRequest;
import com.lookforx.questionformservice.dto.request.UpdateFormTemplateRequest;
import com.lookforx.questionformservice.service.FormTemplateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(
        controllers = FormTemplateController.class
)
class FormTemplateControllerTest extends AbstractRestControllerTest {

    @MockitoBean
    FormTemplateService formTemplateService;

    private FormTemplateDTO dto1, dto2;
    private List<FormTemplateDTO> dtoList;
    private Page<FormTemplateDTO> dtoPage;

    private LocalizedFormTemplateDTO locDto;
    private List<LocalizedFormTemplateDTO> locList;

    private CreateFormTemplateRequest createReq;
    private UpdateFormTemplateRequest updateReq;

    @MockitoBean
    ExceptionServiceClient exceptionClient;

    @BeforeEach
    void setUp() {
        // --- standard DTOs ---
        dto1 = FormTemplateDTO.builder()
                .id(1L)
                .categoryId(10L)
                .active(true)
                .build();
        dto2 = FormTemplateDTO.builder()
                .id(2L)
                .categoryId(10L)
                .active(false)
                .build();
        dtoList = List.of(dto1, dto2);
        dtoPage = new PageImpl<>(dtoList, PageRequest.of(0, 2), dtoList.size());

        // --- localized DTO ---
        locDto = LocalizedFormTemplateDTO.builder()
                .id(1L)
                .name("Name-EN")
                .description("Desc-EN")
                .build();
        locList = List.of(locDto);

        // --- create & update requests ---
        createReq = CreateFormTemplateRequest.builder()
                .nameTranslations(Map.of(LanguageCode.EN, "Name"))
                .categoryId(10L)
                .descriptionTranslations(Map.of(LanguageCode.EN, "Desc"))
                .elements(List.of(
                        CreateFormElementRequest.builder()
                                .labelTranslations(Map.of(LanguageCode.EN, "Label"))
                                .type(ElementType.TEXT)
                                .displayOrder(1)
                                .build()
                ))
                .active(true)
                .build();

        updateReq = UpdateFormTemplateRequest.builder()
                .nameTranslations(Map.of(LanguageCode.EN, "NewName"))
                .categoryId(20L)
                .descriptionTranslations(Map.of(LanguageCode.EN, "NewDesc"))
                .elements(List.of(
                        UpdateFormElementRequest.builder()
                                .id(5L)
                                .labelTranslations(Map.of(LanguageCode.EN, "NewLabel"))
                                .type(ElementType.TEXT)
                                .displayOrder(2)
                                .build()
                ))
                .active(false)
                .build();
    }

    @Test
    void getAllFormTemplates() throws Exception {
        when(formTemplateService.searchFormTemplatesWithPagination(any(), any(), any(), any())).thenReturn(new PageImpl<>(dtoList));

        mockMvc.perform(get("/api/v1/form-templates"))
                .andExpect(status().isOk());

        verify(formTemplateService).searchFormTemplatesWithPagination(any(), any(), any(), any());
    }

    @Test
    void getAllFormTemplatesPaged() throws Exception {
        when(formTemplateService.getAllFormTemplates(any(Pageable.class)))
                .thenReturn(dtoPage);

        mockMvc.perform(get("/api/v1/form-templates/paged")
                        .param("page", "0")
                        .param("size", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[0].id").value(1))
                .andExpect(jsonPath("$.totalElements").value(2));

        verify(formTemplateService).getAllFormTemplates(any(Pageable.class));
    }

    @Test
    void getFormTemplateById() throws Exception {
        when(formTemplateService.getFormTemplateById(1L)).thenReturn(dto1);

        mockMvc.perform(get("/api/v1/form-templates/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1));

        verify(formTemplateService).getFormTemplateById(1L);
    }

    @Test
    void getFormTemplatesByCategoryId() throws Exception {
        when(formTemplateService.getFormTemplatesByCategoryId(10L)).thenReturn(dtoList);

        mockMvc.perform(get("/api/v1/form-templates/category/10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].categoryId").value(10));

        verify(formTemplateService).getFormTemplatesByCategoryId(10L);
    }

    @Test
    void createFormTemplate() throws Exception {
        when(formTemplateService.createFormTemplate(any(CreateFormTemplateRequest.class)))
                .thenReturn(dto1);

        mockMvc.perform(post("/api/v1/form-templates")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createReq)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").value(1));

        verify(formTemplateService).createFormTemplate(any(CreateFormTemplateRequest.class));
    }

    @Test
    void updateFormTemplate() throws Exception {
        when(formTemplateService.updateFormTemplate(eq(1L), any(UpdateFormTemplateRequest.class)))
                .thenReturn(dto2);

        mockMvc.perform(put("/api/v1/form-templates/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateReq)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(2));

        verify(formTemplateService).updateFormTemplate(eq(1L), any(UpdateFormTemplateRequest.class));
    }

    @Test
    void deleteFormTemplate() throws Exception {
        doNothing().when(formTemplateService).deleteFormTemplate(1L);

        mockMvc.perform(delete("/api/v1/form-templates/1"))
                .andExpect(status().isNoContent());

        verify(formTemplateService).deleteFormTemplate(1L);
    }

    @Test
    void activateFormTemplate() throws Exception {
        when(formTemplateService.activateFormTemplate(1L)).thenReturn(dto1);

        mockMvc.perform(patch("/api/v1/form-templates/1/activate"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1));

        verify(formTemplateService).activateFormTemplate(1L);
    }

    @Test
    void deactivateFormTemplate() throws Exception {
        when(formTemplateService.deactivateFormTemplate(1L)).thenReturn(dto2);

        mockMvc.perform(patch("/api/v1/form-templates/1/deactivate"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(2));

        verify(formTemplateService).deactivateFormTemplate(1L);
    }

    @Test
    void getAllFormTemplatesLocalized() throws Exception {
        when(formTemplateService.getAllFormTemplatesLocalized(LanguageCode.TR, LanguageCode.EN))
                .thenReturn(locList);

        mockMvc.perform(get("/api/v1/form-templates/localized")
                        .param("languageCode", "TR")
                        .param("fallbackLanguageCode", "EN"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].name").value("Name-EN"));

        verify(formTemplateService).getAllFormTemplatesLocalized(LanguageCode.TR, LanguageCode.EN);
    }

    @Test
    void getFormTemplateByIdLocalized() throws Exception {
        when(formTemplateService.getFormTemplateByIdLocalized(1L, LanguageCode.EN, LanguageCode.FR))
                .thenReturn(locDto);

        mockMvc.perform(get("/api/v1/form-templates/1/localized")
                        .param("languageCode", "EN")
                        .param("fallbackLanguageCode", "FR"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.description").value("Desc-EN"));

        verify(formTemplateService).getFormTemplateByIdLocalized(1L, LanguageCode.EN, LanguageCode.FR);
    }

    @Test
    void getFormTemplatesByCategoryIdLocalized() throws Exception {
        when(formTemplateService.getFormTemplatesByCategoryIdLocalized(10L, LanguageCode.DE, LanguageCode.EN))
                .thenReturn(locList);

        mockMvc.perform(get("/api/v1/form-templates/category/10/localized")
                        .param("languageCode", "DE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").value(1));

        verify(formTemplateService).getFormTemplatesByCategoryIdLocalized(10L, LanguageCode.DE, LanguageCode.EN);
    }

    @Test
    void searchFormTemplatesByName() throws Exception {
        when(formTemplateService.searchFormTemplates("foo", LanguageCode.EN, LanguageCode.TR))
                .thenReturn(locList);

        mockMvc.perform(get("/api/v1/form-templates/search/name")
                        .param("searchTerm", "foo")
                        .param("languageCode", "EN")
                        .param("fallbackLanguageCode", "TR"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].name").value("Name-EN"));

        verify(formTemplateService).searchFormTemplates("foo", LanguageCode.EN, LanguageCode.TR);
    }

    @Test
    void searchFormTemplates() throws Exception {
        when(formTemplateService.searchFormTemplates("bar", LanguageCode.ZH, LanguageCode.EN))
                .thenReturn(locList);

        mockMvc.perform(get("/api/v1/form-templates/search")
                        .param("searchTerm", "bar")
                        .param("languageCode", "ZH"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].name").value("Name-EN"));

        verify(formTemplateService).searchFormTemplates("bar", LanguageCode.ZH, LanguageCode.EN);
    }

    @Test
    void getFormTemplatesByLanguage() throws Exception {
        when(formTemplateService.getAllFormTemplatesLocalized(LanguageCode.ES))
                .thenReturn(locList);

        mockMvc.perform(get("/api/v1/form-templates/by-language/ES"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").value(1));

        verify(formTemplateService).getAllFormTemplatesLocalized(LanguageCode.ES);
    }
}
