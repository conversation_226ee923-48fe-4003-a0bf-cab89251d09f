package com.lookforx.questionformservice.controller;

import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.questionformservice.base.AbstractRestControllerTest;
import com.lookforx.questionformservice.dto.FormSubmissionDTO;
import com.lookforx.questionformservice.dto.request.FormResponseRequest;
import com.lookforx.questionformservice.dto.request.SubmitFormRequest;
import com.lookforx.questionformservice.service.FormSubmissionService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(
        controllers = FormSubmissionController.class
)
class FormSubmissionControllerTest extends AbstractRestControllerTest {

    @MockitoBean
    FormSubmissionService formSubmissionService;

    private SubmitFormRequest submitFormRequest;
    private FormSubmissionDTO dto1, dto2;
    private List<FormSubmissionDTO> list;
    private Page<FormSubmissionDTO> page;

    @MockitoBean
    ExceptionServiceClient exceptionClient;

    @BeforeEach
    void setUp() {
        dto1 = FormSubmissionDTO.builder()
                .id("ID-1")
                .formTemplateId(100L)
                .categoryId(10L)
                .userId(20L)
                .requestId("REQ-1")
                .build();

        dto2 = FormSubmissionDTO.builder()
                .id("ID-2")
                .formTemplateId(100L)
                .categoryId(10L)
                .userId(20L)
                .requestId("REQ-2")
                .build();

        list = List.of(dto1, dto2);
        page = new PageImpl<>(list, PageRequest.of(0, 2), list.size());

        submitFormRequest = SubmitFormRequest.builder()
                .formTemplateId(100L)
                .categoryId(10L)
                .userId(20L)
                .requestId("REQ-NEW")
                .responses(List.of(
                        FormResponseRequest.builder()
                                .formElementId(5L)
                                .value("answer")
                                .build()
                ))
                .build();
    }

    @Test
    void getAllFormSubmissions() throws Exception {
        when(formSubmissionService.searchFormSubmissions(any(), any(), any(), any(), any())).thenReturn(new PageImpl<>(list));

        mockMvc.perform(get("/api/v1/form-submissions"))
                .andExpect(status().isOk());

        verify(formSubmissionService).searchFormSubmissions(any(), any(), any(), any(), any());
    }

    @Test
    void getAllFormSubmissionsPaged() throws Exception {
        when(formSubmissionService.getAllFormSubmissions(any(Pageable.class)))
                .thenReturn(page);

        mockMvc.perform(get("/api/v1/form-submissions/paged")
                        .param("page", "0")
                        .param("size", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[0].id").value("ID-1"))
                .andExpect(jsonPath("$.totalElements").value(2));

        verify(formSubmissionService).getAllFormSubmissions(any(Pageable.class));
    }

    @Test
    void getFormSubmissionById() throws Exception {
        when(formSubmissionService.getFormSubmissionById("ID-1")).thenReturn(dto1);

        mockMvc.perform(get("/api/v1/form-submissions/ID-1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value("ID-1"));

        verify(formSubmissionService).getFormSubmissionById("ID-1");
    }

    @Test
    void getFormSubmissionsByCategoryId() throws Exception {
        when(formSubmissionService.getFormSubmissionsByCategoryId(10L)).thenReturn(list);

        mockMvc.perform(get("/api/v1/form-submissions/category/10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].categoryId").value(10));

        verify(formSubmissionService).getFormSubmissionsByCategoryId(10L);
    }

    @Test
    void getFormSubmissionsByCategoryIdPaged() throws Exception {
        when(formSubmissionService.getFormSubmissionsByCategoryId(eq(10L), any(Pageable.class)))
                .thenReturn(page);

        mockMvc.perform(get("/api/v1/form-submissions/category/10/paged")
                        .param("page", "0")
                        .param("size", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[1].id").value("ID-2"))
                .andExpect(jsonPath("$.totalElements").value(2));

        verify(formSubmissionService).getFormSubmissionsByCategoryId(eq(10L), any(Pageable.class));
    }

    @Test
    void getFormSubmissionsByFormTemplateId() throws Exception {
        when(formSubmissionService.getFormSubmissionsByFormTemplateId(100L)).thenReturn(list);

        mockMvc.perform(get("/api/v1/form-submissions/form-template/100"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[1].formTemplateId").value(100));

        verify(formSubmissionService).getFormSubmissionsByFormTemplateId(100L);
    }

    @Test
    void getFormSubmissionsByFormTemplateIdPaged() throws Exception {
        when(formSubmissionService.getFormSubmissionsByFormTemplateId(eq(100L), any(Pageable.class)))
                .thenReturn(page);

        mockMvc.perform(get("/api/v1/form-submissions/form-template/100/paged")
                        .param("page", "0")
                        .param("size", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[0].id").value("ID-1"))
                .andExpect(jsonPath("$.totalElements").value(2));

        verify(formSubmissionService).getFormSubmissionsByFormTemplateId(eq(100L), any(Pageable.class));
    }

    @Test
    void getFormSubmissionsByUserId() throws Exception {
        when(formSubmissionService.getFormSubmissionsByUserId(20L)).thenReturn(list);

        mockMvc.perform(get("/api/v1/form-submissions/user/20"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].userId").value(20));

        verify(formSubmissionService).getFormSubmissionsByUserId(20L);
    }

    @Test
    void getFormSubmissionsByUserIdPaged() throws Exception {
        when(formSubmissionService.getFormSubmissionsByUserId(eq(20L), any(Pageable.class)))
                .thenReturn(page);

        mockMvc.perform(get("/api/v1/form-submissions/user/20/paged")
                        .param("page", "0")
                        .param("size", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content.length()").value(2))
                .andExpect(jsonPath("$.totalElements").value(2));

        verify(formSubmissionService).getFormSubmissionsByUserId(eq(20L), any(Pageable.class));
    }

    @Test
    void getFormSubmissionsByUserIdAndCategoryId() throws Exception {
        when(formSubmissionService.getFormSubmissionsByUserIdAndCategoryId(20L, 10L))
                .thenReturn(list);

        mockMvc.perform(get("/api/v1/form-submissions/user/20/category/10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[1].userId").value(20))
                .andExpect(jsonPath("$[1].categoryId").value(10));

        verify(formSubmissionService).getFormSubmissionsByUserIdAndCategoryId(20L, 10L);
    }

    @Test
    void getFormSubmissionsByUserIdAndCategoryIdPaged() throws Exception {
        when(formSubmissionService.getFormSubmissionsByUserIdAndCategoryId(eq(20L), eq(10L), any(Pageable.class)))
                .thenReturn(page);

        mockMvc.perform(get("/api/v1/form-submissions/user/20/category/10/paged")
                        .param("page", "0")
                        .param("size", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[1].id").value("ID-2"))
                .andExpect(jsonPath("$.totalElements").value(2));

        verify(formSubmissionService)
                .getFormSubmissionsByUserIdAndCategoryId(eq(20L), eq(10L), any(Pageable.class));
    }

    @Test
    void getFormSubmissionsByUserIdAndFormTemplateId() throws Exception {
        when(formSubmissionService.getFormSubmissionsByUserIdAndFormTemplateId(20L, 100L))
                .thenReturn(list);

        mockMvc.perform(get("/api/v1/form-submissions/user/20/form-template/100"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].userId").value(20))
                .andExpect(jsonPath("$[0].formTemplateId").value(100));

        verify(formSubmissionService).getFormSubmissionsByUserIdAndFormTemplateId(20L, 100L);
    }

    @Test
    void getFormSubmissionsByUserIdAndFormTemplateIdPaged() throws Exception {
        when(formSubmissionService.getFormSubmissionsByUserIdAndFormTemplateId(eq(20L), eq(100L), any(Pageable.class)))
                .thenReturn(page);

        mockMvc.perform(get("/api/v1/form-submissions/user/20/form-template/100/paged")
                        .param("page", "0")
                        .param("size", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content.length()").value(2))
                .andExpect(jsonPath("$.totalElements").value(2));

        verify(formSubmissionService)
                .getFormSubmissionsByUserIdAndFormTemplateId(eq(20L), eq(100L), any(Pageable.class));
    }

    @Test
    void submitForm() throws Exception {
        when(formSubmissionService.submitForm(any(SubmitFormRequest.class)))
                .thenReturn(dto1);

        mockMvc.perform(post("/api/v1/form-submissions/submit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(submitFormRequest)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").value("ID-1"));

        verify(formSubmissionService).submitForm(any(SubmitFormRequest.class));
    }

    @Test
    void deleteFormSubmission() throws Exception {
        doNothing().when(formSubmissionService).deleteFormSubmission("ID-1");

        mockMvc.perform(delete("/api/v1/form-submissions/ID-1"))
                .andExpect(status().isNoContent());

        verify(formSubmissionService).deleteFormSubmission("ID-1");
    }

    @Test
    void getFormSubmissionByRequestId() throws Exception {
        when(formSubmissionService.getFormSubmissionByRequestId("REQ-1"))
                .thenReturn(dto1);

        mockMvc.perform(get("/api/v1/form-submissions/request/REQ-1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.requestId").value("REQ-1"));

        verify(formSubmissionService).getFormSubmissionByRequestId("REQ-1");
    }

    @Test
    void checkRequestIdExists() throws Exception {
        when(formSubmissionService.existsByRequestId("REQ-1")).thenReturn(true);

        mockMvc.perform(get("/api/v1/form-submissions/request/REQ-1/exists"))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));

        verify(formSubmissionService).existsByRequestId("REQ-1");
    }

}
