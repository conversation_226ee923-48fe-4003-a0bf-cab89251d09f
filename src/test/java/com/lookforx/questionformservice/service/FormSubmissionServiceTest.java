package com.lookforx.questionformservice.service;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.common.exception.ValidationException;
import com.lookforx.questionformservice.base.AbstractBaseServiceTest;
import com.lookforx.questionformservice.domain.*;
import com.lookforx.questionformservice.dto.FormSubmissionDTO;
import com.lookforx.questionformservice.dto.request.FormResponseRequest;
import com.lookforx.questionformservice.dto.request.SubmitFormRequest;
import com.lookforx.common.exception.ResourceNotFoundException;
import com.lookforx.questionformservice.mapper.FormResponseMapper;
import com.lookforx.questionformservice.mapper.FormSubmissionMapper;
import com.lookforx.questionformservice.repository.FormElementRepository;
import com.lookforx.questionformservice.repository.FormSubmissionRepository;
import com.lookforx.questionformservice.repository.FormTemplateRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class FormSubmissionServiceTest extends AbstractBaseServiceTest {

    @Mock
    private FormSubmissionRepository formSubmissionRepository;

    @Mock
    private FormTemplateRepository formTemplateRepository;

    @Mock
    private FormElementRepository formElementRepository;

    @Mock
    private FormSubmissionMapper formSubmissionMapper;

    @Mock
    private FormResponseMapper formResponseMapper;

    @Mock
    private UserContextService userContextService;

    @InjectMocks
    private FormSubmissionService formSubmissionService;

    private FormTemplate formTemplate;
    private FormElement formElement;
    private FormSubmission formSubmission;
    private FormSubmissionDTO formSubmissionDTO;
    private SubmitFormRequest submitFormRequest;

    @BeforeEach
    void setUp() {
        // Setup form element
        Map<LanguageCode, String> labelTranslations = new HashMap<>();
        labelTranslations.put(LanguageCode.EN, "Full Name");
        labelTranslations.put(LanguageCode.TR, "Ad Soyad");

        formElement = FormElement.builder()
                .labelTranslations(labelTranslations)
                .type(ElementType.TEXT)
                .required(true)
                .displayOrder(1)
                .build();
        formElement.setId(1L);

        // Setup form template
        Map<LanguageCode, String> nameTranslations = new HashMap<>();
        nameTranslations.put(LanguageCode.EN, "Contact Form");
        nameTranslations.put(LanguageCode.TR, "İletişim Formu");

        formTemplate = FormTemplate.builder()
                .categoryId(100L)
                .nameTranslations(nameTranslations)
                .active(true)
                .elements(Arrays.asList(formElement))
                .build();
        formTemplate.setId(1L);
        formTemplate.setCreatedBy("admin");
        formTemplate.setCreatedAt(LocalDateTime.now());

        formElement.setFormTemplate(formTemplate);

        // Setup form response
        FormResponseRequest responseRequest = FormResponseRequest.builder()
                .formElementId(1L)
                .value("John Doe")
                .build();

        // Setup submit form request
        submitFormRequest = SubmitFormRequest.builder()
                .formTemplateId(1L)
                .categoryId(100L)
                .userId(123L)
                .requestId("REQ-001")
                .responses(Arrays.asList(responseRequest))
                .build();

        // Setup form response
        FormResponse formResponse = FormResponse.builder()
                .formElementId(1L)
                .value("John Doe")
                .build();

        // Setup form submission
        formSubmission = FormSubmission.builder()
                .id("submission-1")
                .formTemplateId(1L)
                .categoryId(100L)
                .userId(123L)
                .requestId("REQ-001")
                .submittedBy("user123")
                .responses(Arrays.asList(formResponse))
                .createdAt(LocalDateTime.now())
                .build();

        // Setup form submission DTO
        formSubmissionDTO = FormSubmissionDTO.builder()
                .id("submission-1")
                .formTemplateId(1L)
                .categoryId(100L)
                .userId(123L)
                .requestId("REQ-001")
                .submittedBy("user123")
                .createdAt(LocalDateTime.now())
                .build();
    }

    @Test
    @DisplayName("Should submit form successfully")
    void testSubmitForm() {
        // Given
        when(formSubmissionRepository.existsByRequestId("REQ-001")).thenReturn(false);
        when(formTemplateRepository.findByIdWithElements(1L)).thenReturn(Optional.of(formTemplate));
        when(userContextService.getCurrentUsername()).thenReturn("user123");
        when(userContextService.getPreferredLanguage()).thenReturn(LanguageCode.EN);
        when(formSubmissionMapper.toEntity(any(SubmitFormRequest.class))).thenReturn(formSubmission);
        when(formSubmissionRepository.save(any(FormSubmission.class))).thenReturn(formSubmission);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        // When
        FormSubmissionDTO result = formSubmissionService.submitForm(submitFormRequest);

        // Then
        assertNotNull(result);
        assertEquals("REQ-001", result.getRequestId());
        assertEquals(1L, result.getFormTemplateId());
        assertEquals(123L, result.getUserId());
        verify(formSubmissionRepository).existsByRequestId("REQ-001");
        verify(formTemplateRepository).findByIdWithElements(1L);
        verify(formSubmissionRepository).save(any(FormSubmission.class));
        verify(formSubmissionMapper).toDTO(formSubmission);
    }

    @Test
    @DisplayName("Should throw exception when request ID already exists")
    void testSubmitFormDuplicateRequestId() {
        // Given
        when(formSubmissionRepository.existsByRequestId("REQ-001")).thenReturn(true);

        // When & Then
        assertThrows(ValidationException.class,
                () -> formSubmissionService.submitForm(submitFormRequest));
        verify(formSubmissionRepository).existsByRequestId("REQ-001");
        verify(formTemplateRepository, never()).findByIdWithElements(any());
        verify(formSubmissionRepository, never()).save(any());
    }

    @Test
    @DisplayName("Should throw exception when form template not found")
    void testSubmitFormTemplateNotFound() {
        // Given
        when(formSubmissionRepository.existsByRequestId("REQ-001")).thenReturn(false);
        when(formTemplateRepository.findByIdWithElements(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> formSubmissionService.submitForm(submitFormRequest));
        verify(formSubmissionRepository).existsByRequestId("REQ-001");
        verify(formTemplateRepository).findByIdWithElements(1L);
        verify(formSubmissionRepository, never()).save(any());
    }

    @Test
    @DisplayName("Should throw exception when form template is inactive")
    void testSubmitFormInactiveTemplate() {
        // Given
        formTemplate.setActive(false);
        when(formSubmissionRepository.existsByRequestId("REQ-001")).thenReturn(false);
        when(formTemplateRepository.findByIdWithElements(1L)).thenReturn(Optional.of(formTemplate));

        // When & Then
        assertThrows(ValidationException.class,
                () -> formSubmissionService.submitForm(submitFormRequest));
        verify(formSubmissionRepository).existsByRequestId("REQ-001");
        verify(formTemplateRepository).findByIdWithElements(1L);
        verify(formSubmissionRepository, never()).save(any());
    }

    @Test
    @DisplayName("Should throw exception when category ID does not match")
    void testSubmitFormCategoryMismatch() {
        // Given
        submitFormRequest.setCategoryId(999L); // Different category
        when(formSubmissionRepository.existsByRequestId("REQ-001")).thenReturn(false);
        when(formTemplateRepository.findByIdWithElements(1L)).thenReturn(Optional.of(formTemplate));

        // When & Then
        assertThrows(ValidationException.class,
                () -> formSubmissionService.submitForm(submitFormRequest));
        verify(formSubmissionRepository).existsByRequestId("REQ-001");
        verify(formTemplateRepository).findByIdWithElements(1L);
        verify(formSubmissionRepository, never()).save(any());
    }

    @Test
    @DisplayName("Should get form submission by ID")
    void testGetFormSubmissionById() {
        // Given
        String submissionId = "submission-1";
        when(formSubmissionRepository.findById(submissionId)).thenReturn(Optional.of(formSubmission));
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        // When
        FormSubmissionDTO result = formSubmissionService.getFormSubmissionById(submissionId);

        // Then
        assertNotNull(result);
        assertEquals(submissionId, result.getId());
        verify(formSubmissionRepository).findById(submissionId);
        verify(formSubmissionMapper).toDTO(formSubmission);
    }

    @Test
    @DisplayName("Should throw exception when form submission not found")
    void testGetFormSubmissionByIdNotFound() {
        // Given
        String submissionId = "non-existent";
        when(formSubmissionRepository.findById(submissionId)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> formSubmissionService.getFormSubmissionById(submissionId));
        verify(formSubmissionRepository).findById(submissionId);
        verify(formSubmissionMapper, never()).toDTO(any());
    }

    @Test
    @DisplayName("Should get form submissions by user ID")
    void testGetFormSubmissionsByUserId() {
        // Given
        Long userId = 123L;
        Pageable pageable = PageRequest.of(0, 10);
        Page<FormSubmission> submissionPage = new PageImpl<>(Arrays.asList(formSubmission));
        when(formSubmissionRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable)).thenReturn(submissionPage);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        // When
        Page<FormSubmissionDTO> result = formSubmissionService.getFormSubmissionsByUserId(userId, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(userId, result.getContent().get(0).getUserId());
        verify(formSubmissionRepository).findByUserIdOrderByCreatedAtDesc(userId, pageable);
    }

    @Test
    @DisplayName("Should get form submissions by form template ID")
    void testGetFormSubmissionsByFormTemplateId() {
        // Given
        Long formTemplateId = 1L;
        Pageable pageable = PageRequest.of(0, 10);
        Page<FormSubmission> submissionPage = new PageImpl<>(Arrays.asList(formSubmission));
        when(formSubmissionRepository.findByFormTemplateId(formTemplateId, pageable)).thenReturn(submissionPage);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        // When
        Page<FormSubmissionDTO> result = formSubmissionService.getFormSubmissionsByFormTemplateId(formTemplateId, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(formTemplateId, result.getContent().get(0).getFormTemplateId());
        verify(formSubmissionRepository).findByFormTemplateId(formTemplateId, pageable);
    }

    @Test
    @DisplayName("Should get form submission by request ID")
    void testGetFormSubmissionByRequestId() {
        // Given
        String requestId = "REQ-001";
        when(formSubmissionRepository.findByRequestId(requestId)).thenReturn(Optional.of(formSubmission));
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        // When
        FormSubmissionDTO result = formSubmissionService.getFormSubmissionByRequestId(requestId);

        // Then
        assertNotNull(result);
        assertEquals(requestId, result.getRequestId());
        verify(formSubmissionRepository).findByRequestId(requestId);
        verify(formSubmissionMapper).toDTO(formSubmission);
    }

    @Test
    @DisplayName("Should throw exception when form submission not found by request ID")
    void testGetFormSubmissionByRequestIdNotFound() {
        // Given
        String requestId = "NON-EXISTENT";
        when(formSubmissionRepository.findByRequestId(requestId)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> formSubmissionService.getFormSubmissionByRequestId(requestId));
        verify(formSubmissionRepository).findByRequestId(requestId);
        verify(formSubmissionMapper, never()).toDTO(any());
    }

    // ─────────────────────────────────────────────────────────────────
    // getAllFormSubmissions (list & page)
    // ─────────────────────────────────────────────────────────────────

    @Test
    @DisplayName("Should get all form submissions (list)")
    void testGetAllFormSubmissionsList() {
        List<FormSubmission> subs = List.of(formSubmission);
        when(formSubmissionRepository.findAll()).thenReturn(subs);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        List<FormSubmissionDTO> result = formSubmissionService.getAllFormSubmissions();

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("submission-1", result.get(0).getId());
        verify(formSubmissionRepository).findAll();
        verify(formSubmissionMapper).toDTO(formSubmission);
    }

    @Test
    @DisplayName("Should get all form submissions (paged)")
    void testGetAllFormSubmissionsPage() {
        Pageable pageable = PageRequest.of(0, 5);
        Page<FormSubmission> page = new PageImpl<>(List.of(formSubmission), pageable, 1);
        when(formSubmissionRepository.findAll(pageable)).thenReturn(page);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        Page<FormSubmissionDTO> result = formSubmissionService.getAllFormSubmissions(pageable);

        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("submission-1", result.getContent().get(0).getId());
        verify(formSubmissionRepository).findAll(pageable);
    }

    // ─────────────────────────────────────────────────────────────────
    // getFormSubmissionsByCategoryId (list & page)
    // ─────────────────────────────────────────────────────────────────

    @Test
    @DisplayName("Should get form submissions by category ID (list)")
    void testGetFormSubmissionsByCategoryIdList() {
        Long categoryId = 100L;
        List<FormSubmission> subs = List.of(formSubmission);
        when(formSubmissionRepository.findByCategoryId(categoryId)).thenReturn(subs);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        List<FormSubmissionDTO> result =
                formSubmissionService.getFormSubmissionsByCategoryId(categoryId);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(100L, result.get(0).getCategoryId());
        verify(formSubmissionRepository).findByCategoryId(categoryId);
        verify(formSubmissionMapper).toDTO(formSubmission);
    }

    @Test
    @DisplayName("Should get form submissions by category ID (paged)")
    void testGetFormSubmissionsByCategoryIdPage() {
        Long categoryId = 100L;
        Pageable pageable = PageRequest.of(0, 5);
        Page<FormSubmission> page = new PageImpl<>(List.of(formSubmission), pageable, 1);
        when(formSubmissionRepository.findByCategoryId(categoryId, pageable)).thenReturn(page);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        Page<FormSubmissionDTO> result =
                formSubmissionService.getFormSubmissionsByCategoryId(categoryId, pageable);

        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(100L, result.getContent().get(0).getCategoryId());
        verify(formSubmissionRepository).findByCategoryId(categoryId, pageable);
    }

    // ─────────────────────────────────────────────────────────────────
    // getFormSubmissionsByFormTemplateId (list)
    // ─────────────────────────────────────────────────────────────────

    @Test
    @DisplayName("Should get form submissions by form template ID (list)")
    void testGetFormSubmissionsByFormTemplateIdList() {
        Long templateId = 1L;
        List<FormSubmission> subs = List.of(formSubmission);
        when(formSubmissionRepository.findByFormTemplateId(templateId)).thenReturn(subs);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        List<FormSubmissionDTO> result =
                formSubmissionService.getFormSubmissionsByFormTemplateId(templateId);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getFormTemplateId());
        verify(formSubmissionRepository).findByFormTemplateId(templateId);
        verify(formSubmissionMapper).toDTO(formSubmission);
    }

    // ─────────────────────────────────────────────────────────────────
    // deleteFormSubmission
    // ─────────────────────────────────────────────────────────────────

    @Test
    @DisplayName("Should delete form submission when exists")
    void testDeleteFormSubmissionSuccess() {
        String id = "submission-1";
        when(formSubmissionRepository.existsById(id)).thenReturn(true);

        formSubmissionService.deleteFormSubmission(id);

        verify(formSubmissionRepository).existsById(id);
        verify(formSubmissionRepository).deleteById(id);
    }

    @Test
    @DisplayName("Should throw exception when deleting non-existent submission")
    void testDeleteFormSubmissionNotFound() {
        String id = "missing";
        when(formSubmissionRepository.existsById(id)).thenReturn(false);

        assertThrows(ResourceNotFoundException.class,
                () -> formSubmissionService.deleteFormSubmission(id));

        verify(formSubmissionRepository).existsById(id);
        verify(formSubmissionRepository, never()).deleteById(any());
    }

    // ─────────────────────────────────────────────────────────────────
    // getFormSubmissionsByUserId (list)
    // ─────────────────────────────────────────────────────────────────

    @Test
    @DisplayName("Should get form submissions by user ID (list)")
    void testGetFormSubmissionsByUserIdList() {
        Long userId = 123L;
        List<FormSubmission> subs = List.of(formSubmission);
        when(formSubmissionRepository.findByUserId(userId)).thenReturn(subs);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        List<FormSubmissionDTO> result =
                formSubmissionService.getFormSubmissionsByUserId(userId);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(123L, result.get(0).getUserId());
        verify(formSubmissionRepository).findByUserId(userId);
        verify(formSubmissionMapper).toDTO(formSubmission);
    }

    // ─────────────────────────────────────────────────────────────────
    // getFormSubmissionsByUserIdAndCategoryId (list & page)
    // ─────────────────────────────────────────────────────────────────

    @Test
    @DisplayName("Should get form submissions by user ID & category ID (list)")
    void testGetFormSubmissionsByUserIdAndCategoryIdList() {
        Long userId = 123L, categoryId = 100L;
        List<FormSubmission> subs = List.of(formSubmission);
        when(formSubmissionRepository.findByUserIdAndCategoryIdOrderByCreatedAtDesc(userId, categoryId))
                .thenReturn(subs);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        List<FormSubmissionDTO> result =
                formSubmissionService.getFormSubmissionsByUserIdAndCategoryId(userId, categoryId);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(100L, result.get(0).getCategoryId());
        verify(formSubmissionRepository)
                .findByUserIdAndCategoryIdOrderByCreatedAtDesc(userId, categoryId);
        verify(formSubmissionMapper).toDTO(formSubmission);
    }

    @Test
    @DisplayName("Should get form submissions by user ID & category ID (paged)")
    void testGetFormSubmissionsByUserIdAndCategoryIdPage() {
        Long userId = 123L, categoryId = 100L;
        Pageable pageable = PageRequest.of(0, 5);
        Page<FormSubmission> page = new PageImpl<>(List.of(formSubmission), pageable, 1);
        when(formSubmissionRepository.findByUserIdAndCategoryId(userId, categoryId, pageable))
                .thenReturn(page);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        Page<FormSubmissionDTO> result =
                formSubmissionService.getFormSubmissionsByUserIdAndCategoryId(userId, categoryId, pageable);

        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(123L, result.getContent().get(0).getUserId());
        verify(formSubmissionRepository)
                .findByUserIdAndCategoryId(userId, categoryId, pageable);
    }

    // ─────────────────────────────────────────────────────────────────
    // getFormSubmissionsByUserIdAndFormTemplateId (list)
    // ─────────────────────────────────────────────────────────────────

    @Test
    @DisplayName("Should get form submissions by user ID & form template ID (list)")
    void testGetFormSubmissionsByUserIdAndFormTemplateIdList() {
        Long userId = 123L, templateId = 1L;
        List<FormSubmission> subs = List.of(formSubmission);
        when(formSubmissionRepository.findByUserIdAndFormTemplateId(userId, templateId))
                .thenReturn(subs);
        when(formSubmissionMapper.toDTO(formSubmission)).thenReturn(formSubmissionDTO);

        List<FormSubmissionDTO> result =
                formSubmissionService.getFormSubmissionsByUserIdAndFormTemplateId(userId, templateId);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("submission-1", result.get(0).getId());
        verify(formSubmissionRepository)
                .findByUserIdAndFormTemplateId(userId, templateId);
        verify(formSubmissionMapper).toDTO(formSubmission);
    }

    @Test
    @DisplayName("existsByRequestId should return true when repository returns true")
    void testExistsByRequestIdTrue() {
        // Given
        String requestId = "REQ-123";
        when(formSubmissionRepository.existsByRequestId(requestId)).thenReturn(true);

        // When
        boolean result = formSubmissionService.existsByRequestId(requestId);

        // Then
        assertTrue(result);
        verify(formSubmissionRepository).existsByRequestId(requestId);
    }

    @Test
    @DisplayName("existsByRequestId should return false when repository returns false")
    void testExistsByRequestIdFalse() {
        // Given
        String requestId = "REQ-999";
        when(formSubmissionRepository.existsByRequestId(requestId)).thenReturn(false);

        // When
        boolean result = formSubmissionService.existsByRequestId(requestId);

        // Then
        assertFalse(result);
        verify(formSubmissionRepository).existsByRequestId(requestId);
    }

    @Test
    @DisplayName("Should get form submissions by user ID and form template ID with pagination")
    void testGetFormSubmissionsByUserIdAndFormTemplateIdPaged() {
        // Given
        Long userId = 123L;
        Long formTemplateId = 1L;
        Pageable pageable = PageRequest.of(0, 5);
        List<FormSubmission> submissions = List.of(formSubmission);
        Page<FormSubmission> page = new PageImpl<>(submissions, pageable, submissions.size());

        when(formSubmissionRepository.findByUserIdAndFormTemplateId(userId, formTemplateId, pageable))
                .thenReturn(page);
        when(formSubmissionMapper.toDTO(formSubmission))
                .thenReturn(formSubmissionDTO);

        // When
        Page<FormSubmissionDTO> result =
                formSubmissionService.getFormSubmissionsByUserIdAndFormTemplateId(userId, formTemplateId, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("submission-1", result.getContent().get(0).getId());

        verify(formSubmissionRepository)
                .findByUserIdAndFormTemplateId(userId, formTemplateId, pageable);
        verify(formSubmissionMapper).toDTO(formSubmission);
    }

}
