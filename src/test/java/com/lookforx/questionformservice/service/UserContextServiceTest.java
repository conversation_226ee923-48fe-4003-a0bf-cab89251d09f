package com.lookforx.questionformservice.service;

import com.lookforx.questionformservice.base.AbstractBaseServiceTest;
import com.lookforx.common.enums.LanguageCode;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InOrder;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class UserContextServiceTest extends AbstractBaseServiceTest {

    @InjectMocks
    private UserContextService service;

    @Mock
    private ServletRequestAttributes servletRequestAttributes;

    @Mock
    private HttpServletRequest httpServletRequest;

    @Mock
    private Authentication authentication;

    @BeforeEach
    void setUp() {
        RequestContextHolder.resetRequestAttributes();
        SecurityContextHolder.clearContext();
    }

    @AfterEach
    void tearDown() {
        RequestContextHolder.resetRequestAttributes();
        SecurityContextHolder.clearContext();
    }

    // -------------------------------------------------------------------------
    // getCurrentUsername()
    // -------------------------------------------------------------------------

    @Test
    void getCurrentUsername_whenAuthenticationIsNull_returnsSystem() {
        // Given: no Authentication in SecurityContext
        SecurityContextHolder.getContext().setAuthentication(null);

        // When
        String username = service.getCurrentUsername();

        // Then
        assertEquals("system", username);
    }

    @Test
    void getCurrentUsername_whenNotAuthenticated_returnsSystem() {
        // Given: an Authentication that's not authenticated
        when(authentication.isAuthenticated()).thenReturn(false);
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // When
        String username = service.getCurrentUsername();

        // Then
        assertEquals("system", username);
        verify(authentication).isAuthenticated();
        verify(authentication, never()).getName();
    }

    @Test
    void getCurrentUsername_whenAuthenticated_returnsName() {

        // Given: an authenticated Authentication with a name
        when(authentication.isAuthenticated()).thenReturn(true);
        when(authentication.getName()).thenReturn("alice");
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // When
        String username = service.getCurrentUsername();

        // Then
        assertEquals("alice", username);
        InOrder inOrder = inOrder(authentication);
        inOrder.verify(authentication).isAuthenticated();
        inOrder.verify(authentication).getName();

    }

    // -------------------------------------------------------------------------
    // getPreferredLanguage()
    // -------------------------------------------------------------------------

    @Test
    void getPreferredLanguage_whenAcceptLanguageHeaderValid_returnsHeaderLanguage() {
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        when(servletRequestAttributes.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getHeader("Accept-Language"))
                .thenReturn("tr-TR,tr;q=0.9");

        var result = service.getPreferredLanguage();

        assertEquals(LanguageCode.TR, result);
        verify(servletRequestAttributes).getRequest();
        verify(httpServletRequest).getHeader("Accept-Language");
    }

    @Test
    void getPreferredLanguage_whenAcceptLanguageHeaderUnsupported_returnsDefaultEN() {
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        when(servletRequestAttributes.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getHeader("Accept-Language"))
                .thenReturn("xx-XX,yy;q=0.8");

        var result = service.getPreferredLanguage();

        // unsupported header → fallback to EN
        assertEquals(LanguageCode.EN, result);
        verify(httpServletRequest).getHeader("Accept-Language");
    }

    @Test
    void getPreferredLanguage_whenNoRequestAttributes_returnsDefaultEN() {
        // no RequestContextHolder attributes set
        var result = service.getPreferredLanguage();

        // no header available → fallback to EN
        assertEquals(LanguageCode.EN, result);
    }

    @Test
    void getPreferredLanguage_whenHeaderThrowsException_returnsDefaultEN() {
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        when(servletRequestAttributes.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getHeader("Accept-Language"))
                .thenThrow(new RuntimeException("bad header"));

        var result = service.getPreferredLanguage();

        // exception parsing header → fallback to EN
        assertEquals(LanguageCode.EN, result);
    }

    // -------------------------------------------------------------------------
    // getFallbackLanguage()
    // -------------------------------------------------------------------------

    @Test
    void getFallbackLanguage_alwaysReturnsEN() {
        assertEquals(LanguageCode.EN, service.getFallbackLanguage());
    }

    // -------------------------------------------------------------------------
    // getLanguagePreference()
    // -------------------------------------------------------------------------

    @Test
    void getLanguagePreference_whenHeaderPresent_prefersHeaderAndFallbackEN() {
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        when(servletRequestAttributes.getRequest()).thenReturn(httpServletRequest);
        when(httpServletRequest.getHeader("Accept-Language"))
                .thenReturn("es-ES,es;q=0.8");

        var pref = service.getLanguagePreference();

        assertEquals(LanguageCode.ES, pref.getPreferred());
        assertEquals(LanguageCode.EN, pref.getFallback());
        verify(servletRequestAttributes).getRequest();
        verify(httpServletRequest).getHeader("Accept-Language");
    }

    @Test
    void getLanguagePreference_whenNoHeader_prefersDefaultENAndFallbackEN() {
        // no attributes → getPreferredLanguage()==null → fallback to EN
        RequestContextHolder.resetRequestAttributes();

        var pref = service.getLanguagePreference();

        assertEquals(LanguageCode.EN, pref.getPreferred());
        assertEquals(LanguageCode.EN, pref.getFallback());
    }

}