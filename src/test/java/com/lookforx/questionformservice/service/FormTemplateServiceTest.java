package com.lookforx.questionformservice.service;

import com.lookforx.questionformservice.base.AbstractBaseServiceTest;
import com.lookforx.questionformservice.domain.FormTemplate;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.questionformservice.dto.FormTemplateDTO;
import com.lookforx.questionformservice.dto.LocalizedFormTemplateDTO;
import com.lookforx.questionformservice.dto.request.CreateFormTemplateRequest;
import com.lookforx.questionformservice.dto.request.UpdateFormTemplateRequest;
import com.lookforx.common.exception.ResourceNotFoundException;
import com.lookforx.questionformservice.mapper.FormTemplateMapper;
import com.lookforx.questionformservice.repository.FormTemplateRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class FormTemplateServiceTest extends AbstractBaseServiceTest {

    @Mock
    private FormTemplateRepository formTemplateRepository;

    @Mock
    private FormTemplateMapper formTemplateMapper;

    @Mock
    private UserContextService userContextService;

    @InjectMocks
    private FormTemplateService formTemplateService;

    private FormTemplate formTemplate;
    private FormTemplateDTO formTemplateDTO;
    private CreateFormTemplateRequest createRequest;
    private UpdateFormTemplateRequest updateRequest;

    @BeforeEach
    void setUp() {
        // Setup test data
        Map<LanguageCode, String> nameTranslations = new HashMap<>();
        nameTranslations.put(LanguageCode.EN, "Test Form");
        nameTranslations.put(LanguageCode.TR, "Test Formu");

        Map<LanguageCode, String> descTranslations = new HashMap<>();
        descTranslations.put(LanguageCode.EN, "Test Description");
        descTranslations.put(LanguageCode.TR, "Test Açıklaması");

        formTemplate = FormTemplate.builder()
                .categoryId(100L)
                .nameTranslations(nameTranslations)
                .descriptionTranslations(descTranslations)
                .active(true)
                .elements(new ArrayList<>())
                .build();
        formTemplate.setId(1L);
        formTemplate.setCreatedBy("testuser");
        formTemplate.setCreatedAt(LocalDateTime.now());

        formTemplateDTO = FormTemplateDTO.builder()
                .id(1L)
                .categoryId(100L)
                .nameTranslations(nameTranslations)
                .descriptionTranslations(descTranslations)
                .active(true)
                .createdBy("testuser")
                .createdAt(LocalDateTime.now())
                .build();

        createRequest = CreateFormTemplateRequest.builder()
                .categoryId(100L)
                .nameTranslations(nameTranslations)
                .descriptionTranslations(descTranslations)
                .active(true)
                .build();

        updateRequest = UpdateFormTemplateRequest.builder()
                .nameTranslations(nameTranslations)
                .descriptionTranslations(descTranslations)
                .active(false)
                .build();
    }

    @Test
    @DisplayName("Should get all form templates")
    void testGetAllFormTemplates() {
        // Given
        List<FormTemplate> templates = Arrays.asList(formTemplate);
        when(formTemplateRepository.findAll()).thenReturn(templates);
        when(formTemplateMapper.toDTO(formTemplate)).thenReturn(formTemplateDTO);

        // When
        List<FormTemplateDTO> result = formTemplateService.getAllFormTemplates();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(formTemplateDTO.getId(), result.get(0).getId());
        verify(formTemplateRepository).findAll();
        verify(formTemplateMapper).toDTO(formTemplate);
    }

    @Test
    @DisplayName("Should get all form templates with pagination")
    void testGetAllFormTemplatesWithPagination() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<FormTemplate> templatePage = new PageImpl<>(Arrays.asList(formTemplate));
        when(formTemplateRepository.findAll(pageable)).thenReturn(templatePage);
        when(formTemplateMapper.toDTO(formTemplate)).thenReturn(formTemplateDTO);

        // When
        Page<FormTemplateDTO> result = formTemplateService.getAllFormTemplates(pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(formTemplateDTO.getId(), result.getContent().get(0).getId());
        verify(formTemplateRepository).findAll(pageable);
    }

    @Test
    @DisplayName("Should get form template by ID")
    void testGetFormTemplateById() {
        // Given
        Long templateId = 1L;
        when(formTemplateRepository.findByIdWithElements(templateId)).thenReturn(Optional.of(formTemplate));
        when(formTemplateMapper.toDTO(formTemplate)).thenReturn(formTemplateDTO);

        // When
        FormTemplateDTO result = formTemplateService.getFormTemplateById(templateId);

        // Then
        assertNotNull(result);
        assertEquals(templateId, result.getId());
        verify(formTemplateRepository).findByIdWithElements(templateId);
        verify(formTemplateMapper).toDTO(formTemplate);
    }

    @Test
    @DisplayName("Should throw exception when form template not found")
    void testGetFormTemplateByIdNotFound() {
        // Given
        Long templateId = 999L;
        when(formTemplateRepository.findByIdWithElements(templateId)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class,
                () -> formTemplateService.getFormTemplateById(templateId));
        verify(formTemplateRepository).findByIdWithElements(templateId);
        verify(formTemplateMapper, never()).toDTO(any());
    }

    @Test
    @DisplayName("Should create form template")
    void testCreateFormTemplate() {
        // Given
        when(userContextService.getCurrentUsername()).thenReturn("testuser");
        when(formTemplateMapper.toEntity(createRequest)).thenReturn(formTemplate);
        when(formTemplateRepository.save(any(FormTemplate.class))).thenReturn(formTemplate);
        when(formTemplateMapper.toDTO(formTemplate)).thenReturn(formTemplateDTO);

        // When
        FormTemplateDTO result = formTemplateService.createFormTemplate(createRequest);

        // Then
        assertNotNull(result);
        assertEquals(formTemplateDTO.getId(), result.getId());
        verify(userContextService).getCurrentUsername();
        verify(formTemplateMapper).toEntity(createRequest);
        verify(formTemplateRepository).save(any(FormTemplate.class));
        verify(formTemplateMapper).toDTO(formTemplate);
    }

    @Test
    @DisplayName("Should update form template")
    void testUpdateFormTemplate() {
        // Given
        Long templateId = 1L;
        when(formTemplateRepository.findByIdWithElements(templateId)).thenReturn(Optional.of(formTemplate));
        when(userContextService.getCurrentUsername()).thenReturn("updater");
        when(formTemplateRepository.save(any(FormTemplate.class))).thenReturn(formTemplate);
        when(formTemplateMapper.toDTO(formTemplate)).thenReturn(formTemplateDTO);

        // When
        FormTemplateDTO result = formTemplateService.updateFormTemplate(templateId, updateRequest);

        // Then
        assertNotNull(result);
        assertEquals(templateId, result.getId());
        verify(formTemplateRepository).findByIdWithElements(templateId);
        verify(userContextService).getCurrentUsername();
        verify(formTemplateRepository).save(any(FormTemplate.class));
        verify(formTemplateMapper).toDTO(formTemplate);
    }

    @Test
    @DisplayName("Should delete form template")
    void testDeleteFormTemplate() {
        // Given
        Long templateId = 1L;
        when(formTemplateRepository.existsById(templateId)).thenReturn(true);

        // When
        formTemplateService.deleteFormTemplate(templateId);

        // Then
        verify(formTemplateRepository).existsById(templateId);
        verify(formTemplateRepository).deleteById(templateId);
    }

    @Test
    @DisplayName("Should throw exception when deleting non-existent form template")
    void testDeleteFormTemplateNotFound() {
        // Given
        Long templateId = 999L;
        when(formTemplateRepository.existsById(templateId)).thenReturn(false);

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> formTemplateService.deleteFormTemplate(templateId));
        verify(formTemplateRepository).existsById(templateId);
        verify(formTemplateRepository, never()).deleteById(any());
    }

    @Test
    @DisplayName("Should get localized form template")
    void testGetLocalizedFormTemplate() {
        // Given
        Long templateId = 1L;
        LanguageCode language = LanguageCode.TR;
        when(formTemplateRepository.findByIdWithElements(templateId)).thenReturn(Optional.of(formTemplate));

        LocalizedFormTemplateDTO localizedDTO = LocalizedFormTemplateDTO.builder()
                .id(templateId)
                .name("Test Formu")
                .description("Test Açıklaması")
                .build();
        when(formTemplateMapper.toLocalizedDTO(formTemplate, language, LanguageCode.EN))
                .thenReturn(localizedDTO);

        // When
        LocalizedFormTemplateDTO result = formTemplateService.getFormTemplateByIdLocalized(templateId, language);

        // Then
        assertNotNull(result);
        assertEquals("Test Formu", result.getName());
        assertEquals("Test Açıklaması", result.getDescription());
        verify(formTemplateRepository).findByIdWithElements(templateId);
        verify(formTemplateMapper).toLocalizedDTO(formTemplate, language, LanguageCode.EN);
    }

    @Test
    @DisplayName("Should get form templates by category")
    void testGetFormTemplatesByCategory() {
        // Given
        Long categoryId = 100L;
        List<FormTemplate> templates = Arrays.asList(formTemplate);
        when(formTemplateRepository.findByCategoryIdWithElements(categoryId)).thenReturn(templates);
        when(formTemplateMapper.toDTO(formTemplate)).thenReturn(formTemplateDTO);

        // When
        List<FormTemplateDTO> result = formTemplateService.getFormTemplatesByCategoryId(categoryId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(categoryId, result.get(0).getCategoryId());
        verify(formTemplateRepository).findByCategoryIdWithElements(categoryId);
        verify(formTemplateMapper).toDTO(formTemplate);
    }

    // ─────────────────────────────────────────────────────────────────
    // activateFormTemplate & deactivateFormTemplate
    // ─────────────────────────────────────────────────────────────────

    @Test
    @DisplayName("Should activate form template")
    void testActivateFormTemplate() {
        Long templateId = 1L;
        FormTemplate fresh = FormTemplate.builder()
                .active(false)
                .build();
        fresh.setId(templateId);
        when(formTemplateRepository.findById(templateId)).thenReturn(Optional.of(fresh));
        when(userContextService.getCurrentUsername()).thenReturn("activator");
        // simulate save returning the same entity but now active
        fresh.setActive(true);
        when(formTemplateRepository.save(fresh)).thenReturn(fresh);

        FormTemplateDTO dto = FormTemplateDTO.builder().id(templateId).active(true).build();
        when(formTemplateMapper.toDTO(fresh)).thenReturn(dto);

        FormTemplateDTO result = formTemplateService.activateFormTemplate(templateId);

        assertEquals(templateId, result.getId());
        assertTrue(result.isActive());
        verify(formTemplateRepository).findById(templateId);
        verify(userContextService).getCurrentUsername();
        verify(formTemplateRepository).save(fresh);
        verify(formTemplateMapper).toDTO(fresh);
    }

    @Test
    @DisplayName("Should deactivate form template")
    void testDeactivateFormTemplate() {
        Long templateId = 1L;
        FormTemplate fresh = FormTemplate.builder()
                .active(true)
                .build();
        fresh.setId(templateId);
        when(formTemplateRepository.findById(templateId)).thenReturn(Optional.of(fresh));
        when(userContextService.getCurrentUsername()).thenReturn("deactivator");
        // simulate save returning the same entity but now inactive
        fresh.setActive(false);
        when(formTemplateRepository.save(fresh)).thenReturn(fresh);

        FormTemplateDTO dto = FormTemplateDTO.builder().id(templateId).active(false).build();
        when(formTemplateMapper.toDTO(fresh)).thenReturn(dto);

        FormTemplateDTO result = formTemplateService.deactivateFormTemplate(templateId);

        assertEquals(templateId, result.getId());
        assertFalse(result.isActive());
        verify(formTemplateRepository).findById(templateId);
        verify(userContextService).getCurrentUsername();
        verify(formTemplateRepository).save(fresh);
        verify(formTemplateMapper).toDTO(fresh);
    }

    // ─────────────────────────────────────────────────────────────────
    // getAllFormTemplatesLocalized (default & custom fallback)
    // ─────────────────────────────────────────────────────────────────

    @Test
    @DisplayName("Should get all localized templates with default fallback")
    void testGetAllFormTemplatesLocalized_defaultFallback() {
        LanguageCode lang = LanguageCode.TR;
        List<FormTemplate> templs = List.of(formTemplate);
        when(formTemplateRepository.findByLanguageCodeAndActiveTrue(lang)).thenReturn(templs);

        LocalizedFormTemplateDTO locDto = LocalizedFormTemplateDTO.builder()
                .id(formTemplate.getId())
                .build();
        when(formTemplateMapper.toLocalizedDTO(formTemplate, lang, LanguageCode.EN))
                .thenReturn(locDto);

        List<LocalizedFormTemplateDTO> result = formTemplateService.getAllFormTemplatesLocalized(lang);

        assertEquals(1, result.size());
        assertEquals(formTemplate.getId(), result.get(0).getId());
        verify(formTemplateRepository).findByLanguageCodeAndActiveTrue(lang);
        verify(formTemplateMapper).toLocalizedDTO(formTemplate, lang, LanguageCode.EN);
    }

    @Test
    @DisplayName("Should get all localized templates with custom fallback")
    void testGetAllFormTemplatesLocalized_customFallback() {
        LanguageCode lang = LanguageCode.DE;
        LanguageCode fallback = LanguageCode.FR;
        List<FormTemplate> all = List.of(formTemplate);
        when(formTemplateRepository.findAll()).thenReturn(all);

        LocalizedFormTemplateDTO locDto = LocalizedFormTemplateDTO.builder()
                .id(formTemplate.getId())
                .build();
        when(formTemplateMapper.toLocalizedDTO(formTemplate, lang, fallback))
                .thenReturn(locDto);

        List<LocalizedFormTemplateDTO> result = formTemplateService.getAllFormTemplatesLocalized(lang, fallback);

        assertEquals(1, result.size());
        verify(formTemplateRepository).findAll();
        verify(formTemplateMapper).toLocalizedDTO(formTemplate, lang, fallback);
    }

    // ─────────────────────────────────────────────────────────────────
    // getFormTemplatesByCategoryIdLocalized (default & custom)
    // ─────────────────────────────────────────────────────────────────

    @Test
    @DisplayName("Should get localized templates by category with default fallback")
    void testGetFormTemplatesByCategoryIdLocalized_default() {
        Long catId = 100L;
        LanguageCode lang = LanguageCode.ES;
        List<FormTemplate> templs = List.of(formTemplate);
        when(formTemplateRepository.findByCategoryIdAndLanguageCodeWithElements(catId, lang))
                .thenReturn(templs);

        LocalizedFormTemplateDTO locDto = LocalizedFormTemplateDTO.builder()
                .id(formTemplate.getId())
                .build();
        when(formTemplateMapper.toLocalizedDTO(formTemplate, lang, LanguageCode.EN))
                .thenReturn(locDto);

        List<LocalizedFormTemplateDTO> result =
                formTemplateService.getFormTemplatesByCategoryIdLocalized(catId, lang);

        assertEquals(1, result.size());
        verify(formTemplateRepository)
                .findByCategoryIdAndLanguageCodeWithElements(catId, lang);
        verify(formTemplateMapper).toLocalizedDTO(formTemplate, lang, LanguageCode.EN);
    }

    @Test
    @DisplayName("Should get localized templates by category with custom fallback")
    void testGetFormTemplatesByCategoryIdLocalized_custom() {
        Long catId = 100L;
        LanguageCode lang = LanguageCode.IT;
        LanguageCode fallback = LanguageCode.PT;
        List<FormTemplate> templs = List.of(formTemplate);
        when(formTemplateRepository.findByCategoryIdWithElements(catId)).thenReturn(templs);

        LocalizedFormTemplateDTO locDto = LocalizedFormTemplateDTO.builder()
                .id(formTemplate.getId())
                .build();
        when(formTemplateMapper.toLocalizedDTO(formTemplate, lang, fallback))
                .thenReturn(locDto);

        List<LocalizedFormTemplateDTO> result =
                formTemplateService.getFormTemplatesByCategoryIdLocalized(catId, lang, fallback);

        assertEquals(1, result.size());
        verify(formTemplateRepository).findByCategoryIdWithElements(catId);
        verify(formTemplateMapper).toLocalizedDTO(formTemplate, lang, fallback);
    }

    // ─────────────────────────────────────────────────────────────────
    // searchFormTemplatesByName & searchFormTemplates (default & custom)
    // ─────────────────────────────────────────────────────────────────

    @Test
    @DisplayName("Should search templates by name with default fallback")
    void testSearchFormTemplatesByName() {
        String term = "hello";
        LanguageCode lang = LanguageCode.ZH;
        List<FormTemplate> templs = List.of(formTemplate);
        when(formTemplateRepository.searchByNameInLanguage(term, lang)).thenReturn(templs);

        LocalizedFormTemplateDTO locDto = LocalizedFormTemplateDTO.builder()
                .id(formTemplate.getId())
                .build();
        when(formTemplateMapper.toLocalizedDTO(formTemplate, lang, LanguageCode.EN))
                .thenReturn(locDto);

        List<LocalizedFormTemplateDTO> result =
                formTemplateService.searchFormTemplatesByName(term, lang);

        assertEquals(1, result.size());
        verify(formTemplateRepository).searchByNameInLanguage(term, lang);
        verify(formTemplateMapper).toLocalizedDTO(formTemplate, lang, LanguageCode.EN);
    }

    @Test
    @DisplayName("Should search templates by name or description with default fallback")
    void testSearchFormTemplates_default() {
        String term = "world";
        LanguageCode lang = LanguageCode.HI;
        List<FormTemplate> templs = List.of(formTemplate);
        when(formTemplateRepository.searchByNameOrDescriptionInLanguage(term, lang))
                .thenReturn(templs);

        LocalizedFormTemplateDTO locDto = LocalizedFormTemplateDTO.builder()
                .id(formTemplate.getId())
                .build();
        when(formTemplateMapper.toLocalizedDTO(formTemplate, lang, LanguageCode.EN))
                .thenReturn(locDto);

        List<LocalizedFormTemplateDTO> result =
                formTemplateService.searchFormTemplates(term, lang);

        assertEquals(1, result.size());
        verify(formTemplateRepository).searchByNameOrDescriptionInLanguage(term, lang);
        verify(formTemplateMapper).toLocalizedDTO(formTemplate, lang, LanguageCode.EN);
    }

    @Test
    @DisplayName("Should search templates by name or description with custom fallback")
    void testSearchFormTemplates_customFallback() {
        String term = "foo";
        LanguageCode lang = LanguageCode.SV;
        LanguageCode fallback = LanguageCode.NL;
        List<FormTemplate> templs = List.of(formTemplate);
        when(formTemplateRepository.searchByNameOrDescriptionInLanguage(term, lang))
                .thenReturn(templs);

        LocalizedFormTemplateDTO locDto = LocalizedFormTemplateDTO.builder()
                .id(formTemplate.getId())
                .build();
        when(formTemplateMapper.toLocalizedDTO(formTemplate, lang, fallback))
                .thenReturn(locDto);

        List<LocalizedFormTemplateDTO> result =
                formTemplateService.searchFormTemplates(term, lang, fallback);

        assertEquals(1, result.size());
        verify(formTemplateRepository).searchByNameOrDescriptionInLanguage(term, lang);
        verify(formTemplateMapper).toLocalizedDTO(formTemplate, lang, fallback);
    }

    @Test
    @DisplayName("Should get localized form template by ID with custom fallback")
    void testGetFormTemplateByIdLocalizedWithCustomFallback() {
        // Given
        Long templateId = 42L;
        LanguageCode lang = LanguageCode.ES;
        LanguageCode fallback = LanguageCode.DE;

        when(formTemplateRepository.findByIdWithElements(templateId))
                .thenReturn(Optional.of(formTemplate));

        LocalizedFormTemplateDTO expectedDto = LocalizedFormTemplateDTO.builder()
                .id(templateId)
                .name("Nombre-ES")
                .description("Descripción-ES")
                .build();

        when(formTemplateMapper.toLocalizedDTO(formTemplate, lang, fallback))
                .thenReturn(expectedDto);

        // When
        LocalizedFormTemplateDTO result =
                formTemplateService.getFormTemplateByIdLocalized(templateId, lang, fallback);

        // Then
        assertNotNull(result);
        assertEquals(expectedDto.getId(), result.getId());
        assertEquals(expectedDto.getName(), result.getName());
        assertEquals(expectedDto.getDescription(), result.getDescription());

        verify(formTemplateRepository).findByIdWithElements(templateId);
        verify(formTemplateMapper).toLocalizedDTO(formTemplate, lang, fallback);
    }

    @Test
    @DisplayName("Should throw when localized form template ID not found")
    void testGetFormTemplateByIdLocalizedNotFound() {
        // Given
        Long templateId = 99L;
        when(formTemplateRepository.findByIdWithElements(templateId))
                .thenReturn(Optional.empty());

        // When & Then
        ResourceNotFoundException ex = assertThrows(
                ResourceNotFoundException.class,
                () -> formTemplateService.getFormTemplateByIdLocalized(templateId, LanguageCode.FR, LanguageCode.EN)
        );

        assertTrue(ex.getMessage().contains("Form template not found with id: " + templateId));
        verify(formTemplateRepository).findByIdWithElements(templateId);
        verify(formTemplateMapper, never()).toLocalizedDTO(any(), any(), any());
    }

}
