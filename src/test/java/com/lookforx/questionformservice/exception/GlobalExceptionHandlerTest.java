package com.lookforx.questionformservice.exception;

import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.common.dto.ErrorResponse;
import com.lookforx.common.dto.ExceptionMessageResponse;
import com.lookforx.common.exception.ResourceNotFoundException;
import com.lookforx.common.exception.ValidationException;
import com.lookforx.questionformservice.base.AbstractBaseServiceTest;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.context.request.WebRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class GlobalExceptionHandlerTest extends AbstractBaseServiceTest {

    @InjectMocks
    private GlobalExceptionHandler handler;

    @Mock
    private ExceptionServiceClient exceptionClient;

    @Mock
    private MethodArgumentNotValidException methodArgEx;

    @Mock
    private HttpServletRequest request;

    @Test
    void givenFieldError_whenMethodArgumentNotValid_thenReturnsBadRequest() {
        // prepare a single FieldError on “username”
        BindingResult bindingResult = new BeanPropertyBindingResult(new Object(), "objectName");
        bindingResult.addError(new FieldError("objectName", "username", "must not be blank"));
        when(methodArgEx.getBindingResult()).thenReturn(bindingResult);

        // simulate no Accept-Language → default to EN
        when(request.getHeader("Accept-Language")).thenReturn(null);
        when(request.getRequestURI()).thenReturn("/api/v1/form-templates");
        // stub template without % → message == template
        when(exceptionClient.getExceptionMessage("VALIDATION_FAILED", "EN"))
                .thenReturn(new ExceptionMessageResponse("VALIDATION_FAILED", "EN", "Invalid request parameters"));

        // invoke
        ResponseEntity<ErrorResponse> response =
                handler.handleMethodArgumentNotValid(methodArgEx, request);

        // verify
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(400, body.getStatus());
        assertEquals("Bad Request", body.getError());
        assertEquals("Invalid request parameters", body.getMessage());
        assertEquals("VALIDATION_ERROR", body.getErrorCode());
        assertEquals("/api/v1/form-templates", body.getPath());
        assertNotNull(body.getTimestamp());
        assertEquals(1, body.getFieldErrors().size());
        assertEquals("must not be blank", body.getFieldErrors().get("username"));

        verify(methodArgEx).getBindingResult();
        verify(exceptionClient).getExceptionMessage("VALIDATION_FAILED", "EN");
    }

    @Test
    void givenResourceNotFound_whenExceptionThrown_thenReturnsNotFound() {
        ResourceNotFoundException ex = new ResourceNotFoundException("Item", 1L);

        when(request.getHeader("Accept-Language")).thenReturn(null);
        when(request.getRequestURI()).thenReturn("/api/v1/form-templates/1");
        // stub a template with a single % formatter
        when(exceptionClient.getExceptionMessage("RESOURCE_NOT_FOUND", "EN"))
                .thenReturn(new ExceptionMessageResponse("RESOURCE_NOT_FOUND", "EN", "Template: %s"));

        ResponseEntity<ErrorResponse> response =
                handler.handleResourceNotFound(ex, request);

        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(404, body.getStatus());
        assertEquals("Not Found", body.getError());
        assertEquals("RESOURCE_NOT_FOUND", body.getErrorCode());
        // message should include the original ex.getMessage()
        assertTrue(body.getMessage().contains(ex.getMessage()));
        assertEquals("/api/v1/form-templates/1", body.getPath());
        assertNotNull(body.getTimestamp());

        verify(exceptionClient).getExceptionMessage("RESOURCE_NOT_FOUND", "EN");
    }

    @Test
    void givenValidationException_thenReturnsBadRequest() {
        ValidationException ex = new ValidationException("Invalid data");

        when(request.getHeader("Accept-Language")).thenReturn(null);
        when(request.getRequestURI()).thenReturn("/api/v1/form-templates");
        // stub template with % so it’s replaced by ex.getMessage()
        when(exceptionClient.getExceptionMessage("VALIDATION_FAILED", "EN"))
                .thenReturn(new ExceptionMessageResponse("VALIDATION_FAILED", "EN", "%s"));

        ResponseEntity<ErrorResponse> response =
                handler.handleValidationException(ex, request);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(400, body.getStatus());
        assertEquals("Bad Request", body.getError());
        assertEquals("Invalid data", body.getMessage());
        assertEquals("VALIDATION_ERROR", body.getErrorCode());
        assertEquals("/api/v1/form-templates", body.getPath());
        assertNotNull(body.getTimestamp());

        verify(exceptionClient).getExceptionMessage("VALIDATION_FAILED", "EN");
    }

    @Test
    void givenGenericException_thenReturnsInternalServerError() {
        Exception ex = new RuntimeException("Something went wrong");

        when(request.getHeader("Accept-Language")).thenReturn(null);
        when(request.getRequestURI()).thenReturn("/api/v1/form-templates");
        when(exceptionClient.getExceptionMessage("INTERNAL_SERVER_ERROR", "EN"))
                .thenReturn(new ExceptionMessageResponse("INTERNAL_SERVER_ERROR", "EN", "An unexpected error occurred"));

        ResponseEntity<ErrorResponse> response =
                handler.handleGenericException(ex, request);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        ErrorResponse body = response.getBody();
        assertNotNull(body);
        assertEquals(500, body.getStatus());
        assertEquals("Internal Server Error", body.getError());
        assertEquals("An unexpected error occurred", body.getMessage());
        assertEquals("INTERNAL_SERVER_ERROR", body.getErrorCode());
        assertEquals("/api/v1/form-templates", body.getPath());
        assertNotNull(body.getTimestamp());

        verify(exceptionClient).getExceptionMessage("INTERNAL_SERVER_ERROR", "EN");
    }

}