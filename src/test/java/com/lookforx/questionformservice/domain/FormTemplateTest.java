package com.lookforx.questionformservice.domain;

import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

class FormTemplateTest {

    private FormTemplate formTemplate;
    private FormElement formElement;

    @BeforeEach
    void setUp() {
        Map<LanguageCode, String> nameTranslations = new HashMap<>();
        nameTranslations.put(LanguageCode.EN, "Contact Form");
        nameTranslations.put(LanguageCode.TR, "İletişim Formu");

        Map<LanguageCode, String> descTranslations = new HashMap<>();
        descTranslations.put(LanguageCode.EN, "Contact form description");
        descTranslations.put(LanguageCode.TR, "İletişim formu açıklaması");

        formTemplate = FormTemplate.builder()
                .categoryId(100L)
                .nameTranslations(nameTranslations)
                .descriptionTranslations(descTranslations)
                .active(true)
                .elements(new ArrayList<>())
                .build();
        formTemplate.setId(1L);
        formTemplate.setCreatedBy("admin");
        formTemplate.setCreatedAt(LocalDateTime.now());

        Map<LanguageCode, String> labelTranslations = new HashMap<>();
        labelTranslations.put(LanguageCode.EN, "Full Name");
        labelTranslations.put(LanguageCode.TR, "Ad Soyad");

        formElement = FormElement.builder()
                .labelTranslations(labelTranslations)
                .type(ElementType.TEXT)
                .required(true)
                .displayOrder(1)
                .build();
        formElement.setId(1L);
    }

    @Test
    @DisplayName("Should create FormTemplate with builder")
    void testFormTemplateBuilder() {
        // Then
        assertNotNull(formTemplate);
        assertEquals(1L, formTemplate.getId());
        assertEquals(100L, formTemplate.getCategoryId());
        assertTrue(formTemplate.isActive());
        assertEquals("admin", formTemplate.getCreatedBy());
        assertNotNull(formTemplate.getCreatedAt());
    }

    @Test
    @DisplayName("Should get name in preferred language")
    void testGetNameInPreferredLanguage() {
        // When
        String englishName = formTemplate.getName(LanguageCode.EN, LanguageCode.EN);
        String turkishName = formTemplate.getName(LanguageCode.TR, LanguageCode.EN);

        // Then
        assertEquals("Contact Form", englishName);
        assertEquals("İletişim Formu", turkishName);
    }

    @Test
    @DisplayName("Should fallback to default language when preferred not available")
    void testGetNameWithFallback() {
        // When
        String spanishName = formTemplate.getName(LanguageCode.ES, LanguageCode.EN);

        // Then
        assertEquals("Contact Form", spanishName); // Falls back to English
    }

    @Test
    @DisplayName("Should get description in preferred language")
    void testGetDescriptionInPreferredLanguage() {
        // When
        String englishDesc = formTemplate.getDescription(LanguageCode.EN, LanguageCode.EN);
        String turkishDesc = formTemplate.getDescription(LanguageCode.TR, LanguageCode.EN);

        // Then
        assertEquals("Contact form description", englishDesc);
        assertEquals("İletişim formu açıklaması", turkishDesc);
    }

    @Test
    @DisplayName("Should fallback to default language for description when preferred not available")
    void testGetDescriptionWithFallback() {
        // When
        String spanishDesc = formTemplate.getDescription(LanguageCode.ES, LanguageCode.EN);

        // Then
        assertEquals("Contact form description", spanishDesc); // Falls back to English
    }

    @Test
    @DisplayName("Should add form element")
    void testAddElement() {
        // When
        formTemplate.addElement(formElement);

        // Then
        assertEquals(1, formTemplate.getElements().size());
        assertTrue(formTemplate.getElements().contains(formElement));
        assertEquals(formTemplate, formElement.getFormTemplate());
    }

    @Test
    @DisplayName("Should remove form element")
    void testRemoveElement() {
        // Given
        formTemplate.addElement(formElement);
        assertEquals(1, formTemplate.getElements().size());

        // When
        formTemplate.removeElement(formElement);

        // Then
        assertEquals(0, formTemplate.getElements().size());
        assertFalse(formTemplate.getElements().contains(formElement));
        assertNull(formElement.getFormTemplate());
    }

    // BaseEntity lifecycle methods are tested in the common module

    @Test
    @DisplayName("Should handle empty name translations")
    void testGetNameWithEmptyTranslations() {
        // Given
        FormTemplate templateWithEmptyNames = FormTemplate.builder()
                .nameTranslations(new HashMap<>())
                .build();

        // When
        String name = templateWithEmptyNames.getName(LanguageCode.EN, LanguageCode.TR);

        // Then
        assertNull(name);
    }

    @Test
    @DisplayName("getName on a template with no translations returns null")
    void testGetNameWithNoTranslationsReturnsNull() {
        // Given: a freshly‐built template with the default (empty) nameTranslations map
        FormTemplate template = FormTemplate.builder().build();

        // When
        String name = template.getName(LanguageCode.EN, LanguageCode.TR);

        // Then: no exception, and since neither key exists, result is null
        assertThat(name).isNull();
    }

    @Test
    @DisplayName("Should handle empty description translations")
    void testGetDescriptionWithEmptyTranslations() {
        // Given
        FormTemplate templateWithEmptyDescs = FormTemplate.builder()
                .descriptionTranslations(new HashMap<>())
                .build();

        // When
        String description = templateWithEmptyDescs.getDescription(LanguageCode.EN, LanguageCode.TR);

        // Then
        assertNull(description);
    }

    @Test
    @DisplayName("Should maintain element order")
    void testElementOrder() {
        // Given
        FormElement element1 = FormElement.builder()
                .displayOrder(1)
                .type(ElementType.TEXT)
                .build();
        element1.setId(1L);

        FormElement element2 = FormElement.builder()
                .displayOrder(2)
                .type(ElementType.EMAIL)
                .build();
        element2.setId(2L);

        // When
        formTemplate.addElement(element2);
        formTemplate.addElement(element1);

        // Then
        assertEquals(2, formTemplate.getElements().size());
        // Note: Order is maintained by @OrderBy annotation in entity
        assertTrue(formTemplate.getElements().contains(element1));
        assertTrue(formTemplate.getElements().contains(element2));
    }

    @Test
    @DisplayName("Should handle multiple language translations")
    void testMultipleLanguageTranslations() {
        // Given
        Map<LanguageCode, String> multiLangNames = new HashMap<>();
        multiLangNames.put(LanguageCode.EN, "Contact Form");
        multiLangNames.put(LanguageCode.TR, "İletişim Formu");
        multiLangNames.put(LanguageCode.ES, "Formulario de Contacto");
        multiLangNames.put(LanguageCode.FR, "Formulaire de Contact");

        FormTemplate multiLangTemplate = FormTemplate.builder()
                .nameTranslations(multiLangNames)
                .build();

        // When & Then
        assertEquals("Contact Form", multiLangTemplate.getName(LanguageCode.EN, LanguageCode.EN));
        assertEquals("İletişim Formu", multiLangTemplate.getName(LanguageCode.TR, LanguageCode.EN));
        assertEquals("Formulario de Contacto", multiLangTemplate.getName(LanguageCode.ES, LanguageCode.EN));
        assertEquals("Formulaire de Contact", multiLangTemplate.getName(LanguageCode.FR, LanguageCode.EN));
    }
}
