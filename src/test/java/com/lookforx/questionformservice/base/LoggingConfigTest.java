package com.lookforx.questionformservice.base;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import org.junit.jupiter.api.Test;
import org.slf4j.LoggerFactory;

import static org.junit.jupiter.api.Assertions.*;

public class LoggingConfigTest extends AbstractBaseServiceTest {

    private final Logger logger =
            (Logger) LoggerFactory.getLogger(LoggingConfigTest.class);

    @Test
    void shouldCaptureInfo() {
        logger.info("info-message");
        assertTrue(
                logTracker.checkMessage(Level.INFO, "info-message").isPresent(),
                "INFO-level logs must be captured"
        );
    }

    @Test
    void shouldCaptureWarn() {
        logger.warn("warn-message");
        assertTrue(
                logTracker.checkMessage(Level.WARN, "warn-message").isPresent(),
                "WARN-level logs must be captured"
        );
    }

    @Test
    void shouldCaptureError() {
        logger.error("error-message");
        assertTrue(
                logTracker.checkMessage(Level.ERROR, "error-message").isPresent(),
                "ERROR-level logs must be captured"
        );
    }

    @Test
    void debugShouldNotBeCaptured() {
        logger.debug("debug-message");
        assertFalse(
                logTracker.checkMessage(Level.DEBUG, "debug-message").isPresent(),
                "DEBUG-level logs must NOT be captured at INFO level"
        );
    }

    @Test
    void traceShouldNotBeCaptured() {
        logger.trace("trace-message");
        assertFalse(
                logTracker.checkMessage(Level.TRACE, "trace-message").isPresent(),
                "TRACE-level logs must NOT be captured at INFO level"
        );
    }
}
