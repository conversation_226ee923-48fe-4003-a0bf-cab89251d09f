package com.lookforx.questionformservice.mapper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import com.lookforx.questionformservice.domain.ElementOption;
import com.lookforx.questionformservice.domain.ElementOptionPropertyKey;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.questionformservice.dto.ElementOptionDTO;
import com.lookforx.questionformservice.dto.LocalizedElementOptionDTO;
import com.lookforx.questionformservice.dto.request.CreateElementOptionRequest;
import com.lookforx.questionformservice.dto.request.UpdateElementOptionRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ElementOptionMapperTest {

    @InjectMocks
    private ElementOptionMapper mapper;

    @Mock
    private CreateElementOptionRequest createReq;

    @Mock
    private UpdateElementOptionRequest updateReq;

    @Mock
    private ElementOption existingOption;

    @BeforeEach
    void setUp() {
        // nothing to do here since we're using @InjectMocks
    }

    @Test
    void givenCreateRequest_whenToEntity_thenAllFieldsAndPropertiesAreMapped() {

        // Given
        Map<LanguageCode,String> propTrans = Map.of(LanguageCode.EN, "propValEN");
        given(createReq.getValue()).willReturn("myValue");

        // When
        when(createReq.getLabelTranslations()).thenReturn(Map.of(LanguageCode.EN, "LabelEN"));
        when(createReq.getDisplayOrder()).thenReturn(42);
        when(createReq.isDefaultSelected()).thenReturn(true);
        when(createReq.getPropertyTranslations())
                .thenReturn(Map.of("color", propTrans));

        // Then
        ElementOption entity = mapper.toEntity(createReq);

        assertThat(entity.getValue()).isEqualTo("myValue");
        assertThat(entity.getLabelTranslations()).containsEntry(LanguageCode.EN, "LabelEN");
        assertThat(entity.getDisplayOrder()).isEqualTo(42);
        assertThat(entity.isDefaultSelected()).isTrue();
        assertThat(entity.getPropertyTranslations("color"))
                .containsEntry(LanguageCode.EN, "propValEN");

        // Verify
        verify(createReq).getValue();
        verify(createReq).getLabelTranslations();
        verify(createReq).getDisplayOrder();
        verify(createReq).isDefaultSelected();
        verify(createReq, atLeastOnce()).getPropertyTranslations();

    }

    @Test
    void whenUpdateEntityFromRequest_thenExistingOptionIsMutatedCorrectly() {

        // Given
        @SuppressWarnings("unchecked")
        Map<ElementOptionPropertyKey, String> existingProps = mock(Map.class);

        // When
        when(existingOption.getPropertyTranslations()).thenReturn(existingProps);
        when(updateReq.getValue()).thenReturn("newVal");
        when(updateReq.getLabelTranslations()).thenReturn(Map.of(LanguageCode.TR, "EtiketTR"));
        when(updateReq.getDisplayOrder()).thenReturn(7);
        when(updateReq.isDefaultSelected()).thenReturn(false);
        Map<LanguageCode,String> newPropTrans = Map.of(LanguageCode.TR, "yeniDeger");
        when(updateReq.getPropertyTranslations())
                .thenReturn(Map.of("size", newPropTrans));

        // Then
        mapper.updateEntityFromRequest(existingOption, updateReq);

        // Verify
        verify(existingOption).setValue("newVal");
        verify(existingOption).setLabelTranslations(Map.of(LanguageCode.TR, "EtiketTR"));
        verify(existingOption).setDisplayOrder(7);
        verify(existingOption).setDefaultSelected(false);
        verify(existingProps).clear();
        verify(existingOption).setPropertyValue("size", LanguageCode.TR, "yeniDeger");
        verify(updateReq).getValue();
        verify(updateReq).getLabelTranslations();
        verify(updateReq).getDisplayOrder();
        verify(updateReq).isDefaultSelected();
        verify(updateReq, atLeastOnce()).getPropertyTranslations();

    }

    @Test
    void givenUpdateRequest_whenToEntityWithId_thenAllFieldsAndPropertiesAreMapped() {
        // — given —
        long id = 123L;
        given(updateReq.getId()).willReturn(id);
        given(updateReq.getValue()).willReturn("newVal");
        given(updateReq.getLabelTranslations()).willReturn(Map.of(LanguageCode.EN,"lblEN"));
        given(updateReq.getDisplayOrder()).willReturn(7);
        given(updateReq.isDefaultSelected()).willReturn(true);

        Map<LanguageCode,String> colorTrans = Map.of(LanguageCode.EN, "red");
        Map<LanguageCode,String> sizeTrans  = Map.of(LanguageCode.TR, "büyük");
        Map<String,Map<LanguageCode,String>> props = new HashMap<>();
        props.put("color", colorTrans);
        props.put("size",  sizeTrans);
        given(updateReq.getPropertyTranslations()).willReturn(props);

        // — when —
        ElementOption option = mapper.toEntity(updateReq);

        // — then: scalar fields —
        assertThat(option.getId()).isEqualTo(id);
        assertThat(option.getValue()).isEqualTo("newVal");
        assertThat(option.getLabelTranslations()).containsEntry(LanguageCode.EN, "lblEN");
        assertThat(option.getDisplayOrder()).isEqualTo(7);
        assertThat(option.isDefaultSelected()).isTrue();

        // — then: propertyTranslations —
        assertThat(option.getPropertyTranslations("color"))
                .containsEntry(LanguageCode.EN, "red");
        assertThat(option.getPropertyTranslations("size"))
                .containsEntry(LanguageCode.TR, "büyük");

        // — verify request interactions —
        verify(updateReq).getId();
        verify(updateReq).getValue();
        verify(updateReq).getLabelTranslations();
        verify(updateReq).getDisplayOrder();
        verify(updateReq).isDefaultSelected();
        verify(updateReq, atLeastOnce()).getPropertyTranslations();
        verifyNoMoreInteractions(updateReq);
    }

    @Test
    void givenOption_whenToDTO_thenAllFieldsAndPropertyTranslationsMapped() {
        // — given —
        ElementOption opt = new ElementOption();
        opt.setId(55L);
        opt.setValue("val55");
        opt.setLabelTranslations(Map.of(LanguageCode.TR, "Etiket"));
        opt.setDisplayOrder(3);
        opt.setDefaultSelected(false);
        LocalDateTime created = LocalDateTime.of(2025,6,26,10,0);
        LocalDateTime updated = created.plusMinutes(5);
        opt.setCreatedAt(created);
        opt.setUpdatedAt(updated);

        // two property keys
        opt.setPropertyValue("color", LanguageCode.EN, "blue");
        opt.setPropertyValue("size",  LanguageCode.TR, "büyük");

        // — when —
        ElementOptionDTO dto = mapper.toDTO(opt);

        // — then: scalar fields —
        assertThat(dto.getId()).isEqualTo(55L);
        assertThat(dto.getValue()).isEqualTo("val55");
        assertThat(dto.getLabelTranslations())
                .containsEntry(LanguageCode.TR, "Etiket");
        assertThat(dto.getDisplayOrder()).isEqualTo(3);
        assertThat(dto.isDefaultSelected()).isFalse();
        assertThat(dto.getCreatedAt()).isSameAs(created);
        assertThat(dto.getUpdatedAt()).isSameAs(updated);

        // — then: propertyTranslations map —
        assertThat(dto.getPropertyTranslations())
                .containsEntry("color", Map.of(LanguageCode.EN,"blue"))
                .containsEntry("size",  Map.of(LanguageCode.TR,"büyük"));
    }

    @Test
    void givenOptionWithMixedLanguages_whenToLocalizedDTO_thenFallbackAndPropertiesWork() {
        // — given —
        ElementOption opt = new ElementOption();
        opt.setId(77L);
        opt.setValue("v77");
        // label only in fallback
        opt.setLabelTranslations(Map.of(LanguageCode.TR, "Merhaba"));
        // add properties: one key has primary, one only fallback
        opt.setPropertyValue("p1", LanguageCode.EN, "one");
        opt.setPropertyValue("p2", LanguageCode.TR, "iki");

        LanguageCode primary  = LanguageCode.EN;
        LanguageCode fallback = LanguageCode.TR;

        LocalDateTime created = LocalDateTime.now();
        LocalDateTime updated = created.plusMinutes(1);
        opt.setCreatedAt(created);
        opt.setUpdatedAt(updated);

        // — when —
        LocalizedElementOptionDTO dto = mapper.toLocalizedDTO(opt, primary, fallback);

        // — then: fallback for label —
        assertThat(dto.getId()).isEqualTo(77L);
        assertThat(dto.getValue()).isEqualTo("v77");
        assertThat(dto.getLabel()).isEqualTo("Merhaba");
        assertThat(dto.isLabelFromFallback()).isTrue();

        // — then: properties resolved per-language —
        assertThat(dto.getProperties())
                .containsEntry("p1","one")  // from primary
                .containsEntry("p2","iki"); // from fallback

        // — then: language fields and timestamps —
        assertThat(dto.getLanguageCode()).isEqualTo(primary);
        assertThat(dto.getFallbackLanguageCode()).isEqualTo(fallback);
        assertThat(dto.getCreatedAt()).isSameAs(created);
        assertThat(dto.getUpdatedAt()).isSameAs(updated);
    }

}