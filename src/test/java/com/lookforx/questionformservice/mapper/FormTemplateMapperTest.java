package com.lookforx.questionformservice.mapper;


import com.lookforx.questionformservice.base.AbstractBaseServiceTest;
import com.lookforx.questionformservice.domain.FormElement;
import com.lookforx.questionformservice.domain.FormTemplate;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.questionformservice.dto.FormElementDTO;
import com.lookforx.questionformservice.dto.FormTemplateDTO;
import com.lookforx.questionformservice.dto.LocalizedFormElementDTO;
import com.lookforx.questionformservice.dto.LocalizedFormTemplateDTO;
import com.lookforx.questionformservice.dto.request.CreateFormElementRequest;
import com.lookforx.questionformservice.dto.request.CreateFormTemplateRequest;
import com.lookforx.questionformservice.dto.request.UpdateFormElementRequest;
import com.lookforx.questionformservice.dto.request.UpdateFormTemplateRequest;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.*;

class FormTemplateMapperTest extends AbstractBaseServiceTest {

    @InjectMocks
    private FormTemplateMapper mapper;

    @Mock
    private FormElementMapper formElementMapper;

    @Mock
    private CreateFormTemplateRequest createReq;

    @Mock
    private UpdateFormTemplateRequest updateReq;

    @Test
    void givenCreateRequest_whenToEntity_thenAllFieldsAndElementsAreMapped() {
        // — given —
        Map<LanguageCode,String> names = Map.of(LanguageCode.EN, "Form EN");
        Map<LanguageCode,String> descs = Map.of(LanguageCode.EN, "Desc EN");

        given(createReq.getNameTranslations()).willReturn(names);
        given(createReq.getCategoryId()).willReturn(77L);
        given(createReq.getDescriptionTranslations()).willReturn(descs);
        given(createReq.isActive()).willReturn(true);

        // Mock the two *request* objects, not domain FormElement:
        CreateFormElementRequest elReq1 = mock(CreateFormElementRequest.class);
        CreateFormElementRequest elReq2 = mock(CreateFormElementRequest.class);
        given(createReq.getElements()).willReturn(List.of(elReq1, elReq2));

        // And stub the mapper to produce domain FormElement instances:
        FormElement e1 = new FormElement();
        FormElement e2 = new FormElement();
        given(formElementMapper.toEntity(elReq1)).willReturn(e1);
        given(formElementMapper.toEntity(elReq2)).willReturn(e2);

        // — when —
        FormTemplate result = mapper.toEntity(createReq);

        // — then: fields —
        assertThat(result.getNameTranslations()).isSameAs(names);
        assertThat(result.getCategoryId()).isEqualTo(77L);
        assertThat(result.getDescriptionTranslations()).isSameAs(descs);
        assertThat(result.isActive()).isTrue();

        // — then: elements —
        assertThat(result.getElements()).containsExactly(e1, e2);

        // — verify interactions —
        then(createReq).should().getNameTranslations();
        then(createReq).should().getCategoryId();
        then(createReq).should().getDescriptionTranslations();
        then(createReq).should().isActive();
        then(createReq).should().getElements();

        then(formElementMapper).should().toEntity(elReq1);
        then(formElementMapper).should().toEntity(elReq2);

        then(createReq).shouldHaveNoMoreInteractions();
        then(formElementMapper).shouldHaveNoMoreInteractions();

    }

    @Test
    void givenExistingTemplate_whenUpdateEntity_thenFieldsAndElementsAreReplaced() {
        // — given: a template with one old element —
        FormTemplate template = new FormTemplate();
        template.setElements(new ArrayList<>());
        FormElement old = new FormElement();
        template.addElement(old);

        // stub the UpdateFormTemplateRequest scalars
        Map<LanguageCode,String> names = Map.of(LanguageCode.TR, "Şablon");
        Map<LanguageCode,String> descs = Map.of(LanguageCode.TR, "Açıklama");
        given(updateReq.getNameTranslations()).willReturn(names);
        given(updateReq.getCategoryId()).willReturn(99L);
        given(updateReq.getDescriptionTranslations()).willReturn(descs);
        given(updateReq.isActive()).willReturn(false);

        // mock the *element* request objects (UpdateFormElementRequest)
        UpdateFormElementRequest elReqA = mock(UpdateFormElementRequest.class);
        UpdateFormElementRequest elReqB = mock(UpdateFormElementRequest.class);
        given(updateReq.getElements()).willReturn(List.of(elReqA, elReqB));

        // stub the mapper to produce new FormElement instances
        FormElement newA = new FormElement();
        FormElement newB = new FormElement();
        given(formElementMapper.toEntity(elReqA)).willReturn(newA);
        given(formElementMapper.toEntity(elReqB)).willReturn(newB);

        // — when —
        mapper.updateEntity(template, updateReq);

        // — then: scalar fields updated —
        assertThat(template.getNameTranslations()).isSameAs(names);
        assertThat(template.getCategoryId()).isEqualTo(99L);
        assertThat(template.getDescriptionTranslations()).isSameAs(descs);
        assertThat(template.isActive()).isFalse();

        // — then: elements list replaced —
        assertThat(template.getElements()).containsExactly(newA, newB);

        // — verify interactions with the request —
        then(updateReq).should().getNameTranslations();
        then(updateReq).should().getCategoryId();
        then(updateReq).should().getDescriptionTranslations();
        then(updateReq).should().isActive();
        then(updateReq).should().getElements();

        // — verify we used the formElementMapper with the correct request objects —
        then(formElementMapper).should().toEntity(elReqA);
        then(formElementMapper).should().toEntity(elReqB);

        then(updateReq).shouldHaveNoMoreInteractions();
        then(formElementMapper).shouldHaveNoMoreInteractions();
    }

    @Test
    void givenTemplateWithElements_whenToDTO_thenAllFieldsAndElementDTOsAreMapped() {
        // — given —
        FormTemplate tpl = new FormTemplate();
        tpl.setId(5L);
        tpl.setNameTranslations(Map.of(LanguageCode.EN, "MyForm"));
        tpl.setCategoryId(33L);
        tpl.setDescriptionTranslations(Map.of());
        tpl.setActive(true);
        tpl.setCreatedBy("alice");
        LocalDateTime now = LocalDateTime.now();
        tpl.setCreatedAt(now);
        tpl.setUpdatedAt(now.plusHours(1));
        tpl.setUpdatedBy("bob");

        // elements
        FormElement fe1 = new FormElement();
        FormElement fe2 = new FormElement();
        tpl.setElements(new ArrayList<>(List.of(fe1, fe2)));

        // two distinct DTOs
        FormElementDTO dto1 = mock(FormElementDTO.class);
        FormElementDTO dto2 = mock(FormElementDTO.class);

        // stub mapper.toDTO to return dto1 for fe1, dto2 for fe2
        when(formElementMapper.toDTO(any(FormElement.class)))
                .thenAnswer(inv -> inv.getArgument(0) == fe1 ? dto1 : dto2);

        // — when —
        FormTemplateDTO out = mapper.toDTO(tpl);

        // — then: scalar fields —
        assertThat(out.getId()).isEqualTo(5L);
        assertThat(out.getNameTranslations()).containsEntry(LanguageCode.EN, "MyForm");
        assertThat(out.getCategoryId()).isEqualTo(33L);
        assertThat(out.getDescriptionTranslations()).isEmpty();
        assertThat(out.isActive()).isTrue();
        assertThat(out.getCreatedBy()).isEqualTo("alice");
        assertThat(out.getCreatedAt()).isEqualTo(now);
        assertThat(out.getUpdatedAt()).isEqualTo(now.plusHours(1));
        assertThat(out.getUpdatedBy()).isEqualTo("bob");

        // — then: elements list of DTOs —
        assertThat(out.getElements()).containsExactly(dto1, dto2);

        // — verify interactions —
        verify(formElementMapper, atLeastOnce()).toDTO(fe1);
        verify(formElementMapper, atLeastOnce()).toDTO(fe2);
        verifyNoMoreInteractions(formElementMapper);
    }

    @Test
    void givenTemplateWithMixedTranslations_whenToLocalizedDTO_thenFieldsElementsAndFallbackFlagsAreCorrect() {
        // — given —
        LanguageCode primary  = LanguageCode.EN;
        LanguageCode fallback = LanguageCode.TR;

        FormTemplate tpl = new FormTemplate();
        tpl.setId(7L);
        tpl.setNameTranslations(Map.of(fallback, "Şablon"));
        tpl.setDescriptionTranslations(Map.of(primary, "Description"));
        tpl.setCategoryId(44L);
        tpl.setActive(false);
        tpl.setCreatedBy("u1");
        LocalDateTime created = LocalDateTime.of(2025,6,26,10,0);
        tpl.setCreatedAt(created);
        tpl.setUpdatedAt(created.plusMinutes(5));
        tpl.setUpdatedBy("u2");

        FormElement feA = new FormElement();
        FormElement feB = new FormElement();
        tpl.setElements(new ArrayList<>(List.of(feA, feB)));

        // two distinct DTOs
        LocalizedFormElementDTO locDtoA = mock(LocalizedFormElementDTO.class);
        LocalizedFormElementDTO locDtoB = mock(LocalizedFormElementDTO.class);

        // stub via Answer so we return A for feA and B for feB
        when(formElementMapper.toLocalizedDTO(
                any(FormElement.class), eq(primary), eq(fallback)))
                .thenAnswer(inv -> {
                    FormElement arg = inv.getArgument(0);
                    return arg == feA ? locDtoA : locDtoB;
                });

        // — when —
        LocalizedFormTemplateDTO out = mapper.toLocalizedDTO(tpl, primary, fallback);

        // — then: fields & fallback flags —
        assertThat(out.getId()).isEqualTo(7L);
        assertThat(out.getName()).isEqualTo("Şablon");
        assertThat(out.isNameFromFallback()).isTrue();
        assertThat(out.getDescription()).isEqualTo("Description");
        assertThat(out.isDescriptionFromFallback()).isFalse();
        assertThat(out.getCategoryId()).isEqualTo(44L);
        assertThat(out.isActive()).isFalse();
        assertThat(out.getCreatedBy()).isEqualTo("u1");
        assertThat(out.getCreatedAt()).isEqualTo(created);
        assertThat(out.getUpdatedAt()).isEqualTo(created.plusMinutes(5));
        assertThat(out.getUpdatedBy()).isEqualTo("u2");

        // — then: elements list of localized DTOs —
        assertThat(out.getElements()).containsExactly(locDtoA, locDtoB);
        assertThat(out.getLanguageCode()).isEqualTo(primary);
        assertThat(out.getFallbackLanguageCode()).isEqualTo(fallback);

        // — verify interactions —
        verify(formElementMapper, atLeastOnce()).toLocalizedDTO(feA, primary, fallback);
        verify(formElementMapper, atLeastOnce()).toLocalizedDTO(feB, primary, fallback);
        verifyNoMoreInteractions(formElementMapper);
    }


}