package com.lookforx.questionformservice.mapper;

import com.lookforx.questionformservice.base.AbstractBaseServiceTest;
import com.lookforx.questionformservice.domain.FormResponse;
import com.lookforx.questionformservice.dto.FormResponseDTO;
import com.lookforx.questionformservice.dto.request.FormResponseRequest;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;

class FormResponseMapperTest extends AbstractBaseServiceTest {

    @InjectMocks
    private FormResponseMapper mapper;

    @Mock
    private FormResponseRequest request;

    @Mock
    private FormResponse response;

    @Test
    void givenRequest_whenToEntity_thenMapsFormElementIdAndValue() {
        // — given —
        given(request.getFormElementId()).willReturn(11L);
        given(request.getValue()).willReturn("myAnswer");

        // — when —
        FormResponse entity = mapper.toEntity(request);

        // — then: mapped fields —
        assertThat(entity.getFormElementId()).isEqualTo(11L);
        assertThat(entity.getValue()).isEqualTo("myAnswer");
        assertThat(entity.getCreatedAt()).isNull();
        assertThat(entity.getUpdatedAt()).isNull();

        // — verify interactions —
        verify(request).getFormElementId();
        verify(request).getValue();
        verifyNoMoreInteractions(request);
    }

    @Test
    void givenResponse_whenToDTO_thenMapsAllFields() {
        // — given —
        given(response.getFormElementId()).willReturn(22L);
        given(response.getValue()).willReturn("respVal");
        LocalDateTime created = LocalDateTime.now().minusDays(1);
        LocalDateTime updated = LocalDateTime.now();
        given(response.getCreatedAt()).willReturn(created);
        given(response.getUpdatedAt()).willReturn(updated);

        // — when —
        FormResponseDTO dto = mapper.toDTO(response);

        // — then: mapped fields —
        assertThat(dto.getFormElementId()).isEqualTo(22L);
        assertThat(dto.getValue()).isEqualTo("respVal");
        assertThat(dto.getCreatedAt()).isSameAs(created);
        assertThat(dto.getUpdatedAt()).isSameAs(updated);

        // — verify interactions —
        verify(response).getFormElementId();
        verify(response).getValue();
        verify(response).getCreatedAt();
        verify(response).getUpdatedAt();
        verifyNoMoreInteractions(response);
    }

    @Test
    void givenResponseAndLabel_whenToDTOWithElement_thenIncludesElementLabel() {
        // — given —
        given(response.getFormElementId()).willReturn(33L);
        given(response.getValue()).willReturn("anotherVal");
        LocalDateTime created = LocalDateTime.now().minusHours(2);
        LocalDateTime updated = LocalDateTime.now().minusHours(1);
        given(response.getCreatedAt()).willReturn(created);
        given(response.getUpdatedAt()).willReturn(updated);
        String label = "My Element Label";

        // — when —
        FormResponseDTO dto = mapper.toDTOWithElement(response, label);

        // — then: mapped fields plus label —
        assertThat(dto.getFormElementId()).isEqualTo(33L);
        assertThat(dto.getElementLabel()).isEqualTo(label);
        assertThat(dto.getValue()).isEqualTo("anotherVal");
        assertThat(dto.getCreatedAt()).isSameAs(created);
        assertThat(dto.getUpdatedAt()).isSameAs(updated);

        // — verify interactions —
        verify(response).getFormElementId();
        verify(response).getValue();
        verify(response).getCreatedAt();
        verify(response).getUpdatedAt();
        verifyNoMoreInteractions(response);

    }

}