package com.lookforx.questionformservice.mapper;

import com.lookforx.common.enums.LanguageCode;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;

class ElementOptionDTOTest {

    @Test
    void whenPrimaryPresent_thenReturnsPrimaryOnly() {
        // mock the map
        @SuppressWarnings("unchecked")
        Map<LanguageCode,String> mockMap = mock(Map.class);
        // stub get calls
        given(mockMap.get(LanguageCode.EN)).willReturn("PrimaryValue");

        ElementOptionDTO dto = ElementOptionDTO.builder()
                .labelTranslations(mockMap)
                .build();

        // call under test
        String label = dto.getLabel(LanguageCode.EN, LanguageCode.TR);

        // assert
        assertThat(label).isEqualTo("PrimaryValue");

        // verify only primary was requested
        verify(mockMap).get(LanguageCode.EN);
        verifyNoMoreInteractions(mockMap);
    }

    @Test
    void whenPrimaryNull_thenFallsBackToSecondary() {
        @SuppressWarnings("unchecked")
        Map<LanguageCode,String> mockMap = mock(Map.class);
        given(mockMap.get(LanguageCode.EN)).willReturn(null);
        given(mockMap.get(LanguageCode.TR)).willReturn("FallbackValue");

        ElementOptionDTO dto = ElementOptionDTO.builder()
                .labelTranslations(mockMap)
                .build();

        String label = dto.getLabel(LanguageCode.EN, LanguageCode.TR);

        assertThat(label).isEqualTo("FallbackValue");

        verify(mockMap).get(LanguageCode.EN);
        verify(mockMap).get(LanguageCode.TR);
        verifyNoMoreInteractions(mockMap);
    }

    @Test
    void whenPrimaryEmpty_thenFallsBackToSecondary() {
        @SuppressWarnings("unchecked")
        Map<LanguageCode,String> mockMap = mock(Map.class);
        given(mockMap.get(LanguageCode.EN)).willReturn("");
        given(mockMap.get(LanguageCode.TR)).willReturn("FallbackValue");

        ElementOptionDTO dto = ElementOptionDTO.builder()
                .labelTranslations(mockMap)
                .build();

        String label = dto.getLabel(LanguageCode.EN, LanguageCode.TR);

        assertThat(label).isEqualTo("FallbackValue");

        verify(mockMap).get(LanguageCode.EN);
        verify(mockMap).get(LanguageCode.TR);
        verifyNoMoreInteractions(mockMap);
    }

    @Test
    void whenBothMissing_thenReturnsNull() {
        @SuppressWarnings("unchecked")
        Map<LanguageCode,String> mockMap = mock(Map.class);
        given(mockMap.get(LanguageCode.EN)).willReturn(null);
        given(mockMap.get(LanguageCode.TR)).willReturn(null);

        ElementOptionDTO dto = ElementOptionDTO.builder()
                .labelTranslations(mockMap)
                .build();

        String label = dto.getLabel(LanguageCode.EN, LanguageCode.TR);

        assertThat(label).isNull();

        verify(mockMap).get(LanguageCode.EN);
        verify(mockMap).get(LanguageCode.TR);
        verifyNoMoreInteractions(mockMap);
    }

}