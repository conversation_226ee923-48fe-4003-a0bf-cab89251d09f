package com.lookforx.questionformservice.mapper;

import com.lookforx.questionformservice.base.AbstractBaseServiceTest;
import com.lookforx.questionformservice.domain.FormResponse;
import com.lookforx.questionformservice.domain.FormSubmission;
import com.lookforx.questionformservice.dto.FormResponseDTO;
import com.lookforx.questionformservice.dto.FormSubmissionDTO;
import com.lookforx.questionformservice.dto.request.SubmitFormRequest;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;

class FormSubmissionMapperTest extends AbstractBaseServiceTest {

    @InjectMocks
    private FormSubmissionMapper mapper;

    @Mock
    private FormResponseMapper responseMapper;

    @Mock
    private SubmitFormRequest request;

    @Mock
    private FormSubmission submission;

    @Test
    void givenRequest_whenToEntity_thenMapsCategoryUserAndRequestId() {
        // — given —
        given(request.getCategoryId()).willReturn(10L);
        given(request.getUserId()).willReturn(20L);
        given(request.getRequestId()).willReturn("REQ-123");

        // — when —
        FormSubmission entity = mapper.toEntity(request);

        // — then: fields mapped —
        assertThat(entity.getCategoryId()).isEqualTo(10L);
        assertThat(entity.getUserId()).isEqualTo(20L);
        assertThat(entity.getRequestId()).isEqualTo("REQ-123");

        // builder does not set other fields
        assertThat(entity.getId()).isNull();
        assertThat(entity.getResponses()).isEmpty();

        // — verify interactions —
        verify(request).getCategoryId();
        verify(request).getUserId();
        verify(request).getRequestId();
        verifyNoMoreInteractions(request);
    }

    @Test
    void givenSubmissionWithResponses_whenToDTO_thenAllFieldsAndResponsesAreMapped() {
        // — given —
        LocalDateTime created = LocalDateTime.now().minusHours(1);
        LocalDateTime updated = LocalDateTime.now();

        // stub primitive getters
        given(submission.getId()).willReturn("100");
        given(submission.getFormTemplateId()).willReturn(200L);
        given(submission.getCategoryId()).willReturn(10L);
        given(submission.getUserId()).willReturn(20L);
        given(submission.getRequestId()).willReturn("REQ-123");
        given(submission.getSubmittedBy()).willReturn("<EMAIL>");
        given(submission.getCreatedAt()).willReturn(created);
        given(submission.getUpdatedAt()).willReturn(updated);

        // prepare two FormResponse entities
        FormResponse r1 = new FormResponse();
        FormResponse r2 = new FormResponse();
        given(submission.getResponses()).willReturn(List.of(r1, r2));

        // stub responseMapper
        FormResponseDTO dr1 = FormResponseDTO.builder().formElementId(1L).value("v1").build();
        FormResponseDTO dr2 = FormResponseDTO.builder().formElementId(2L).value("v2").build();
        given(responseMapper.toDTO(r1)).willReturn(dr1);
        given(responseMapper.toDTO(r2)).willReturn(dr2);

        // — when —
        FormSubmissionDTO dto = mapper.toDTO(submission);

        // — then: scalar fields —
        assertThat(dto.getId()).isEqualTo("100");
        assertThat(dto.getFormTemplateId()).isEqualTo(200L);
        assertThat(dto.getCategoryId()).isEqualTo(10L);
        assertThat(dto.getUserId()).isEqualTo(20L);
        assertThat(dto.getRequestId()).isEqualTo("REQ-123");
        assertThat(dto.getSubmittedBy()).isEqualTo("<EMAIL>");
        assertThat(dto.getCreatedAt()).isSameAs(created);
        assertThat(dto.getUpdatedAt()).isSameAs(updated);

        // — verify interactions —
        verify(submission).getId();
        verify(submission).getFormTemplateId();
        verify(submission).getCategoryId();
        verify(submission).getUserId();
        verify(submission).getRequestId();
        verify(submission).getSubmittedBy();
        verify(submission).getResponses();
        verify(submission).getCreatedAt();
        verify(submission).getUpdatedAt();

        verify(responseMapper, atLeastOnce()).toDTO(r1);
        verify(responseMapper, atLeastOnce()).toDTO(r2);

        verifyNoMoreInteractions(submission, responseMapper);

    }

}