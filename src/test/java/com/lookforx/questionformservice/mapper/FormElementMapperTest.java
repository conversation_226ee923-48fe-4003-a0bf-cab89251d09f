package com.lookforx.questionformservice.mapper;

import com.lookforx.questionformservice.base.AbstractBaseServiceTest;
import com.lookforx.questionformservice.domain.ElementOption;
import com.lookforx.questionformservice.domain.ElementType;
import com.lookforx.questionformservice.domain.FormElement;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.questionformservice.dto.FormElementDTO;
import com.lookforx.questionformservice.dto.LocalizedElementOptionDTO;
import com.lookforx.questionformservice.dto.LocalizedFormElementDTO;
import com.lookforx.questionformservice.dto.request.CreateElementOptionRequest;
import com.lookforx.questionformservice.dto.request.CreateFormElementRequest;
import com.lookforx.questionformservice.dto.request.UpdateElementOptionRequest;
import com.lookforx.questionformservice.dto.request.UpdateFormElementRequest;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.mockito.Mockito.*;

class FormElementMapperTest extends AbstractBaseServiceTest {

    @InjectMocks
    private FormElementMapper mapper;

    @Mock
    private ElementOptionMapper optionMapper;

    @Mock
    private CreateFormElementRequest createReq;

    @Mock
    private UpdateFormElementRequest updateReq;

    @Test
    void givenCreateRequest_whenToEntity_thenMapsAllFieldsAndOptions() {
        // --- given ---
        Map<LanguageCode, String> labelTrans = Map.of(LanguageCode.EN, "lblEN");
        Map<LanguageCode, String> descTrans = Map.of(LanguageCode.EN, "descEN");
        Map<LanguageCode, String> placeholderTrans = Map.of(LanguageCode.EN, "phEN");
        Map<LanguageCode, String> helpTextTrans = Map.of(LanguageCode.EN, "helpEN");
        ElementType type = ElementType.TEXT;

        given(createReq.getLabelTranslations()).willReturn(labelTrans);
        given(createReq.getDescriptionTranslations()).willReturn(descTrans);
        given(createReq.getPlaceholderTranslations()).willReturn(placeholderTrans);
        given(createReq.getHelpTextTranslations()).willReturn(helpTextTrans);
        given(createReq.getType()).willReturn(type);
        given(createReq.getDisplayOrder()).willReturn(7);
        given(createReq.isRequired()).willReturn(true);
        given(createReq.getValidationRegex()).willReturn("\\d+");
        given(createReq.getMinLength()).willReturn(1);
        given(createReq.getMaxLength()).willReturn(5);
        given(createReq.getMinValue()).willReturn(1.0);
        given(createReq.getMaxValue()).willReturn(100.0);
        given(createReq.getOptions()).willReturn(new ArrayList<>());
        Map<String,String> props = Map.of("foo","bar");
        given(createReq.getProperties()).willReturn(props);

        // two dummy option‐requests
        CreateElementOptionRequest optReq1 = mock(CreateElementOptionRequest.class);
        CreateElementOptionRequest optReq2 = mock(CreateElementOptionRequest.class);
        given(createReq.getOptions()).willReturn(List.of(optReq1, optReq2));

        // and what the optionMapper will return for each
        ElementOption ent1 = new ElementOption();
        ElementOption ent2 = new ElementOption();
        given(optionMapper.toEntity(optReq1)).willReturn(ent1);
        given(optionMapper.toEntity(optReq2)).willReturn(ent2);

        // --- when ---
        FormElement result = mapper.toEntity(createReq);

        // --- then: field mapping ---
        assertThat(result.getLabelTranslations()).isEqualTo(labelTrans);
        assertThat(result.getDescriptionTranslations()).isEqualTo(descTrans);
        assertThat(result.getPlaceholderTranslations()).isEqualTo(placeholderTrans);
        assertThat(result.getHelpTextTranslations()).isEqualTo(helpTextTrans);
        assertThat(result.getType()).isEqualTo(type);
        assertThat(result.getDisplayOrder()).isEqualTo(7);
        assertThat(result.isRequired()).isTrue();
        assertThat(result.getValidationRegex()).isEqualTo("\\d+");
        assertThat(result.getMinLength()).isEqualTo(1);
        assertThat(result.getMaxLength()).isEqualTo(5);
        assertThat(result.getMinValue()).isEqualTo(1.0);
        assertThat(result.getMaxValue()).isEqualTo(100.0);
        assertThat(result.getProperties()).isEqualTo(props);

        // --- then: options added in order ---
        assertThat(result.getOptions()).containsExactly(ent1, ent2);

        // --- then: verify interactions ---
        then(createReq).should().getLabelTranslations();
        then(createReq).should().getDescriptionTranslations();
        then(createReq).should().getPlaceholderTranslations();
        then(createReq).should().getHelpTextTranslations();
        then(createReq).should().getType();
        then(createReq).should().getDisplayOrder();
        then(createReq).should().isRequired();
        then(createReq).should().getValidationRegex();
        then(createReq).should().getMinLength();
        then(createReq).should().getMaxLength();
        then(createReq).should().getMinValue();
        then(createReq).should().getMaxValue();
        then(createReq).should().getProperties();
        then(createReq).should().getOptions();

        then(optionMapper).should().toEntity(optReq1);
        then(optionMapper).should().toEntity(optReq2);

        then(createReq).shouldHaveNoMoreInteractions();
        then(optionMapper).shouldHaveNoMoreInteractions();
    }

    @Test
    void givenUpdateRequest_whenToEntity_thenMapsIdPlusOptions() {
        // --- given ---
        long id = 42L;
        given(updateReq.getId()).willReturn(id);

        UpdateElementOptionRequest optReq = mock(UpdateElementOptionRequest.class);
        given(updateReq.getOptions()).willReturn(List.of(optReq));

        ElementOption ent = new ElementOption();
        given(optionMapper.toEntity(optReq)).willReturn(ent);

        // also stub all the other getters that the mapper uses:
        Map<LanguageCode,String> labelTrans = Map.of(LanguageCode.EN, "lbl");
        Map<LanguageCode,String> descTrans  = Map.of(LanguageCode.EN, "desc");
        Map<LanguageCode,String> placeholder = Map.of(LanguageCode.EN, "ph");
        Map<LanguageCode,String> helpText    = Map.of(LanguageCode.EN, "help");
        ElementType type                    = ElementType.TEXT;
        given(updateReq.getLabelTranslations()).willReturn(labelTrans);
        given(updateReq.getDescriptionTranslations()).willReturn(descTrans);
        given(updateReq.getPlaceholderTranslations()).willReturn(placeholder);
        given(updateReq.getHelpTextTranslations()).willReturn(helpText);
        given(updateReq.getType()).willReturn(type);
        given(updateReq.getDisplayOrder()).willReturn(9);
        given(updateReq.isRequired()).willReturn(true);
        given(updateReq.getValidationRegex()).willReturn("\\w+");
        given(updateReq.getMinLength()).willReturn(2);
        given(updateReq.getMaxLength()).willReturn(8);
        given(updateReq.getMinValue()).willReturn(0.5);
        given(updateReq.getMaxValue()).willReturn(3.5);
        Map<String,String> props = Map.of("x","y");
        given(updateReq.getProperties()).willReturn(props);

        // --- when ---
        FormElement result = mapper.toEntity(updateReq);

        // --- then: data was mapped correctly ---
        assertThat(result.getId()).isEqualTo(id);
        assertThat(result.getOptions()).containsExactly(ent);
        assertThat(result.getLabelTranslations()).isSameAs(labelTrans);
        assertThat(result.getDescriptionTranslations()).isSameAs(descTrans);
        assertThat(result.getPlaceholderTranslations()).isSameAs(placeholder);
        assertThat(result.getHelpTextTranslations()).isSameAs(helpText);
        assertThat(result.getType()).isEqualTo(type);
        assertThat(result.getDisplayOrder()).isEqualTo(9);
        assertThat(result.isRequired()).isTrue();
        assertThat(result.getValidationRegex()).isEqualTo("\\w+");
        assertThat(result.getMinLength()).isEqualTo(2);
        assertThat(result.getMaxLength()).isEqualTo(8);
        assertThat(result.getMinValue()).isEqualTo(0.5);
        assertThat(result.getMaxValue()).isEqualTo(3.5);
        assertThat(result.getProperties()).isSameAs(props);

        // --- then: verify every interaction on updateReq ---
        then(updateReq).should().getId();
        then(updateReq).should().getLabelTranslations();
        then(updateReq).should().getDescriptionTranslations();
        then(updateReq).should().getPlaceholderTranslations();
        then(updateReq).should().getHelpTextTranslations();
        then(updateReq).should().getType();
        then(updateReq).should().getDisplayOrder();
        then(updateReq).should().isRequired();
        then(updateReq).should().getValidationRegex();
        then(updateReq).should().getMinLength();
        then(updateReq).should().getMaxLength();
        then(updateReq).should().getMinValue();
        then(updateReq).should().getMaxValue();
        then(updateReq).should().getProperties();
        then(updateReq).should().getOptions();

        // --- then: verify optionMapper call ---
        then(optionMapper).should().toEntity(optReq);

        // --- and no other calls happened ---
        then(updateReq).shouldHaveNoMoreInteractions();
        then(optionMapper).shouldHaveNoMoreInteractions();
    }

    @Test
    void givenExistingElement_whenUpdateEntityFromRequest_thenFieldsAreUpdatedAndOptionsReplaced() {
        // --- given ---
        FormElement target = new FormElement();
        // seed with an old option, so we can test that it's cleared
        ElementOption old = new ElementOption();
        target.setOptions(new ArrayList<>(List.of(old)));

        // stub all the UpdateFormElementRequest getters
        Map<LanguageCode,String> lt = Map.of(LanguageCode.EN,"L");
        Map<LanguageCode,String> dt = Map.of(LanguageCode.EN,"D");
        Map<LanguageCode,String> pt = Map.of(LanguageCode.EN,"P");
        Map<LanguageCode,String> ht = Map.of(LanguageCode.EN,"H");
        ElementType type = ElementType.TEXT;
        given(updateReq.getLabelTranslations()).willReturn(lt);
        given(updateReq.getDescriptionTranslations()).willReturn(dt);
        given(updateReq.getPlaceholderTranslations()).willReturn(pt);
        given(updateReq.getHelpTextTranslations()).willReturn(ht);
        given(updateReq.getType()).willReturn(type);
        given(updateReq.getDisplayOrder()).willReturn(8);
        given(updateReq.isRequired()).willReturn(false);
        given(updateReq.getValidationRegex()).willReturn("r");
        given(updateReq.getMinLength()).willReturn(1);
        given(updateReq.getMaxLength()).willReturn(2);
        given(updateReq.getMinValue()).willReturn(3.0);
        given(updateReq.getMaxValue()).willReturn(4.0);
        Map<String,String> props = Map.of("x","y");
        given(updateReq.getProperties()).willReturn(props);

        // prepare two new options
        UpdateElementOptionRequest or1 = mock(UpdateElementOptionRequest.class);
        UpdateElementOptionRequest or2 = mock(UpdateElementOptionRequest.class);
        given(updateReq.getOptions()).willReturn(List.of(or1, or2));
        ElementOption e1 = new ElementOption();
        ElementOption e2 = new ElementOption();
        given(optionMapper.toEntity(or1)).willReturn(e1);
        given(optionMapper.toEntity(or2)).willReturn(e2);

        // --- when ---
        mapper.updateEntityFromRequest(target, updateReq);

        // --- then: the target’s fields reflect the request ---
        assertThat(target.getLabelTranslations()).isSameAs(lt);
        assertThat(target.getDescriptionTranslations()).isSameAs(dt);
        assertThat(target.getPlaceholderTranslations()).isSameAs(pt);
        assertThat(target.getHelpTextTranslations()).isSameAs(ht);
        assertThat(target.getType()).isEqualTo(type);
        assertThat(target.getDisplayOrder()).isEqualTo(8);
        assertThat(target.isRequired()).isFalse();
        assertThat(target.getValidationRegex()).isEqualTo("r");
        assertThat(target.getMinLength()).isEqualTo(1);
        assertThat(target.getMaxLength()).isEqualTo(2);
        assertThat(target.getMinValue()).isEqualTo(3.0);
        assertThat(target.getMaxValue()).isEqualTo(4.0);
        assertThat(target.getProperties()).isSameAs(props);

        // --- then: old options were cleared and replaced with exactly the new ones ---
        assertThat(target.getOptions()).containsExactly(e1, e2);

        // --- then: verify interactions with updateReq and optionMapper ---
        then(updateReq).should().getLabelTranslations();
        then(updateReq).should().getDescriptionTranslations();
        then(updateReq).should().getPlaceholderTranslations();
        then(updateReq).should().getHelpTextTranslations();
        then(updateReq).should().getType();
        then(updateReq).should().getDisplayOrder();
        then(updateReq).should().isRequired();
        then(updateReq).should().getValidationRegex();
        then(updateReq).should().getMinLength();
        then(updateReq).should().getMaxLength();
        then(updateReq).should().getMinValue();
        then(updateReq).should().getMaxValue();
        then(updateReq).should().getProperties();
        then(updateReq).should().getOptions();

        then(optionMapper).should().toEntity(or1);
        then(optionMapper).should().toEntity(or2);

        then(updateReq).shouldHaveNoMoreInteractions();
        then(optionMapper).shouldHaveNoMoreInteractions();
    }

    @Test
    void givenFormElementWithMixedTranslations_whenToLocalizedDTO_thenMapsAllFieldsAndOptions() {
        // — given —
        LanguageCode primary  = LanguageCode.EN;
        LanguageCode fallback = LanguageCode.TR;
        LocalDateTime created  = LocalDateTime.of(2025, 6, 26, 9, 0);
        LocalDateTime updated  = created.plusMinutes(30);

        FormElement element = FormElement.builder()
                .labelTranslations(Map.of(primary, "Hello", fallback, "Merhaba"))
                .descriptionTranslations(Map.of(fallback, "Açıklama"))    // no primary → fallback
                .placeholderTranslations(Map.of())                        // empty → fallback
                .helpTextTranslations(Map.of(primary, "", fallback, "Yardım")) // blank primary → fallback
                .type(ElementType.TEXT)
                .displayOrder(2)
                .required(true)
                .validationRegex("\\d{3}")
                .minLength(3)
                .maxLength(5)
                .minValue(1.5)
                .maxValue(9.9)
                .properties(Map.of("pkey", "pval"))
                .build();
        element.setId(10L);
        element.setCreatedAt(created);
        element.setUpdatedAt(updated);

        // builder-default gives us a non-null options list
        ElementOption opt1 = new ElementOption();
        ElementOption opt2 = new ElementOption();
        element.addOption(opt1);
        element.addOption(opt2);

        // stub the optionMapper for each option
        LocalizedElementOptionDTO dto1 = LocalizedElementOptionDTO.builder().id(101L).build();
        LocalizedElementOptionDTO dto2 = LocalizedElementOptionDTO.builder().id(102L).build();
        given(optionMapper.toLocalizedDTO(opt1, primary, fallback)).willReturn(dto1);
        given(optionMapper.toLocalizedDTO(opt2, primary, fallback)).willReturn(dto2);

        // — when —
        LocalizedFormElementDTO out = mapper.toLocalizedDTO(element, primary, fallback);

        // — then: fallback logic —
        assertThat(out.getId()).isEqualTo(10L);
        assertThat(out.getLabel()).isEqualTo("Hello");
        assertThat(out.isLabelFromFallback()).isFalse();

        assertThat(out.getDescription()).isEqualTo("Açıklama");
        assertThat(out.isDescriptionFromFallback()).isTrue();

        assertThat(out.getPlaceholder()).isNull();
        assertThat(out.isPlaceholderFromFallback()).isTrue();

        assertThat(out.getHelpText()).isEqualTo("Yardım");
        assertThat(out.isHelpTextFromFallback()).isTrue();

        // — then: simple fields —
        assertThat(out.getType()).isEqualTo(ElementType.TEXT);
        assertThat(out.getDisplayOrder()).isEqualTo(2);
        assertThat(out.isRequired()).isTrue();
        assertThat(out.getValidationRegex()).isEqualTo("\\d{3}");
        assertThat(out.getMinLength()).isEqualTo(3);
        assertThat(out.getMaxLength()).isEqualTo(5);
        assertThat(out.getMinValue()).isEqualTo(1.5);
        assertThat(out.getMaxValue()).isEqualTo(9.9);
        assertThat(out.getProperties()).containsEntry("pkey","pval");
        assertThat(out.getCreatedAt()).isSameAs(created);
        assertThat(out.getUpdatedAt()).isSameAs(updated);
        assertThat(out.getLanguageCode()).isEqualTo(primary);
        assertThat(out.getFallbackLanguageCode()).isEqualTo(fallback);

    }

    @Test
    void testToDTO() {
        // — given: a fully‐populated FormElement —
        LocalDateTime created = LocalDateTime.of(2025, 6, 26, 8, 30);
        LocalDateTime updated = created.plusMinutes(15);

        FormElement element = FormElement.builder()
                .labelTranslations(Map.of(LanguageCode.EN, "Label"))
                .descriptionTranslations(Map.of(LanguageCode.EN, "Desc"))
                .placeholderTranslations(Map.of(LanguageCode.EN, "Place"))
                .helpTextTranslations(Map.of(LanguageCode.EN, "Help"))
                .type(ElementType.CHECKBOX)
                .displayOrder(5)
                .required(true)
                .validationRegex("\\d+")
                .minLength(1)
                .maxLength(10)
                .minValue(0.5)
                .maxValue(9.5)
                .properties(Map.of("key", "value"))
                .build();
        element.setId(123L);
        element.setCreatedAt(created);
        element.setUpdatedAt(updated);

        // builder.default ensures options != null
        ElementOption o1 = new ElementOption();
        ElementOption o2 = new ElementOption();
        element.addOption(o1);
        element.addOption(o2);

        // stub the option‐mapper
        ElementOptionDTO dto1 = ElementOptionDTO.builder().id(1L).build();
        ElementOptionDTO dto2 = ElementOptionDTO.builder().id(2L).build();

        // — when —
        FormElementDTO result = mapper.toDTO(element);

        // — then: scalar fields —
        assertThat(result.getId()).isEqualTo(123L);
        assertThat(result.getLabelTranslations()).containsEntry(LanguageCode.EN, "Label");
        assertThat(result.getDescriptionTranslations()).containsEntry(LanguageCode.EN, "Desc");
        assertThat(result.getPlaceholderTranslations()).containsEntry(LanguageCode.EN, "Place");
        assertThat(result.getHelpTextTranslations()).containsEntry(LanguageCode.EN, "Help");
        assertThat(result.getType()).isEqualTo(ElementType.CHECKBOX);
        assertThat(result.getDisplayOrder()).isEqualTo(5);
        assertThat(result.isRequired()).isTrue();
        assertThat(result.getValidationRegex()).isEqualTo("\\d+");
        assertThat(result.getMinLength()).isEqualTo(1);
        assertThat(result.getMaxLength()).isEqualTo(10);
        assertThat(result.getMinValue()).isEqualTo(0.5);
        assertThat(result.getMaxValue()).isEqualTo(9.5);
        assertThat(result.getProperties()).containsEntry("key", "value");
        assertThat(result.getCreatedAt()).isSameAs(created);
        assertThat(result.getUpdatedAt()).isSameAs(updated);

    }

}