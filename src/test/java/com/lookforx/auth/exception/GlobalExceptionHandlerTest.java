package com.lookforx.auth.exception;

import com.lookforx.auth.base.AbstractBaseServiceTest;
import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.common.dto.ExceptionMessageResponse;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import com.lookforx.common.dto.ErrorResponse;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

class GlobalExceptionHandlerTest extends AbstractBaseServiceTest {

    @InjectMocks
    private GlobalExceptionHandler handler;

    @Mock
    private ExceptionServiceClient exceptionClient;

    @Mock
    private HttpServletRequest request;

    @Mock
    private MethodArgumentNotValidException methodArgEx;

    @Test
    void givenFieldError_whenMethodArgumentNotValid_thenReturnsBadRequest() {
        // 1) Language
        when(request.getHeader("Accept-Language")).thenReturn("en");

        // 2) Bind a single field error
        BindingResult br = new BeanPropertyBindingResult(new Object(), "obj");
        br.addError(new FieldError("obj", "username", "must not be blank"));
        when(methodArgEx.getBindingResult()).thenReturn(br);

        // 3) Stub exceptionClient to return a template with one %s
        String template = "Validation failed for field: %s";
        when(exceptionClient.getExceptionMessage("VALIDATION_FAILED", "EN"))
                .thenReturn(new ExceptionMessageResponse("VALIDATION_FAILED","EN", template));

        // 4) Exercise
        ResponseEntity<ErrorResponse> resp =
                handler.handleValidationErrors(methodArgEx, request);

        // 5) Verify status and body
        assertEquals(HttpStatus.BAD_REQUEST, resp.getStatusCode());
        ErrorResponse body = resp.getBody();
        assertNotNull(body);

        // Now expect the HTTP reason phrase, not “Validation error”
        assertEquals(HttpStatus.BAD_REQUEST.getReasonPhrase(), body.getError());
        assertEquals("Validation failed for field: username", body.getMessage());

    }

    @Test
    void givenAuthenticationException_thenReturnsUnauthorized() {
        // 1) language header
        when(request.getHeader("Accept-Language")).thenReturn("en");

        // 2) the exception
        AuthenticationException ex = new AuthenticationException("Bad credentials");

        // 3) stub the client to return the actual message
        when(exceptionClient.getExceptionMessage("AUTHENTICATION_ERROR", "EN"))
                .thenReturn(new ExceptionMessageResponse(
                        "AUTHENTICATION_ERROR",
                        "EN",
                        "Bad credentials"      // <-- return the literal text
                ));

        // 4) invoke
        ResponseEntity<ErrorResponse> resp = handler.handleAuthenticationException(ex, request);

        // 5) verify
        assertEquals(HttpStatus.UNAUTHORIZED, resp.getStatusCode());
        ErrorResponse body = resp.getBody();
        assertNotNull(body);
        assertEquals("Authentication error", body.getError());
        assertEquals("Bad credentials", body.getMessage());
    }


    @Test
    void givenOAuth2AuthenticationException_thenReturnsUnauthorized() {
        // 1) Stub language header
        when(request.getHeader("Accept-Language")).thenReturn("en");

        // 2) Create the OAuth2 exception
        OAuth2Error oauthErr = new OAuth2Error(
                "invalid_token",
                "token expired",
                null
        );
        OAuth2AuthenticationException ex =
                new OAuth2AuthenticationException(oauthErr, "token expired");

        // 3) Return the literal message in the template (no % placeholders)
        when(exceptionClient.getExceptionMessage("GOOGLE_AUTHENTICATION_ERROR", "EN"))
                .thenReturn(new ExceptionMessageResponse(
                        "GOOGLE_AUTHENTICATION_ERROR",
                        "EN",
                        "token expired"
                ));

        // 4) Call the handler
        ResponseEntity<ErrorResponse> resp =
                handler.handleOAuth2Exception(ex, request);

        // 5) Assert
        assertEquals(HttpStatus.UNAUTHORIZED, resp.getStatusCode());
        ErrorResponse body = resp.getBody();
        assertNotNull(body);
        assertEquals("Google authentication error", body.getError());
        assertEquals("token expired", body.getMessage());
    }


    @Test
    void givenGenericException_thenReturnsInternalServerError() {
        // 1) Make sure the handler sees “EN” as the language
        when(request.getHeader("Accept-Language")).thenReturn("en");

        // 2) Create the exception
        Exception ex = new RuntimeException("something went wrong");

        // 3) Stub the ExceptionServiceClient to return our desired message directly
        when(exceptionClient.getExceptionMessage("INTERNAL_SERVER_ERROR", "EN"))
                .thenReturn(new ExceptionMessageResponse(
                        "INTERNAL_SERVER_ERROR",
                        "EN",
                        "something went wrong"
                ));

        // 4) Call the handler
        ResponseEntity<ErrorResponse> resp = handler.handleAllUncaught(ex, request);

        // 5) Verify
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, resp.getStatusCode());
        ErrorResponse body = resp.getBody();
        assertNotNull(body);
        assertEquals("Internal Server Error", body.getError());
        assertEquals("something went wrong", body.getMessage());
    }

    @Test
    void givenUserNotFoundException_thenReturnsNotFound() {
        when(request.getHeader("Accept-Language")).thenReturn("en");

        String userId = UUID.randomUUID().toString();
        UserNotFoundException ex = UserNotFoundException.forId(userId);

        when(exceptionClient.getExceptionMessage("USER_NOT_FOUND", "EN"))
                .thenReturn(new ExceptionMessageResponse("USER_NOT_FOUND","EN","User not found with id: %s"));

        ResponseEntity<ErrorResponse> resp =
                handler.handleUserNotFoundException(ex, request);

        assertEquals(HttpStatus.NOT_FOUND, resp.getStatusCode());
        ErrorResponse body = resp.getBody();
        assertNotNull(body);
        assertEquals("User not found", body.getError());
        assertEquals(
                String.format("User not found with id: %s", userId),
                body.getMessage()
        );
    }

    @Test
    void givenIllegalArgumentException_thenReturnsBadRequest() {
        when(request.getHeader("Accept-Language")).thenReturn("en");

        IllegalArgumentException ex = new IllegalArgumentException("Invalid role");
        when(exceptionClient.getExceptionMessage("INVALID_ARGUMENT", "EN"))
                .thenReturn(new ExceptionMessageResponse("INVALID_ARGUMENT","EN","%s"));

        ResponseEntity<ErrorResponse> resp =
                handler.handleIllegalArgumentException(ex, request);

        assertEquals(HttpStatus.BAD_REQUEST, resp.getStatusCode());
        ErrorResponse body = resp.getBody();
        assertNotNull(body);
        assertEquals("Invalid argument", body.getError());
        assertEquals("Invalid role", body.getMessage());
    }

}