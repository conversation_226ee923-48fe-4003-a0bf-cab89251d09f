package com.lookforx.auth.controller;

import com.lookforx.auth.base.AbstractRestControllerTest;
import com.lookforx.auth.dto.*;
import com.lookforx.auth.entity.User;
import com.lookforx.auth.repository.UserRepository;
import com.lookforx.auth.service.AuthService;
import com.lookforx.auth.service.JwtService;
import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.common.dto.ExceptionMessageResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.util.Collections;
import java.util.UUID;

import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(controllers = AuthController.class)
@AutoConfigureMockMvc(addFilters = false)
class AuthControllerTest extends AbstractRestControllerTest {

    @MockBean
    private AuthService authService;

    @MockBean
    private UserRepository userRepository;

    @MockBean
    JwtService jwtService;

    @MockBean
    UserDetailsService userDetailsService;

    @MockBean
    ExceptionServiceClient exceptionServiceClient;

    @Test
    void signupSuccess() throws Exception {

        // Given
        var signup = new SignupRequest("<EMAIL>", "Passw0rd!", "Alice");
        var userResp = UserResponse.builder()
                .id(UUID.randomUUID().toString())
                .email(signup.getEmail())
                .name(signup.getName())
                .imageUrl(null)
                .build();
        var authResp = AuthResponse.builder()
                .accessToken("at")
                .refreshToken("rt")
                .user(userResp)
                .message("Account created successfully")
                .build();

        // When
        when(authService.signup(any(SignupRequest.class))).thenReturn(authResp);

        // Then
        mockMvc.perform(post("/v1/auth/signup")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(signup)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").value("at"))
                .andExpect(jsonPath("$.refreshToken").value("rt"))
                .andExpect(jsonPath("$.user.email").value("<EMAIL>"))
                .andExpect(cookie().value("accessToken", "at"))
                .andExpect(cookie().value("refreshToken", "rt"))
                .andExpect(cookie().exists("user"));

        // Verify
        verify(authService).signup(any(SignupRequest.class));

    }

    @Test
    void signupFailure() throws Exception {
        // stub the exception‐service template for the VALIDATION_FAILED code
        when(exceptionServiceClient.getExceptionMessage("VALIDATION_FAILED", "EN"))
                .thenReturn(new ExceptionMessageResponse(
                        "VALIDATION_FAILED",
                        "EN",
                        "Password must be at least 8 characters long, contain an uppercase letter, a number and a special character"
                ));

        // Given
        var signup = new SignupRequest("<EMAIL>", "short", "Alice");

        // When / Then
        mockMvc.perform(post("/v1/auth/signup")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(signup)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.message")
                        .value("Password must be at least 8 characters long, contain an uppercase letter, a number and a special character password"));

        // service.signup should never be called
        verify(authService, times(0)).signup(any(SignupRequest.class));
    }

    @Test
    void loginSuccess() throws Exception {
        var login = new LoginRequest("<EMAIL>", "Passw0rd!");
        var userResp = UserResponse.builder()
                .id(UUID.randomUUID().toString())
                .email(login.getEmail())
                .name("Alice")
                .imageUrl(null)
                .build();
        var authResp = AuthResponse.builder()
                .accessToken("lat")
                .refreshToken("lrt")
                .user(userResp)
                .message("Login successful")
                .build();

        when(authService.login(any(LoginRequest.class))).thenReturn(authResp);

        mockMvc.perform(post("/v1/auth/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(login)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").value("lat"))
                .andExpect(jsonPath("$.refreshToken").value("lrt"))
                .andExpect(jsonPath("$.user.email").value("<EMAIL>"))
                .andExpect(cookie().value("accessToken", "lat"))
                .andExpect(cookie().value("refreshToken", "lrt"));

        verify(authService).login(any(LoginRequest.class));
    }

    @Test
    void loginFailure() throws Exception {
        var login = new LoginRequest("<EMAIL>", "wrongpwd");

        when(authService.login(any(LoginRequest.class)))
                .thenThrow(new RuntimeException("Invalid credentials"));

        mockMvc.perform(post("/v1/auth/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(login)))
                .andExpect(status().isUnauthorized())
                .andExpect(jsonPath("$.message").value("Invalid credentials"));

        verify(authService).login(any(LoginRequest.class));
    }

    @Test
    void refreshTokenSuccess() throws Exception {
        String refreshToken = "lrt";
        var userResp = UserResponse.builder()
                .id(UUID.randomUUID().toString())
                .email("<EMAIL>")
                .name("Alice")
                .imageUrl(null)
                .build();
        var authResp = AuthResponse.builder()
                .accessToken("nat")
                .refreshToken(refreshToken)
                .user(userResp)
                .message("Token refreshed")
                .build();

        when(authService.refreshToken(eq(refreshToken))).thenReturn(authResp);

        mockMvc.perform(post("/v1/auth/refresh-token")
                        .contentType(MediaType.TEXT_PLAIN)
                        .content(refreshToken))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").value("nat"))
                .andExpect(jsonPath("$.refreshToken").value("lrt"))
                .andExpect(cookie().value("accessToken", "nat"))
                .andExpect(cookie().value("refreshToken", "lrt"));

        verify(authService).refreshToken(eq(refreshToken));
    }

    @Test
    void getCurrentUser_withDomainUserPrincipal() throws Exception {

        // Given
        var user = User.builder()
                .id("1L")
                .email("<EMAIL>")
                .name("Alice")
                .roles(Collections.emptySet())
                .build();

        // When & Then
        mockMvc.perform(get("/v1/auth/me")
                        .principal(new org.springframework.security.authentication.UsernamePasswordAuthenticationToken(user, null)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.name").value("Alice"));

        // Verify
        verifyNoInteractions(userRepository);

    }

}