package com.lookforx.auth.controller;

import com.lookforx.auth.base.AbstractRestControllerTest;
import com.lookforx.auth.dto.CreateUserProfileRequest;
import com.lookforx.auth.dto.UpdateUserProfileRequest;
import com.lookforx.auth.dto.UserProfileResponse;
import com.lookforx.auth.service.JwtService;
import com.lookforx.auth.service.UserProfileService;
import com.lookforx.common.client.ExceptionServiceClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = UserProfileController.class)
class UserProfileControllerTest extends AbstractRestControllerTest {

    @MockBean
    private UserProfileService userProfileService;

    private UserProfileResponse sampleResponse;
    private CreateUserProfileRequest createReq;
    private UpdateUserProfileRequest updateReq;

    @MockBean
    JwtService jwtService;

    @MockBean
    UserDetailsService userDetailsService;

    @MockBean
    ExceptionServiceClient exceptionServiceClient;

    @BeforeEach
    void setUp() {
        sampleResponse = UserProfileResponse.builder()
                .id("user123")
                .username("alice")
                .firstName("Alice")
                .lastName("Wonderland")
                .mobilePhone("1234567890")
                .build();

        createReq = CreateUserProfileRequest.builder()
                .firstName("Alice")
                .lastName("Wonderland")
                .mobilePhone("1234567890")
                .build();

        updateReq = UpdateUserProfileRequest.builder()
                .firstName("AliceUpdated")
                .lastName("WonderlandUpdated")
                .mobilePhone("0987654321")
                .build();
    }

    @Test
    void createProfile_ShouldReturnCreated() throws Exception {

        // Given
        String email = "<EMAIL>";

        // When
        when(userProfileService.createProfile(eq(email), any(CreateUserProfileRequest.class)))
                .thenReturn(sampleResponse);

        // Then
        mockMvc.perform(post("/v1/profile")
                        .with(user(email).roles("USER"))
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createReq)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").value("user123"));

        // Verify
        verify(userProfileService).createProfile(eq(email), any(CreateUserProfileRequest.class));

    }

    @Test
    void getMyProfile_ShouldReturnOk() throws Exception {

        // Given
        String email = "<EMAIL>";

        // When
        when(userProfileService.getProfileByUserId(email))
                .thenReturn(sampleResponse);

        // Then
        mockMvc.perform(get("/v1/profile/me")
                        .with(user(email).roles("USER")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.username").value("alice"));

        // Verify
        verify(userProfileService).getProfileByUserId(email);

    }

    @Test
    void updateMyProfile_ShouldReturnOk() throws Exception {

        // Given
        String email = "<EMAIL>";

        // When
        when(userProfileService.updateProfile(eq(email), any(UpdateUserProfileRequest.class)))
                .thenReturn(sampleResponse);

        // Then
        mockMvc.perform(put("/v1/profile/me")
                        .with(user(email).roles("USER"))
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateReq)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.mobilePhone").value("1234567890"));

        // Verify
        verify(userProfileService).updateProfile(eq(email), any(UpdateUserProfileRequest.class));

    }

    @Test
    void deleteMyProfile_ShouldReturnNoContent() throws Exception {

        // Given
        String email = "<EMAIL>";

        // When
        doNothing().when(userProfileService).deleteProfile("email");

        // Then
        mockMvc.perform(delete("/v1/profile/me")
                        .with(user(email).roles("USER"))
                        .with(csrf())
                )
                .andExpect(status().isNoContent());

        // Verify
        verify(userProfileService).deleteProfile(email);

    }

    @Test
    void getProfileByUsername_ShouldReturnOk() throws Exception {

        // Given
        String userName = "user1";
        sampleResponse.setUsername(userName);

        // When
        when(userProfileService.getProfileByUsername(userName))
                .thenReturn(sampleResponse);

        // Then
        mockMvc.perform(get("/v1/profile/username/{username}", userName)
                        .with(user("publicUser").roles("USER")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.username").value(userName));

        // Verify
        verify(userProfileService).getProfileByUsername(userName);

    }

    @Test
    void checkUsernameAvailability_ShouldReturnMap() throws Exception {

        // Given
        String userName = "bob";

        // When
        when(userProfileService.isUsernameAvailable(userName))
                .thenReturn(true);

        // Then
        mockMvc.perform(get("/v1/profile/username/{username}/available", userName)
                        .with(user("publicUser").roles("USER")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.available").value(true));

        // Verify
        verify(userProfileService).isUsernameAvailable(userName);

    }

    @Test
    void searchProfiles_ShouldReturnList() throws Exception {

        // Given
        String term = "ali";

        // When
        when(userProfileService.searchProfiles(term))
                .thenReturn(List.of(sampleResponse));

        // Then
        mockMvc.perform(get("/v1/profile/search").param("q", term)
                        .with(user("publicUser").roles("USER")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").value("user123"));

        // Verify
        verify(userProfileService).searchProfiles(term);

    }

    @Test
    void getPublicProfiles_ShouldReturnList() throws Exception {

        // When
        when(userProfileService.getPublicProfiles())
                .thenReturn(List.of(sampleResponse));

        // Then
        mockMvc.perform(get("/v1/profile/public")
                        .with(user("publicUser").roles("USER")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].id").value("user123"));

        // Verify
        verify(userProfileService).getPublicProfiles();

    }

    @Test
    void adminGetProfileByUserId_ShouldReturnOk() throws Exception {

        // Given
        String userId = "admin123";

        // When
        when(userProfileService.getProfileByUserId(userId))
                .thenReturn(sampleResponse);

        // Then
        mockMvc.perform(get("/v1/profile/admin/user/{userId}", userId)
                        .with(user("adminUser").roles("ADMIN")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value("user123"));

        // Verify
        verify(userProfileService).getProfileByUserId(userId);

    }

    @Test
    void adminUpdateProfileByUserId_ShouldReturnOk() throws Exception {

        // Given
        String userId = "admin123";

        // When
        when(userProfileService.updateProfile(eq(userId), any(UpdateUserProfileRequest.class)))
                .thenReturn(sampleResponse);

        // Then
        mockMvc.perform(put("/v1/profile/admin/user/{userId}", userId)
                        .with(user("adminUser").roles("ADMIN"))
                        .with(csrf())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateReq)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value("user123"));

        // Verify
        verify(userProfileService).updateProfile(eq(userId), any(UpdateUserProfileRequest.class));

    }

    @Test
    void adminDeleteProfileByUserId_ShouldReturnNoContent() throws Exception {

        // Given
        String userId = "admin123";

        // When & Then
        mockMvc.perform(delete("/v1/profile/admin/user/{userId}", userId)
                        .with(user("adminUser").roles("ADMIN"))
                        .with(csrf())
                )
                .andExpect(status().isNoContent());

        // Verify
        verify(userProfileService).deleteProfile(userId);

    }

}