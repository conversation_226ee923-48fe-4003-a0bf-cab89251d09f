package com.lookforx.auth.controller;

import com.lookforx.auth.base.AbstractRestControllerTest;
import com.lookforx.auth.dto.AuthResponse;
import com.lookforx.auth.service.JwtService;
import com.lookforx.auth.service.OAuth2Service;
import com.lookforx.common.client.ExceptionServiceClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.mockito.BDDMockito.given;
import static org.mockito.BDDMockito.then;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(controllers = OAuth2Controller.class)
@AutoConfigureMockMvc(addFilters = false)
class OAuth2ControllerTest extends AbstractRestControllerTest {

    @MockBean
    private OAuth2Service oAuth2Service;

    @Autowired
    private OAuth2Controller controller;

    @MockBean
    JwtService jwtService;

    @MockBean
    UserDetailsService userDetailsService;

    @MockBean
    ExceptionServiceClient exceptionServiceClient;

    @BeforeEach
    void setupCache() {
        // clear cache before each test
        ReflectionTestUtils.setField(controller, "authCodeCache", new ConcurrentHashMap<String, AuthResponse>());
    }

    @Test
    void getGoogleAuthUrl_success() throws Exception {
        // given
        String url = "https://accounts.google.com/o/oauth2/auth?...";
        given(oAuth2Service.getGoogleAuthUrl()).willReturn(url);

        // when/then
        mockMvc.perform(get("/v1/oauth2/google/url"))
                .andExpect(status().isOk())
                .andExpect(content().string(url));

        // verify
        then(oAuth2Service).should().getGoogleAuthUrl();
    }

    @Test
    void handleGoogleLogin_success() throws Exception {
        // given
        String idToken = "sample-id-token";
        AuthResponse resp = AuthResponse.builder()
                .accessToken("at123")
                .refreshToken("rt123")
                .message("Logged in")
                .user(null)
                .build();
        given(oAuth2Service.handleGoogleLogin(idToken)).willReturn(resp);

        // when/then
        mockMvc.perform(post("/v1/oauth2/google/login")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of("idToken", idToken))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").value("at123"))
                .andExpect(jsonPath("$.refreshToken").value("rt123"));

        // verify
        then(oAuth2Service).should().handleGoogleLogin(idToken);
    }

    @Test
    void handleGoogleCallback_success() throws Exception {
        // given
        String code = "authCode";
        AuthResponse resp = AuthResponse.builder()
                .accessToken("atCB")
                .refreshToken("rtCB")
                .user(com.lookforx.auth.dto.UserResponse.builder()
                    .id("user123")
                    .email("<EMAIL>")
                    .name("Test User")
                    .build())
                .message("Callback ok")
                .build();
        given(oAuth2Service.handleGoogleCallback(code)).willReturn(resp);

        // when/then
        mockMvc.perform(get("/v1/oauth2/callback/google")
                        .param("code", code))
                .andExpect(status().isFound())
                .andExpect(header().string(HttpHeaders.LOCATION, "http://localhost:3000/auth/callback/google?success=true&user_id=user123"))
                .andExpect(cookie().value("accessToken", "atCB"))
                .andExpect(cookie().value("refreshToken", "rtCB"))
                .andExpect(cookie().exists("user"));

        // verify
        then(oAuth2Service).should().handleGoogleCallback(code);
    }

    @Test
    void handleGoogleCallback_errorParam() throws Exception {
        // when/then
        mockMvc.perform(get("/v1/oauth2/callback/google")
                        .param("code", "ignored")                  // ← dummy code so it doesn’t 400 on missing param
                        .param("error", "access_denied"))
                .andExpect(status().isFound())
                .andExpect(header().string(HttpHeaders.LOCATION,
                    "http://localhost:3000/auth/callback/google?error=access_denied"));

        // verify no callback handling
        then(oAuth2Service).shouldHaveNoInteractions();
    }

    @Test
    void exchangeCode_flow() throws Exception {
        // given
        String code = "oneTimeCode";
        AuthResponse resp = AuthResponse.builder()
                .accessToken("atX")
                .refreshToken("rtX")
                .user(null)
                .message("Exchanged")
                .build();
        // preload cache map
        @SuppressWarnings("unchecked")
        Map<String, AuthResponse> cache = (Map<String, AuthResponse>)
                ReflectionTestUtils.getField(controller, "authCodeCache");
        cache.put(code, resp);

        // when/then
        mockMvc.perform(post("/v1/oauth2/exchange-code")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(Map.of("code", code))))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.accessToken").value("atX"))
                .andExpect(jsonPath("$.refreshToken").value("rtX"));
    }
}
