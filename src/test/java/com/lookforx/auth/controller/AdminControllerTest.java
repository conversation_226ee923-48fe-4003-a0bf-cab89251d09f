package com.lookforx.auth.controller;

import com.lookforx.auth.base.AbstractRestControllerTest;
import com.lookforx.auth.dto.UpdateUserRoleRequest;
import com.lookforx.auth.dto.UpdateUserStatusRequest;
import com.lookforx.auth.dto.UserListResponse;
import com.lookforx.auth.dto.UserResponse;
import com.lookforx.auth.service.AdminService;
import com.lookforx.auth.service.JwtService;
import com.lookforx.common.client.ExceptionServiceClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.user;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(controllers = AdminController.class)
@AutoConfigureMockMvc(addFilters = false)
class AdminControllerTest extends AbstractRestControllerTest {

    @MockBean
    private AdminService adminService;

    private UserResponse user1;
    private UserResponse user2;
    private UserListResponse listResponse;

    @MockBean
    ExceptionServiceClient exceptionServiceClient;

    @MockBean
    JwtService jwtService;

    @MockBean
    UserDetailsService userDetailsService;

    @BeforeEach
    void setUp() {
        user1 = UserResponse.builder()
                .id("u1").email("<EMAIL>").name("Alice").roles(Set.of("USER"))
                .active(true).build();
        user2 = UserResponse.builder()
                .id("u2").email("<EMAIL>").name("Bob").roles(Set.of("ADMIN"))
                .active(false).build();
        listResponse = UserListResponse.builder()
                .users(List.of(user1, user2))
                .totalCount(2)
                .build();
    }

    @Test
    void getUsers_ShouldReturnPagedList() throws Exception {

        // When
        when(adminService.getUsers(any(Pageable.class), eq(null), eq(null), eq(null), eq(null)))
                .thenReturn(listResponse);

        // Then
        mockMvc.perform(get("/v1/admin/users")
                        .with(user("admin").roles("ADMIN"))
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalCount").value(2))
                .andExpect(jsonPath("$.users[0].id").value("u1"));

        // Verify
        verify(adminService).getUsers(any(Pageable.class), eq(null), eq(null), eq(null), eq(null));

    }

    @Test
    void getUserById_ShouldReturnUser() throws Exception {

        // When
        when(adminService.getUserById("u1")).thenReturn(user1);

        // Then
        mockMvc.perform(get("/v1/admin/users/{userId}", "u1")
                        .with(user("admin").roles("ADMIN")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"));

        // Verify
        verify(adminService).getUserById("u1");

    }

    @Test
    void updateUserRoles_ShouldReturnUpdated() throws Exception {

        // Given
        UpdateUserRoleRequest roleReq = UpdateUserRoleRequest.builder()
                .roles(List.of("ADMIN", "USER")).build();

        // When
        when(adminService.updateUserRoles("u1", roleReq.getRoles())).thenReturn(user1);

        // Then
        mockMvc.perform(put("/v1/admin/users/{userId}/roles", "u1")
                        .with(user("admin").roles("ADMIN"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(roleReq)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.roles").isArray());

        // Verify
        verify(adminService).updateUserRoles("u1", roleReq.getRoles());

    }

    @Test
    void updateUserStatus_ShouldReturnUpdated() throws Exception {

        // Given
        UpdateUserStatusRequest statusReq = UpdateUserStatusRequest.builder()
                .active(false).build();

        // When
        when(adminService.updateUserStatus("u2", false)).thenReturn(user2);

        // Then
        mockMvc.perform(put("/v1/admin/users/{userId}/status", "u2")
                        .with(user("admin").roles("ADMIN"))
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(statusReq)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.active").value(false));

        // Verify
        verify(adminService).updateUserStatus("u2", false);

    }

    @Test
    void deleteUser_ShouldReturnNoContent() throws Exception {

        // When & Then
        mockMvc.perform(delete("/v1/admin/users/{userId}", "u1")
                        .with(user("admin").roles("ADMIN")))
                .andExpect(status().isNoContent());

        // Verify
        verify(adminService).deleteUser("u1");

    }

    @Test
    void getAvailableRoles_ShouldReturnList() throws Exception {

        // When
        when(adminService.getAvailableRoles()).thenReturn(List.of("ADMIN", "USER"));

        // Then
        mockMvc.perform(get("/v1/admin/users/roles")
                        .with(user("admin").roles("ADMIN")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0]").value("ADMIN"));

        // Verify
        verify(adminService).getAvailableRoles();

    }

    @Test
    void getUserStats_ShouldReturnMap() throws Exception {

        // Given
        Map<String, Object> stats = Map.of("total", 5, "active", 3);

        // When
        when(adminService.getUserStats()).thenReturn(stats);

        // Then
        mockMvc.perform(get("/v1/admin/users/stats")
                        .with(user("admin").roles("ADMIN")))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.total").value(5));

        // Verify
        verify(adminService).getUserStats();

    }

}