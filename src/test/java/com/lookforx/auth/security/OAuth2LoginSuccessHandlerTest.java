package com.lookforx.auth.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.auth.service.JwtService;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.web.RedirectStrategy;

import java.io.IOException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OAuth2LoginSuccessHandlerTest {

    @Mock
    private JwtService jwtService;

    @Mock
    private RedirectStrategy redirectStrategy;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private Authentication authentication;

    @Mock
    private OAuth2User oAuth2User;


    @InjectMocks
    private OAuth2LoginSuccessHandler handler;

    @BeforeEach
    void setUp() {
        handler.setRedirectStrategy(redirectStrategy);
    }

    @Test
    void onAuthenticationSuccess_addsTokensAndRedirects() throws ServletException, IOException {
        // Given
        String email = "<EMAIL>";
        when(authentication.getPrincipal()).thenReturn(oAuth2User);
        when(oAuth2User.getAttribute("email")).thenReturn(email);

        when(jwtService.generateAccessToken(any(CustomUserDetails.class))).thenReturn("acc-token");
        when(jwtService.generateRefreshToken(any(CustomUserDetails.class))).thenReturn("ref-token");

        // When
        handler.onAuthenticationSuccess(request, response, authentication);

        // Then
        // response headers
        verify(response).addHeader("Access-Token", "acc-token");
        verify(response).addHeader("Refresh-Token", "ref-token");

        // redirect
        verify(redirectStrategy).sendRedirect(request, response, "/auth/success");

        // jwtService invoked with a CustomUserDetails carrying the right email and role
        verify(jwtService).generateAccessToken(argThat(arg -> {
            if (!(arg instanceof CustomUserDetails cud)) return false;
            return email.equals(cud.getEmail()) &&
                    cud.getAuthorities().stream()
                            .anyMatch(a -> "ROLE_USER".equals(a.getAuthority()));
        }));
        verify(jwtService).generateRefreshToken(argThat(arg -> {
            if (!(arg instanceof CustomUserDetails cud)) return false;
            return email.equals(cud.getEmail()) &&
                    cud.getAuthorities().stream()
                            .anyMatch(a -> "ROLE_USER".equals(a.getAuthority()));
        }));
    }
}