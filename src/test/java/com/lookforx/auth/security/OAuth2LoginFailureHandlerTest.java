package com.lookforx.auth.security;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.RedirectStrategy;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OAuth2LoginFailureHandlerTest {

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private AuthenticationException exception;

    @Mock
    private RedirectStrategy redirectStrategy;

    @InjectMocks
    private OAuth2LoginFailureHandler handler;

    @BeforeEach
    void setUp() {
        // inject our mock RedirectStrategy so no real redirect is attempted
        handler.setRedirectStrategy(redirectStrategy);

        // stub the exception message
        when(exception.getMessage()).thenReturn("bad credentials");
    }

    @Test
    void onAuthenticationFailure_sendsUrlEncodedRedirect() throws ServletException, IOException {
        // When
        handler.onAuthenticationFailure(request, response, exception);

        // Then
        String encoded = URLEncoder.encode("bad credentials", StandardCharsets.UTF_8.toString());
        String expectedUrl = "http://192.168.1.35:3000/login?error=" + encoded;

        verify(redirectStrategy).sendRedirect(
                eq(request),
                eq(response),
                eq(expectedUrl)
        );
    }
}