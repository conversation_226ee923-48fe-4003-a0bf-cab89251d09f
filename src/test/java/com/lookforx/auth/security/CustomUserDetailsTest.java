package com.lookforx.auth.security;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class CustomUserDetailsTest {

    @Mock
    private GrantedAuthority authority;

    private final String EMAIL = "<EMAIL>";
    private final String PASSWORD = "secret";

    private CustomUserDetails userDetails;

    @BeforeEach
    void setUp() {
        lenient().when(authority.getAuthority()).thenReturn("ROLE_USER");
        Collection<GrantedAuthority> auths = Collections.singletonList(authority);
        userDetails = new CustomUserDetails(EMAIL, PASSWORD, auths);
    }

    @Test
    void gettersShouldReturnConstructorValues() {
        // username/password
        assertEquals(EMAIL, userDetails.getUsername());
        assertEquals(PASSWORD, userDetails.getPassword());

        Collection<? extends GrantedAuthority> returned = userDetails.getAuthorities();
        assertSame(1, returned.size());
        GrantedAuthority returnedAuth = returned.iterator().next();

        String role = returnedAuth.getAuthority();
        assertEquals("ROLE_USER", role);

        verify(authority).getAuthority();
    }

    @Test
    void accountStatusFlagsShouldAllBeTrue() {
        assertTrue(userDetails.isAccountNonExpired(),    "account should be non-expired");
        assertTrue(userDetails.isAccountNonLocked(),     "account should be non-locked");
        assertTrue(userDetails.isCredentialsNonExpired(),"credentials should be non-expired");
        assertTrue(userDetails.isEnabled(),              "account should be enabled");
    }

    @Test
    void noArgsConstructorAndSettersShouldWork() {
        CustomUserDetails dupe = new CustomUserDetails();
        dupe.setEmail("<EMAIL>");
        dupe.setPassword("pw");
        dupe.setAuthorities(Collections.singletonList(authority));

        assertEquals("<EMAIL>", dupe.getUsername());
        assertEquals("pw",       dupe.getPassword());
        assertEquals(1, dupe.getAuthorities().size());
    }
}