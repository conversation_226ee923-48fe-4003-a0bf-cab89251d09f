package com.lookforx.auth.security;

import com.lookforx.auth.service.JwtService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class JwtAuthenticationFilterTest {

    @Mock
    private JwtService jwtService;

    @Mock
    private UserDetailsService userDetailsService;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private FilterChain filterChain;

    @InjectMocks
    private JwtAuthenticationFilter filter;

    @BeforeEach
    void setUp() {
        SecurityContextHolder.clearContext();
    }

    @Test
    void skipsFilterForLoginEndpoint() throws ServletException, IOException {
        when(request.getServletPath()).thenReturn("/v1/auth/login");

        filter.doFilter(request, response, filterChain);

        verify(filterChain).doFilter(request, response);
        assertNull(SecurityContextHolder.getContext().getAuthentication());
    }

    @Test
    void skipsFilterForSignupEndpoint() throws ServletException, IOException {
        when(request.getServletPath()).thenReturn("/v1/auth/signup");

        filter.doFilter(request, response, filterChain);

        verify(filterChain).doFilter(request, response);
        assertNull(SecurityContextHolder.getContext().getAuthentication());
    }

    @Test
    void authenticatesWithBearerHeader() throws ServletException, IOException {
        when(request.getServletPath()).thenReturn("/api/some");
        when(request.getHeader("Authorization")).thenReturn("Bearer goodJwt");
        when(jwtService.extractUsername("goodJwt")).thenReturn("<EMAIL>");

        UserDetails userDetails = mock(UserDetails.class);
        when(userDetailsService.loadUserByUsername("<EMAIL>"))
                .thenReturn(userDetails);
        when(jwtService.isTokenValid("goodJwt", userDetails)).thenReturn(true);

        List<GrantedAuthority> emptyAuths = Collections.emptyList();
        doReturn(emptyAuths)
                .when(userDetails)
                .getAuthorities();

        filter.doFilter(request, response, filterChain);

        verify(jwtService).extractUsername("goodJwt");
        verify(userDetailsService).loadUserByUsername("<EMAIL>");
        verify(jwtService).isTokenValid("goodJwt", userDetails);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        assertNotNull(auth, "Authentication should be set in context");
        assertSame(userDetails, auth.getPrincipal());
        verify(filterChain).doFilter(request, response);
    }

    @Test
    void authenticatesWithCookieIfNoHeader() throws ServletException, IOException {
        when(request.getServletPath()).thenReturn("/api/other");
        when(request.getHeader("Authorization")).thenReturn(null);
        Cookie[] cookies = { new Cookie("accessToken", "cookieJwt") };
        when(request.getCookies()).thenReturn(cookies);

        when(jwtService.extractUsername("cookieJwt")).thenReturn("<EMAIL>");
        UserDetails userDetails = mock(UserDetails.class);
        when(userDetailsService.loadUserByUsername("<EMAIL>"))
                .thenReturn(userDetails);
        when(jwtService.isTokenValid("cookieJwt", userDetails)).thenReturn(true);
        when(userDetails.getAuthorities()).thenReturn(Collections.emptyList());

        filter.doFilter(request, response, filterChain);

        verify(jwtService).extractUsername("cookieJwt");
        verify(userDetailsService).loadUserByUsername("<EMAIL>");
        verify(jwtService).isTokenValid("cookieJwt", userDetails);

        assertNotNull(SecurityContextHolder.getContext().getAuthentication());
        verify(filterChain).doFilter(request, response);
    }

    @Test
    void continuesWhenNoHeaderAndNoCookie() throws ServletException, IOException {
        when(request.getServletPath()).thenReturn("/api/none");
        when(request.getHeader("Authorization")).thenReturn(null);
        when(request.getCookies()).thenReturn(null);

        filter.doFilter(request, response, filterChain);

        verify(filterChain).doFilter(request, response);
        assertNull(SecurityContextHolder.getContext().getAuthentication());
    }

    @Test
    void doesNotAuthenticateIfTokenInvalid() throws ServletException, IOException {
        when(request.getServletPath()).thenReturn("/api/bad");
        when(request.getHeader("Authorization")).thenReturn("Bearer badJwt");
        when(jwtService.extractUsername("badJwt")).thenReturn("<EMAIL>");

        UserDetails userDetails = mock(UserDetails.class);
        when(userDetailsService.loadUserByUsername("<EMAIL>"))
                .thenReturn(userDetails);
        when(jwtService.isTokenValid(eq("badJwt"), any(UserDetails.class)))
                .thenReturn(false);

        filter.doFilter(request, response, filterChain);

        verify(jwtService).extractUsername("badJwt");
        verify(userDetailsService).loadUserByUsername("<EMAIL>");
        verify(jwtService).isTokenValid(eq("badJwt"), any(UserDetails.class));

        assertNull(SecurityContextHolder.getContext().getAuthentication());
        verify(filterChain).doFilter(request, response);

    }
}