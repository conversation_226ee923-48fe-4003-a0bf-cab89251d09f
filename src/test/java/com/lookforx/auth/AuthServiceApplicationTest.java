package com.lookforx.auth;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.SpringApplication;

import static org.mockito.Mockito.mockStatic;

@ExtendWith(MockitoExtension.class)
class AuthServiceApplicationTest {

    @Test
    void main_shouldCallSpringApplicationRun() {
        // Given
        String[] args = new String[]{"--server.port=8081"};

        try (MockedStatic<SpringApplication> mockSpringApplication = mockStatic(SpringApplication.class)) {
            // When
            AuthServiceApplication.main(args);

            // Then
            mockSpringApplication.verify(() -> SpringApplication.run(AuthServiceApplication.class, args));
        }

    }

}