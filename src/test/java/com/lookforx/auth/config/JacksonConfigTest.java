package com.lookforx.auth.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.auth.base.AbstractBaseServiceTest;

import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class JacksonConfigTest extends AbstractBaseServiceTest {

    private ObjectMapper objectMapper;
    private static final String DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    @BeforeEach
    void setUp() {
        JacksonConfig jacksonConfig = new JacksonConfig();
        objectMapper = jacksonConfig.objectMapper();
    }

    @Test
    void objectMapperBean_IsNotNull() {
        assertNotNull(objectMapper, "ObjectMapper bean should not be null");
    }

    @Test
    void objectMapper_WritesLocalDateTimeInConfiguredFormat() throws Exception {
        // Given
        LocalDateTime dateTime = LocalDateTime.of(2025, 6, 27, 14, 30, 15, 123_000_000);
        Map<String, LocalDateTime> payload = Collections.singletonMap("timestamp", dateTime);

        // When
        String json = objectMapper.writeValueAsString(payload);

        // Then
        String expected = dateTime.format(DateTimeFormatter.ofPattern(DATE_TIME_FORMAT));
        assertTrue(json.contains("\"timestamp\":\"" + expected + "\""),
                "Serialized JSON should contain the date in the configured format");
    }

    @Test
    void objectMapper_DisablesWriteDatesAsTimestamps() {
        // The WRITE_DATES_AS_TIMESTAMPS feature should be disabled
        assertFalse(objectMapper.getSerializationConfig()
                .isEnabled(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS));
    }

}