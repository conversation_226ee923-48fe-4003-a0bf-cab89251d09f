package com.lookforx.auth.config;

import com.lookforx.auth.security.JwtAuthenticationFilter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.List;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SecurityConfigTest {

    @Mock
    private JwtAuthenticationFilter jwtAuthenticationFilter;


    @InjectMocks
    private SecurityConfig securityConfig;

    @Mock
    private HttpSecurity httpSecurity;

    @BeforeEach
    void setup() {
        securityConfig = new SecurityConfig(jwtAuthenticationFilter);
    }

    @Test
    void corsConfigurationSource_shouldReturnProperCorsSettings() {

        // Given
        UrlBasedCorsConfigurationSource source = (UrlBasedCorsConfigurationSource) securityConfig.corsConfigurationSource();
        CorsConfiguration config = source.getCorsConfigurations().get("/**");

        // Then
        assertNotNull(config);
        assertEquals(List.of("http://localhost:3000", "http://************:3000"), config.getAllowedOrigins());
        assertTrue(Objects.requireNonNull(config.getAllowedMethods()).containsAll(List.of("GET", "POST", "PUT", "DELETE", "OPTIONS")));
        assertEquals(Boolean.TRUE, config.getAllowCredentials());
        assertTrue(Objects.requireNonNull(config.getExposedHeaders()).contains("Access-Token"));
        assertTrue(config.getExposedHeaders().contains("Refresh-Token"));

    }


    @Test
    void securityFilterChain_shouldBuildSuccessfully() throws Exception {

        // Given
        JwtAuthenticationFilter jwtAuthenticationFilter = mock(JwtAuthenticationFilter.class);
        SecurityConfig config = new SecurityConfig(jwtAuthenticationFilter);
        HttpSecurity http = mock(HttpSecurity.class, withSettings().defaultAnswer(RETURNS_DEEP_STUBS));
        DefaultSecurityFilterChain chain = mock(DefaultSecurityFilterChain.class);

        // When
        when(http.csrf(any())).thenReturn(http);
        when(http.cors(any())).thenReturn(http);
        when(http.sessionManagement(any())).thenReturn(http);
        when(http.addFilterBefore(any(), eq(UsernamePasswordAuthenticationFilter.class))).thenReturn(http);
        when(http.authorizeHttpRequests(any())).thenReturn(http);
        when(http.build()).thenReturn(chain);

        // Then
        SecurityFilterChain result = config.securityFilterChain(http);

        assertNotNull(result);

        // Verify
        verify(http).csrf(any());
        verify(http).cors(any());
        verify(http).sessionManagement(any());
        verify(http).addFilterBefore(any(), eq(UsernamePasswordAuthenticationFilter.class));
        verify(http).authorizeHttpRequests(any());
        verify(http).build();

    }

}
