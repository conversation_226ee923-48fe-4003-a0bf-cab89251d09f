package com.lookforx.auth.config;

import com.lookforx.auth.entity.User;
import com.lookforx.auth.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(SpringExtension.class)
class ApplicationConfigTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private AuthenticationConfiguration authenticationConfiguration;

    @Mock
    private AuthenticationManager authenticationManager;
    
    @Mock
    private User user;

    @InjectMocks
    private ApplicationConfig applicationConfig;

    private final String testEmail = "<EMAIL>";

    @BeforeEach
    void setup() throws Exception {
        when(authenticationConfiguration.getAuthenticationManager()).thenReturn(authenticationManager);
    }

    @Test
    void userDetailsService_shouldReturnUserDetails_whenUserExists() {

        // Given & When
        when(userRepository.findByEmail(testEmail)).thenReturn(Optional.of(user));

        // Then
        UserDetailsService userDetailsService = applicationConfig.userDetailsService();
        UserDetails userDetails = userDetailsService.loadUserByUsername(testEmail);

        assertEquals(user, userDetails);

        // Verify
        verify(userRepository).findByEmail(testEmail);

    }

    @Test
    void userDetailsService_shouldThrowException_whenUserNotFound() {

        // Given & When
        when(userRepository.findByEmail(testEmail)).thenReturn(Optional.empty());

        // Then
        UserDetailsService userDetailsService = applicationConfig.userDetailsService();

        assertThrows(UsernameNotFoundException.class, () -> userDetailsService.loadUserByUsername(testEmail));

        // Verify
        verify(userRepository).findByEmail(testEmail);

    }

    @Test
    void authenticationProvider_shouldAuthenticateSuccessfully() {

        // Given
        String username = "<EMAIL>";
        String password = "password";
        String encodedPassword = applicationConfig.passwordEncoder().encode(password);

        User user = mock(User.class);
        when(user.getUsername()).thenReturn(username);
        when(user.getPassword()).thenReturn(encodedPassword);
        when(user.isAccountNonExpired()).thenReturn(true);
        when(user.isAccountNonLocked()).thenReturn(true);
        when(user.isCredentialsNonExpired()).thenReturn(true);
        when(user.isEnabled()).thenReturn(true);
        when(user.getAuthorities()).thenReturn(List.of());

        // When
        when(userRepository.findByEmail(username)).thenReturn(Optional.of(user));

        AuthenticationProvider authenticationProvider = applicationConfig.authenticationProvider();
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(username, password);

        // Then
        Authentication authenticationResult = authenticationProvider.authenticate(authentication);

        // Then
        assertNotNull(authenticationResult);
        assertTrue(authenticationResult.isAuthenticated());
        assertEquals(username, authenticationResult.getName());

        // Verify
        verify(userRepository).findByEmail(username);

    }


    @Test
    void authenticationManager_shouldReturnProvidedManager() throws Exception {
        AuthenticationManager authenticationManagerResult = applicationConfig.authenticationManager(authenticationConfiguration);
        assertEquals(authenticationManager, authenticationManagerResult);
        verify(authenticationConfiguration).getAuthenticationManager();
    }

    @Test
    void passwordEncoder_shouldReturnBCryptPasswordEncoder() {
        PasswordEncoder passwordEncoder = applicationConfig.passwordEncoder();
        assertNotNull(passwordEncoder);
        assertTrue(passwordEncoder.matches("password", passwordEncoder.encode("password")));
    }
}
