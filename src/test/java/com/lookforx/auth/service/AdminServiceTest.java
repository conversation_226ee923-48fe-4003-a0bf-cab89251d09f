package com.lookforx.auth.service;

import com.lookforx.auth.base.AbstractBaseServiceTest;
import com.lookforx.auth.dto.UserListResponse;
import com.lookforx.auth.dto.UserResponse;
import com.lookforx.auth.entity.User;
import com.lookforx.auth.exception.UserNotFoundException;
import com.lookforx.auth.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class AdminServiceTest extends AbstractBaseServiceTest {

    @InjectMocks
    private AdminService adminService;

    @Mock
    private UserRepository userRepository;

    @Mock
    private MongoTemplate mongoTemplate;

    private User user1;
    private User user2;

    @BeforeEach
    void setUp() {
        user1 = User.builder()
                .id("u1")
                .name("Alice")
                .email("<EMAIL>")
                .rolesString("USER")
                .active(true)
                .build();
        user2 = User.builder()
                .id("u2")
                .name("Bob")
                .email("<EMAIL>")
                .rolesString("ADMIN")
                .active(false)
                .build();
    }

    @Test
    void getUsers_noFilters_returnsPagedList() {

        // Given
        Pageable pageable = PageRequest.of(0, 10);
        long totalCount = 2L;

        // When
        doReturn(totalCount)
                .when(mongoTemplate).count(any(Query.class), any(Class.class));
        doReturn(List.of(user1, user2))
                .when(mongoTemplate).find(any(Query.class), any(Class.class));

        // Then
        UserListResponse resp = adminService.getUsers(pageable, null, null, null, null);

        assertEquals(2, resp.getTotalCount());
        assertEquals(0, resp.getPage());
        assertEquals(10, resp.getSize());
        assertEquals(1, resp.getTotalPages());
        assertEquals(2, resp.getUsers().size());
        assertEquals("u1", resp.getUsers().get(0).getId());

        // Verify
        verify(mongoTemplate).count(any(Query.class), any(Class.class));
        verify(mongoTemplate).find(any(Query.class), any(Class.class));

    }

    @Test
    void getUserById_whenExists_returnsUser() {

        // When
        when(userRepository.findById("u1")).thenReturn(Optional.of(user1));

        // Then
        UserResponse resp = adminService.getUserById("u1");

        assertEquals("u1", resp.getId());
        assertEquals("<EMAIL>", resp.getEmail());
        assertTrue(resp.getRoles().contains("USER"));

        // Verify
        verify(userRepository).findById("u1");

    }

    @Test
    void getUserById_notFound_throws() {

        //When
        when(userRepository.findById("nx")).thenReturn(Optional.empty());

        // Then
        assertThrows(UserNotFoundException.class,
                () -> adminService.getUserById("nx"));

    }

    @Test
    void updateUserRoles_invalidRole_throws() {

        // When
        when(userRepository.findById("u1")).thenReturn(Optional.of(user1));

        // Then
        assertThrows(IllegalArgumentException.class,
                () -> adminService.updateUserRoles("u1", List.of("BAD")));

        // Verify
        verify(userRepository).findById("u1");

    }

    @Test
    void updateUserRoles_notFound_throws() {
        // When
        when(userRepository.findById("nx")).thenReturn(Optional.empty());

        // Then
        assertThrows(UserNotFoundException.class,
                () -> adminService.updateUserRoles("nx", List.of("USER")));

    }

    @Test
    void updateUserRoles_success_updatesAndReturns() {

        // When
        when(userRepository.findById("u1")).thenReturn(Optional.of(user1));
        when(userRepository.save(any(User.class))).thenAnswer(inv -> inv.getArgument(0));

        // Then
        UserResponse resp = adminService.updateUserRoles("u1", List.of("ADMIN", "USER"));

        assertEquals(Set.of("ADMIN","USER"), resp.getRoles());

        // Verify
        verify(userRepository).findById("u1");
        verify(userRepository).save(any(User.class));

    }

    @Test
    void updateUserStatus_notFound_throws() {

        // When
        when(userRepository.findById("nx")).thenReturn(Optional.empty());

        // Then
        assertThrows(UserNotFoundException.class,
                () -> adminService.updateUserStatus("nx", false));

    }

    @Test
    void updateUserStatus_success_saves() {

        // Given
        user1.setActive(false);

        // When
        when(userRepository.findById("u1")).thenReturn(Optional.of(user1));
        when(userRepository.save(any(User.class))).thenAnswer(inv -> inv.getArgument(0));

        // Then
        UserResponse resp = adminService.updateUserStatus("u1", true);

        assertTrue(resp.isActive());

        // Verify
        verify(userRepository).save(user1);

    }

    @Test
    void deleteUser_notFound_throws() {

        // When
        when(userRepository.findById("nx")).thenReturn(Optional.empty());

        // Then
        assertThrows(UserNotFoundException.class,
                () -> adminService.deleteUser("nx"));

    }

    @Test
    void deleteUser_success_setsInactive() {

        // Given
        user1.setActive(true);

        // When
        when(userRepository.findById("u1")).thenReturn(Optional.of(user1));

        // Then
        adminService.deleteUser("u1");

        assertFalse(user1.isActive());

        // Verify
        verify(userRepository).save(user1);

    }

    @Test
    void getAvailableRoles_returnsList() {

        // Then
        List<String> roles = adminService.getAvailableRoles();

        assertTrue(roles.containsAll(List.of("USER","ADMIN","MODERATOR")));

    }

    @Test
    void getUserStats_returnsCorrectMap() {

        // When
        when(userRepository.count()).thenReturn(100L);
        when(userRepository.countByActive(true)).thenReturn(80L);
        when(userRepository.countByActive(false)).thenReturn(20L);
        when(userRepository.countByRolesStringContaining("ADMIN")).thenReturn(10L);

        // Then
        Map<String, Object> stats = adminService.getUserStats();

        assertEquals(100L, stats.get("totalUsers"));
        assertEquals(80L, stats.get("activeUsers"));
        assertEquals(20L, stats.get("inactiveUsers"));
        assertEquals(10L, stats.get("adminUsers"));
        assertEquals(90L, stats.get("regularUsers"));

        // Verify
        verify(userRepository).count();
        verify(userRepository).countByActive(true);
        verify(userRepository).countByActive(false);
        verify(userRepository).countByRolesStringContaining("ADMIN");

    }

}