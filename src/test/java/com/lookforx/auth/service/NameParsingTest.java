package com.lookforx.auth.service;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * Test class for name parsing logic
 */
public class NameParsingTest {

    /**
     * Test name parsing logic similar to UserProfileService
     */
    @Test
    public void testNameParsing() {
        // Test case 1: Ali <PERSON>
        String fullName1 = "Ali Turgut Bozkurt";
        String[] result1 = parseFullName(fullName1);
        assertEquals("Ali Turgut", result1[0]); // firstName
        assertEquals("Bozkurt", result1[1]); // lastName
        
        // Test case 2: John <PERSON>e
        String fullName2 = "John Doe";
        String[] result2 = parseFullName(fullName2);
        assertEquals("John", result2[0]); // firstName
        assertEquals("Doe", result2[1]); // lastName
        
        // Test case 3: Single name
        String fullName3 = "<PERSON>";
        String[] result3 = parseFullName(fullName3);
        assertEquals("Ali", result3[0]); // firstName
        assertNull(result3[1]); // lastName should be null
        
        // Test case 4: Multiple names
        String fullName4 = "Ali Turgut Bozkurt Mehmet";
        String[] result4 = parseFullName(fullName4);
        assertEquals("Ali Turgut Bozkurt", result4[0]); // firstName
        assertEquals("Mehmet", result4[1]); // lastName
        
        // Test case 5: Empty/null
        String[] result5 = parseFullName(null);
        assertNull(result5[0]);
        assertNull(result5[1]);
        
        String[] result6 = parseFullName("");
        assertNull(result6[0]);
        assertNull(result6[1]);
    }
    
    /**
     * Helper method that mimics the name parsing logic in UserProfileService
     */
    private String[] parseFullName(String fullName) {
        String firstName = null;
        String lastName = null;
        
        if (fullName != null && !fullName.trim().isEmpty()) {
            String[] nameParts = fullName.trim().split("\\s+");
            if (nameParts.length == 1) {
                // Sadece bir kelime varsa firstName olarak kullan
                firstName = nameParts[0];
            } else if (nameParts.length >= 2) {
                // Son kelime lastName, geri kalanı firstName
                lastName = nameParts[nameParts.length - 1];
                firstName = String.join(" ", java.util.Arrays.copyOfRange(nameParts, 0, nameParts.length - 1));
            }
        }
        
        return new String[]{firstName, lastName};
    }
}
