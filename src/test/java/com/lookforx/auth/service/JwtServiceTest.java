package com.lookforx.auth.service;

import com.lookforx.auth.base.AbstractBaseServiceTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Base64;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class JwtServiceTest extends AbstractBaseServiceTest {

    private final String secret = Base64.getEncoder().encodeToString("my-test-secret".getBytes());
    private final long accessTokenExpiration = 1000 * 60 * 60; // 1 hour
    private final long refreshTokenExpiration = 1000 * 60 * 60 * 24; // 24 hours

    @Mock
    private UserDetails userDetails;

    @InjectMocks
    private JwtService jwtService;

    @BeforeEach
    void setUp() {
        // Use at least 32-byte key for HS256
        String rawSecret = "01234567890123456789012345678901"; // 32 bytes = 256 bits
        String encodedSecret = Base64.getEncoder().encodeToString(rawSecret.getBytes());

        jwtService = new JwtService();
        ReflectionTestUtils.setField(jwtService, "secretKey", encodedSecret);
        ReflectionTestUtils.setField(jwtService, "accessTokenExpiration", 3600000L); // 1 hour
        ReflectionTestUtils.setField(jwtService, "refreshTokenExpiration", 86400000L); // 24 hours
    }

    @Test
    void shouldExtractUsername() {

        // When
        when(userDetails.getUsername()).thenReturn("<EMAIL>");

        // Then
        String token = jwtService.generateAccessToken(userDetails); // Generate with correct key
        String username = jwtService.extractUsername(token);

        assertEquals("<EMAIL>", username);

    }

    @Test
    void shouldGenerateAccessToken() {

        // When
        when(userDetails.getUsername()).thenReturn("<EMAIL>");

        // Then
        String token = jwtService.generateAccessToken(userDetails);

        assertNotNull(token);
        assertTrue(token.contains("."));

    }

    @Test
    void shouldValidateTokenSuccessfully() {

        // When
        when(userDetails.getUsername()).thenReturn("<EMAIL>");

        // Then
        String token = jwtService.generateAccessToken(userDetails);
        boolean isValid = jwtService.isTokenValid(token, userDetails);

        assertTrue(isValid);

        // Verify
        verify(userDetails, atLeastOnce()).getUsername();

    }

    @Test
    void shouldReturnExpirationTimeInSeconds() {
        long seconds = jwtService.getAccessTokenExpirationTime();
        assertEquals(accessTokenExpiration / 1000, seconds);
    }

    @Test
    void shouldGenerateRefreshToken() {

        // When
        when(userDetails.getUsername()).thenReturn("<EMAIL>");

        // Then
        String refreshToken = jwtService.generateRefreshToken(userDetails);

        assertNotNull(refreshToken, "Refresh token should not be null");
        assertTrue(refreshToken.contains("."), "JWT should have at least two dots");

    }

    @Test
    void shouldValidateRefreshTokenSuccessfully() {

        // When
        when(userDetails.getUsername()).thenReturn("<EMAIL>");

        // Then
        String refreshToken = jwtService.generateRefreshToken(userDetails);
        boolean valid = jwtService.isTokenValid(refreshToken, userDetails);

        assertTrue(valid, "Refresh token should be valid");

        // Verify
        verify(userDetails, atLeastOnce()).getUsername();

    }

}