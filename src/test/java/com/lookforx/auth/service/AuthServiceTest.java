package com.lookforx.auth.service;

import com.lookforx.auth.base.AbstractBaseServiceTest;
import com.lookforx.auth.dto.AuthResponse;
import com.lookforx.auth.dto.LoginRequest;
import com.lookforx.auth.dto.SignupRequest;
import com.lookforx.auth.dto.UserResponse;
import com.lookforx.auth.entity.User;
import com.lookforx.auth.enums.AuthProvider;
import com.lookforx.auth.enums.Role;
import com.lookforx.auth.exception.AuthenticationException;
import com.lookforx.auth.repository.UserRepository;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class AuthServiceTest extends AbstractBaseServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private JwtService jwtService;

    @Mock
    private AuthenticationManager authenticationManager;

    @Mock
    private OAuth2Service oAuth2Service;  // not used in these methods

    @InjectMocks
    private AuthService authService;

    private static final String EMAIL = "<EMAIL>";
    private static final String RAW_PW = "Passw0rd!";
    private static final String ENC_PW = "encoded";
    private static final String USER_ID = "1L";

    @Test
    void signupSuccess() {

        // Given
        SignupRequest req = new SignupRequest(EMAIL, RAW_PW, "Foo");

        // When
        when(userRepository.existsByEmail(EMAIL)).thenReturn(false);
        when(passwordEncoder.encode(RAW_PW)).thenReturn(ENC_PW);
        when(userRepository.save(any(User.class))).thenAnswer(inv -> {
            User u = inv.getArgument(0);
            u.setId(USER_ID);
            return u;
        });
        when(jwtService.generateAccessToken(any(User.class))).thenReturn("at");
        when(jwtService.generateRefreshToken(any(User.class))).thenReturn("rt");

        // Then
        AuthResponse resp = authService.signup(req);

        assertNotNull(resp);
        assertEquals("at", resp.getAccessToken());
        assertEquals("rt", resp.getRefreshToken());
        UserResponse ur = resp.getUser();
        assertEquals(USER_ID.toString(), ur.getId());
        assertEquals(EMAIL, ur.getEmail());
        assertEquals("Foo", ur.getName());

        // Verify
        verify(userRepository).existsByEmail(EMAIL);
        verify(passwordEncoder).encode(RAW_PW);
        verify(userRepository).save(any(User.class));
        verify(jwtService).generateAccessToken(any(User.class));
        verify(jwtService).generateRefreshToken(any(User.class));

    }

    @Test
    void signupEmailExistsThrows() {

        // Given
        when(userRepository.existsByEmail(EMAIL)).thenReturn(true);

        // When & Then
        assertThrows(
                AuthenticationException.class,
                () -> authService.signup(new SignupRequest(EMAIL, RAW_PW, "Foo"))
        );

        // Verify
        verify(userRepository).existsByEmail(EMAIL);
        verify(userRepository, never()).save(any());

    }

    @Test
    void loginSuccess() {

        // Given
        LoginRequest req = new LoginRequest(EMAIL, RAW_PW);
        User dbUser = User.builder()
                .email(EMAIL)
                .password(ENC_PW)
                .name("Foo")
                .provider(AuthProvider.LOCAL)
                .roles(Set.of(Role.USER))
                .emailVerified(false)
                .build();
        dbUser.setId(USER_ID);
        Authentication dummyAuth = mock(Authentication.class);

        // When
        when(userRepository.findByEmail(EMAIL)).thenReturn(Optional.of(dbUser));
        when(passwordEncoder.matches(RAW_PW, ENC_PW)).thenReturn(true);
        when(authenticationManager.authenticate(
                any(UsernamePasswordAuthenticationToken.class))
        ).thenReturn(dummyAuth);
        when(jwtService.generateAccessToken(dbUser)).thenReturn("at2");
        when(jwtService.generateRefreshToken(dbUser)).thenReturn("rt2");

        // Then
        AuthResponse resp = authService.login(req);

        assertNotNull(resp);
        assertEquals("at2", resp.getAccessToken());
        assertEquals("rt2", resp.getRefreshToken());
        UserResponse ur = resp.getUser();
        assertEquals(USER_ID.toString(), ur.getId());
        assertEquals(EMAIL, ur.getEmail());

        // Verify
        verify(userRepository).findByEmail(EMAIL);
        verify(passwordEncoder).matches(RAW_PW, ENC_PW);
        verify(authenticationManager).authenticate(any());
        verify(jwtService).generateAccessToken(dbUser);
        verify(jwtService).generateRefreshToken(dbUser);

    }

    @Test
    void loginInvalidPasswordThrows() {

        // Given
        when(userRepository.findByEmail(EMAIL))
                .thenReturn(Optional.of(new User()));
        when(passwordEncoder.matches(anyString(), anyString()))
                .thenReturn(false);

        // When & Then
        assertThrows(
                AuthenticationException.class,
                () -> authService.login(new LoginRequest(EMAIL, RAW_PW))
        );

        // Verify
        verify(userRepository).findByEmail(EMAIL);
        verify(passwordEncoder).matches(RAW_PW, null);
        verify(authenticationManager, never()).authenticate(any());

    }

    @Test
    void loginUserNotFoundThrows() {

        // Given
        when(userRepository.findByEmail(EMAIL)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(
                AuthenticationException.class,
                () -> authService.login(new LoginRequest(EMAIL, RAW_PW))
        );

        // Verify
        verify(userRepository).findByEmail(EMAIL);
        verify(passwordEncoder, never()).matches(any(), any());

    }

    @Test
    void loginAuthManagerFailsThrows() {
        // Given
        User dbUser = new User();
        dbUser.setPassword(ENC_PW);

        // When
        when(userRepository.findByEmail(EMAIL)).thenReturn(Optional.of(dbUser));
        when(passwordEncoder.matches(RAW_PW, ENC_PW)).thenReturn(true);
        doThrow(new RuntimeException("bad"))
                .when(authenticationManager)
                .authenticate(any());

        // Then
        AuthenticationException ex = assertThrows(
                AuthenticationException.class,
                () -> authService.login(new LoginRequest(EMAIL, RAW_PW))
        );

        assertTrue(ex.getMessage().contains("Authentication failed"));

        // Verify
        verify(authenticationManager).authenticate(any());

    }

    @Test
    void refreshTokenSuccess() {

        // Given
        String oldRt = "old-rt";
        User dbUser = new User();
        dbUser.setId(USER_ID);
        dbUser.setEmail(EMAIL);

        // When
        when(jwtService.extractUsername(oldRt)).thenReturn(EMAIL);
        when(userRepository.findByEmail(EMAIL)).thenReturn(Optional.of(dbUser));
        when(jwtService.isTokenValid(oldRt, dbUser)).thenReturn(true);
        when(jwtService.generateAccessToken(dbUser)).thenReturn("new-at");
        when(jwtService.generateRefreshToken(dbUser)).thenReturn("new-rt");

        // Then
        AuthResponse resp = authService.refreshToken(oldRt);

        assertEquals("new-at", resp.getAccessToken());
        assertEquals("new-rt", resp.getRefreshToken());
        assertEquals(USER_ID.toString(), resp.getUser().getId());

        // Verify
        verify(jwtService).extractUsername(oldRt);
        verify(userRepository).findByEmail(EMAIL);
        verify(jwtService).isTokenValid(oldRt, dbUser);

    }

    @Test
    void refreshTokenInvalidThrows() {

        // Given
        String oldRt = "old-rt";

        // When
        when(jwtService.extractUsername(oldRt)).thenReturn(EMAIL);
        when(userRepository.findByEmail(EMAIL))
                .thenReturn(Optional.of(new User()));
        when(jwtService.isTokenValid(anyString(), any(UserDetails.class)))
                .thenReturn(false);

        // Then
        assertThrows(
                AuthenticationException.class,
                () -> authService.refreshToken(oldRt)
        );

        // Verify
        verify(jwtService).isTokenValid(anyString(), any(UserDetails.class));

    }

    @Test
    void refreshTokenUserNotFoundThrows() {

        // Given
        when(jwtService.extractUsername("rt")).thenReturn(EMAIL);
        when(userRepository.findByEmail(EMAIL)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(
                AuthenticationException.class,
                () -> authService.refreshToken("rt")
        );

        // Verify
        verify(userRepository).findByEmail(EMAIL);

    }

}