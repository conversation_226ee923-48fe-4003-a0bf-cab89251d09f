package com.lookforx.auth.service;

import com.lookforx.auth.base.AbstractBaseServiceTest;
import com.lookforx.auth.entity.User;
import com.lookforx.auth.enums.Role;
import com.lookforx.auth.repository.UserRepository;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.Collections;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class CustomUserDetailsServiceTest extends AbstractBaseServiceTest {

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private CustomUserDetailsService customUserDetailsService;

    @Test
    void shouldLoadUserByUsernameSuccessfully() {

        // Given
        String email = "<EMAIL>";
        User user = User.builder()
                .email(email)
                .password("hashed-password")
                .roles(Collections.singleton(Role.USER))
                .build();

        // When
        when(userRepository.findByEmail(email)).thenReturn(Optional.of(user));

        // Then
        UserDetails userDetails = customUserDetailsService.loadUserByUsername(email);

        assertNotNull(userDetails);
        assertEquals(email, userDetails.getUsername());
        assertEquals("hashed-password", userDetails.getPassword());
        assertTrue(userDetails.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_USER")));

        // Verify
        verify(userRepository).findByEmail(email);

    }

    @Test
    void shouldThrowUsernameNotFoundExceptionIfUserNotFound() {

        // Given
        String email = "<EMAIL>";

        // When
        when(userRepository.findByEmail(email)).thenReturn(Optional.empty());

        // Then
        assertThrows(UsernameNotFoundException.class, () -> {
            customUserDetailsService.loadUserByUsername(email);
        });

        // Verify
        verify(userRepository).findByEmail(email);

    }

}