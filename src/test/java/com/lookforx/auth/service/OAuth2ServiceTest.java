package com.lookforx.auth.service;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.lookforx.auth.base.AbstractBaseServiceTest;
import com.lookforx.auth.dto.AuthResponse;
import com.lookforx.auth.entity.User;
import com.lookforx.auth.enums.AuthProvider;
import com.lookforx.auth.enums.Role;
import com.lookforx.auth.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class OAuth2ServiceTest extends AbstractBaseServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private JwtService jwtService;

    @Mock
    private GoogleTokenVerifier googleTokenVerifier;

    @Mock
    private ClientRegistrationRepository clientRegistrationRepository;

    @Mock
    private UserProfileService userProfileService;

    @InjectMocks
    private OAuth2Service service;


    private GoogleIdToken.Payload payload;

    private User testUser;

    @BeforeEach
    void setUp() {
        // Provide a dummy redirect URI
        ReflectionTestUtils.setField(service, "redirectUri", "http://redirect");

        // Initialize a sample user
        testUser = User.builder()
                .id("1L")
                .email("<EMAIL>")
                .name("Test User")
                .imageUrl("http://image.url")
                .provider(AuthProvider.GOOGLE)
                .roles(Set.of(Role.USER))
                .emailVerified(true)
                .build();

        // create a real Payload and populate its fields
        payload = new GoogleIdToken.Payload();
        payload.setEmail(testUser.getEmail());
        payload.set("name", testUser.getName());
        payload.set("picture", testUser.getImageUrl());

    }

    @Test
    void handleGoogleCallback_newUser_success() throws Exception {
        // Given: a Google registration
        ClientRegistration reg = ClientRegistration.withRegistrationId("google")
                .clientId("cid").clientSecret("sec")
                .authorizationUri("authUri").tokenUri("tokenUri")
                .redirectUri("redirectUri")
                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .build();
        when(clientRegistrationRepository.findByRegistrationId("google")).thenReturn(reg);

        // And the token endpoint returns an ID token
        Map<String, Object> tokenBody = Map.of("id_token", "id.jwt.token");
        ResponseEntity<Map> tokenResponse = ResponseEntity.ok(tokenBody);
        try ( var mocked = mockConstruction(RestTemplate.class, (rest, ctx) -> {
            when(rest.postForEntity(eq("tokenUri"), any(HttpEntity.class), eq(Map.class)))
                    .thenReturn(tokenResponse);
        })) {
            // stub verifier to return our real payload
            when(googleTokenVerifier.verify("id.jwt.token")).thenReturn(payload);

            // no existing user in DB
            when(userRepository.findByEmail(testUser.getEmail())).thenReturn(Optional.empty());
            when(userRepository.save(any(User.class))).thenReturn(testUser);

            // profile creation and JWT generation
            when(userProfileService.createInitialProfileWithImage(
                    anyString(), anyString(), anyString(), anyString()))
                    .thenReturn(null);
            when(jwtService.generateAccessToken(testUser)).thenReturn("access");
            when(jwtService.generateRefreshToken(testUser)).thenReturn("refresh");
            when(jwtService.getAccessTokenExpirationTime()).thenReturn(3600L);

            // When
            AuthResponse resp = service.handleGoogleCallback("code");

            // Then
            assertEquals("access", resp.getAccessToken());
            assertEquals("refresh", resp.getRefreshToken());
            assertEquals(testUser.getEmail(), resp.getUser().getEmail());

            verify(googleTokenVerifier).verify("id.jwt.token");
            verify(userRepository).save(any(User.class));
            verify(userProfileService).createInitialProfileWithImage(
                    testUser.getId(), testUser.getEmail(), testUser.getName(), testUser.getImageUrl());
        }

    }

    @Test
    void handleGoogleCallback_existingUser_updates() throws GeneralSecurityException, IOException {
        // Given: a Google client registration
        ClientRegistration reg = ClientRegistration.withRegistrationId("google")
                .clientId("cid")
                .clientSecret("sec")
                .authorizationUri("authUri")
                .tokenUri("tokenUri")
                .redirectUri("redirectUri")
                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .build();
        when(clientRegistrationRepository.findByRegistrationId("google")).thenReturn(reg);

        // Stub the token endpoint to return an id_token
        Map<String, Object> tokenBody = Map.of("id_token", "id.jwt.token");
        ResponseEntity<Map> tokenResponse = ResponseEntity.ok(tokenBody);

        try (var mocked = mockConstruction(RestTemplate.class, (rest, ctx) -> {
            when(rest.postForEntity(eq("tokenUri"), any(HttpEntity.class), eq(Map.class)))
                    .thenReturn(tokenResponse);
        })) {
            // -- prepare a real Payload object --
            payload.setEmail(testUser.getEmail());
            payload.set("name", "NewName");
            payload.set("picture", "http://new");

            // stub verifier to return that payload
            when(googleTokenVerifier.verify("id.jwt.token")).thenReturn(payload);

            // existing user found in DB
            when(userRepository.findByEmail(testUser.getEmail()))
                    .thenReturn(Optional.of(testUser));
            when(userRepository.save(testUser)).thenReturn(testUser);

            // profile image update should be called
            doNothing().when(userProfileService)
                    .updateProfileImage(testUser.getId(), "http://new");

            // JWT stubs
            when(jwtService.generateAccessToken(testUser)).thenReturn("access");
            when(jwtService.generateRefreshToken(testUser)).thenReturn("refresh");
            when(jwtService.getAccessTokenExpirationTime()).thenReturn(3600L);

            // When
            AuthResponse resp = service.handleGoogleCallback("code");

            // Then
            assertEquals(testUser.getEmail(), resp.getUser().getEmail());

            // Verify
            verify(userProfileService).updateProfileImage(testUser.getId(), "http://new");

        }

    }

    @Test
    void handleGoogleCallback_missingIdToken_throws() {
        // Given
        ClientRegistration reg = ClientRegistration.withRegistrationId("google")
                .clientId("cid").clientSecret("sec")
                .authorizationUri("authUri").tokenUri("tokenUri")
                .redirectUri("redirectUri").authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .build();
        when(clientRegistrationRepository.findByRegistrationId("google")).thenReturn(reg);

        ResponseEntity<Map> tokenResponse = ResponseEntity.ok(Collections.emptyMap());
        try (var mocked = mockConstruction(RestTemplate.class, (rest, ctx) -> {
            when(rest.postForEntity(eq("tokenUri"), any(HttpEntity.class), eq(Map.class)))
                    .thenReturn(tokenResponse);
        })) {
            // When & Then
            RuntimeException ex = assertThrows(RuntimeException.class,
                    () -> service.handleGoogleCallback("code"));
            assertTrue(ex.getMessage().contains("ID token not found"));
        }
    }

    @Test
    void handleGoogleCallback_verifyFail_throws() throws GeneralSecurityException, IOException {
        // Given
        ClientRegistration reg = ClientRegistration.withRegistrationId("google")
                .clientId("cid").clientSecret("sec")
                .authorizationUri("authUri").tokenUri("tokenUri")
                .redirectUri("redirectUri").authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .build();
        when(clientRegistrationRepository.findByRegistrationId("google")).thenReturn(reg);

        Map<String, Object> tokenBody = Map.of("id_token", "id.jwt.token");
        ResponseEntity<Map> tokenResponse = ResponseEntity.ok(tokenBody);
        try (var mocked = mockConstruction(RestTemplate.class, (rest, ctx) -> {
            when(rest.postForEntity(eq("tokenUri"), any(HttpEntity.class), eq(Map.class)))
                    .thenReturn(tokenResponse);
        })) {
            when(googleTokenVerifier.verify("id.jwt.token")).thenThrow(new IOException("fail"));

            RuntimeException ex = assertThrows(RuntimeException.class,
                    () -> service.handleGoogleCallback("code"));
            assertTrue(ex.getMessage().contains("Error handling Google callback"));
        }
    }


    @Test
    void shouldGetGoogleAuthUrl() {
        // Given
        ClientRegistration registration = ClientRegistration.withRegistrationId("google")
                .clientId("cid")
                .clientSecret("secret")
                .scope("openid", "profile")
                .authorizationUri("https://auth")
                .tokenUri("https://token")
                .redirectUri("http://redirect")
                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                .build();
        when(clientRegistrationRepository.findByRegistrationId("google"))
                .thenReturn(registration);

        // When
        String url = service.getGoogleAuthUrl();

        // Then
        assertTrue(url.startsWith("https://auth?"));
        assertTrue(url.contains("client_id=cid"));
        assertTrue(url.contains("redirect_uri=http://redirect"));
        assertTrue(url.contains("response_type=code"));
        assertTrue(url.contains("scope=openid%20profile") || url.contains("scope=openid profile"));
    }

    @Test
    void handleGoogleCallback_registrationNotFound_throws() {
        // Given
        when(clientRegistrationRepository.findByRegistrationId("google")).thenReturn(null);

        // When & Then
        RuntimeException ex = assertThrows(RuntimeException.class,
                () -> service.handleGoogleCallback("code"));
        assertTrue(ex.getMessage().contains("Error handling Google callback: Google client registration not found"));
    }

    @Test
    void shouldHandleGoogleLoginSuccessfully() throws GeneralSecurityException, IOException {

        // Given
        String idToken = "fake-id-token";

        // Populate a real Payload instead of mocking it
        payload.setEmail(testUser.getEmail());
        payload.set("name", testUser.getName());
        payload.set("picture", testUser.getImageUrl());

        // Stub the verifier to return our populated payload
        when(googleTokenVerifier.verify(idToken)).thenReturn(payload);

        // Existing user in DB
        when(userRepository.findByEmail(testUser.getEmail()))
                .thenReturn(Optional.of(testUser));
        // Service will call save(updatedUser)
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // JWT generation
        when(jwtService.generateAccessToken(eq(testUser))).thenReturn("access-token");
        when(jwtService.generateRefreshToken(eq(testUser))).thenReturn("refresh-token");
        when(jwtService.getAccessTokenExpirationTime()).thenReturn(3600L);

        // When
        AuthResponse response = service.handleGoogleLogin(idToken);

        // Then
        assertNotNull(response);
        assertEquals("access-token",        response.getAccessToken());
        assertEquals("refresh-token",       response.getRefreshToken());
        assertEquals(testUser.getEmail(),   response.getUser().getEmail());

        verify(userRepository).findByEmail(testUser.getEmail());
        verify(userRepository).save(any(User.class));
        verify(jwtService).generateAccessToken(testUser);
        verify(jwtService).generateRefreshToken(testUser);

    }

    @Test
    void handleGoogleLogin_existingUser_updatesAndReturnsTokens() throws GeneralSecurityException, IOException {
        // Given
        String idToken = "fake-id-token";

        // 1) Populate a real Payload instead of mocking it
        payload.setEmail(testUser.getEmail());
        payload.set("name", "Updated Name");
        payload.set("picture", "http://new-pic");

        // 2) Stub the verifier to return our real payload
        when(googleTokenVerifier.verify(idToken)).thenReturn(payload);

        // 3) Existing user in DB
        when(userRepository.findByEmail(testUser.getEmail()))
                .thenReturn(Optional.of(testUser));
        // service will call save(updatedUser), so stub save(...)
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // 4) JWT service
        when(jwtService.generateAccessToken(testUser)).thenReturn("access-token");
        when(jwtService.generateRefreshToken(testUser)).thenReturn("refresh-token");
        when(jwtService.getAccessTokenExpirationTime()).thenReturn(3600L);

        // When
        AuthResponse response = service.handleGoogleLogin(idToken);

        // Then
        assertEquals(testUser.getEmail(),   response.getUser().getEmail());
        assertEquals("access-token",        response.getAccessToken());
        assertEquals("refresh-token",       response.getRefreshToken());
        assertEquals("Updated Name",        testUser.getName());
        assertEquals("http://new-pic",      testUser.getImageUrl());

        // Verify
        verify(userRepository).findByEmail(testUser.getEmail());
        verify(userRepository).save(testUser);

    }

    @Test
    void handleGoogleLogin_verifyFails_throws() throws GeneralSecurityException, IOException {
        // Given
        String idToken = "bad-token";
        when(googleTokenVerifier.verify(idToken))
                .thenThrow(new GeneralSecurityException("fail"));

        // When & Then
        IllegalArgumentException ex = assertThrows(IllegalArgumentException.class,
                () -> service.handleGoogleLogin(idToken));
        assertTrue(ex.getMessage().contains("Google token verification failed"));

    }
}
