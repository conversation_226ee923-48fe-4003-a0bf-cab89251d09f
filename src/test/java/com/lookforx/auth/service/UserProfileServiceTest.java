package com.lookforx.auth.service;

import com.lookforx.auth.base.AbstractBaseServiceTest;
import com.lookforx.auth.dto.CreateUserProfileRequest;
import com.lookforx.auth.dto.UpdateUserProfileRequest;
import com.lookforx.auth.dto.UserProfileResponse;
import com.lookforx.auth.entity.User;
import com.lookforx.auth.entity.UserProfile;
import com.lookforx.auth.enums.ProfileVisibility;
import com.lookforx.auth.enums.Role;
import com.lookforx.auth.enums.UserType;
import com.lookforx.auth.exception.UserNotFoundException;
import com.lookforx.auth.repository.UserProfileRepository;
import com.lookforx.auth.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class UserProfileServiceTest extends AbstractBaseServiceTest {

    @InjectMocks
    private UserProfileService service;

    @Mock
    private UserProfileRepository userProfileRepository;

    @Mock
    private UserRepository userRepository;


    private UserProfile sampleProfile;
    private User sampleUser;

    @BeforeEach
    void setUp() {
        sampleUser = User.builder()
                .id("u1")
                .email("<EMAIL>")
                .name("User Test")
                .roles(Set.of(Role.USER))
                .build();

        sampleProfile = UserProfile.builder()
                .id("p1")
                .userId(sampleUser.getId())
                .username("user")
                .email(sampleUser.getEmail())
                .firstName("User")
                .lastName("Test")
                .userType(UserType.REGULAR)
                .preferredLanguage("tr")
                .profileVisibility(ProfileVisibility.PUBLIC)
                .build();
        sampleProfile.calculateCompletionPercentage();

    }

    @Test
    void createInitialProfile_whenExists_returnsExisting() {

        // When
        when(userProfileRepository.findByUserId("u1"))
                .thenReturn(Optional.of(sampleProfile));

        // Then
        UserProfileResponse resp = service.createInitialProfile(
                "u1", sampleUser.getEmail(), sampleUser.getName());

        assertEquals(sampleProfile.getUsername(), resp.getUsername());

        // Verify
        verify(userProfileRepository, never()).save(any());

    }

    @Test
    void createInitialProfile_whenNotExists_savesNew() {

        // When
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.empty());
        when(userProfileRepository.save(any(UserProfile.class))).thenReturn(sampleProfile);

        // Then
        UserProfileResponse resp = service.createInitialProfile(
                "u1", sampleUser.getEmail(), sampleUser.getName());

        assertEquals("user", resp.getUsername());

        // Verify
        verify(userProfileRepository).save(any(UserProfile.class));

    }

    @Test
    void createInitialProfileWithImage_whenNotExists_savesNewWithImage() {

        // Given
        UserProfile withImage = sampleProfile.toBuilder()
                .profilePhotoUrl("http://img").build();

        // When
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.empty());
        when(userProfileRepository.save(any(UserProfile.class))).thenReturn(withImage);

        // Then
        UserProfileResponse resp = service.createInitialProfileWithImage(
                "u1", sampleUser.getEmail(), sampleUser.getName(), "http://img");

        assertEquals("http://img", resp.getProfilePhotoUrl());

        // Verify
        verify(userProfileRepository).save(any(UserProfile.class));

    }

    @Test
    void createProfile_userNotFound_throws() {

        // When
        when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.empty());

        // Then
        assertThrows(UserNotFoundException.class,
                () -> service.createProfile("<EMAIL>", new CreateUserProfileRequest()));

        // Verify
        verify(userRepository).findByEmail("<EMAIL>");

    }

    @Test
    void createProfile_profileExists_throws() {

        // When
        when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.of(sampleUser));
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.of(sampleProfile));

        // Then
        assertThrows(IllegalStateException.class,
                () -> service.createProfile("<EMAIL>", new CreateUserProfileRequest()));

        // Verify
        verify(userRepository).findByEmail("<EMAIL>");
        verify(userProfileRepository).findByUserId("u1");

    }

    @Test
    void createProfile_usernameExists_throws() {

        // When
        when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.of(sampleUser));
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.empty());
        when(userProfileRepository.existsByUsername("user"))
                .thenReturn(true);

        // Then
        CreateUserProfileRequest req = CreateUserProfileRequest.builder()
                .username("user").build();

        assertThrows(IllegalArgumentException.class,
                () -> service.createProfile("<EMAIL>", req));

        // Verify
        verify(userRepository).findByEmail("<EMAIL>");
        verify(userProfileRepository).findByUserId("u1");
        verify(userProfileRepository).existsByUsername("user");

    }

    @Test
    void createProfile_success_saves() {

        // Given
        CreateUserProfileRequest req = CreateUserProfileRequest.builder()
                .username("newuser").build();

        // When
        when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.of(sampleUser));
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.empty());
        when(userProfileRepository.existsByUsername("newuser")).thenReturn(false);
        when(userProfileRepository.save(any(UserProfile.class))).thenReturn(sampleProfile);

        // Then
        UserProfileResponse resp = service.createProfile("<EMAIL>", req);

        assertEquals(sampleProfile.getUsername(), resp.getUsername());

        // Verify
        verify(userRepository).findByEmail("<EMAIL>");
        verify(userProfileRepository).findByUserId("u1");
        verify(userProfileRepository).existsByUsername("newuser");
        verify(userProfileRepository).save(any(UserProfile.class));

    }

    @Test
    void getProfileByUserId_notFound_throws() {

        // When
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.empty());
        when(userProfileRepository.count()).thenReturn(0L);
        when(userProfileRepository.findAll()).thenReturn(List.of());

        // Then
        assertThrows(UserNotFoundException.class,
                () -> service.getProfileByUserId("u1"));

        // Verify
        verify(userProfileRepository).findByUserId("u1");
        verify(userProfileRepository).count();
        verify(userProfileRepository).findAll();

    }

    @Test
    void getProfileByUserId_success() {

        // When
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.of(sampleProfile));

        // Then
        UserProfileResponse resp = service.getProfileByUserId("u1");

        assertEquals("user", resp.getUsername());

        // Verify
        verify(userProfileRepository).findByUserId("u1");

    }

    @Test
    void getProfileByUsername_notFound_throws() {

        // When
        when(userProfileRepository.findByUsername("user")).thenReturn(Optional.empty());

        // Then
        assertThrows(UserNotFoundException.class,
                () -> service.getProfileByUsername("user"));

        // Verify
        verify(userProfileRepository).findByUsername("user");

    }

    @Test
    void getProfileByUsername_success() {

        // When
        when(userProfileRepository.findByUsername("user")).thenReturn(Optional.of(sampleProfile));

        // Then
        UserProfileResponse resp = service.getProfileByUsername("user");
        assertEquals("user", resp.getUsername());

        // Verify
        verify(userProfileRepository).findByUsername("user");

    }

    @Test
    void updateProfile_notFound_throws() {

        // When
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.empty());

        // Then
        assertThrows(UserNotFoundException.class,
                () -> service.updateProfile("u1", new UpdateUserProfileRequest()));

        // Verify
        verify(userProfileRepository).findByUserId("u1");

    }

    @Test
    void updateProfile_usernameConflict_throws() {

        // Given
        UpdateUserProfileRequest req = UpdateUserProfileRequest.builder()
                .username("other").build();

        // When
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.of(sampleProfile));
        when(userProfileRepository.existsByUsernameAndUserIdNot("other", "u1")).thenReturn(true);

        // Then
        assertThrows(IllegalArgumentException.class,
                () -> service.updateProfile("u1", req));

        // Verify
        verify(userProfileRepository).findByUserId("u1");
        verify(userProfileRepository).existsByUsernameAndUserIdNot("other", "u1");

    }

    @Test
    void updateProfile_success_saves() {

        // Given
        UpdateUserProfileRequest req = UpdateUserProfileRequest.builder()
                .firstName("New").build();

        // When
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.of(sampleProfile));
        when(userProfileRepository.save(any(UserProfile.class))).thenReturn(sampleProfile);

        // Then
        UserProfileResponse resp = service.updateProfile("u1", req);

        assertEquals("New", resp.getFirstName());

        // Verify
        verify(userProfileRepository).findByUserId("u1");
        verify(userProfileRepository).save(any(UserProfile.class));
    }

    @Test
    void updateProfileImage_exists_saves() {

        // When
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.of(sampleProfile));

        // Then
        service.updateProfileImage("u1", "http://img");

        assertEquals("http://img", sampleProfile.getProfilePhotoUrl());

        // Verify
        verify(userProfileRepository).save(sampleProfile);
    }

    @Test
    void updateProfileImage_notFound_doesNothing() {

        // When
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.empty());

        // Then
        service.updateProfileImage("u1", "http://img");

        // Verify
        verify(userProfileRepository, never()).save(any());

    }

    @Test
    void deleteProfile_notFound_throws() {

        // When
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.empty());

        // Then
        assertThrows(UserNotFoundException.class,
                () -> service.deleteProfile("u1"));

        // Verify
        verify(userProfileRepository).findByUserId("u1");

    }

    @Test
    void deleteProfile_success() {

        // When
        when(userProfileRepository.findByUserId("u1")).thenReturn(Optional.of(sampleProfile));

        // Then
        service.deleteProfile("u1");

        // Verify
        verify(userProfileRepository).delete(sampleProfile);

    }

    @Test
    void isUsernameAvailable_returnsTrueWhenNotExists() {

        // When
        when(userProfileRepository.existsByUsername("user")).thenReturn(false);

        // Then
        boolean available = service.isUsernameAvailable("user");

        // Then
        assertTrue(available);

        // Verify
        verify(userProfileRepository).existsByUsername("user");

    }

    @Test
    void searchProfiles_returnsMapped() {

        // When
        when(userProfileRepository.searchByUsernameOrCompanyName("term"))
                .thenReturn(List.of(sampleProfile));

        // Then
        List<UserProfileResponse> list = service.searchProfiles("term");

        assertEquals(1, list.size());
        assertEquals(sampleProfile.getUsername(), list.get(0).getUsername());

        // Verify
        verify(userProfileRepository).searchByUsernameOrCompanyName("term");

    }

    @Test
    void getPublicProfiles_returnsMapped() {

        // When
        when(userProfileRepository.findPublicCompletedProfiles())
                .thenReturn(List.of(sampleProfile));

        // Then
        List<UserProfileResponse> list = service.getPublicProfiles();

        assertEquals(1, list.size());
        assertEquals(sampleProfile.getUsername(), list.get(0).getUsername());

        // Verify
        verify(userProfileRepository).findPublicCompletedProfiles();

    }


}