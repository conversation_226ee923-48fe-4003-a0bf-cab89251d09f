package com.lookforx.auth.service;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.client.json.webtoken.JsonWebSignature;
import com.lookforx.auth.base.AbstractBaseServiceTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class GoogleTokenVerifierTest extends AbstractBaseServiceTest {

    private GoogleTokenVerifier googleTokenVerifier;
    private GoogleIdToken googleIdToken;
    private GoogleIdToken.Payload payload;

    @BeforeEach
    void setUp() {
        // 1) Prepare a real Payload
        payload = new GoogleIdToken.Payload();
        payload.setSubject("user-123");
        // (you can set other fields if needed)

        // 2) Construct a real GoogleIdToken using its public constructor
        JsonWebSignature.Header header = new JsonWebSignature.Header();
        byte[] sig = new byte[0];
        byte[] content = new byte[0];
        googleIdToken = new GoogleIdToken(header, payload, sig, content);

        // 3) Create a stub verifier that simply returns our token or null
        GoogleIdTokenVerifier stubVerifier = new GoogleIdTokenVerifier(new NetHttpTransport(), new GsonFactory()) {
            @Override
            public GoogleIdToken verify(String idTokenString) {
                if ("valid-id-token".equals(idTokenString)) {
                    return googleIdToken;
                }
                return null;
            }
        };

        // 4) Inject stubVerifier into your component
        googleTokenVerifier = new GoogleTokenVerifier("dummy-client-id");
        ReflectionTestUtils.setField(googleTokenVerifier, "verifier", stubVerifier);

    }

    @Test
    void shouldReturnPayloadWhenTokenIsValid() throws Exception {
        // Act
        GoogleIdToken.Payload result = googleTokenVerifier.verify("valid-id-token");

        // Assert
        assertSame(payload, result, "Expected the stub payload to be returned");
    }

    @Test
    void shouldThrowExceptionWhenTokenIsInvalid() {
        // Act & Assert
        IllegalArgumentException ex = assertThrows(
                IllegalArgumentException.class,
                () -> googleTokenVerifier.verify("invalid-id-token")
        );
        assertEquals("Invalid ID token", ex.getMessage());
    }

}