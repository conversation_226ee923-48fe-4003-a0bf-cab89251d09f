package com.lookforx.auth.service;

import com.lookforx.auth.base.AbstractBaseServiceTest;
import com.lookforx.auth.entity.User;
import com.lookforx.auth.enums.AuthProvider;
import com.lookforx.auth.enums.Role;
import com.lookforx.auth.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.userinfo.DefaultOAuth2UserService;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.core.OAuth2AccessToken.TokenType;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.client.RestOperations;

import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class CustomOAuth2UserServiceTest extends AbstractBaseServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private OAuth2UserRequest userRequest;

    @Mock
    private RestOperations restOperations;

    @InjectMocks
    private CustomOAuth2UserService customOAuth2UserService;

    private Map<String, Object> attributes;
    private ClientRegistration clientRegistration;
    private OAuth2AccessToken accessToken;

    @BeforeEach
    void setUp() throws OAuth2AuthenticationException {
        // 1) Prepare the fake user-info attributes
        attributes = new HashMap<>();
        attributes.put("email",   "<EMAIL>");
        attributes.put("name",    "Test User");
        attributes.put("picture", "http://image.url/profile.jpg");

        // 2) Override the RestOperations on the parent class so super.loadUser() uses our stub
        ((DefaultOAuth2UserService) customOAuth2UserService)
                .setRestOperations(restOperations);

        // 3) Give the userRequest a non-null ClientRegistration
        clientRegistration = ClientRegistration.withRegistrationId("google")
                .clientId("dummy-client")
                .clientSecret("dummy-secret")
                .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)   // ← add this
                .authorizationUri("https://auth.server/oauth/authorize")
                .tokenUri("https://auth.server/oauth/token")
                .userInfoUri("https://auth.server/userinfo")
                .redirectUri("{baseUrl}/login/oauth2/code/{registrationId}")
                .userNameAttributeName("email")
                .build();
        when(userRequest.getClientRegistration()).thenReturn(clientRegistration);

        // 4) And a valid access token
        accessToken = new OAuth2AccessToken(
                TokenType.BEARER,
                "fake-token",
                Instant.now(),
                Instant.now().plusSeconds(3600)
        );
        when(userRequest.getAccessToken()).thenReturn(accessToken);

        // 5) Stub the REST call to return our attributes
        when(restOperations.exchange(
                any(RequestEntity.class),
                any(ParameterizedTypeReference.class))
        ).thenReturn(ResponseEntity.ok(attributes));
    }

    @Test
    void shouldCreateNewUserIfNotExist() {
        // Given no existing user
        when(userRepository.findByEmail("<EMAIL>"))
                .thenReturn(Optional.empty());

        // Echo back the saved user
        when(userRepository.save(any(User.class)))
                .thenAnswer(inv -> inv.getArgument(0));

        // When
        OAuth2User result = customOAuth2UserService.loadUser(userRequest);

        // Then
        assertNotNull(result);
        assertEquals("<EMAIL>", result.getAttribute("email"));
        assertEquals("Test User",        result.getAttribute("name"));
        assertEquals("http://image.url/profile.jpg",
                result.getAttribute("picture"));

        verify(userRepository).findByEmail("<EMAIL>");
        verify(userRepository).save(any(User.class));
    }

    @Test
    void shouldUpdateExistingUser() {
        // Given an existing user in the repo
        User existingUser = User.builder()
                .email("<EMAIL>")
                .name("Old Name")
                .imageUrl("old.url")
                .provider(AuthProvider.GOOGLE)
                .roles(Collections.singleton(Role.USER))
                .emailVerified(true)
                .build();

        when(userRepository.findByEmail("<EMAIL>"))
                .thenReturn(Optional.of(existingUser));
        when(userRepository.save(any(User.class)))
                .thenAnswer(inv -> inv.getArgument(0));

        // When
        OAuth2User result = customOAuth2UserService.loadUser(userRequest);

        // Then
        assertNotNull(result);
        assertEquals("<EMAIL>", result.getAttribute("email"));
        assertEquals("Test User",        existingUser.getName());
        assertEquals("http://image.url/profile.jpg",
                existingUser.getImageUrl());

        verify(userRepository).findByEmail("<EMAIL>");
        verify(userRepository).save(existingUser);
    }

}