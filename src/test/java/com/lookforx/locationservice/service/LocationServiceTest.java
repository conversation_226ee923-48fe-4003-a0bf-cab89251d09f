package com.lookforx.locationservice.service;

import com.lookforx.locationservice.dto.CountryDto;
import com.lookforx.locationservice.entity.Country;
import com.lookforx.locationservice.repository.CountryRepository;
import com.lookforx.locationservice.service.impl.LocationServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Location Service Test
 * 
 * Unit tests for LocationService with mocked dependencies.
 */
@ExtendWith(MockitoExtension.class)
class LocationServiceTest {

    @Mock
    private CountryRepository countryRepository;

    @InjectMocks
    private LocationServiceImpl locationService;

    private Country testCountry;
    private CountryDto testCountryDto;

    @BeforeEach
    void setUp() {
        testCountry = Country.builder()
                .id(1L)
                .name("Turkey")
                .iso2("TR")
                .iso3("TUR")
                .phonecode("+90")
                .capital("Ankara")
                .currency("TRY")
                .currencySymbol("₺")
                .region("Asia")
                .subregion("Western Asia")
                .nationality("Turkish")
                .emoji("🇹🇷")
                .flag(1)
                .build();

        testCountryDto = CountryDto.builder()
                .id(1L)
                .name("Turkey")
                .iso2("TR")
                .iso3("TUR")
                .phonecode("+90")
                .capital("Ankara")
                .currency("TRY")
                .currencySymbol("₺")
                .region("Asia")
                .subregion("Western Asia")
                .nationality("Turkish")
                .emoji("🇹🇷")
                .build();
    }

    @Test
    void getAllCountries_ShouldReturnCountryList() {
        // Given
        List<CountryDto> expectedCountries = Arrays.asList(testCountryDto);
        when(countryRepository.findAllActiveCountriesMinimal()).thenReturn(expectedCountries);

        // When
        List<CountryDto> result = locationService.getAllCountries();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Turkey", result.get(0).getName());
        assertEquals("TR", result.get(0).getIso2());
        verify(countryRepository, times(1)).findAllActiveCountriesMinimal();
    }

    @Test
    void getCountryById_WhenCountryExists_ShouldReturnCountry() {
        // Given
        when(countryRepository.findById(1L)).thenReturn(Optional.of(testCountry));

        // When
        Optional<CountryDto> result = locationService.getCountryById(1L);

        // Then
        assertTrue(result.isPresent());
        assertEquals("Turkey", result.get().getName());
        assertEquals("TR", result.get().getIso2());
        verify(countryRepository, times(1)).findById(1L);
    }

    @Test
    void getCountryById_WhenCountryNotExists_ShouldReturnEmpty() {
        // Given
        when(countryRepository.findById(999L)).thenReturn(Optional.empty());

        // When
        Optional<CountryDto> result = locationService.getCountryById(999L);

        // Then
        assertFalse(result.isPresent());
        verify(countryRepository, times(1)).findById(999L);
    }

    @Test
    void getCountryByIso2_WhenCountryExists_ShouldReturnCountry() {
        // Given
        when(countryRepository.findByIso2("TR")).thenReturn(Optional.of(testCountry));

        // When
        Optional<CountryDto> result = locationService.getCountryByIso2("TR");

        // Then
        assertTrue(result.isPresent());
        assertEquals("Turkey", result.get().getName());
        assertEquals("TR", result.get().getIso2());
        verify(countryRepository, times(1)).findByIso2("TR");
    }

    @Test
    void searchCountries_WithValidQuery_ShouldReturnMatchingCountries() {
        // Given
        String query = "Tur";
        List<CountryDto> expectedCountries = Arrays.asList(testCountryDto);
        when(countryRepository.searchCountriesByName(query)).thenReturn(expectedCountries);

        // When
        List<CountryDto> result = locationService.searchCountries(query);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Turkey", result.get(0).getName());
        verify(countryRepository, times(1)).searchCountriesByName(query);
    }

    @Test
    void searchCountries_WithEmptyQuery_ShouldReturnAllCountries() {
        // Given
        String query = "";
        List<CountryDto> expectedCountries = Arrays.asList(testCountryDto);
        when(countryRepository.findAllActiveCountriesMinimal()).thenReturn(expectedCountries);

        // When
        List<CountryDto> result = locationService.searchCountries(query);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(countryRepository, times(1)).findAllActiveCountriesMinimal();
        verify(countryRepository, never()).searchCountriesByName(any());
    }

    @Test
    void existsCountry_WhenCountryExists_ShouldReturnTrue() {
        // Given
        when(countryRepository.existsByIdAndActive(1L)).thenReturn(true);

        // When
        boolean result = locationService.existsCountry(1L);

        // Then
        assertTrue(result);
        verify(countryRepository, times(1)).existsByIdAndActive(1L);
    }

    @Test
    void existsCountry_WhenCountryNotExists_ShouldReturnFalse() {
        // Given
        when(countryRepository.existsByIdAndActive(999L)).thenReturn(false);

        // When
        boolean result = locationService.existsCountry(999L);

        // Then
        assertFalse(result);
        verify(countryRepository, times(1)).existsByIdAndActive(999L);
    }

    @Test
    void getPopularCountries_ShouldReturnPopularCountriesList() {
        // Given
        List<CountryDto> expectedCountries = Arrays.asList(testCountryDto);
        when(countryRepository.findPopularCountries()).thenReturn(expectedCountries);

        // When
        List<CountryDto> result = locationService.getPopularCountries();

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Turkey", result.get(0).getName());
        verify(countryRepository, times(1)).findPopularCountries();
    }
}
