package com.lookforx.locationservice.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.locationservice.dto.CountryDto;
import com.lookforx.locationservice.service.LocationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Location Controller Test
 * 
 * Integration tests for LocationController endpoints.
 */
@WebMvcTest(LocationController.class)
class LocationControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private LocationService locationService;

    @Autowired
    private ObjectMapper objectMapper;

    private CountryDto testCountryDto;

    @BeforeEach
    void setUp() {
        testCountryDto = CountryDto.builder()
                .id(1L)
                .name("Turkey")
                .iso2("TR")
                .iso3("TUR")
                .phonecode("+90")
                .capital("Ankara")
                .currency("TRY")
                .currencySymbol("₺")
                .region("Asia")
                .subregion("Western Asia")
                .nationality("Turkish")
                .emoji("🇹🇷")
                .build();
    }

    @Test
    void getAllCountries_ShouldReturnCountriesList() throws Exception {
        // Given
        List<CountryDto> countries = Arrays.asList(testCountryDto);
        when(locationService.getAllCountries()).thenReturn(countries);

        // When & Then
        mockMvc.perform(get("/api/v1/locations/countries")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].name").value("Turkey"))
                .andExpect(jsonPath("$[0].iso2").value("TR"))
                .andExpect(jsonPath("$[0].emoji").value("🇹🇷"));
    }

    @Test
    void getAllCountries_WithPhonecode_ShouldReturnCountriesWithPhonecode() throws Exception {
        // Given
        List<CountryDto> countries = Arrays.asList(testCountryDto);
        when(locationService.getAllCountriesWithPhonecode()).thenReturn(countries);

        // When & Then
        mockMvc.perform(get("/api/v1/locations/countries")
                        .param("includePhonecode", "true")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").value(1))
                .andExpect(jsonPath("$[0].name").value("Turkey"))
                .andExpect(jsonPath("$[0].phonecode").value("+90"));
    }

    @Test
    void getCountryById_WhenCountryExists_ShouldReturnCountry() throws Exception {
        // Given
        when(locationService.getCountryById(1L)).thenReturn(Optional.of(testCountryDto));

        // When & Then
        mockMvc.perform(get("/api/v1/locations/countries/1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("Turkey"))
                .andExpect(jsonPath("$.iso2").value("TR"));
    }

    @Test
    void getCountryById_WhenCountryNotExists_ShouldReturnNotFound() throws Exception {
        // Given
        when(locationService.getCountryById(999L)).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(get("/api/v1/locations/countries/999")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    void getCountryByIso2_WhenCountryExists_ShouldReturnCountry() throws Exception {
        // Given
        when(locationService.getCountryByIso2("TR")).thenReturn(Optional.of(testCountryDto));

        // When & Then
        mockMvc.perform(get("/api/v1/locations/countries/iso2/TR")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("Turkey"))
                .andExpect(jsonPath("$.iso2").value("TR"));
    }

    @Test
    void searchCountries_ShouldReturnMatchingCountries() throws Exception {
        // Given
        List<CountryDto> countries = Arrays.asList(testCountryDto);
        when(locationService.searchCountries("Tur")).thenReturn(countries);

        // When & Then
        mockMvc.perform(get("/api/v1/locations/countries/search")
                        .param("q", "Tur")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].name").value("Turkey"));
    }

    @Test
    void getPopularCountries_ShouldReturnPopularCountries() throws Exception {
        // Given
        List<CountryDto> countries = Arrays.asList(testCountryDto);
        when(locationService.getPopularCountries()).thenReturn(countries);

        // When & Then
        mockMvc.perform(get("/api/v1/locations/countries/popular")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].name").value("Turkey"));
    }
}
