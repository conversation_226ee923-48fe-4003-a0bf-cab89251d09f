package com.lookforx.requestservice.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.requestservice.domain.RequestStatus;
import com.lookforx.requestservice.dto.CreateRequestDto;
import com.lookforx.requestservice.dto.LocationDto;
import com.lookforx.requestservice.dto.MediaDto;
import com.lookforx.requestservice.dto.PriceRangeDto;
import com.lookforx.requestservice.dto.RequestResponseDto;
import com.lookforx.requestservice.service.RequestService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Unit tests for RequestController
 */
@WebMvcTest(controllers = RequestController.class)
@AutoConfigureMockMvc(addFilters = false)
class RequestControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private RequestService requestService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private CreateRequestDto createRequestDto;
    private RequestResponseDto requestResponseDto;
    
    @BeforeEach
    void setUp() {
        LocationDto locationDto = LocationDto.builder()
                .countryId("TR")
                .cityId("34")
                .districtId("1")
                .build();
        
        PriceRangeDto priceRangeDto = PriceRangeDto.builder()
                .minPrice(BigDecimal.valueOf(100))
                .maxPrice(BigDecimal.valueOf(500))
                .currency("TRY")
                .build();
        
        MediaDto mediaDto = MediaDto.builder()
                .imageUrls(Arrays.asList("https://example.com/image1.jpg"))
                .documentUrls(Arrays.asList("https://example.com/doc1.pdf"))
                .videoUrl("https://example.com/video1.mp4")
                .build();
        
        createRequestDto = CreateRequestDto.builder()
                .userId("1")
                .categoryId("1")
                .submissionFormId("form123")
                .title("Test Request")
                .description("Test Description")
                .location(locationDto)
                .priceRange(priceRangeDto)
                .media(mediaDto)
                .expiresAt(LocalDateTime.now().plusDays(30))
                .build();
        
        requestResponseDto = RequestResponseDto.builder()
                .id("req123")
                .userId("1")
                .categoryId("1")
                .submissionFormId("form123")
                .title("Test Request")
                .description("Test Description")
                .location(locationDto)
                .priceRange(priceRangeDto)
                .media(mediaDto)
                .status(RequestStatus.ACTIVE)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }
    
    @Test
    void createRequest_Success() throws Exception {
        // Given
        when(requestService.createRequest(any(CreateRequestDto.class))).thenReturn(requestResponseDto);
        
        // When & Then
        mockMvc.perform(post("/api/v1/requests")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequestDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value("req123"))
                .andExpect(jsonPath("$.data.title").value("Test Request"));
    }
    
    @Test
    void getRequestById_Success() throws Exception {
        // Given
        when(requestService.getRequestById(anyString())).thenReturn(requestResponseDto);
        
        // When & Then
        mockMvc.perform(get("/api/v1/requests/req123"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value("req123"))
                .andExpect(jsonPath("$.data.title").value("Test Request"));
    }
    
    @Test
    void getRequestsByUserId_Success() throws Exception {
        // Given
        Page<RequestResponseDto> page = new PageImpl<>(Collections.singletonList(requestResponseDto));
        when(requestService.getRequestsByUserId(anyString(), any())).thenReturn(page);
        
        // When & Then
        mockMvc.perform(get("/api/v1/requests/user/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content[0].id").value("req123"));
    }
    
    @Test
    void getActiveRequests_Success() throws Exception {
        // Given
        Page<RequestResponseDto> page = new PageImpl<>(Collections.singletonList(requestResponseDto));
        when(requestService.getActiveRequests(any())).thenReturn(page);
        
        // When & Then
        mockMvc.perform(get("/api/v1/requests/active"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content[0].status").value("ACTIVE"));
    }
    
    @Test
    void searchRequests_Success() throws Exception {
        // Given
        Page<RequestResponseDto> page = new PageImpl<>(Collections.singletonList(requestResponseDto));
        when(requestService.searchRequests(anyString(), anyString(), anyString(), anyString(), anyString(), any())).thenReturn(page);
        
        // When & Then
        mockMvc.perform(get("/api/v1/requests/search")
                .param("countryId", "TR")
                .param("cityId", "34")
                .param("title", "Test"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content[0].id").value("req123"));
    }
    
    @Test
    void deleteRequest_Success() throws Exception {
        // When & Then
        mockMvc.perform(delete("/api/v1/requests/req123")
                .param("userId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Request deleted successfully"));
    }
    
    @Test
    void changeRequestStatus_Success() throws Exception {
        // Given
        requestResponseDto.setStatus(RequestStatus.COMPLETED);
        when(requestService.changeRequestStatus(anyString(), any(RequestStatus.class), anyString())).thenReturn(requestResponseDto);
        
        // When & Then
        mockMvc.perform(patch("/api/v1/requests/req123/status")
                .param("userId", "1")
                .param("status", "COMPLETED"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.status").value("COMPLETED"));
    }
}
