package com.lookforx.requestservice.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.requestservice.domain.Request;
import com.lookforx.requestservice.domain.RequestStatus;
import com.lookforx.requestservice.dto.CreateRequestDto;
import com.lookforx.requestservice.dto.LocationDto;
import com.lookforx.requestservice.dto.MediaDto;
import com.lookforx.requestservice.dto.PriceRangeDto;
import com.lookforx.requestservice.repository.RequestRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for Request Service
 */
@SpringBootTest(classes = com.lookforx.requestservice.RequestServiceApplication.class)
@AutoConfigureMockMvc
@ActiveProfiles("test")
class RequestIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private RequestRepository requestRepository;
    
    @MockBean
    private KafkaTemplate<String, Object> kafkaTemplate;
    
    private CreateRequestDto createRequestDto;
    
    @BeforeEach
    void setUp() {
        requestRepository.deleteAll();
        
        LocationDto locationDto = LocationDto.builder()
                .countryId("TR")
                .cityId("34")
                .districtId("1")
                .build();
        
        PriceRangeDto priceRangeDto = PriceRangeDto.builder()
                .minPrice(BigDecimal.valueOf(100))
                .maxPrice(BigDecimal.valueOf(500))
                .currency("TRY")
                .build();
        
        MediaDto mediaDto = MediaDto.builder()
                .imageUrls(Arrays.asList("https://example.com/image1.jpg"))
                .documentUrls(Arrays.asList("https://example.com/doc1.pdf"))
                .videoUrl("https://example.com/video1.mp4")
                .build();
        
        createRequestDto = CreateRequestDto.builder()
                .userId("1")
                .categoryId("1")
                .submissionFormId("form123")
                .title("Integration Test Request")
                .description("This is a test request for integration testing")
                .location(locationDto)
                .priceRange(priceRangeDto)
                .media(mediaDto)
                .expiresAt(LocalDateTime.now().plusDays(30))
                .build();
    }
    
    @Test
    void createAndRetrieveRequest_Success() throws Exception {
        // Create request
        String response = mockMvc.perform(post("/api/v1/requests")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createRequestDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.title").value("Integration Test Request"))
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        // Extract request ID from response
        String requestId = objectMapper.readTree(response)
                .get("data")
                .get("id")
                .asText();
        
        // Retrieve the created request
        mockMvc.perform(get("/api/v1/requests/" + requestId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(requestId))
                .andExpect(jsonPath("$.data.title").value("Integration Test Request"))
                .andExpect(jsonPath("$.data.status").value("ACTIVE"));
    }
    
    @Test
    void getRequestsByUserId_Success() throws Exception {
        // Create a request first
        Request request = Request.builder()
                .userId("1")
                .categoryId("1")
                .submissionFormId("form123")
                .title("Test Request")
                .description("Test Description")
                .status(RequestStatus.ACTIVE)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .expiresAt(LocalDateTime.now().plusDays(30))
                .build();
        
        requestRepository.save(request);
        
        // Get requests by user ID
        mockMvc.perform(get("/api/v1/requests/user/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content[0].userId").value("1"))
                .andExpect(jsonPath("$.data.content[0].title").value("Test Request"));
    }
    
    @Test
    void getActiveRequests_Success() throws Exception {
        // Create an active request
        Request activeRequest = Request.builder()
                .userId("1")
                .categoryId("1")
                .submissionFormId("form123")
                .title("Active Request")
                .description("Active Description")
                .status(RequestStatus.ACTIVE)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .expiresAt(LocalDateTime.now().plusDays(30))
                .build();
        
        // Create an expired request
        Request expiredRequest = Request.builder()
                .userId("2")
                .categoryId("1")
                .submissionFormId("form456")
                .title("Expired Request")
                .description("Expired Description")
                .status(RequestStatus.EXPIRED)
                .createdAt(LocalDateTime.now().minusDays(10))
                .updatedAt(LocalDateTime.now())
                .expiresAt(LocalDateTime.now().minusDays(1))
                .build();
        
        requestRepository.save(activeRequest);
        requestRepository.save(expiredRequest);
        
        // Get only active requests
        mockMvc.perform(get("/api/v1/requests/active"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content[0].status").value("ACTIVE"))
                .andExpect(jsonPath("$.data.content[0].title").value("Active Request"));
    }
    
    @Test
    void searchRequestsByTitle_Success() throws Exception {
        // Create requests with different titles
        Request request1 = Request.builder()
                .userId("1")
                .categoryId("1")
                .submissionFormId("form123")
                .title("Web Development Project")
                .description("Need a website")
                .status(RequestStatus.ACTIVE)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .expiresAt(LocalDateTime.now().plusDays(30))
                .build();
        
        Request request2 = Request.builder()
                .userId("2")
                .categoryId("1")
                .submissionFormId("form456")
                .title("Mobile App Development")
                .description("Need a mobile app")
                .status(RequestStatus.ACTIVE)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .expiresAt(LocalDateTime.now().plusDays(30))
                .build();
        
        requestRepository.save(request1);
        requestRepository.save(request2);
        
        // Search by title
        mockMvc.perform(get("/api/v1/requests/search")
                .param("title", "Web"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.content").isArray())
                .andExpect(jsonPath("$.data.content[0].title").value("Web Development Project"));
    }
    
    @Test
    void deleteRequest_Success() throws Exception {
        // Create a request first
        Request request = Request.builder()
                .userId("1")
                .categoryId("1")
                .submissionFormId("form123")
                .title("Request to Delete")
                .description("This will be deleted")
                .status(RequestStatus.ACTIVE)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .expiresAt(LocalDateTime.now().plusDays(30))
                .build();
        
        Request savedRequest = requestRepository.save(request);
        
        // Delete the request
        mockMvc.perform(delete("/api/v1/requests/" + savedRequest.getId())
                .param("userId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("Request deleted successfully"));
        
        // Verify it's deleted
        Optional<Request> deletedRequest = requestRepository.findById(savedRequest.getId());
        assert deletedRequest.isEmpty();
    }
}
