package com.lookforx.requestservice.service;

import com.lookforx.requestservice.client.CategoryServiceClient;
import com.lookforx.requestservice.client.FormServiceClient;
import com.lookforx.requestservice.client.LocationServiceClient;
import com.lookforx.requestservice.domain.Request;
import com.lookforx.requestservice.domain.RequestStatus;
import com.lookforx.requestservice.dto.CreateRequestDto;
import com.lookforx.requestservice.dto.LocationDto;
import com.lookforx.requestservice.dto.MediaDto;
import com.lookforx.requestservice.dto.PriceRangeDto;
import com.lookforx.requestservice.exception.RequestNotFoundException;
import com.lookforx.requestservice.mapper.RequestMapper;
import com.lookforx.requestservice.repository.RequestRepository;
import com.lookforx.requestservice.service.impl.RequestServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for RequestService
 */
@ExtendWith(MockitoExtension.class)
class RequestServiceTest {
    
    @Mock
    private RequestRepository requestRepository;
    
    @Mock
    private RequestMapper requestMapper;
    
    @Mock
    private FormServiceClient formServiceClient;
    
    @Mock
    private LocationServiceClient locationServiceClient;
    
    @Mock
    private CategoryServiceClient categoryServiceClient;
    
    @Mock
    private KafkaTemplate<String, Object> kafkaTemplate;
    
    @InjectMocks
    private RequestServiceImpl requestService;
    
    private CreateRequestDto createRequestDto;
    private Request request;
    
    @BeforeEach
    void setUp() {
        LocationDto locationDto = LocationDto.builder()
                .countryId("TR")
                .cityId("34")
                .districtId("1")
                .build();
        
        PriceRangeDto priceRangeDto = PriceRangeDto.builder()
                .minPrice(BigDecimal.valueOf(100))
                .maxPrice(BigDecimal.valueOf(500))
                .currency("TRY")
                .build();
        
        MediaDto mediaDto = MediaDto.builder()
                .imageUrls(Arrays.asList("https://example.com/image1.jpg"))
                .documentUrls(Arrays.asList("https://example.com/doc1.pdf"))
                .videoUrl("https://example.com/video1.mp4")
                .build();
        
        createRequestDto = CreateRequestDto.builder()
                .userId("1")
                .categoryId("1")
                .submissionFormId("form123")
                .title("Test Request")
                .description("Test Description")
                .location(locationDto)
                .priceRange(priceRangeDto)
                .media(mediaDto)
                .expiresAt(LocalDateTime.now().plusDays(30))
                .build();
        
        request = Request.builder()
                .id("req123")
                .userId("1")
                .categoryId("1")
                .submissionFormId("form123")
                .title("Test Request")
                .description("Test Description")
                .status(RequestStatus.ACTIVE)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }
    
    @Test
    void createRequest_Success() {
        // Given
        when(formServiceClient.existsBySubmissionFormId(anyString())).thenReturn(true);
        when(categoryServiceClient.existsCategory(anyString())).thenReturn(true);
        when(locationServiceClient.existsCountry(anyString())).thenReturn(true);
        when(locationServiceClient.existsCity(anyString(), anyString())).thenReturn(true);
        when(locationServiceClient.existsDistrict(anyString(), anyString(), anyString())).thenReturn(true);
        when(requestMapper.toEntity(any(CreateRequestDto.class))).thenReturn(request);
        when(requestRepository.save(any(Request.class))).thenReturn(request);
        when(requestMapper.toDto(any(Request.class))).thenReturn(any());
        
        // When
        requestService.createRequest(createRequestDto);
        
        // Then
        verify(requestRepository).save(any(Request.class));
        // Note: Kafka event publishing might fail due to ID conversion, but service should still work
    }
    
    @Test
    void getRequestById_Success() {
        // Given
        when(requestRepository.findById(anyString())).thenReturn(Optional.of(request));
        when(requestMapper.toDto(any(Request.class))).thenReturn(any());
        
        // When
        requestService.getRequestById("req123");
        
        // Then
        verify(requestRepository).findById("req123");
        verify(requestMapper).toDto(request);
    }
    
    @Test
    void getRequestById_NotFound() {
        // Given
        when(requestRepository.findById(anyString())).thenReturn(Optional.empty());
        
        // When & Then
        assertThrows(RequestNotFoundException.class, () -> {
            requestService.getRequestById("nonexistent");
        });
    }
    
    @Test
    void deleteRequest_Success() {
        // Given
        when(requestRepository.findByIdAndUserId(anyString(), anyString())).thenReturn(Optional.of(request));
        
        // When
        requestService.deleteRequest("req123", "1");
        
        // Then
        verify(requestRepository).delete(request);
    }
    
    @Test
    void deleteRequest_NotFound() {
        // Given
        when(requestRepository.findByIdAndUserId(anyString(), anyString())).thenReturn(Optional.empty());
        
        // When & Then
        assertThrows(RequestNotFoundException.class, () -> {
            requestService.deleteRequest("nonexistent", "1");
        });
    }
}
