
package com.lookforx.notificationservice.strategy;

import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.strategy.NotificationStrategy;
import com.lookforx.notificationservice.strategy.NotificationStrategyFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * Unit tests for NotificationStrategyFactory using Mockito
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class NotificationStrategyFactoryTest {

    @Mock
    private NotificationStrategy emailStrategy;

    @Mock
    private NotificationStrategy smsStrategy;

    @Mock
    private NotificationStrategy pushStrategy;

    private NotificationStrategyFactory strategyFactory;

    @BeforeEach
    void setUp() {
        // Configure all strategy mocks
        when(emailStrategy.getNotificationType()).thenReturn(NotificationType.EMAIL);
        when(emailStrategy.isAvailable()).thenReturn(true);

        when(smsStrategy.getNotificationType()).thenReturn(NotificationType.SMS);
        when(smsStrategy.isAvailable()).thenReturn(true);

        when(pushStrategy.getNotificationType()).thenReturn(NotificationType.PUSH_NOTIFICATION);
        when(pushStrategy.isAvailable()).thenReturn(false);

        List<NotificationStrategy> strategies = Arrays.asList(emailStrategy, smsStrategy, pushStrategy);
        strategyFactory = new NotificationStrategyFactory(strategies);
    }

    @Test
    void getStrategy_EmailAvailable() {
        // When
        Optional<NotificationStrategy> result = strategyFactory.getStrategy(NotificationType.EMAIL);

        // Then
        assertTrue(result.isPresent());
        assertEquals(emailStrategy, result.get());
    }

    @Test
    void getStrategy_SmsAvailable() {
        // When
        Optional<NotificationStrategy> result = strategyFactory.getStrategy(NotificationType.SMS);

        // Then
        assertTrue(result.isPresent());
        assertEquals(smsStrategy, result.get());
    }

    @Test
    void getStrategy_PushNotAvailable() {
        // When
        Optional<NotificationStrategy> result = strategyFactory.getStrategy(NotificationType.PUSH_NOTIFICATION);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void getStrategy_NonExistentType() {
        // When
        Optional<NotificationStrategy> result = strategyFactory.getStrategy(NotificationType.IN_APP_NOTIFICATION);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void getAvailableStrategies() {
        // When
        List<NotificationStrategy> result = strategyFactory.getAvailableStrategies();

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains(emailStrategy));
        assertTrue(result.contains(smsStrategy));
        assertFalse(result.contains(pushStrategy));
    }

    @Test
    void isStrategyAvailable_Email() {
        // When
        boolean result = strategyFactory.isStrategyAvailable(NotificationType.EMAIL);

        // Then
        assertTrue(result);
    }

    @Test
    void isStrategyAvailable_Push() {
        // When
        boolean result = strategyFactory.isStrategyAvailable(NotificationType.PUSH_NOTIFICATION);

        // Then
        assertFalse(result);
    }

    @Test
    void getSupportedTypes() {
        // When
        List<NotificationType> result = strategyFactory.getSupportedTypes();

        // Then
        assertEquals(2, result.size());
        assertTrue(result.contains(NotificationType.EMAIL));
        assertTrue(result.contains(NotificationType.SMS));
        assertFalse(result.contains(NotificationType.PUSH_NOTIFICATION));
    }
}
