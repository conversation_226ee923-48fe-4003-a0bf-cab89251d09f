package com.lookforx.notificationservice.strategy.impl;

import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for SmsNotificationStrategy
 */
@ExtendWith(MockitoExtension.class)
class SmsNotificationStrategyTest {

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private SmsNotificationStrategy smsStrategy;

    private Notification testNotification;

    @BeforeEach
    void setUp() {
        // Set up test properties
        ReflectionTestUtils.setField(smsStrategy, "smsEnabled", true);
        ReflectionTestUtils.setField(smsStrategy, "smsProviderUrl", "https://api.sms-provider.com/send");
        ReflectionTestUtils.setField(smsStrategy, "apiKey", "test-api-key");
        ReflectionTestUtils.setField(smsStrategy, "senderId", "LookForX");

        // Use reflection to set the RestTemplate
        ReflectionTestUtils.setField(smsStrategy, "restTemplate", restTemplate);

        testNotification = Notification.builder()
                .eventId("TEST-123")
                .eventType("TEST_EVENT")
                .notificationType(NotificationType.SMS)
                .priority(NotificationPriority.NORMAL)
                .userId(1L)
                .recipient("+**********")
                .subject("Test Subject")
                .content("Test SMS Content")
                .serviceName("test-service")
                .build();
    }

    @Test
    void sendNotification_Success() {
        // Given
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("messageId", "SMS-123456");
        ResponseEntity<Map> response = new ResponseEntity<>(responseBody, HttpStatus.OK);

        when(restTemplate.exchange(anyString(), any(), any(), eq(Map.class)))
                .thenReturn(response);

        // When
        boolean result = smsStrategy.sendNotification(testNotification);

        // Then
        assertTrue(result);
        assertEquals("SMS-123456", testNotification.getExternalId());
        verify(restTemplate).exchange(anyString(), any(), any(), eq(Map.class));
    }

    @Test
    void sendNotification_SuccessWithoutMessageId() {
        // Given
        Map<String, Object> responseBody = new HashMap<>();
        ResponseEntity<Map> response = new ResponseEntity<>(responseBody, HttpStatus.OK);

        when(restTemplate.exchange(anyString(), any(), any(), eq(Map.class)))
                .thenReturn(response);

        // When
        boolean result = smsStrategy.sendNotification(testNotification);

        // Then
        assertTrue(result);
        assertNotNull(testNotification.getExternalId()); // Should generate UUID
        verify(restTemplate).exchange(anyString(), any(), any(), eq(Map.class));
    }

    @Test
    void sendNotification_HttpError() {
        // Given
        ResponseEntity<Map> response = new ResponseEntity<>(HttpStatus.BAD_REQUEST);

        when(restTemplate.exchange(anyString(), any(), any(), eq(Map.class)))
                .thenReturn(response);

        // When
        boolean result = smsStrategy.sendNotification(testNotification);

        // Then
        assertFalse(result);
        assertEquals("SMS provider error: 400 BAD_REQUEST", testNotification.getErrorMessage());
        verify(restTemplate).exchange(anyString(), any(), any(), eq(Map.class));
    }

    @Test
    void sendNotification_RestClientException() {
        // Given
        when(restTemplate.exchange(anyString(), any(), any(), eq(Map.class)))
                .thenThrow(new RestClientException("Connection timeout"));

        // When
        boolean result = smsStrategy.sendNotification(testNotification);

        // Then
        assertFalse(result);
        assertEquals("Connection timeout", testNotification.getErrorMessage());
        verify(restTemplate).exchange(anyString(), any(), any(), eq(Map.class));
    }

    @Test
    void sendNotification_SmsDisabled() {
        // Given
        ReflectionTestUtils.setField(smsStrategy, "smsEnabled", false);

        // When
        boolean result = smsStrategy.sendNotification(testNotification);

        // Then
        assertFalse(result);
        verify(restTemplate, never()).exchange(anyString(), any(), any(), eq(Map.class));
    }

    @Test
    void getNotificationType() {
        // When
        NotificationType result = smsStrategy.getNotificationType();

        // Then
        assertEquals(NotificationType.SMS, result);
    }

    @Test
    void isAvailable_Enabled() {
        // When
        boolean result = smsStrategy.isAvailable();

        // Then
        assertTrue(result);
    }

    @Test
    void isAvailable_Disabled() {
        // Given
        ReflectionTestUtils.setField(smsStrategy, "smsEnabled", false);

        // When
        boolean result = smsStrategy.isAvailable();

        // Then
        assertFalse(result);
    }

    @Test
    void isAvailable_NoUrl() {
        // Given
        ReflectionTestUtils.setField(smsStrategy, "smsProviderUrl", "");

        // When
        boolean result = smsStrategy.isAvailable();

        // Then
        assertFalse(result);
    }

    @Test
    void isAvailable_NoApiKey() {
        // Given
        ReflectionTestUtils.setField(smsStrategy, "apiKey", "");

        // When
        boolean result = smsStrategy.isAvailable();

        // Then
        assertFalse(result);
    }

    @Test
    void getMaxRetryAttempts() {
        // When
        int result = smsStrategy.getMaxRetryAttempts();

        // Then
        assertEquals(2, result);
    }

    @Test
    void getRetryDelayMinutes_FirstAttempt() {
        // When
        long result = smsStrategy.getRetryDelayMinutes(1);

        // Then
        assertEquals(1L, result);
    }

    @Test
    void getRetryDelayMinutes_SecondAttempt() {
        // When
        long result = smsStrategy.getRetryDelayMinutes(2);

        // Then
        assertEquals(3L, result);
    }
}
