package com.lookforx.notificationservice.strategy.impl;

import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.test.util.ReflectionTestUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for EmailNotificationStrategy
 */
@ExtendWith(MockitoExtension.class)
class EmailNotificationStrategyTest {

    @Mock
    private JavaMailSender mailSender;

    @Mock
    private TemplateEngine templateEngine;

    @Mock
    private MimeMessage mimeMessage;

    @InjectMocks
    private EmailNotificationStrategy emailStrategy;

    private Notification testNotification;

    @BeforeEach
    void setUp() {
        // Set up test properties
        ReflectionTestUtils.setField(emailStrategy, "fromEmail", "<EMAIL>");
        ReflectionTestUtils.setField(emailStrategy, "fromName", "LookForX Test");
        ReflectionTestUtils.setField(emailStrategy, "emailEnabled", true);

        testNotification = Notification.builder()
                .eventId("TEST-123")
                .eventType("TEST_EVENT")
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.NORMAL)
                .userId(1L)
                .recipient("<EMAIL>")
                .subject("Test Subject")
                .content("Test Content")
                .serviceName("test-service")
                .build();
    }

    @Test
    void sendNotification_Success() throws MessagingException {
        // Given
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        doNothing().when(mailSender).send(any(MimeMessage.class));

        // When
        boolean result = emailStrategy.sendNotification(testNotification);

        // Then
        assertTrue(result);
        verify(mailSender).createMimeMessage();
        verify(mailSender).send(mimeMessage);
        assertNotNull(testNotification.getExternalId());
    }

    @Test
    void sendNotification_WithTemplate_Success() throws MessagingException {
        // Given
        testNotification.setTemplateName("test-template");
        Map<String, String> params = new HashMap<>();
        params.put("name", "John Doe");
        testNotification.setTemplateParameters(params);

        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        when(templateEngine.process(eq("test-template"), any(Context.class)))
                .thenReturn("<html><body>Hello John Doe</body></html>");
        doNothing().when(mailSender).send(any(MimeMessage.class));

        // When
        boolean result = emailStrategy.sendNotification(testNotification);

        // Then
        assertTrue(result);
        verify(templateEngine).process(eq("test-template"), any(Context.class));
        verify(mailSender).send(mimeMessage);
    }

    @Test
    void sendNotification_TemplateProcessingFails() throws MessagingException {
        // Given
        testNotification.setTemplateName("invalid-template");
        
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        when(templateEngine.process(eq("invalid-template"), any(Context.class)))
                .thenThrow(new RuntimeException("Template not found"));
        doNothing().when(mailSender).send(any(MimeMessage.class));

        // When
        boolean result = emailStrategy.sendNotification(testNotification);

        // Then
        assertTrue(result); // Should fallback to plain content
        verify(templateEngine).process(eq("invalid-template"), any(Context.class));
        verify(mailSender).send(mimeMessage);
    }

    @Test
    void sendNotification_MailException() {
        // Given
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        doThrow(new MailException("SMTP server error") {}).when(mailSender).send(any(MimeMessage.class));

        // When
        boolean result = emailStrategy.sendNotification(testNotification);

        // Then
        assertFalse(result);
        assertEquals("SMTP server error", testNotification.getErrorMessage());
        verify(mailSender).send(mimeMessage);
    }

    @Test
    void sendNotification_MessagingException() throws MessagingException {
        // Given
        when(mailSender.createMimeMessage()).thenReturn(mimeMessage);
        doAnswer(invocation -> {
            throw new MessagingException("Invalid email format");
        }).when(mailSender).send(any(MimeMessage.class));

        // When
        boolean result = emailStrategy.sendNotification(testNotification);

        // Then
        assertFalse(result);
        assertEquals("Invalid email format", testNotification.getErrorMessage());
        verify(mailSender).send(mimeMessage);
    }

    @Test
    void sendNotification_EmailDisabled() {
        // Given
        ReflectionTestUtils.setField(emailStrategy, "emailEnabled", false);

        // When
        boolean result = emailStrategy.sendNotification(testNotification);

        // Then
        assertFalse(result);
        verify(mailSender, never()).createMimeMessage();
        verify(mailSender, never()).send(any(MimeMessage.class));
    }

    @Test
    void getNotificationType() {
        // When
        NotificationType result = emailStrategy.getNotificationType();

        // Then
        assertEquals(NotificationType.EMAIL, result);
    }

    @Test
    void isAvailable_Enabled() {
        // When
        boolean result = emailStrategy.isAvailable();

        // Then
        assertTrue(result);
    }

    @Test
    void isAvailable_Disabled() {
        // Given
        ReflectionTestUtils.setField(emailStrategy, "emailEnabled", false);

        // When
        boolean result = emailStrategy.isAvailable();

        // Then
        assertFalse(result);
    }

    @Test
    void isAvailable_NoMailSender() {
        // Given
        ReflectionTestUtils.setField(emailStrategy, "mailSender", null);

        // When
        boolean result = emailStrategy.isAvailable();

        // Then
        assertFalse(result);
    }

    @Test
    void getMaxRetryAttempts() {
        // When
        int result = emailStrategy.getMaxRetryAttempts();

        // Then
        assertEquals(3, result);
    }
}
