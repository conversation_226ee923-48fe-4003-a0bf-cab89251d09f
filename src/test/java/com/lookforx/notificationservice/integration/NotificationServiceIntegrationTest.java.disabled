package com.lookforx.notificationservice.integration;

import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;
import com.lookforx.notificationservice.repository.NotificationRepository;
import com.lookforx.notificationservice.service.NotificationService;
import com.lookforx.notificationservice.strategy.NotificationStrategyFactory;
import com.lookforx.notificationservice.strategy.impl.EmailNotificationStrategy;
import com.lookforx.notificationservice.strategy.impl.SmsNotificationStrategy;
import com.lookforx.notificationservice.strategy.impl.PushNotificationStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration tests for NotificationService
 */
@DataJpaTest
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect",
    "notification.email.enabled=false",
    "notification.sms.enabled=false",
    "notification.push.enabled=false"
})
@Import({
    NotificationService.class,
    NotificationStrategyFactory.class,
    EmailNotificationStrategy.class,
    SmsNotificationStrategy.class,
    PushNotificationStrategy.class
})
class NotificationServiceIntegrationTest {

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private NotificationRepository notificationRepository;

    private Notification testNotification;

    @BeforeEach
    void setUp() {
        notificationRepository.deleteAll();
        
        testNotification = Notification.builder()
                .eventId("INTEGRATION-TEST-123")
                .eventType("TEST_EVENT")
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.NORMAL)
                .userId(1L)
                .recipient("<EMAIL>")
                .subject("Integration Test Subject")
                .content("Integration Test Content")
                .serviceName("test-service")
                .build();
    }

    @Test
    void sendNotification_SavesToDatabase() {
        // When
        boolean result = notificationService.sendNotification(testNotification);

        // Then
        assertFalse(result); // Should fail because email is disabled
        
        // Verify notification was saved to database
        Optional<Notification> saved = notificationRepository.findByEventId("INTEGRATION-TEST-123");
        assertTrue(saved.isPresent());
        assertEquals("<EMAIL>", saved.get().getRecipient());
        assertEquals(Notification.NotificationStatus.FAILED, saved.get().getStatus());
    }

    @Test
    void getByUserId_ReturnsUserNotifications() {
        // Given
        Notification notification1 = createTestNotification("EVENT-1", 1L, "<EMAIL>");
        Notification notification2 = createTestNotification("EVENT-2", 1L, "<EMAIL>");
        Notification notification3 = createTestNotification("EVENT-3", 2L, "<EMAIL>");
        
        notificationRepository.saveAll(List.of(notification1, notification2, notification3));

        // When
        List<Notification> userNotifications = notificationService.getByUserId(1L);

        // Then
        assertEquals(2, userNotifications.size());
        assertTrue(userNotifications.stream().allMatch(n -> n.getUserId().equals(1L)));
    }

    @Test
    void getByStatus_ReturnsNotificationsByStatus() {
        // Given
        Notification pendingNotification = createTestNotification("EVENT-1", 1L, "<EMAIL>");
        pendingNotification.setStatus(Notification.NotificationStatus.PENDING);
        
        Notification sentNotification = createTestNotification("EVENT-2", 1L, "<EMAIL>");
        sentNotification.setStatus(Notification.NotificationStatus.SENT);
        
        notificationRepository.saveAll(List.of(pendingNotification, sentNotification));

        // When
        List<Notification> pendingNotifications = notificationService.getByStatus(Notification.NotificationStatus.PENDING);

        // Then
        assertEquals(1, pendingNotifications.size());
        assertEquals(Notification.NotificationStatus.PENDING, pendingNotifications.get(0).getStatus());
    }

    @Test
    void getByNotificationType_ReturnsNotificationsByType() {
        // Given
        Notification emailNotification = createTestNotification("EVENT-1", 1L, "<EMAIL>");
        emailNotification.setNotificationType(NotificationType.EMAIL);
        
        Notification smsNotification = createTestNotification("EVENT-2", 1L, "+1234567890");
        smsNotification.setNotificationType(NotificationType.SMS);
        
        notificationRepository.saveAll(List.of(emailNotification, smsNotification));

        // When
        List<Notification> emailNotifications = notificationService.getByNotificationType(NotificationType.EMAIL);

        // Then
        assertEquals(1, emailNotifications.size());
        assertEquals(NotificationType.EMAIL, emailNotifications.get(0).getNotificationType());
    }

    @Test
    void getStats_ReturnsCorrectStatistics() {
        // Given
        Notification pendingEmail = createTestNotification("EVENT-1", 1L, "<EMAIL>");
        pendingEmail.setStatus(Notification.NotificationStatus.PENDING);
        pendingEmail.setNotificationType(NotificationType.EMAIL);
        
        Notification sentSms = createTestNotification("EVENT-2", 1L, "+1234567890");
        sentSms.setStatus(Notification.NotificationStatus.SENT);
        sentSms.setNotificationType(NotificationType.SMS);
        
        Notification failedPush = createTestNotification("EVENT-3", 1L, "device-token");
        failedPush.setStatus(Notification.NotificationStatus.FAILED);
        failedPush.setNotificationType(NotificationType.PUSH_NOTIFICATION);
        
        notificationRepository.saveAll(List.of(pendingEmail, sentSms, failedPush));

        // When
        NotificationService.NotificationStats stats = notificationService.getStats();

        // Then
        assertEquals(3L, stats.totalNotifications);
        assertEquals(1L, stats.pendingNotifications);
        assertEquals(1L, stats.sentNotifications);
        assertEquals(1L, stats.failedNotifications);
        assertEquals(1L, stats.emailNotifications);
        assertEquals(1L, stats.smsNotifications);
        assertEquals(1L, stats.pushNotifications);
    }

    @Test
    void retryFailedNotifications_ProcessesFailedNotifications() {
        // Given
        Notification failedNotification = createTestNotification("EVENT-1", 1L, "<EMAIL>");
        failedNotification.setStatus(Notification.NotificationStatus.FAILED);
        failedNotification.setDeliveryAttempts(1);
        failedNotification.setMaxAttempts(3);
        
        notificationRepository.save(failedNotification);

        // When
        notificationService.retryFailedNotifications();

        // Then
        Optional<Notification> updated = notificationRepository.findByEventId("EVENT-1");
        assertTrue(updated.isPresent());
        assertEquals(2, updated.get().getDeliveryAttempts()); // Should increment attempts
    }

    private Notification createTestNotification(String eventId, Long userId, String recipient) {
        return Notification.builder()
                .eventId(eventId)
                .eventType("TEST_EVENT")
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.NORMAL)
                .userId(userId)
                .recipient(recipient)
                .subject("Test Subject")
                .content("Test Content")
                .serviceName("test-service")
                .status(Notification.NotificationStatus.PENDING)
                .deliveryAttempts(0)
                .maxAttempts(3)
                .build();
    }
}
