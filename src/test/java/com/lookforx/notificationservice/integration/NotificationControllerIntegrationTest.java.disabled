package com.lookforx.notificationservice.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.controller.NotificationTestController;
import com.lookforx.notificationservice.domain.Notification;
import com.lookforx.notificationservice.repository.NotificationRepository;
import com.lookforx.notificationservice.service.NotificationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for NotificationTestController
 */
@SpringBootTest
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "notification.email.enabled=false",
    "notification.sms.enabled=false",
    "notification.push.enabled=false"
})
@AutoConfigureWebMvc
@Transactional
class NotificationControllerIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        notificationRepository.deleteAll();
    }

    @Test
    void createTestNotification_Success() throws Exception {
        // Given
        NotificationTestController.TestNotificationRequest request = new NotificationTestController.TestNotificationRequest();
        request.setNotificationType(NotificationType.EMAIL);
        request.setPriority(NotificationPriority.HIGH);
        request.setUserId(1L);
        request.setRecipient("<EMAIL>");
        request.setSubject("Test Subject");
        request.setContent("Test Content");

        // When & Then
        mockMvc.perform(post("/api/v1/notifications/test")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest()) // Should fail because email is disabled
                .andExpect(content().string("Failed to send notification"));
    }

    @Test
    void getUserNotifications_ReturnsUserNotifications() throws Exception {
        // Given
        Notification notification1 = createAndSaveNotification("EVENT-1", 1L, "<EMAIL>");
        Notification notification2 = createAndSaveNotification("EVENT-2", 1L, "<EMAIL>");
        createAndSaveNotification("EVENT-3", 2L, "<EMAIL>"); // Different user

        // When & Then
        mockMvc.perform(get("/api/v1/notifications/user/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))
                .andExpect(jsonPath("$[0].userId", is(1)))
                .andExpect(jsonPath("$[1].userId", is(1)));
    }

    @Test
    void getUserNotifications_EmptyList() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/notifications/user/999"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(0)));
    }

    @Test
    void getStats_ReturnsStatistics() throws Exception {
        // Given
        Notification pendingNotification = createAndSaveNotification("EVENT-1", 1L, "<EMAIL>");
        pendingNotification.setStatus(Notification.NotificationStatus.PENDING);
        notificationRepository.save(pendingNotification);

        Notification sentNotification = createAndSaveNotification("EVENT-2", 1L, "<EMAIL>");
        sentNotification.setStatus(Notification.NotificationStatus.SENT);
        notificationRepository.save(sentNotification);

        // When & Then
        mockMvc.perform(get("/api/v1/notifications/stats"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalNotifications", is(2)))
                .andExpect(jsonPath("$.pendingNotifications", is(1)))
                .andExpect(jsonPath("$.sentNotifications", is(1)))
                .andExpect(jsonPath("$.emailNotifications", is(2)));
    }

    @Test
    void health_ReturnsHealthStatus() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/notifications/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status", is("UP")))
                .andExpect(jsonPath("$.service", is("notification-service")))
                .andExpect(jsonPath("$.timestamp", notNullValue()));
    }

    @Test
    void createTestNotification_InvalidRequest() throws Exception {
        // Given - Invalid request with missing required fields
        NotificationTestController.TestNotificationRequest request = new NotificationTestController.TestNotificationRequest();
        // Missing required fields

        // When & Then
        mockMvc.perform(post("/api/v1/notifications/test")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void createTestNotification_WithAllFields() throws Exception {
        // Given
        NotificationTestController.TestNotificationRequest request = new NotificationTestController.TestNotificationRequest();
        request.setNotificationType(NotificationType.SMS);
        request.setPriority(NotificationPriority.URGENT);
        request.setUserId(123L);
        request.setRecipient("+1234567890");
        request.setSubject("Urgent SMS");
        request.setContent("This is an urgent SMS notification");

        // When & Then
        mockMvc.perform(post("/api/v1/notifications/test")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest()) // Should fail because SMS is disabled
                .andExpect(content().string("Failed to send notification"));

        // Verify notification was saved to database
        List<Notification> notifications = notificationRepository.findAll();
        assertEquals(1, notifications.size());
        assertEquals(NotificationType.SMS, notifications.get(0).getNotificationType());
        assertEquals(NotificationPriority.URGENT, notifications.get(0).getPriority());
    }

    private Notification createAndSaveNotification(String eventId, Long userId, String recipient) {
        Notification notification = Notification.builder()
                .eventId(eventId)
                .eventType("TEST_EVENT")
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.NORMAL)
                .userId(userId)
                .recipient(recipient)
                .subject("Test Subject")
                .content("Test Content")
                .serviceName("test-service")
                .status(Notification.NotificationStatus.PENDING)
                .deliveryAttempts(0)
                .maxAttempts(3)
                .build();
        
        return notificationRepository.save(notification);
    }
}
