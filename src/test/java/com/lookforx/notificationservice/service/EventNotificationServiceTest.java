package com.lookforx.notificationservice.service;

import com.lookforx.common.events.UserRegisteredEvent;
import com.lookforx.common.events.FormSubmittedEvent;
import com.lookforx.common.events.RequestCreatedEvent;
import com.lookforx.notificationservice.domain.Notification;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for EventNotificationService
 */
@ExtendWith(MockitoExtension.class)
class EventNotificationServiceTest {

    @Mock
    private NotificationService notificationService;

    @InjectMocks
    private EventNotificationService eventNotificationService;

    @BeforeEach
    void setUp() {
        when(notificationService.sendNotification(any(Notification.class))).thenReturn(true);
    }

    @Test
    void handleUserRegisteredEvent_WithPhoneNumber() {
        // Given
        UserRegisteredEvent event = UserRegisteredEvent.builder()
                .eventId("USER-123")
                .userId(1L)
                .email("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .phoneNumber("+1234567890")
                .verificationToken("abc123")
                .serviceName("auth-service")
                .correlationId("corr-123")
                .build();

        // When
        eventNotificationService.handleUserRegisteredEvent(event);

        // Then
        ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);
        verify(notificationService, times(2)).sendNotification(notificationCaptor.capture());

        // Verify email notification
        Notification emailNotification = notificationCaptor.getAllValues().get(0);
        assertEquals("USER-123", emailNotification.getEventId());
        assertEquals("<EMAIL>", emailNotification.getRecipient());
        assertEquals("Welcome to LookForX - Please verify your email", emailNotification.getSubject());
        assertEquals("user-welcome-email", emailNotification.getTemplateName());

        // Verify SMS notification
        Notification smsNotification = notificationCaptor.getAllValues().get(1);
        assertEquals("USER-123-sms", smsNotification.getEventId());
        assertEquals("+1234567890", smsNotification.getRecipient());
        assertEquals("Welcome to LookForX", smsNotification.getSubject());
    }

    @Test
    void handleUserRegisteredEvent_WithoutPhoneNumber() {
        // Given
        UserRegisteredEvent event = UserRegisteredEvent.builder()
                .eventId("USER-123")
                .userId(1L)
                .email("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .phoneNumber(null)
                .verificationToken("abc123")
                .serviceName("auth-service")
                .correlationId("corr-123")
                .build();

        // When
        eventNotificationService.handleUserRegisteredEvent(event);

        // Then
        verify(notificationService, times(1)).sendNotification(any(Notification.class));
    }

    @Test
    void handleFormSubmittedEvent() {
        // Given
        FormSubmittedEvent event = FormSubmittedEvent.builder()
                .eventId("FORM-123")
                .userId(1L)
                .formSubmissionId("SUB-123")
                .formTemplateName("Contact Form")
                .submitterEmail("<EMAIL>")
                .submitterName("Jane Smith")
                .requestId("REQ-123")
                .responseCount(5)
                .serviceName("form-service")
                .correlationId("corr-123")
                .build();

        // When
        eventNotificationService.handleFormSubmittedEvent(event);

        // Then
        ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);
        verify(notificationService).sendNotification(notificationCaptor.capture());

        Notification notification = notificationCaptor.getValue();
        assertEquals("FORM-123", notification.getEventId());
        assertEquals("<EMAIL>", notification.getRecipient());
        assertEquals("Form submission confirmation - Contact Form", notification.getSubject());
        assertEquals("form-submission-confirmation", notification.getTemplateName());
        assertTrue(notification.getTemplateParameters().containsKey("formName"));
        assertEquals("Contact Form", notification.getTemplateParameters().get("formName"));
    }

    @Test
    void handleRequestCreatedEvent() {
        // Given
        RequestCreatedEvent event = RequestCreatedEvent.builder()
                .eventId("REQ-123")
                .userId(1L)
                .requestId(123L)
                .title("Need a website")
                .categoryName("Web Development")
                .budget(new BigDecimal("1000.00"))
                .currency("USD")
                .deadline("2025-07-01")
                .requesterEmail("<EMAIL>")
                .serviceName("request-service")
                .correlationId("corr-123")
                .build();

        // When
        eventNotificationService.handleRequestCreatedEvent(event);

        // Then
        ArgumentCaptor<Notification> notificationCaptor = ArgumentCaptor.forClass(Notification.class);
        verify(notificationService).sendNotification(notificationCaptor.capture());

        Notification notification = notificationCaptor.getValue();
        assertEquals("REQ-123", notification.getEventId());
        assertEquals("<EMAIL>", notification.getRecipient());
        assertEquals("Request created successfully - Need a website", notification.getSubject());
        assertEquals("request-created-confirmation", notification.getTemplateName());
        assertTrue(notification.getTemplateParameters().containsKey("requestTitle"));
        assertEquals("Need a website", notification.getTemplateParameters().get("requestTitle"));
    }
}
