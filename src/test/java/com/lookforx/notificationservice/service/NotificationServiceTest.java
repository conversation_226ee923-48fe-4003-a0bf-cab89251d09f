package com.lookforx.notificationservice.service;

import com.lookforx.common.enums.NotificationPriority;
import com.lookforx.common.enums.NotificationType;
import com.lookforx.notificationservice.domain.Notification;
import com.lookforx.notificationservice.repository.NotificationRepository;
import com.lookforx.notificationservice.strategy.NotificationStrategy;
import com.lookforx.notificationservice.strategy.NotificationStrategyFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for NotificationService
 */
@ExtendWith(MockitoExtension.class)
class NotificationServiceTest {

    @Mock
    private NotificationRepository notificationRepository;

    @Mock
    private NotificationStrategyFactory strategyFactory;

    @Mock
    private NotificationStrategy mockStrategy;

    @InjectMocks
    private NotificationService notificationService;

    private Notification testNotification;

    @BeforeEach
    void setUp() {
        testNotification = Notification.builder()
                .eventId("TEST-123")
                .eventType("TEST_EVENT")
                .notificationType(NotificationType.EMAIL)
                .priority(NotificationPriority.NORMAL)
                .userId(1L)
                .recipient("<EMAIL>")
                .subject("Test Subject")
                .content("Test Content")
                .serviceName("test-service")
                .status(Notification.NotificationStatus.PENDING)
                .deliveryAttempts(0)
                .maxAttempts(3)
                .build();
    }

    @Test
    void sendNotification_Success() {
        // Given
        when(notificationRepository.save(any(Notification.class))).thenReturn(testNotification);
        when(strategyFactory.getStrategy(NotificationType.EMAIL)).thenReturn(Optional.of(mockStrategy));
        when(mockStrategy.sendNotification(any(Notification.class))).thenReturn(true);

        // When
        boolean result = notificationService.sendNotification(testNotification);

        // Then
        assertTrue(result);
        verify(notificationRepository, times(2)).save(any(Notification.class));
        verify(strategyFactory).getStrategy(NotificationType.EMAIL);
        verify(mockStrategy).sendNotification(any(Notification.class));
    }

    @Test
    void sendNotification_StrategyNotAvailable() {
        // Given
        when(notificationRepository.save(any(Notification.class))).thenReturn(testNotification);
        when(strategyFactory.getStrategy(NotificationType.EMAIL)).thenReturn(Optional.empty());

        // When
        boolean result = notificationService.sendNotification(testNotification);

        // Then
        assertFalse(result);
        verify(notificationRepository, times(2)).save(any(Notification.class));
        verify(strategyFactory).getStrategy(NotificationType.EMAIL);
        verify(mockStrategy, never()).sendNotification(any(Notification.class));
    }

    @Test
    void sendNotification_StrategyFails() {
        // Given
        when(notificationRepository.save(any(Notification.class))).thenReturn(testNotification);
        when(strategyFactory.getStrategy(NotificationType.EMAIL)).thenReturn(Optional.of(mockStrategy));
        when(mockStrategy.sendNotification(any(Notification.class))).thenReturn(false);
        when(mockStrategy.getMaxRetryAttempts()).thenReturn(3);
        when(mockStrategy.getRetryDelayMinutes(1)).thenReturn(1L);

        // When
        boolean result = notificationService.sendNotification(testNotification);

        // Then
        assertFalse(result);
        verify(notificationRepository, times(2)).save(any(Notification.class));
        verify(mockStrategy).sendNotification(any(Notification.class));
    }

    @Test
    void sendNotification_Exception() {
        // Given
        when(notificationRepository.save(any(Notification.class)))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            notificationService.sendNotification(testNotification);
        });

        verify(notificationRepository, atLeastOnce()).save(any(Notification.class));
    }

    @Test
    void getByEventId_Found() {
        // Given
        when(notificationRepository.findByEventId("TEST-123")).thenReturn(Optional.of(testNotification));

        // When
        Optional<Notification> result = notificationService.getByEventId("TEST-123");

        // Then
        assertTrue(result.isPresent());
        assertEquals(testNotification, result.get());
        verify(notificationRepository).findByEventId("TEST-123");
    }

    @Test
    void getByEventId_NotFound() {
        // Given
        when(notificationRepository.findByEventId("NONEXISTENT")).thenReturn(Optional.empty());

        // When
        Optional<Notification> result = notificationService.getByEventId("NONEXISTENT");

        // Then
        assertFalse(result.isPresent());
        verify(notificationRepository).findByEventId("NONEXISTENT");
    }

    @Test
    void getByUserId() {
        // Given
        List<Notification> notifications = Arrays.asList(testNotification);
        when(notificationRepository.findByUserIdOrderByCreatedAtDesc(1L)).thenReturn(notifications);

        // When
        List<Notification> result = notificationService.getByUserId(1L);

        // Then
        assertEquals(1, result.size());
        assertEquals(testNotification, result.get(0));
        verify(notificationRepository).findByUserIdOrderByCreatedAtDesc(1L);
    }

    @Test
    void getByStatus() {
        // Given
        List<Notification> notifications = Arrays.asList(testNotification);
        when(notificationRepository.findByStatus(Notification.NotificationStatus.PENDING))
                .thenReturn(notifications);

        // When
        List<Notification> result = notificationService.getByStatus(Notification.NotificationStatus.PENDING);

        // Then
        assertEquals(1, result.size());
        assertEquals(testNotification, result.get(0));
        verify(notificationRepository).findByStatus(Notification.NotificationStatus.PENDING);
    }

    @Test
    void getByNotificationType() {
        // Given
        List<Notification> notifications = Arrays.asList(testNotification);
        when(notificationRepository.findByNotificationType(NotificationType.EMAIL))
                .thenReturn(notifications);

        // When
        List<Notification> result = notificationService.getByNotificationType(NotificationType.EMAIL);

        // Then
        assertEquals(1, result.size());
        assertEquals(testNotification, result.get(0));
        verify(notificationRepository).findByNotificationType(NotificationType.EMAIL);
    }

    @Test
    void retryFailedNotifications() {
        // Given
        List<Notification> failedNotifications = Arrays.asList(testNotification);
        when(notificationRepository.findFailedNotificationsForRetry(any(LocalDateTime.class)))
                .thenReturn(failedNotifications);
        when(notificationRepository.save(any(Notification.class))).thenReturn(testNotification);
        when(strategyFactory.getStrategy(NotificationType.EMAIL)).thenReturn(Optional.of(mockStrategy));
        when(mockStrategy.sendNotification(any(Notification.class))).thenReturn(true);

        // When
        notificationService.retryFailedNotifications();

        // Then
        verify(notificationRepository).findFailedNotificationsForRetry(any(LocalDateTime.class));
        verify(strategyFactory).getStrategy(NotificationType.EMAIL);
        verify(mockStrategy).sendNotification(any(Notification.class));
    }

    @Test
    void getStats() {
        // Given
        when(notificationRepository.count()).thenReturn(100L);
        when(notificationRepository.countByStatus(Notification.NotificationStatus.PENDING)).thenReturn(10L);
        when(notificationRepository.countByStatus(Notification.NotificationStatus.SENT)).thenReturn(80L);
        when(notificationRepository.countByStatus(Notification.NotificationStatus.FAILED)).thenReturn(10L);
        when(notificationRepository.countByNotificationType(NotificationType.EMAIL)).thenReturn(60L);
        when(notificationRepository.countByNotificationType(NotificationType.SMS)).thenReturn(25L);
        when(notificationRepository.countByNotificationType(NotificationType.PUSH_NOTIFICATION)).thenReturn(15L);

        // When
        NotificationService.NotificationStats stats = notificationService.getStats();

        // Then
        assertEquals(100L, stats.totalNotifications);
        assertEquals(10L, stats.pendingNotifications);
        assertEquals(80L, stats.sentNotifications);
        assertEquals(10L, stats.failedNotifications);
        assertEquals(60L, stats.emailNotifications);
        assertEquals(25L, stats.smsNotifications);
        assertEquals(15L, stats.pushNotifications);
    }

    @Test
    void deleteOldNotifications() {
        // Given
        int daysToKeep = 30;

        // When
        notificationService.deleteOldNotifications(daysToKeep);

        // Then
        // Verify that MongoTemplate was used for deletion (since we're using MongoDB now)
        // The actual verification would depend on how we mock MongoTemplate
    }
}
