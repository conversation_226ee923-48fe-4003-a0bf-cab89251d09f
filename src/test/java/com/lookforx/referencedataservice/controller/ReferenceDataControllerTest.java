package com.lookforx.referencedataservice.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.referencedataservice.base.AbstractRestControllerTest;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.referencedataservice.domain.ReferenceDataType;
import com.lookforx.referencedataservice.dto.ReferenceDataRequest;
import com.lookforx.referencedataservice.dto.ReferenceDataResponse;
import com.lookforx.referencedataservice.service.ReferenceDataService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(
        controllers = ReferenceDataController.class
)
class ReferenceDataControllerTest extends AbstractRestControllerTest {

    private static final String BASE = "/api/v1/reference-data";

    @MockitoBean
    ReferenceDataService service;

    private ReferenceDataResponse sampleDto;
    private ReferenceDataRequest sampleReq;

    @MockitoBean
    ExceptionServiceClient exceptionClient;

    @BeforeEach
    void setUp() {

        sampleDto = ReferenceDataResponse.builder()
                .id(1L)
                .type(ReferenceDataType.COUNTRY)
                .code("TR")
                .translations(Map.of(LanguageCode.EN, "Turkey", LanguageCode.TR, "Türkiye"))
                .properties(Map.of("continent", "Europe"))
                .active(true)
                .displayOrder(100)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        sampleReq = ReferenceDataRequest.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("TR")
                .translations(Map.of(LanguageCode.EN, "Turkey"))
                .properties(Map.of())
                .active(true)
                .displayOrder(100)
                .build();

    }

    @Test
    void getAllReferenceData_ShouldReturnList() throws Exception {
        when(service.getAllReferenceData(any(), any(), any())).thenReturn(new PageImpl<>(List.of(sampleDto)));

        mockMvc.perform(get(BASE)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(service).getAllReferenceData(any(), any(), any());
    }

    @Test
    void getByType_ShouldReturnList() throws Exception {
        when(service.getReferenceDataByType(ReferenceDataType.COUNTRY))
                .thenReturn(List.of(sampleDto));

        mockMvc.perform(get(BASE + "/type/{type}", "COUNTRY")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].type").value("COUNTRY"));

        verify(service).getReferenceDataByType(ReferenceDataType.COUNTRY);
    }

    @Test
    void getById_ShouldReturnDto() throws Exception {
        when(service.getReferenceDataById(1L)).thenReturn(sampleDto);

        mockMvc.perform(get(BASE + "/{id}", 1L)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.code").value("TR"));

        verify(service).getReferenceDataById(1L);
    }

    @Test
    void createReferenceData_ShouldReturnCreated() throws Exception {
        when(service.createReferenceData(any(ReferenceDataRequest.class)))
                .thenReturn(sampleDto);

        mockMvc.perform(post(BASE)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(sampleReq)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.code").value("TR"));

        verify(service).createReferenceData(any(ReferenceDataRequest.class));
    }

    @Test
    void updateReferenceData_ShouldReturnOk() throws Exception {
        ReferenceDataResponse updated = sampleDto.toBuilder()
                .code("US")
                .build();

        ReferenceDataRequest updateReq = sampleReq.toBuilder()
                .code("US")
                .build();

        when(service.updateReferenceData(eq(1L), any(ReferenceDataRequest.class)))
                .thenReturn(updated);

        mockMvc.perform(put(BASE + "/{id}", 1L)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateReq)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("US"));

        verify(service).updateReferenceData(eq(1L), any(ReferenceDataRequest.class));
    }

    @Test
    void toggleReferenceDataStatus_ShouldReturnToggled() throws Exception {
        ReferenceDataResponse toggled = sampleDto.toBuilder()
                .active(false)
                .build();

        when(service.toggleReferenceDataStatus(1L)).thenReturn(toggled);

        mockMvc.perform(patch(BASE + "/{id}/toggle-status", 1L))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.active").value(false));

        verify(service).toggleReferenceDataStatus(1L);
    }

    @Test
    void deleteReferenceData_ShouldReturnNoContent() throws Exception {
        // no stub needed for void
        doNothing().when(service).deleteReferenceData(1L);

        mockMvc.perform(delete(BASE + "/{id}", 1L))
                .andExpect(status().isNoContent());

        verify(service).deleteReferenceData(1L);
    }

    @Test
    void getReferenceDataName_ShouldReturnString() throws Exception {
        when(service.getReferenceDataName(ReferenceDataType.COUNTRY, "TR", LanguageCode.TR))
                .thenReturn("Türkiye");

        mockMvc.perform(get(BASE + "/type/{type}/code/{code}/name", "COUNTRY", "TR")
                        .param("languageCode", "TR")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string("Türkiye"));

        verify(service).getReferenceDataName(ReferenceDataType.COUNTRY, "TR", LanguageCode.TR);
    }

    @Test
    void getReferenceDataByTypePaged_ShouldReturnPage() throws Exception {
        Page<ReferenceDataResponse> page = new PageImpl<>(List.of(sampleDto));
        when(service.getReferenceDataByType(eq(ReferenceDataType.COUNTRY), any(Pageable.class)))
                .thenReturn(page);

        mockMvc.perform(get(BASE + "/type/{type}/paged", "COUNTRY")
                        .param("page", "0")
                        .param("size", "10")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[0].id").value(1));

        verify(service).getReferenceDataByType(eq(ReferenceDataType.COUNTRY), any(Pageable.class));

    }

    @Test
    void getActiveReferenceDataByType_ShouldReturnActiveList() throws Exception {

        // When
        when(service.getActiveReferenceDataByType(ReferenceDataType.COUNTRY))
                .thenReturn(List.of(sampleDto));

        // Then
        mockMvc.perform(get(BASE + "/type/{type}/active", ReferenceDataType.COUNTRY)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].active").value(true))
                .andExpect(jsonPath("$[0].code").value("TR"));

        // Verify
        verify(service).getActiveReferenceDataByType(ReferenceDataType.COUNTRY);

    }

    @Test
    void getActiveReferenceDataByTypePaged_ShouldReturnPage() throws Exception {

        // Given
        Page<ReferenceDataResponse> page = new PageImpl<>(List.of(sampleDto), PageRequest.of(0,1), 1);

        // When
        when(service.getActiveReferenceDataByType(eq(ReferenceDataType.COUNTRY), any(PageRequest.class)))
                .thenReturn(page);

        // Then
        mockMvc.perform(get(BASE + "/type/{type}/active/paged", ReferenceDataType.COUNTRY)
                        .param("page", "0")
                        .param("size", "1")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[0].id").value(1))
                .andExpect(jsonPath("$.totalElements").value(1));

        // Verify
        verify(service).getActiveReferenceDataByType(eq(ReferenceDataType.COUNTRY), any(PageRequest.class));

    }

    @Test
    void getReferenceDataByTypeOrdered_ShouldReturnOrderedList() throws Exception {

        // When
        when(service.getReferenceDataByTypeOrdered(ReferenceDataType.COUNTRY))
                .thenReturn(List.of(sampleDto));

        // Then
        mockMvc.perform(get(BASE + "/type/{type}/ordered", ReferenceDataType.COUNTRY)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].displayOrder").value(100));

        // Verify
        verify(service).getReferenceDataByTypeOrdered(ReferenceDataType.COUNTRY);

    }

    @Test
    void getActiveReferenceDataByTypeOrdered_ShouldReturnOrderedActiveList() throws Exception {

        // When
        when(service.getActiveReferenceDataByTypeOrdered(ReferenceDataType.COUNTRY))
                .thenReturn(List.of(sampleDto));

        // Then
        mockMvc.perform(get(BASE + "/type/{type}/active/ordered", ReferenceDataType.COUNTRY)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$[0].active").value(true));

        // Verify
        verify(service).getActiveReferenceDataByTypeOrdered(ReferenceDataType.COUNTRY);

    }

    @Test
    void getReferenceDataByTypeAndCode_ShouldReturnDto() throws Exception {

        // When
        when(service.getReferenceDataByTypeAndCode(ReferenceDataType.COUNTRY, "TR"))
                .thenReturn(sampleDto);

        // Then
        mockMvc.perform(get(BASE + "/type/{type}/code/{code}", ReferenceDataType.COUNTRY, "TR")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("TR"));

        // Verify
        verify(service).getReferenceDataByTypeAndCode(ReferenceDataType.COUNTRY, "TR");

    }

    @Test
    void getActiveReferenceDataByTypeAndCode_ShouldReturnDto() throws Exception {

        // When
        when(service.getActiveReferenceDataByTypeAndCode(ReferenceDataType.COUNTRY, "TR"))
                .thenReturn(sampleDto);

        // Then
        mockMvc.perform(get(BASE + "/type/{type}/code/{code}/active", ReferenceDataType.COUNTRY, "TR")
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.active").value(true));

        // Verify
        verify(service).getActiveReferenceDataByTypeAndCode(ReferenceDataType.COUNTRY, "TR");

    }


}