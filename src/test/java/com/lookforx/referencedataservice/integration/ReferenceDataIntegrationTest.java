package com.lookforx.referencedataservice.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.referencedataservice.domain.ReferenceDataType;
import com.lookforx.referencedataservice.dto.ReferenceDataRequest;
import com.lookforx.referencedataservice.repository.ReferenceDataRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.util.HashMap;
import java.util.Map;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
@Testcontainers
@Transactional
class ReferenceDataIntegrationTest {

    @Container
    static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>(DockerImageName.parse("postgres:15"))
            .withDatabaseName("testdb")
            .withUsername("test")
            .withPassword("test");

    @Container
    static GenericContainer<?> redis = new GenericContainer<>(DockerImageName.parse("redis:7-alpine"))
            .withExposedPorts(6379);

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "org.postgresql.Driver");
        registry.add("spring.jpa.database-platform", () -> "org.hibernate.dialect.PostgreSQLDialect");
        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", redis::getFirstMappedPort);
        registry.add("spring.cache.type", () -> "redis");
        registry.add("spring.cache.redis.database", () -> "0");
    }

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ReferenceDataRepository repository;

    @BeforeEach
    void setUp() {
        repository.deleteAll();
    }

    @Test
    void shouldCreateReferenceData() throws Exception {
        // Given
        Map<LanguageCode, String> translations = new HashMap<>();
        translations.put(LanguageCode.EN, "Turkey");
        translations.put(LanguageCode.TR, "Türkiye");

        Map<String, Object> properties = new HashMap<>();
        properties.put("continent", "Europe");

        ReferenceDataRequest request = ReferenceDataRequest.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("TR")
                .translations(translations)
                .properties(properties)
                .active(true)
                .displayOrder(1)
                .build();

        // When & Then
        mockMvc.perform(post("/api/v1/reference-data")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.type").value("COUNTRY"))
                .andExpect(jsonPath("$.code").value("TR"))
                .andExpect(jsonPath("$.translations.en").value("Turkey"))
                .andExpect(jsonPath("$.translations.tr").value("Türkiye"))
                .andExpect(jsonPath("$.properties.continent").value("Europe"))
                .andExpect(jsonPath("$.active").value(true))
                .andExpect(jsonPath("$.displayOrder").value(1))
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.createdAt").exists())
                .andExpect(jsonPath("$.updatedAt").exists());
    }

    @Test
    void shouldGetAllReferenceData() throws Exception {
        // Given - create some test data
        createTestCountry("TR", "Turkey", "Türkiye");
        createTestCountry("US", "United States", "Amerika Birleşik Devletleri");

        // When & Then
        mockMvc.perform(get("/api/v1/reference-data"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(2)))
                .andExpect(jsonPath("$.content[0].code").value("US"))
                .andExpect(jsonPath("$.content[1].code").value("TR"));
    }

    @Test
    void shouldGetReferenceDataWithPagination() throws Exception {
        // Given - create test data
        for (int i = 0; i < 5; i++) {
            createTestCountry("C" + i, "Country " + i, "Ülke " + i);
        }

        // When & Then
        mockMvc.perform(get("/api/v1/reference-data")
                        .param("page", "0")
                        .param("size", "3"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(3)))
                .andExpect(jsonPath("$.totalElements").value(5))
                .andExpect(jsonPath("$.totalPages").value(2))
                .andExpect(jsonPath("$.first").value(true))
                .andExpect(jsonPath("$.last").value(false));
    }

    @Test
    void shouldSearchReferenceData() throws Exception {
        // Given
        createTestCountry("TR", "Turkey", "Türkiye");
        createTestCountry("US", "United States", "Amerika Birleşik Devletleri");

        // When & Then - search by code
        mockMvc.perform(get("/api/v1/reference-data")
                        .param("search", "tr")
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(1)))
                .andExpect(jsonPath("$.content[0].code").value("TR"));
    }

    @Test
    void shouldFilterByType() throws Exception {
        // Given
        createTestCountry("TR", "Turkey", "Türkiye");
        createTestLanguage("EN", "English", "İngilizce");

        // When & Then
        mockMvc.perform(get("/api/v1/reference-data")
                        .param("type", "COUNTRY")
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(1)))
                .andExpect(jsonPath("$.content[0].type").value("COUNTRY"))
                .andExpect(jsonPath("$.content[0].code").value("TR"));
    }

    @Test
    void shouldGetReferenceDataById() throws Exception {
        // Given
        Long id = createTestCountry("TR", "Turkey", "Türkiye");

        // When & Then
        mockMvc.perform(get("/api/v1/reference-data/{id}", id))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(id))
                .andExpect(jsonPath("$.code").value("TR"))
                .andExpect(jsonPath("$.translations.en").value("Turkey"));
    }

    @Test
    void shouldUpdateReferenceData() throws Exception {
        // Given
        Long id = createTestCountry("TR", "Turkey", "Türkiye");

        Map<LanguageCode, String> updateTranslations = new HashMap<>();
        updateTranslations.put(LanguageCode.EN, "Republic of Turkey");
        updateTranslations.put(LanguageCode.TR, "Türkiye Cumhuriyeti");

        Map<String, Object> updateProperties = new HashMap<>();
        updateProperties.put("continent", "Eurasia");

        ReferenceDataRequest updateRequest = ReferenceDataRequest.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("TR")
                .translations(updateTranslations)
                .properties(updateProperties)
                .active(true)
                .displayOrder(1)
                .build();

        // When & Then
        mockMvc.perform(put("/api/v1/reference-data/{id}", id)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.translations.en").value("Republic of Turkey"))
                .andExpect(jsonPath("$.translations.tr").value("Türkiye Cumhuriyeti"))
                .andExpect(jsonPath("$.properties.continent").value("Eurasia"));
    }

    @Test
    void shouldDeleteReferenceData() throws Exception {
        // Given
        Long id = createTestCountry("TR", "Turkey", "Türkiye");

        // When & Then
        mockMvc.perform(delete("/api/v1/reference-data/{id}", id))
                .andExpect(status().isNoContent());

        // Verify deletion
        mockMvc.perform(get("/api/v1/reference-data/{id}", id))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldGetByTypeAndCode() throws Exception {
        // Given
        createTestCountry("TR", "Turkey", "Türkiye");

        // When & Then
        mockMvc.perform(get("/api/v1/reference-data/type/{type}/code/{code}", "COUNTRY", "TR"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.type").value("COUNTRY"))
                .andExpect(jsonPath("$.code").value("TR"))
                .andExpect(jsonPath("$.translations.en").value("Turkey"));
    }

    @Test
    void shouldReturnNotFoundForNonExistentData() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/reference-data/{id}", 999L))
                .andExpect(status().isNotFound());

        mockMvc.perform(get("/api/v1/reference-data/type/{type}/code/{code}", "COUNTRY", "XX"))
                .andExpect(status().isNotFound());
    }

    @Test
    void shouldValidateRequiredFields() throws Exception {
        // Given - request with missing required fields
        ReferenceDataRequest invalidRequest = ReferenceDataRequest.builder()
                .code("TR")
                // Missing type and translations
                .build();

        // When & Then
        mockMvc.perform(post("/api/v1/reference-data")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest());
    }

    @Test
    void shouldClearCache() throws Exception {
        // Given
        createTestCountry("TR", "Turkey", "Türkiye");

        // When & Then
        mockMvc.perform(post("/api/v1/reference-data/cache/clear"))
                .andExpect(status().isOk())
                .andExpect(content().string("Cache cleared successfully"));
    }

    private Long createTestCountry(String code, String englishName, String turkishName) throws Exception {
        Map<LanguageCode, String> translations = new HashMap<>();
        translations.put(LanguageCode.EN, englishName);
        translations.put(LanguageCode.TR, turkishName);

        Map<String, Object> properties = new HashMap<>();
        properties.put("continent", "Europe");

        ReferenceDataRequest request = ReferenceDataRequest.builder()
                .type(ReferenceDataType.COUNTRY)
                .code(code)
                .translations(translations)
                .properties(properties)
                .active(true)
                .displayOrder(1)
                .build();

        String response = mockMvc.perform(post("/api/v1/reference-data")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString();

        return objectMapper.readTree(response).get("id").asLong();
    }

    private Long createTestLanguage(String code, String englishName, String turkishName) throws Exception {
        Map<LanguageCode, String> translations = new HashMap<>();
        translations.put(LanguageCode.EN, englishName);
        translations.put(LanguageCode.TR, turkishName);

        Map<String, Object> properties = new HashMap<>();
        properties.put("nativeName", englishName);

        ReferenceDataRequest request = ReferenceDataRequest.builder()
                .type(ReferenceDataType.LANGUAGE)
                .code(code)
                .translations(translations)
                .properties(properties)
                .active(true)
                .displayOrder(1)
                .build();

        String response = mockMvc.perform(post("/api/v1/reference-data")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString();

        return objectMapper.readTree(response).get("id").asLong();
    }
}
