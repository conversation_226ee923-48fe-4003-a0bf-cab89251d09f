package com.lookforx.referencedataservice.service;

import java.time.LocalDateTime;
import java.util.*;

import com.lookforx.referencedataservice.base.AbstractBaseServiceTest;
import com.lookforx.common.enums.LanguageCode;
import com.lookforx.referencedataservice.domain.ReferenceData;
import com.lookforx.referencedataservice.domain.ReferenceDataType;
import com.lookforx.referencedataservice.dto.ReferenceDataRequest;
import com.lookforx.referencedataservice.dto.ReferenceDataResponse;
import com.lookforx.common.exception.ResourceNotFoundException;
import com.lookforx.referencedataservice.repository.ReferenceDataRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ReferenceDataServiceTest extends AbstractBaseServiceTest {

    @InjectMocks
    private ReferenceDataService service;

    @Mock
    private ReferenceDataRepository repository;

    private ReferenceData sampleEntity;

    @BeforeEach
    void setUp() {
        sampleEntity = new ReferenceData();
        sampleEntity.setId(1L);
        sampleEntity.setType(ReferenceDataType.COUNTRY);
        sampleEntity.setCode("TR");
        sampleEntity.setTranslations(Map.of(LanguageCode.EN, "Turkey", LanguageCode.TR, "Türkiye"));
        sampleEntity.setPropertiesFromMap(Map.of("continent", "Europe"));
        sampleEntity.setActive(true);
        sampleEntity.setDisplayOrder(10);
        sampleEntity.setCreatedAt(LocalDateTime.of(2025, 6, 1, 12, 0));
        sampleEntity.setUpdatedAt(LocalDateTime.of(2025, 6, 2, 12, 0));
    }

    @Test
    void testGetAllReferenceData() {
        when(repository.findAll()).thenReturn(List.of(sampleEntity));

        List<ReferenceDataResponse> result = service.getAllReferenceData();

        assertEquals(1, result.size());
        ReferenceDataResponse dto = result.get(0);
        assertEquals(sampleEntity.getId(), dto.getId());
        assertEquals(sampleEntity.getType(), dto.getType());
        assertEquals(sampleEntity.getCode(), dto.getCode());
        verify(repository).findAll();
    }

    @Test
    void testGetReferenceDataByType() {
        when(repository.findByType(ReferenceDataType.COUNTRY))
                .thenReturn(List.of(sampleEntity));

        List<ReferenceDataResponse> result = service.getReferenceDataByType(ReferenceDataType.COUNTRY);

        assertFalse(result.isEmpty());
        assertEquals(ReferenceDataType.COUNTRY, result.get(0).getType());
        verify(repository).findByType(ReferenceDataType.COUNTRY);
    }

    @Test
    void testGetReferenceDataById_Success() {
        when(repository.findById(1L)).thenReturn(Optional.of(sampleEntity));

        ReferenceDataResponse dto = service.getReferenceDataById(1L);

        assertEquals(1L, dto.getId());
        assertEquals("TR", dto.getCode());
        verify(repository).findById(1L);
    }

    @Test
    void testGetReferenceDataById_NotFound() {
        when(repository.findById(99L)).thenReturn(Optional.empty());

        assertThrows(ResourceNotFoundException.class, () -> service.getReferenceDataById(99L));
        verify(repository).findById(99L);
    }

    @Test
    void testCreateReferenceData() {
        ReferenceDataRequest req = ReferenceDataRequest.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("US")
                .translations(Map.of(LanguageCode.EN, "United States"))
                .properties(Map.of())
                .active(true)
                .displayOrder(5)
                .build();

        ReferenceData toSave = new ReferenceData();
        toSave.setType(req.getType());
        toSave.setCode(req.getCode());
        toSave.setTranslations(req.getTranslations());
        toSave.setPropertiesFromMap(req.getProperties());
        toSave.setActive(req.isActive());
        toSave.setDisplayOrder(req.getDisplayOrder());

        ReferenceData saved = new ReferenceData();
        saved.setId(2L);
        saved.setType(req.getType());
        saved.setCode(req.getCode());
        saved.setTranslations(req.getTranslations());
        saved.setPropertiesFromMap(req.getProperties());
        saved.setActive(req.isActive());
        saved.setDisplayOrder(req.getDisplayOrder());
        saved.setCreatedAt(LocalDateTime.now());
        saved.setUpdatedAt(LocalDateTime.now());

        when(repository.save(any(ReferenceData.class))).thenReturn(saved);

        ReferenceDataResponse dto = service.createReferenceData(req);

        assertEquals(2L, dto.getId());
        assertEquals("US", dto.getCode());
        verify(repository).save(any(ReferenceData.class));
    }

    @Test
    void testUpdateReferenceData() {
        ReferenceDataRequest req = ReferenceDataRequest.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("DE")
                .translations(Map.of(LanguageCode.EN, "Germany"))
                .properties(Map.of())
                .active(false)
                .displayOrder(20)
                .build();

        when(repository.findById(1L)).thenReturn(Optional.of(sampleEntity));
        when(repository.save(any(ReferenceData.class))).thenAnswer(invocation -> invocation.getArgument(0));

        ReferenceDataResponse dto = service.updateReferenceData(1L, req);

        assertEquals("DE", dto.getCode());
        assertFalse(dto.isActive());
        verify(repository).findById(1L);
        verify(repository).save(any(ReferenceData.class));
    }

    @Test
    void testToggleReferenceDataStatus() {
        when(repository.findById(1L)).thenReturn(Optional.of(sampleEntity));
        when(repository.save(any(ReferenceData.class))).thenAnswer(inv -> inv.getArgument(0));

        ReferenceDataResponse dto = service.toggleReferenceDataStatus(1L);

        assertFalse(dto.isActive());  // was true, now toggled to false
        verify(repository).findById(1L);
        verify(repository).save(any(ReferenceData.class));
    }

    @Test
    void testDeleteReferenceData_Success() {
        when(repository.existsById(1L)).thenReturn(true);

        assertDoesNotThrow(() -> service.deleteReferenceData(1L));
        verify(repository).existsById(1L);
        verify(repository).deleteById(1L);
    }

    @Test
    void testDeleteReferenceData_NotFound() {

        when(repository.existsById(99L)).thenReturn(false);

        assertThrows(ResourceNotFoundException.class, () -> service.deleteReferenceData(99L));
        verify(repository).existsById(99L);
        verify(repository, never()).deleteById(anyLong());

    }

    @Test
    void testGetActiveReferenceDataByType() {
        when(repository.findByTypeAndActiveTrue(ReferenceDataType.COUNTRY))
                .thenReturn(List.of(sampleEntity));

        List<ReferenceDataResponse> result = service.getActiveReferenceDataByType(ReferenceDataType.COUNTRY);

        assertFalse(result.isEmpty());
        assertTrue(result.stream().allMatch(ReferenceDataResponse::isActive));
        verify(repository).findByTypeAndActiveTrue(ReferenceDataType.COUNTRY);
    }

    @Test
    void testGetReferenceDataByTypePaged() {
        Pageable pageable = PageRequest.of(0, 10);
        Page<ReferenceData> page = new PageImpl<>(List.of(sampleEntity), pageable, 1);
        when(repository.findByType(ReferenceDataType.COUNTRY, pageable)).thenReturn(page);

        Page<ReferenceDataResponse> result = service.getReferenceDataByType(ReferenceDataType.COUNTRY, pageable);

        assertEquals(1, result.getTotalElements());
        assertEquals(sampleEntity.getId(), result.getContent().get(0).getId());
        verify(repository).findByType(ReferenceDataType.COUNTRY, pageable);
    }

    @Test
    void testGetActiveReferenceDataByTypePaged() {
        Pageable pageable = PageRequest.of(0, 10);
        Page<ReferenceData> page = new PageImpl<>(List.of(sampleEntity), pageable, 1);
        when(repository.findByTypeAndActiveTrue(ReferenceDataType.COUNTRY, pageable)).thenReturn(page);

        Page<ReferenceDataResponse> result = service.getActiveReferenceDataByType(ReferenceDataType.COUNTRY, pageable);

        assertEquals(1, result.getTotalElements());
        assertTrue(result.getContent().get(0).isActive());
        verify(repository).findByTypeAndActiveTrue(ReferenceDataType.COUNTRY, pageable);
    }
    
    @Test
    void testGetReferenceDataByTypeAndCode_Success() {
        
        // When
        when(repository.findByTypeAndCode(ReferenceDataType.COUNTRY, "TR"))
                .thenReturn(Optional.of(sampleEntity));
        
        // Then
        ReferenceDataResponse dto = service.getReferenceDataByTypeAndCode(ReferenceDataType.COUNTRY, "TR");
        
        assertEquals("TR", dto.getCode());
        
        // Verify
        verify(repository).findByTypeAndCode(ReferenceDataType.COUNTRY, "TR");
        
    }

    @Test
    void testGetReferenceDataByTypeAndCode_NotFound() {
        
        // When
        when(repository.findByTypeAndCode(ReferenceDataType.COUNTRY, "XX"))
                .thenReturn(Optional.empty());
        
        // Then
        assertThrows(ResourceNotFoundException.class,
                () -> service.getReferenceDataByTypeAndCode(ReferenceDataType.COUNTRY, "XX"));
        
        // Verify
        verify(repository).findByTypeAndCode(ReferenceDataType.COUNTRY, "XX");
        
    }

    @Test
    void testGetActiveReferenceDataByTypeAndCode_Success() {
        
        // When
        when(repository.findByTypeAndCodeAndActiveTrue(ReferenceDataType.COUNTRY, "TR"))
                .thenReturn(Optional.of(sampleEntity));

        // Then
        ReferenceDataResponse dto = service.getActiveReferenceDataByTypeAndCode(ReferenceDataType.COUNTRY, "TR");

        assertEquals("TR", dto.getCode());
        
        // Verify
        verify(repository).findByTypeAndCodeAndActiveTrue(ReferenceDataType.COUNTRY, "TR");
        
    }

    @Test
    void testGetActiveReferenceDataByTypeAndCode_NotFound() {

        // When
        when(repository.findByTypeAndCodeAndActiveTrue(ReferenceDataType.COUNTRY, "TR"))
                .thenReturn(Optional.empty());

        // Then
        assertThrows(ResourceNotFoundException.class,
                () -> service.getActiveReferenceDataByTypeAndCode(ReferenceDataType.COUNTRY, "TR"));

        // Verify
        verify(repository).findByTypeAndCodeAndActiveTrue(ReferenceDataType.COUNTRY, "TR");

    }

    @Test
    void testGetReferenceDataByTypeOrdered() {
        
        // Given
        ReferenceData second = new ReferenceData(); 
        second.setId(2L); 
        second.setType(ReferenceDataType.COUNTRY);
        second.setCode("DE"); 
        second.setActive(true); 
        second.setDisplayOrder(5);
        
        // When
        when(repository.findByTypeOrderByDisplayOrderAsc(ReferenceDataType.COUNTRY))
                .thenReturn(List.of(second, sampleEntity));
        
        // Then
        List<ReferenceDataResponse> referenceDataByTypeOrdered = service.getReferenceDataByTypeOrdered(ReferenceDataType.COUNTRY);
        assertEquals(2, referenceDataByTypeOrdered.size());
        assertEquals(5, referenceDataByTypeOrdered.get(0).getDisplayOrder());
        
        // Verify
        verify(repository).findByTypeOrderByDisplayOrderAsc(ReferenceDataType.COUNTRY);
        
    }

    @Test
    void testGetActiveReferenceDataByTypeOrdered() {
        
        // Given
        ReferenceData second = new ReferenceData(); 
        second.setId(2L); 
        second.setType(ReferenceDataType.COUNTRY);
        second.setCode("DE"); 
        second.setActive(true); 
        second.setDisplayOrder(5);
        
        // When
        when(repository.findByTypeAndActiveTrueOrderByDisplayOrderAsc(ReferenceDataType.COUNTRY))
                .thenReturn(List.of(second, sampleEntity));
        
        // Then
        List<ReferenceDataResponse> activeReferenceDataByTypeOrdered = service.getActiveReferenceDataByTypeOrdered(ReferenceDataType.COUNTRY);
        assertTrue(activeReferenceDataByTypeOrdered.stream().allMatch(ReferenceDataResponse::isActive));
        assertEquals(2, activeReferenceDataByTypeOrdered.size());
        
        // Verify
        verify(repository).findByTypeAndActiveTrueOrderByDisplayOrderAsc(ReferenceDataType.COUNTRY);
        
    }

    @Test
    void testGetReferenceDataName_ExistingLanguage() {
        
        // When
        when(repository.findByTypeAndCode(ReferenceDataType.COUNTRY, "TR"))
                .thenReturn(Optional.of(sampleEntity));
        
        // Then
        String name = service.getReferenceDataName(ReferenceDataType.COUNTRY, "TR", LanguageCode.TR);
        assertEquals("Türkiye", name);
        
        // Verify
        verify(repository).findByTypeAndCode(ReferenceDataType.COUNTRY, "TR");
        
    }

    @Test
    void testGetReferenceDataName_FallbackToEnglish() {
        
        // When
        when(repository.findByTypeAndCode(ReferenceDataType.COUNTRY, "TR"))
                .thenReturn(Optional.of(sampleEntity));
        
        // Then
        String name = service.getReferenceDataName(ReferenceDataType.COUNTRY, "TR", LanguageCode.DE);
        assertEquals("Turkey", name);

        // Verify
        verify(repository).findByTypeAndCode(ReferenceDataType.COUNTRY, "TR");

    }

    @Test
    void testGetReferenceDataName_Unknown() {

        // Given
        ReferenceData onlyTr = new ReferenceData();
        onlyTr.setType(ReferenceDataType.COUNTRY);
        onlyTr.setCode("TR");
        onlyTr.setTranslations(Map.of(LanguageCode.TR, "Türkiye"));

        // When
        when(repository.findByTypeAndCode(ReferenceDataType.COUNTRY, "TR"))
                .thenReturn(Optional.of(onlyTr));

        // Then
        String name = service.getReferenceDataName(ReferenceDataType.COUNTRY, "TR", LanguageCode.DE);
        assertEquals("Unknown", name);

        // Verify
        verify(repository).findByTypeAndCode(ReferenceDataType.COUNTRY, "TR");

    }

}