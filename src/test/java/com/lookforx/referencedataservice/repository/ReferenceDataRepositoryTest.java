package com.lookforx.referencedataservice.repository;

import com.lookforx.common.enums.LanguageCode;
import com.lookforx.referencedataservice.domain.ReferenceData;
import com.lookforx.referencedataservice.domain.ReferenceDataType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

@DataJpaTest
@ActiveProfiles("test")
class ReferenceDataRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private ReferenceDataRepository repository;

    private ReferenceData sampleCountry;
    private ReferenceData sampleLanguage;

    @BeforeEach
    void setUp() {
        // Create sample country
        Map<LanguageCode, String> countryTranslations = new HashMap<>();
        countryTranslations.put(LanguageCode.EN, "Turkey");
        countryTranslations.put(LanguageCode.TR, "Türkiye");

        sampleCountry = ReferenceData.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("TR")
                .translations(countryTranslations)
                .active(true)
                .displayOrder(1)
                .build();

        Map<String, Object> countryProps = new HashMap<>();
        countryProps.put("continent", "Europe");
        sampleCountry.setPropertiesFromMap(countryProps);

        // Create sample language
        Map<LanguageCode, String> languageTranslations = new HashMap<>();
        languageTranslations.put(LanguageCode.EN, "English");
        languageTranslations.put(LanguageCode.TR, "İngilizce");

        sampleLanguage = ReferenceData.builder()
                .type(ReferenceDataType.LANGUAGE)
                .code("EN")
                .translations(languageTranslations)
                .active(true)
                .displayOrder(1)
                .build();

        Map<String, Object> languageProps = new HashMap<>();
        languageProps.put("nativeName", "English");
        sampleLanguage.setPropertiesFromMap(languageProps);

        entityManager.persistAndFlush(sampleCountry);
        entityManager.persistAndFlush(sampleLanguage);
    }

    @Test
    void shouldFindByType() {
        // When
        List<ReferenceData> countries = repository.findByType(ReferenceDataType.COUNTRY);
        List<ReferenceData> languages = repository.findByType(ReferenceDataType.LANGUAGE);

        // Then
        assertThat(countries).hasSize(1);
        assertThat(countries.get(0).getCode()).isEqualTo("TR");
        
        assertThat(languages).hasSize(1);
        assertThat(languages.get(0).getCode()).isEqualTo("EN");
    }

    @Test
    void shouldFindByTypeAndCode() {
        // When
        Optional<ReferenceData> found = repository.findByTypeAndCode(ReferenceDataType.COUNTRY, "TR");
        Optional<ReferenceData> notFound = repository.findByTypeAndCode(ReferenceDataType.COUNTRY, "US");

        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getTranslations().get(LanguageCode.EN)).isEqualTo("Turkey");
        
        assertThat(notFound).isEmpty();
    }

    @Test
    void shouldFindByTypeAndActive() {
        // Given - create inactive reference data
        Map<LanguageCode, String> inactiveTranslations = new HashMap<>();
        inactiveTranslations.put(LanguageCode.EN, "United States");

        ReferenceData inactive = ReferenceData.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("US")
                .translations(inactiveTranslations)
                .active(false)
                .displayOrder(2)
                .build();
        entityManager.persistAndFlush(inactive);

        // When
        List<ReferenceData> activeCountries = repository.findByTypeAndActive(ReferenceDataType.COUNTRY, true);
        List<ReferenceData> inactiveCountries = repository.findByTypeAndActive(ReferenceDataType.COUNTRY, false);

        // Then
        assertThat(activeCountries).hasSize(1);
        assertThat(activeCountries.get(0).getCode()).isEqualTo("TR");
        
        assertThat(inactiveCountries).hasSize(1);
        assertThat(inactiveCountries.get(0).getCode()).isEqualTo("US");
    }

    @Test
    void shouldFindByTypeOrderByDisplayOrder() {
        // Given - create another country with different display order
        Map<LanguageCode, String> germanyTranslations = new HashMap<>();
        germanyTranslations.put(LanguageCode.EN, "Germany");

        ReferenceData germany = ReferenceData.builder()
                .type(ReferenceDataType.COUNTRY)
                .code("DE")
                .translations(germanyTranslations)
                .active(true)
                .displayOrder(0) // Lower order should come first
                .build();
        entityManager.persistAndFlush(germany);

        // When
        List<ReferenceData> countries = repository.findByTypeOrderByDisplayOrder(ReferenceDataType.COUNTRY);

        // Then
        assertThat(countries).hasSize(2);
        assertThat(countries.get(0).getCode()).isEqualTo("DE"); // displayOrder = 0
        assertThat(countries.get(1).getCode()).isEqualTo("TR"); // displayOrder = 1
    }

    @Test
    void shouldSearchByCodeContainingIgnoreCase() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<ReferenceData> result = repository.findByCodeContainingIgnoreCase("tr", pageable);

        // Then
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getCode()).isEqualTo("TR");
    }

    @Test
    void shouldSearchByTypeAndCodeContainingIgnoreCase() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<ReferenceData> result = repository.findByTypeAndCodeContainingIgnoreCase(
                ReferenceDataType.COUNTRY, "tr", pageable);

        // Then
        assertThat(result.getContent()).hasSize(1);
        assertThat(result.getContent().get(0).getCode()).isEqualTo("TR");
        assertThat(result.getContent().get(0).getType()).isEqualTo(ReferenceDataType.COUNTRY);
    }

    @Test
    void shouldFindByTypeWithPagination() {
        // Given - create more countries
        for (int i = 0; i < 5; i++) {
            Map<LanguageCode, String> countryTranslations = new HashMap<>();
            countryTranslations.put(LanguageCode.EN, "Country " + i);

            ReferenceData country = ReferenceData.builder()
                    .type(ReferenceDataType.COUNTRY)
                    .code("C" + i)
                    .translations(countryTranslations)
                    .active(true)
                    .displayOrder(i + 10)
                    .build();
            entityManager.persistAndFlush(country);
        }

        // When
        Pageable pageable = PageRequest.of(0, 3);
        Page<ReferenceData> result = repository.findByType(ReferenceDataType.COUNTRY, pageable);

        // Then
        assertThat(result.getContent()).hasSize(3);
        assertThat(result.getTotalElements()).isEqualTo(6); // 1 original + 5 new
        assertThat(result.getTotalPages()).isEqualTo(2);
        assertThat(result.hasNext()).isTrue();
    }

    @Test
    void shouldSaveAndRetrieveWithTranslations() {
        // Given
        Map<LanguageCode, String> currencyTranslations = new HashMap<>();
        currencyTranslations.put(LanguageCode.EN, "US Dollar");
        currencyTranslations.put(LanguageCode.TR, "ABD Doları");
        currencyTranslations.put(LanguageCode.DE, "US-Dollar");

        ReferenceData currency = ReferenceData.builder()
                .type(ReferenceDataType.CURRENCY)
                .code("USD")
                .translations(currencyTranslations)
                .active(true)
                .displayOrder(1)
                .build();

        Map<String, Object> currencyProps = new HashMap<>();
        currencyProps.put("symbol", "$");
        currencyProps.put("decimals", 2);
        currency.setPropertiesFromMap(currencyProps);

        // When
        ReferenceData saved = repository.save(currency);
        entityManager.flush();
        entityManager.clear();
        
        Optional<ReferenceData> retrieved = repository.findById(saved.getId());

        // Then
        assertThat(retrieved).isPresent();
        ReferenceData found = retrieved.get();
        
        assertThat(found.getType()).isEqualTo(ReferenceDataType.CURRENCY);
        assertThat(found.getCode()).isEqualTo("USD");
        assertThat(found.getTranslations()).hasSize(3);
        assertThat(found.getTranslations().get(LanguageCode.EN)).isEqualTo("US Dollar");
        assertThat(found.getTranslations().get(LanguageCode.TR)).isEqualTo("ABD Doları");
        assertThat(found.getTranslations().get(LanguageCode.DE)).isEqualTo("US-Dollar");
        
        Map<String, Object> properties = found.getPropertiesAsMap();
        assertThat(properties.get("symbol")).isEqualTo("$");
        assertThat(properties.get("decimals")).isEqualTo(2);
        
        // BaseEntity fields should be populated
        assertThat(found.getCreatedAt()).isNotNull();
        assertThat(found.getUpdatedAt()).isNotNull();
    }

    @Test
    void shouldUpdateTimestampsOnUpdate() throws InterruptedException {
        // Given
        ReferenceData saved = repository.save(sampleCountry);
        entityManager.flush();
        
        // Wait a bit to ensure different timestamps
        Thread.sleep(10);
        
        // When
        saved.setDisplayOrder(999);
        ReferenceData updated = repository.save(saved);
        entityManager.flush();

        // Then
        assertThat(updated.getUpdatedAt()).isAfter(updated.getCreatedAt());
    }
}
