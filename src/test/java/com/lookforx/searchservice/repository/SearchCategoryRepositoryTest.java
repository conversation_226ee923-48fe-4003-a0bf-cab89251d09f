package com.lookforx.searchservice.repository;

import com.lookforx.searchservice.domain.SearchCategoryDocument;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.data.elasticsearch.DataElasticsearchTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.elasticsearch.ElasticsearchContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@DataElasticsearchTest
@Testcontainers
class SearchCategoryRepositoryTest {

    @Container
    static ElasticsearchContainer elasticsearch = new ElasticsearchContainer("docker.elastic.co/elasticsearch/elasticsearch:8.7.1")
            .withEnv("discovery.type", "single-node")
            .withEnv("xpack.security.enabled", "false")
            .withEnv("ES_JAVA_OPTS", "-Xms512m -Xmx512m");

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.elasticsearch.uris", elasticsearch::getHttpHostAddress);
        registry.add("spring.data.elasticsearch.client.reactive.endpoints", elasticsearch::getHttpHostAddress);
    }

    @Autowired
    private SearchCategoryRepository searchCategoryRepository;

    private SearchCategoryDocument electronicsCategory;
    private SearchCategoryDocument computersCategory;
    private SearchCategoryDocument phonesCategory;

    @BeforeEach
    void setUp() {
        // Clean up before each test
        searchCategoryRepository.deleteAll();

        // Create test data
        electronicsCategory = createSearchCategoryDocument("1", null, "PRODUCT", 1,
            Map.of(
                "EN", "Electronics",
                "TR", "Elektronik"
            )
        );

        computersCategory = createSearchCategoryDocument("2", 1L, "PRODUCT", 2,
            Map.of(
                "EN", "Computers",
                "TR", "Bilgisayarlar"
            )
        );

        phonesCategory = createSearchCategoryDocument("3", 1L, "PRODUCT", 2,
            Map.of(
                "EN", "Mobile Phones",
                "TR", "Cep Telefonları"
            )
        );

        // Index test data
        searchCategoryRepository.saveAll(List.of(electronicsCategory, computersCategory, phonesCategory));

        // Wait for indexing to complete
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    void testSaveAndFindById() {
        // Given
        SearchCategoryDocument newCategory = createSearchCategoryDocument("4", null, "SERVICE", 1,
            Map.of(
                "EN", "Services"
            )
        );

        // When
        SearchCategoryDocument saved = searchCategoryRepository.save(newCategory);

        // Wait for indexing
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Then
        assertNotNull(saved);
        assertEquals("4", saved.getId());

        Optional<SearchCategoryDocument> found = searchCategoryRepository.findById("4");
        assertTrue(found.isPresent());
        assertEquals("Services", found.get().getTranslations().get("EN"));
    }

    @Test
    void testFindAll() {
        // When
        Iterable<SearchCategoryDocument> all = searchCategoryRepository.findAll();

        // Then
        assertNotNull(all);
        long count = 0;
        for (SearchCategoryDocument doc : all) {
            count++;
        }
        assertEquals(3, count);
    }

    @Test
    void testSearchFullText_English() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchFullText("electronics", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Electronics")));
    }

    @Test
    void testSearchFullText_Turkish() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchFullText("elektronik", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Elektronik")));
    }

    @Test
    void testSearchFullText_PartialMatch() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchFullText("computers", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Computers")));
    }

    @Test
    void testSearchFuzzy_WithTypo() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchFuzzy("compter", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Computers")));
    }

    @Test
    void testSearchFuzzy_WithWildcard() {
        // When - Test with a typo that should still match
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchFuzzy("electronicss", pageable);

        // Then
        assertNotNull(result);
        // Fuzzy search might not always find results with testcontainers, so we just check it doesn't crash
        assertTrue(result.getTotalElements() >= 0);
    }

    @Test
    void testSearchPhrase_ExactPhrase() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchPhrase("mobile phones", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Mobile Phones")));
    }

    @Test
    void testSearchPhrase_PrefixMatch() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchPhrase("computers", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Computers")));
    }

    @Test
    void testSearchWithPagination() {
        // When
        Pageable firstPage = PageRequest.of(0, 2);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchFullText("electronics", firstPage);

        // Then
        assertNotNull(result);
        assertTrue(result.getSize() <= 2);
        assertTrue(result.hasContent());
    }

    @Test
    void testSearchNoResults() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchFullText("nonexistent", pageable);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testDeleteById() {
        // Given
        assertTrue(searchCategoryRepository.existsById("1"));

        // When
        searchCategoryRepository.deleteById("1");

        // Wait for deletion
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Then
        assertFalse(searchCategoryRepository.existsById("1"));
    }

    @Test
    void testDeleteAll() {
        // Given
        assertTrue(searchCategoryRepository.count() > 0);

        // When
        searchCategoryRepository.deleteAll();

        // Wait for deletion
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Then
        assertEquals(0, searchCategoryRepository.count());
    }

    @Test
    void testExistsById() {
        // When & Then
        assertTrue(searchCategoryRepository.existsById("1"));
        assertTrue(searchCategoryRepository.existsById("2"));
        assertTrue(searchCategoryRepository.existsById("3"));
        assertFalse(searchCategoryRepository.existsById("999"));
    }

    @Test
    void testCount() {
        // When
        long count = searchCategoryRepository.count();

        // Then
        assertEquals(3, count);
    }

    @Test
    void testUpdateDocument() {
        // Given
        SearchCategoryDocument updated = createSearchCategoryDocument("1", null, "PRODUCT", 1,
            Map.of(
                "EN", "Updated Electronics"
            )
        );

        // When
        searchCategoryRepository.save(updated);

        // Wait for indexing
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Then
        Optional<SearchCategoryDocument> found = searchCategoryRepository.findById("1");
        assertTrue(found.isPresent());
        assertEquals("Updated Electronics", found.get().getTranslations().get("EN"));
    }

    @Test
    void testSearchSimple_AllLanguages() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchSimple("electronics", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Electronics")));
    }

    @Test
    void testSearchSimple_PartialMatch() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchSimple("electr", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Electronics")));
    }

    @Test
    void testSearchSimple_TurkishMatch() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchSimple("elektronik", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Elektronik")));
    }

    @Test
    void testSearchSimpleByLanguage_English() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchSimpleByLanguage("electronics", "EN", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Electronics")));
    }

    @Test
    void testSearchSimpleByLanguage_Turkish() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchSimpleByLanguage("elektronik", "TR", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Elektronik")));
    }

    @Test
    void testSearchSimpleByLanguage_PartialMatch() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchSimpleByLanguage("comp", "EN", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Computers")));
    }

    @Test
    void testSearchSimpleByLanguage_NoMatch() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchSimpleByLanguage("nonexistent", "EN", pageable);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testSearchSimple_FuzzyMatching() {
        // When - Test with typo
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchSimple("compter", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Computers")));
    }

    @Test
    void testSearchSimple_WildcardMatching() {
        // Create a category with "kit" in the name
        SearchCategoryDocument kitCategory = createSearchCategoryDocument("4", null, "PRODUCT", 1,
            Map.of(
                "EN", "Books & Media",
                "TR", "Kitap ve Medya"
            )
        );
        searchCategoryRepository.save(kitCategory);

        // Wait for indexing
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // When - Test with partial word
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryRepository.searchSimple("kit", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Kitap ve Medya") ||
                                doc.getTranslations().containsValue("Books & Media")));
    }

    private SearchCategoryDocument createSearchCategoryDocument(String id, Long parentId, String type, Integer level, Map<String, String> translations) {
        SearchCategoryDocument category = new SearchCategoryDocument();
        category.setId(id);
        category.setParentId(parentId);
        category.setType(type);
        category.setLevel(level);
        category.setTranslations(new HashMap<>(translations));
        return category;
    }
}
