package com.lookforx.searchservice.service;

import com.lookforx.searchservice.domain.SearchCategoryDocument;
import com.lookforx.searchservice.repository.SearchCategoryRepository;
import com.lookforx.searchservice.service.impl.SearchCategoryServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;


import java.util.*;
import java.util.NoSuchElementException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SearchCategoryServiceTest {

    @Mock
    private SearchCategoryRepository searchCategoryRepository;



    @InjectMocks
    private SearchCategoryServiceImpl searchCategoryService;

    private SearchCategoryDocument sampleCategory;
    private List<SearchCategoryDocument> sampleCategories;

    @BeforeEach
    void setUp() {
        // Create sample category with translations
        Map<String, String> translations = new HashMap<>();
        translations.put("EN.keyword", "Electronics");
        translations.put("EN.description", "Electronic devices and gadgets");

        sampleCategory = new SearchCategoryDocument();
        sampleCategory.setId("1");
        sampleCategory.setTranslations(translations);

        sampleCategories = Arrays.asList(sampleCategory);
    }

    @Test
    void testSearch_FullTextMode_Success() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> expectedPage = new PageImpl<>(sampleCategories, pageable, 1);

        when(searchCategoryRepository.searchSimple(anyString(), any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<SearchCategoryDocument> result = searchCategoryService.search("electronics", "FULL_TEXT", pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals(sampleCategory.getId(), result.getContent().get(0).getId());

        verify(searchCategoryRepository).searchSimple("electronics", pageable);
    }

    @Test
    void testSearch_FuzzyMode_Success() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> expectedPage = new PageImpl<>(sampleCategories, pageable, 1);

        when(searchCategoryRepository.searchFuzzy(anyString(), any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<SearchCategoryDocument> result = searchCategoryService.search("elektr", "FUZZY", pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals(sampleCategory.getId(), result.getContent().get(0).getId());

        verify(searchCategoryRepository).searchFuzzy("elektr", pageable);
    }

    @Test
    void testSearch_PhraseMode_Success() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> expectedPage = new PageImpl<>(sampleCategories, pageable, 1);

        when(searchCategoryRepository.searchPhrase(anyString(), any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<SearchCategoryDocument> result = searchCategoryService.search("electronic devices", "PHRASE", pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals(sampleCategory.getId(), result.getContent().get(0).getId());

        verify(searchCategoryRepository).searchPhrase("electronic devices", pageable);
    }

    @Test
    void testSearch_InvalidMode_DefaultsToFullText() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> expectedPage = new PageImpl<>(sampleCategories, pageable, 1);

        when(searchCategoryRepository.searchSimple(anyString(), any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<SearchCategoryDocument> result = searchCategoryService.search("electronics", "INVALID_MODE", pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals(sampleCategory.getId(), result.getContent().get(0).getId());

        verify(searchCategoryRepository).searchSimple("electronics", pageable);
    }

    @Test
    void testUpdateCategory_Success() {
        // When
        searchCategoryService.updateCategory("1", sampleCategory);

        // Then
        verify(searchCategoryRepository).save(sampleCategory);
        assertEquals("1", sampleCategory.getId());
    }

    @Test
    void testDeleteCategory_Success() {
        // Given
        when(searchCategoryRepository.existsById("1")).thenReturn(true);

        // When
        searchCategoryService.deleteCategory("1");

        // Then
        verify(searchCategoryRepository).existsById("1");
        verify(searchCategoryRepository).deleteById("1");
    }

    @Test
    void testDeleteCategory_NotFound() {
        // Given
        when(searchCategoryRepository.existsById("1")).thenReturn(false);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            searchCategoryService.deleteCategory("1");
        });

        assertEquals("Failed to delete category", exception.getMessage());
        assertTrue(exception.getCause() instanceof NoSuchElementException);

        verify(searchCategoryRepository).existsById("1");
        verify(searchCategoryRepository, never()).deleteById(any());
    }

    @Test
    void testSearch_EmptyQuery_ReturnsEmptyPage() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<SearchCategoryDocument> result = searchCategoryService.search("", "FULL_TEXT", pageable);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());

        // Verify no repository calls were made
        verifyNoInteractions(searchCategoryRepository);
    }

    @Test
    void testSearch_NullQuery_ReturnsEmptyPage() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);

        // When
        Page<SearchCategoryDocument> result = searchCategoryService.search(null, "FULL_TEXT", pageable);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());

        // Verify no repository calls were made
        verifyNoInteractions(searchCategoryRepository);
    }

    @Test
    void testSearch_WithLanguage_Success() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> expectedPage = new PageImpl<>(sampleCategories, pageable, 1);

        when(searchCategoryRepository.searchSimpleByLanguage(anyString(), anyString(), any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<SearchCategoryDocument> result = searchCategoryService.search("electronics", "FULL_TEXT", "EN", pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals(sampleCategory.getId(), result.getContent().get(0).getId());

        verify(searchCategoryRepository).searchSimpleByLanguage("electronics", "EN", pageable);
    }

    @Test
    void testSearch_WithoutLanguage_UsesAllLanguages() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> expectedPage = new PageImpl<>(sampleCategories, pageable, 1);

        when(searchCategoryRepository.searchSimple(anyString(), any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<SearchCategoryDocument> result = searchCategoryService.search("electronics", "FULL_TEXT", null, pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals(sampleCategory.getId(), result.getContent().get(0).getId());

        verify(searchCategoryRepository).searchSimple("electronics", pageable);
    }

    @Test
    void testSearch_WithEmptyLanguage_UsesAllLanguages() {
        // Given
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> expectedPage = new PageImpl<>(sampleCategories, pageable, 1);

        when(searchCategoryRepository.searchSimple(anyString(), any(Pageable.class)))
                .thenReturn(expectedPage);

        // When
        Page<SearchCategoryDocument> result = searchCategoryService.search("electronics", "FULL_TEXT", "", pageable);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals(sampleCategory.getId(), result.getContent().get(0).getId());

        verify(searchCategoryRepository).searchSimple("electronics", pageable);
    }

    @Test
    void testClearAllCategories_Success() {
        // When
        searchCategoryService.clearAllCategories();

        // Then
        verify(searchCategoryRepository).deleteAll();
    }

    @Test
    void testIndexCategory_Success() {
        // When
        searchCategoryService.indexCategory(sampleCategory);

        // Then
        verify(searchCategoryRepository).save(sampleCategory);
    }

    @Test
    void testBulkIndexCategories_Success() {
        // When
        searchCategoryService.bulkIndexCategories(sampleCategories);

        // Then
        verify(searchCategoryRepository).saveAll(sampleCategories);
    }
}
