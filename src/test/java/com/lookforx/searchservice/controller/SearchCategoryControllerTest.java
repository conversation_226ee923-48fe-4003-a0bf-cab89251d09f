package com.lookforx.searchservice.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.searchservice.domain.SearchCategoryDocument;
import com.lookforx.searchservice.service.SearchCategoryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(SearchCategoryController.class)
class SearchCategoryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SearchCategoryService searchCategoryService;

    @Autowired
    private ObjectMapper objectMapper;

    private SearchCategoryDocument sampleCategory;
    private List<SearchCategoryDocument> sampleCategories;

    @BeforeEach
    void setUp() {
        // Create sample category with translations
        Map<String, String> translations = new HashMap<>();
        translations.put("EN.keyword", "Electronics");
        translations.put("EN.description", "Electronic devices and gadgets");

        sampleCategory = new SearchCategoryDocument();
        sampleCategory.setId("1");
        sampleCategory.setTranslations(translations);

        sampleCategories = Arrays.asList(sampleCategory);
    }

    @Test
    void testSearchPaginated_Success() throws Exception {
        // Bu test POST endpoint için yazılmıştı ama controller'da sadece GET var
        // Bu test'i kaldırıyoruz çünkü paginated search endpoint'i yok
    }

    @Test
    void testSearchSimple_Success() throws Exception {
        // Given
        Page<SearchCategoryDocument> page = new PageImpl<>(sampleCategories, PageRequest.of(0, 50), 1);
        
        when(searchCategoryService.search(eq("electronics"), eq("FULL_TEXT"), isNull(), any(Pageable.class)))
                .thenReturn(page);

        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "electronics")
                        .param("mode", "FULL_TEXT")
                        .param("limit", "50")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").value(1));
    }

    @Test
    void testSearchSimple_WithDefaultParameters() throws Exception {
        // Given
        Page<SearchCategoryDocument> page = new PageImpl<>(sampleCategories, PageRequest.of(0, 50), 1);
        
        when(searchCategoryService.search(eq("electronics"), eq("FULL_TEXT"), isNull(), any(Pageable.class)))
                .thenReturn(page);

        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "electronics")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testSearchSimple_FuzzyMode() throws Exception {
        // Given
        Page<SearchCategoryDocument> page = new PageImpl<>(sampleCategories, PageRequest.of(0, 50), 1);
        
        when(searchCategoryService.search(eq("elektr"), eq("FUZZY"), isNull(), any(Pageable.class)))
                .thenReturn(page);

        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "elektr")
                        .param("mode", "FUZZY")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testSearchSimple_PhraseMode() throws Exception {
        // Given
        Page<SearchCategoryDocument> page = new PageImpl<>(sampleCategories, PageRequest.of(0, 50), 1);
        
        when(searchCategoryService.search(eq("electronic devices"), eq("PHRASE"), isNull(), any(Pageable.class)))
                .thenReturn(page);

        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "electronic devices")
                        .param("mode", "PHRASE")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testIndexCategory_Success() throws Exception {
        // Given
        SearchCategoryDocument categoryToIndex = new SearchCategoryDocument();
        categoryToIndex.setId("2");

        // When & Then
        mockMvc.perform(post("/api/v1/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(categoryToIndex)))
                .andExpect(status().isCreated());
    }

    @Test
    void testUpdateCategory_Success() throws Exception {
        // Given
        SearchCategoryDocument categoryToUpdate = new SearchCategoryDocument();
        categoryToUpdate.setId("1");

        // When & Then
        mockMvc.perform(put("/api/v1/categories/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(categoryToUpdate)))
                .andExpect(status().isNoContent());
    }

    @Test
    void testDeleteCategory_Success() throws Exception {
        // When & Then
        mockMvc.perform(delete("/api/v1/categories/1"))
                .andExpect(status().isNoContent());
    }

    @Test
    void testSearchWithMissingQuery_BadRequest() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }
}
