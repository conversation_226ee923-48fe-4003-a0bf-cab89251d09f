package com.lookforx.searchservice.integration;

import com.lookforx.searchservice.domain.SearchCategoryDocument;
import com.lookforx.searchservice.repository.SearchCategoryRepository;
import com.lookforx.searchservice.service.SearchCategoryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.elasticsearch.ElasticsearchContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Testcontainers
class SearchCategoryIntegrationTest {

    @Container
    static ElasticsearchContainer elasticsearch = new ElasticsearchContainer("docker.elastic.co/elasticsearch/elasticsearch:8.7.1")
            .withEnv("discovery.type", "single-node")
            .withEnv("xpack.security.enabled", "false")
            .withEnv("ES_JAVA_OPTS", "-Xms512m -Xmx512m");

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.elasticsearch.uris", elasticsearch::getHttpHostAddress);
        registry.add("spring.data.elasticsearch.client.reactive.endpoints", elasticsearch::getHttpHostAddress);
    }

    @Autowired
    private SearchCategoryRepository searchCategoryRepository;

    @Autowired
    private SearchCategoryService searchCategoryService;

    private SearchCategoryDocument electronicsCategory;
    private SearchCategoryDocument computersCategory;
    private SearchCategoryDocument phonesCategory;

    @BeforeEach
    void setUp() {
        // Clean up before each test
        searchCategoryRepository.deleteAll();

        // Create test data
        electronicsCategory = createSearchCategoryDocument("1", null, "PRODUCT", 1,
            Map.of(
                "EN", "Electronics",
                "TR", "Elektronik"
            )
        );

        computersCategory = createSearchCategoryDocument("2", 1L, "PRODUCT", 2,
            Map.of(
                "EN", "Computers",
                "TR", "Bilgisayarlar"
            )
        );

        phonesCategory = createSearchCategoryDocument("3", 1L, "PRODUCT", 2,
            Map.of(
                "EN", "Mobile Phones",
                "TR", "Cep Telefonları"
            )
        );

        // Index test data
        searchCategoryRepository.save(electronicsCategory);
        searchCategoryRepository.save(computersCategory);
        searchCategoryRepository.save(phonesCategory);

        // Wait for indexing to complete
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    void testIndexAndRetrieveCategory() {
        // Given
        SearchCategoryDocument newCategory = createSearchCategoryDocument("4", null, "SERVICE", 1,
            Map.of(
                "EN", "Services"
            )
        );

        // When
        searchCategoryService.indexCategory(newCategory);

        // Wait for indexing
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Then
        Optional<SearchCategoryDocument> found = searchCategoryRepository.findById("4");
        assertTrue(found.isPresent());
        assertEquals("Services", found.get().getTranslations().get("EN"));
        assertEquals("SERVICE", found.get().getType());
    }

    @Test
    void testFullTextSearch_Success() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryService.search("electronics", "FULL_TEXT", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Electronics")));
    }

    @Test
    void testFullTextSearch_Turkish() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryService.search("elektronik", "FULL_TEXT", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Elektronik")));
    }

    @Test
    void testFuzzySearch_Success() {
        // When - Search with typo
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryService.search("compter", "FUZZY", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Computers")));
    }

    @Test
    void testPhraseSearch_Success() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryService.search("mobile phones", "PHRASE", pageable);

        // Then
        assertNotNull(result);
        assertTrue(result.getTotalElements() > 0);
        assertTrue(result.getContent().stream()
                .anyMatch(doc -> doc.getTranslations().containsValue("Mobile Phones")));
    }

    @Test
    void testSearchWithPagination() {
        // When
        Pageable firstPage = PageRequest.of(0, 2);
        Page<SearchCategoryDocument> result = searchCategoryService.search("electronic", "FULL_TEXT", firstPage);

        // Then
        assertNotNull(result);
        assertTrue(result.getSize() <= 2);
        assertTrue(result.getTotalElements() >= 1);
    }

    @Test
    void testUpdateCategory() {
        // Given
        SearchCategoryDocument updatedCategory = createSearchCategoryDocument("1", null, "PRODUCT", 1,
            Map.of(
                "EN", "Updated Electronics"
            )
        );

        // When
        searchCategoryService.updateCategory("1", updatedCategory);

        // Wait for indexing
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Then
        Optional<SearchCategoryDocument> found = searchCategoryRepository.findById("1");
        assertTrue(found.isPresent());
        assertEquals("Updated Electronics", found.get().getTranslations().get("EN"));
    }

    @Test
    void testDeleteCategory() {
        // Given
        assertTrue(searchCategoryRepository.existsById("1"));

        // When
        searchCategoryService.deleteCategory("1");

        // Wait for deletion
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Then
        assertFalse(searchCategoryRepository.existsById("1"));
    }

    @Test
    void testSearchNoResults() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryService.search("nonexistent", "FULL_TEXT", pageable);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testSearchEmptyQuery() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryService.search("", "FULL_TEXT", pageable);

        // Then
        assertNotNull(result);
        // Empty query should return empty results or all results depending on implementation
    }

    @Test
    void testSearchNullQuery() {
        // When - Null query should return empty page
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryService.search(null, "FULL_TEXT", pageable);

        // Then
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void testSearchInvalidMode() {
        // When
        Pageable pageable = PageRequest.of(0, 10);
        Page<SearchCategoryDocument> result = searchCategoryService.search("electronics", "INVALID_MODE", pageable);

        // Then
        assertNotNull(result);
        // Should default to FULL_TEXT mode
    }

    private SearchCategoryDocument createSearchCategoryDocument(String id, Long parentId, String type, Integer level, Map<String, String> translations) {
        SearchCategoryDocument category = new SearchCategoryDocument();
        category.setId(id);
        category.setParentId(parentId);
        category.setType(type);
        category.setLevel(level);
        category.setTranslations(new HashMap<>(translations));
        return category;
    }
}
