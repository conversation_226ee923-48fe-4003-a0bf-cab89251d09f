package com.lookforx.searchservice.web;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lookforx.searchservice.domain.SearchCategoryDocument;
import com.lookforx.searchservice.repository.SearchCategoryRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;
import org.testcontainers.elasticsearch.ElasticsearchContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureWebMvc
@Testcontainers
class SearchControllerIntegrationTest {

    @Container
    static ElasticsearchContainer elasticsearch = new ElasticsearchContainer("docker.elastic.co/elasticsearch/elasticsearch:8.7.1")
            .withEnv("discovery.type", "single-node")
            .withEnv("xpack.security.enabled", "false")
            .withEnv("ES_JAVA_OPTS", "-Xms512m -Xmx512m");

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.elasticsearch.uris", elasticsearch::getHttpHostAddress);
        registry.add("spring.data.elasticsearch.client.reactive.endpoints", elasticsearch::getHttpHostAddress);
    }

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private SearchCategoryRepository searchCategoryRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    private SearchCategoryDocument electronicsCategory;
    private SearchCategoryDocument computersCategory;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();

        // Clean up before each test
        searchCategoryRepository.deleteAll();

        // Create test data
        electronicsCategory = createSearchCategoryDocument("1", null, "PRODUCT", 1,
            Map.of(
                "EN", "Electronics",
                "TR", "Elektronik"
            )
        );

        computersCategory = createSearchCategoryDocument("2", 1L, "PRODUCT", 2,
            Map.of(
                "EN", "Computers",
                "TR", "Bilgisayarlar"
            )
        );

        // Index test data
        searchCategoryRepository.saveAll(List.of(electronicsCategory, computersCategory));

        // Wait for indexing to complete
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Test
    void testSearchCategories_FullText_Success() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "electronics")
                        .param("mode", "FULL_TEXT")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].id").exists());
    }

    @Test
    void testSearchCategories_Turkish_Success() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "elektronik")
                        .param("mode", "FULL_TEXT")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testSearchCategories_Fuzzy_Success() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "compter") // typo
                        .param("mode", "FUZZY")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testSearchCategories_Phrase_Success() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "desktop laptop")
                        .param("mode", "PHRASE")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testSearchCategories_WithPagination() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "electronic")
                        .param("mode", "FULL_TEXT")
                        .param("limit", "1"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testSearchCategories_NoResults() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "nonexistent")
                        .param("mode", "FULL_TEXT")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isEmpty());
    }

    @Test
    void testSearchCategories_EmptyQuery() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "")
                        .param("mode", "FULL_TEXT")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));
    }

    @Test
    void testSearchCategories_InvalidMode_DefaultsToFullText() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "electronics")
                        .param("mode", "INVALID_MODE")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testIndexCategory_Success() throws Exception {
        // Given
        SearchCategoryDocument newCategory = createSearchCategoryDocument("3", null, "SERVICE", 1,
            Map.of(
                "EN", "Services"
            )
        );

        // When & Then
        mockMvc.perform(post("/api/v1/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(newCategory)))
                .andExpect(status().isCreated());

        // Verify indexing
        Thread.sleep(1000);
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "services")
                        .param("mode", "FULL_TEXT")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testUpdateCategory_Success() throws Exception {
        // Given
        SearchCategoryDocument updatedCategory = createSearchCategoryDocument("1", null, "PRODUCT", 1,
            Map.of(
                "EN", "Updated Electronics"
            )
        );

        // When & Then
        mockMvc.perform(put("/api/v1/categories/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updatedCategory)))
                .andExpect(status().isNoContent());

        // Verify update
        Thread.sleep(1000);
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "Updated Electronics")
                        .param("mode", "FULL_TEXT")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray());
    }

    @Test
    void testDeleteCategory_Success() throws Exception {
        // When & Then
        mockMvc.perform(delete("/api/v1/categories/1"))
                .andExpect(status().isNoContent());

        // Verify deletion
        Thread.sleep(1000);
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("query", "electronics")
                        .param("mode", "FULL_TEXT")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$").isEmpty());
    }

    @Test
    void testDeleteCategory_NotFound() throws Exception {
        // When & Then
        mockMvc.perform(delete("/api/v1/categories/999"))
                .andExpect(status().isInternalServerError()); // Service throws RuntimeException, returns 500
    }

    @Test
    void testSearchCategories_MissingQueryParameter() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/v1/categories/search")
                        .param("mode", "FULL_TEXT")
                        .param("limit", "10"))
                .andExpect(status().isBadRequest());
    }

    private SearchCategoryDocument createSearchCategoryDocument(String id, Long parentId, String type, Integer level, Map<String, String> translations) {
        SearchCategoryDocument category = new SearchCategoryDocument();
        category.setId(id);
        category.setParentId(parentId);
        category.setType(type);
        category.setLevel(level);
        category.setTranslations(new HashMap<>(translations));
        return category;
    }
}
