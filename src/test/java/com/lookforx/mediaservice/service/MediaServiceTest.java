package com.lookforx.mediaservice.service;

import static org.assertj.core.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.BDDMockito.*;
import static org.mockito.Mockito.verify;

import java.io.IOException;
import com.lookforx.mediaservice.base.AbstractBaseServiceTest;
import com.lookforx.mediaservice.exception.FileDeletionException;
import com.lookforx.mediaservice.exception.FileDownloadException;
import com.lookforx.mediaservice.exception.FileUploadException;
import com.lookforx.mediaservice.exception.UnsupportedMediaTypeException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import software.amazon.awssdk.awscore.exception.AwsServiceException;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

class MediaServiceTest extends AbstractBaseServiceTest {

    @Mock
    private S3Client s3Client;

    @InjectMocks
    private MediaService mediaService;

    @BeforeEach
    void setUp() {
        // set the bucket field injected by @Value
        ReflectionTestUtils.setField(mediaService, "bucket", "test-bucket");
    }

    @Test
    void givenValidImageFile_whenUpload_thenCallsPutObjectWithCorrectRequest() throws Exception {
        // Given
        MultipartFile file = mock(MultipartFile.class);
        given(file.getOriginalFilename()).willReturn("MyPic.PNG");
        given(file.getContentType()).willReturn("image/png");
        byte[] bytes = {1,2,3};
        given(file.getBytes()).willReturn(bytes);
        given(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .willReturn(null);

        // When
        String key = mediaService.uploadFile(file);

        // Then
        // 1) Capture both arguments
        ArgumentCaptor<PutObjectRequest> reqCaptor = ArgumentCaptor.forClass(PutObjectRequest.class);
        ArgumentCaptor<RequestBody> bodyCaptor  = ArgumentCaptor.forClass(RequestBody.class);

        verify(s3Client).putObject(reqCaptor.capture(), bodyCaptor.capture());

        PutObjectRequest actualReq = reqCaptor.getValue();
        RequestBody       actualBody = bodyCaptor.getValue();

        // 2) Assert the metadata on the captured request
        assertThat(actualReq.bucket()).isEqualTo("test-bucket");
        assertThat(actualReq.key()).isEqualTo(key);
        assertThat(actualReq.contentType()).isEqualTo("image/png");

        // 3) (Optional) verify the body bytes too
        try (var is = actualBody.contentStreamProvider().newStream()) {
            byte[] uploaded = is.readAllBytes();
            assertArrayEquals(bytes, uploaded);
        }
    }


    @Test
    void givenUnsupportedExtension_whenUpload_thenThrowsUnsupportedMediaType() throws Exception {
        // Given
        MultipartFile file = mock(MultipartFile.class);
        given(file.getOriginalFilename()).willReturn("evil.exe");
        given(file.getContentType()).willReturn("application/octet-stream");

        // When / Then
        assertThatThrownBy(() -> mediaService.uploadFile(file))
                .isInstanceOf(UnsupportedMediaTypeException.class)
                .hasMessageContaining("Unsupported file type: application/octet-stream");

        // and we never call S3
        then(s3Client).shouldHaveNoInteractions();
    }

    @Test
    void givenMissingFilename_whenUpload_thenThrowsUnsupportedMediaType() {
        // Given
        MultipartFile file = mock(MultipartFile.class);
        when(file.getOriginalFilename()).thenReturn(null);
        // (contentType isn’t needed here because the code should fail on filename first)

        // When / Then
        UnsupportedMediaTypeException ex = assertThrows(
                UnsupportedMediaTypeException.class,
                () -> mediaService.uploadFile(file)
        );
        assertEquals("Filename is missing", ex.getMessage());

        // Verify
        verifyNoInteractions(s3Client);
    }

    @Test
    void givenMissingContentType_whenUpload_thenThrowsUnsupportedMediaType() {
        // Given
        MultipartFile file = mock(MultipartFile.class);
        when(file.getOriginalFilename()).thenReturn("somefile.txt");
        when(file.getContentType()).thenReturn(null);

        // When / Then
        UnsupportedMediaTypeException ex = assertThrows(
                UnsupportedMediaTypeException.class,
                () -> mediaService.uploadFile(file)
        );
        assertEquals("Content-Type is unknown", ex.getMessage());

        // Verify
        verifyNoInteractions(s3Client);
    }

    @Test
    void givenIoExceptionDuringRead_whenUpload_thenThrowsFileUploadException() throws Exception {
        // Given
        MultipartFile file = mock(MultipartFile.class);
        given(file.getOriginalFilename()).willReturn("readfail.txt");
        given(file.getContentType()).willReturn("text/plain");
        given(file.getBytes()).willThrow(new IOException("disk error"));

        // When / Then
        assertThatThrownBy(() -> mediaService.uploadFile(file))
                .isInstanceOf(FileUploadException.class)
                .hasMessage("File upload to Cloudflare R2 failed")
                .hasCauseInstanceOf(IOException.class);

        // and we never call S3
        then(s3Client).shouldHaveNoInteractions();

    }

    @Test
    void givenValidFile_whenUpdateFile_thenDeletesOldAndUploadsNew() throws Exception {
        // — prepare a dummy MultipartFile —
        MultipartFile file = mock(MultipartFile.class);
        when(file.getOriginalFilename()).thenReturn("report.pdf");
        when(file.getContentType()).thenReturn("application/pdf");
        when(file.getBytes()).thenReturn(new byte[]{1,2,3});

        // — stub deleteObject and putObject on the S3 client —
        when(s3Client.deleteObject(any(DeleteObjectRequest.class)))
                .thenReturn(DeleteObjectResponse.builder().build());
        when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .thenReturn(PutObjectResponse.builder().build());

        // — act —
        String newKey = mediaService.updateFile("documents/old-report.pdf", file);

        // — basic assertions on the returned key —
        assertNotNull(newKey);
        assertTrue(newKey.startsWith("documents/"), "should reuse the same folder");

        // — verify deleteObject was called with exactly the expected delete request —
        DeleteObjectRequest expectedDelete = DeleteObjectRequest.builder()
                .bucket("test-bucket")
                .key("documents/old-report.pdf")
                .build();
        verify(s3Client).deleteObject(eq(expectedDelete));

        // — verify that putObject was called once (we don't assert its exact key here) —
        verify(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));
    }

    @Test
    void givenDeleteThrows_whenUpdateFile_thenThrowsFileDeletionException() {
        MultipartFile file = mock(MultipartFile.class);
        when(file.getOriginalFilename()).thenReturn("ignore.txt");

        // simulate delete failure
        when(s3Client.deleteObject(any(DeleteObjectRequest.class)))
                .thenThrow(S3Exception.builder().message("boom").build());

        // delete happens first, so updateFile should bubble up our custom FileDeletionException
        assertThatThrownBy(() -> mediaService.updateFile("docs/old.txt", file))
                .isInstanceOf(FileDeletionException.class)
                .hasMessageContaining("Failed to delete file: docs/old.txt");

        verify(s3Client).deleteObject(any(DeleteObjectRequest.class));
    }

    @Test
    void givenUploadThrows_whenUpdateFile_thenPropagatesS3Exception() throws Exception {
        // — Given —
        MultipartFile file = mock(MultipartFile.class);
        when(file.getOriginalFilename()).thenReturn("pic.jpg");
        when(file.getContentType()).thenReturn("image/jpeg");
        when(file.getBytes()).thenReturn(new byte[]{9, 9});

        // Cast the builder result to S3Exception so the variable matches
        S3Exception uploadFail = (S3Exception) S3Exception.builder()
                .message("upload fail")
                .build();

        // stub deleteObject to succeed
        doReturn(DeleteObjectResponse.builder().build())
                .when(s3Client).deleteObject(any(DeleteObjectRequest.class));

        // stub putObject to throw our S3Exception
        doThrow(uploadFail)
                .when(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));

        // — When / Then —
        S3Exception thrown = assertThrows(
                S3Exception.class,
                () -> mediaService.updateFile("images/old.jpg", file)
        );
        assertEquals("upload fail", thrown.getMessage());

        // — Verify —
        verify(s3Client).deleteObject(any(DeleteObjectRequest.class));
        verify(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));
    }
    @Test
    void downloadFile_success_returnsByteArray() throws Exception {

        // Given
        String key = "images/pic.png";
        byte[] expected = {10, 20, 30};

        HeadObjectRequest headReq = HeadObjectRequest.builder()
                .bucket("test-bucket")
                .key(key)
                .build();

        GetObjectRequest getReq = GetObjectRequest.builder()
                .bucket("test-bucket")
                .key(key)
                .build();

        @SuppressWarnings("unchecked")
        ResponseBytes<GetObjectResponse> respBytes =
                ResponseBytes.fromByteArray(GetObjectResponse.builder().build(), expected);

        // When
        when(s3Client.headObject(eq(headReq)))
                .thenReturn(HeadObjectResponse.builder().build());
        when(s3Client.getObject(eq(getReq), eq(ResponseTransformer.toBytes())))
                .thenReturn(respBytes);


        // Then
        byte[] actual = mediaService.downloadFile(key);

        assertArrayEquals(expected, actual);

        // Verify
        verify(s3Client).headObject(eq(headReq));
        verify(s3Client).getObject(eq(getReq), eq(ResponseTransformer.toBytes()));

    }


    @Test
    void downloadFile_failure_throwsFileDownloadException() {

        // Given
        String key = "videos/missing.mp4";

        // When
        when(s3Client.headObject(any(HeadObjectRequest.class)))
                .thenThrow(S3Exception.builder().message("Not found").build());

        // Then
        FileDownloadException ex = assertThrows(
                FileDownloadException.class,
                () -> mediaService.downloadFile(key)
        );

        assertEquals("Failed to download file: " + key, ex.getMessage());

        // Verify
        verify(s3Client).headObject(any(HeadObjectRequest.class));
        verify(s3Client, never()).getObject(any(GetObjectRequest.class), any(ResponseTransformer.class));

    }

    @Test
    void getContentType_success_returnsCorrectType() {

        // Given
        String key = "docs/readme.txt";
        String ct  = "text/plain";

        HeadObjectRequest expectedReq = HeadObjectRequest.builder()
                .bucket("test-bucket")
                .key(key)
                .build();

        // When
        when(s3Client.headObject(eq(expectedReq)))
                .thenReturn(HeadObjectResponse.builder().contentType(ct).build());

        // Then
        String actual = mediaService.getContentType(key);

        assertEquals(ct, actual);

        // Verify
        verify(s3Client).headObject(eq(expectedReq));

    }

    @Test
    void getContentType_failure_throwsFileDownloadException() {

        // Given
        String key = "docs/secret.pdf";

        // When
        when(s3Client.headObject(any(HeadObjectRequest.class)))
                .thenThrow(S3Exception.builder().message("Access denied").build());

        // Then
        FileDownloadException ex = assertThrows(
                FileDownloadException.class,
                () -> mediaService.getContentType(key)
        );


        assertEquals("Could not retrieve metadata for file: " + key, ex.getMessage());

        // Verify
        verify(s3Client).headObject(any(HeadObjectRequest.class));

    }

}
