package com.lookforx.mediaservice.exception;

import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.common.dto.ErrorResponse;
import com.lookforx.common.dto.ExceptionMessageResponse;
import com.lookforx.mediaservice.base.AbstractBaseServiceTest;
import jakarta.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.HttpMediaTypeNotSupportedException;

import java.time.LocalDateTime;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

class GlobalExceptionHandlerTest extends AbstractBaseServiceTest {

    @InjectMocks
    private GlobalExceptionHandler handler;

    @Mock
    private ExceptionServiceClient exceptionClient;

    @Mock
    private HttpServletRequest request;

    @BeforeEach
    void setUp() {
        // no Accept-Language header => default to "EN"
        when(request.getHeader("Accept-Language")).thenReturn(null);
    }

    @Test
    void handleUnsupportedMediaType_returns415AndErrorBody() {
        // Given
        HttpMediaTypeNotSupportedException ex =
                new HttpMediaTypeNotSupportedException("Unsupported file type: .exe");

        when(exceptionClient.getExceptionMessage("UNSUPPORTED_MEDIA_TYPE", "EN"))
                .thenReturn(new ExceptionMessageResponse(
                        "UNSUPPORTED_MEDIA_TYPE", "EN", "Unsupported file type: .exe"));

        // When
        ResponseEntity<ErrorResponse> resp =
                handler.handleUnsupportedMediaType(ex, request);

        // Then
        assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.UNSUPPORTED_MEDIA_TYPE);
        ErrorResponse body = resp.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo(415);
        assertThat(body.getError()).isEqualTo("Unsupported Media Type");
        assertThat(body.getMessage()).isEqualTo("Unsupported file type: .exe");
        assertThat(body.getTimestamp()).isBeforeOrEqualTo(LocalDateTime.now());
    }

    @Test
    void handleFileUploadException_returns500AndErrorBody() {
        // Given
        FileUploadException ex =
                new FileUploadException("Failed to stream bytes", new RuntimeException("ioerror"));

        // stub the language header
        when(request.getHeader("Accept-Language")).thenReturn(null);

        // stub with literals — no matchers at all
        when(exceptionClient.getExceptionMessage("FILE_UPLOAD_FAILED", "EN"))
                .thenReturn(new ExceptionMessageResponse(
                        "FILE_UPLOAD_FAILED",
                        "EN",
                        "%s"                  // use "%s" so String.format yields exactly the exception message
                ));

        // When
        ResponseEntity<ErrorResponse> resp =
                handler.handleFileUploadException(ex, request);

        // Then
        assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);

        ErrorResponse body = resp.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo(500);
        assertThat(body.getError()).isEqualTo("File Upload Failed");
        assertThat(body.getMessage()).isEqualTo("Failed to stream bytes");  // no duplication now
        assertThat(body.getTimestamp()).isBeforeOrEqualTo(LocalDateTime.now());
    }

    @Test
    void handleFileDeletionException_returns500AndErrorBody() {
        // Given
        FileDeletionException ex = new FileDeletionException("Deletion failed", null);

        // Stub the template *with* a placeholder so that String.format("%s", args)
        // yields exactly the exception message (no duplication).
        when(exceptionClient.getExceptionMessage(anyString(), anyString()))
                .thenReturn(new ExceptionMessageResponse(
                        "FILE_DELETION_FAILED",
                        "EN",
                        "%s"
                ));

        // When
        ResponseEntity<ErrorResponse> resp =
                handler.handleFileDeletionException(ex, request);

        // Then
        assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        ErrorResponse body = resp.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo(500);
        assertThat(body.getError()).isEqualTo("File Deletion Failed");
        assertThat(body.getMessage()).isEqualTo("Deletion failed");
        assertThat(body.getTimestamp()).isBeforeOrEqualTo(LocalDateTime.now());
    }

    @Test
    void handleFileDownloadException_returns500AndErrorBody() {
        // Given
        FileDownloadException ex = new FileDownloadException("Download failed", null);

        // Stub with a "%s" template so that String.format("%s", "Download failed") → "Download failed"
        when(exceptionClient.getExceptionMessage(anyString(), anyString()))
                .thenReturn(new ExceptionMessageResponse(
                        "FILE_DOWNLOAD_FAILED",
                        "EN",
                        "%s"
                ));

        // When
        ResponseEntity<ErrorResponse> resp =
                handler.handleFileDownloadException(ex, request);

        // Then
        assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        ErrorResponse body = resp.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo(500);
        assertThat(body.getError()).isEqualTo("File Download Failed");
        assertThat(body.getMessage()).isEqualTo("Download failed");
        assertThat(body.getTimestamp()).isBeforeOrEqualTo(LocalDateTime.now());
    }

    @Test
    void handleAllUncaught_returns500AndErrorBody() {
        // Given
        IllegalStateException ex = new IllegalStateException("Something went wrong");

        when(exceptionClient.getExceptionMessage("INTERNAL_SERVER_ERROR", "EN"))
                .thenReturn(new ExceptionMessageResponse(
                        "INTERNAL_SERVER_ERROR", "EN", "Something went wrong"));

        // When
        ResponseEntity<ErrorResponse> resp =
                handler.handleAllUncaught(ex, request);

        // Then
        assertThat(resp.getStatusCode()).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR);
        ErrorResponse body = resp.getBody();
        assertThat(body).isNotNull();
        assertThat(body.getStatus()).isEqualTo(500);
        assertThat(body.getError()).isEqualTo("Internal Server Error");
        assertThat(body.getMessage()).isEqualTo("Something went wrong");
        assertThat(body.getTimestamp()).isBeforeOrEqualTo(LocalDateTime.now());
    }

}