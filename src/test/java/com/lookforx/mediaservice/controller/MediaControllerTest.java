package com.lookforx.mediaservice.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.lookforx.common.client.ExceptionServiceClient;
import com.lookforx.common.dto.ExceptionMessageResponse;
import com.lookforx.mediaservice.base.AbstractRestControllerTest;
import com.lookforx.mediaservice.exception.FileDeletionException;
import com.lookforx.mediaservice.exception.FileDownloadException;
import com.lookforx.mediaservice.service.MediaService;
import com.lookforx.mediaservice.exception.UnsupportedMediaTypeException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.web.multipart.MultipartFile;

@SpringBootTest(properties = {
        "cloudflare.r2.endpoint=https://dummy.local",
        "cloudflare.r2.accessKey=AKIA…",
        "cloudflare.r2.secretKey=SECRET",
        "cloudflare.r2.bucket=test-bucket"
})
@AutoConfigureMockMvc
class MediaControllerTest extends AbstractRestControllerTest {

    @MockitoBean
    MediaService mediaService;

    @MockitoBean
    ExceptionServiceClient exceptionClient;

    @BeforeEach
    void stubExceptionTemplates() {
        // return "%s" for any exceptionCode / language
        given(exceptionClient.getExceptionMessage(anyString(), anyString()))
                .willAnswer(invocation -> {
                    String code = invocation.getArgument(0);
                    String lang = invocation.getArgument(1);
                    return new ExceptionMessageResponse(code, lang, "%s");
                });
    }

    @Test
    @DisplayName("POST /api/media/upload → 200 OK with returned key")
    void uploadImage_whenValidFile_thenReturnsKey() throws Exception {

        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file",                       // param name
                "cloudflare_sample.txt",      // original filename
                MediaType.TEXT_PLAIN_VALUE,   // content type
                "hello world".getBytes()      // payload
        );

        String expectedKey = "documents/123e4567-e89b-12d3-a456-426614174000-cloudflare_sample.txt";

        // When
        when(mediaService.uploadFile(any())).thenReturn(expectedKey);

        // Then
        mockMvc.perform(multipart("/api/media/upload")
                        .file(file)
                )
                .andExpect(status().isOk())
                .andExpect(content().string(expectedKey));

        // Verify
        verify(mediaService).uploadFile(any());

    }

    @Test
    @DisplayName("POST /api/media/upload → 415 Unsupported Media Type on bad extension")
    void uploadImage_whenUnsupportedType_thenReturns415() throws Exception {

        // Given
        MockMultipartFile file = new MockMultipartFile(
                "file",
                "badfile.exe",
                MediaType.APPLICATION_OCTET_STREAM_VALUE,
                new byte[]{0x00}
        );

        // When
        when(mediaService.uploadFile(any()))
                .thenThrow(new UnsupportedMediaTypeException("Unsupported file type"));

        // Then
        mockMvc.perform(multipart("/api/media/upload")
                        .file(file)
                )
                .andExpect(status().isUnsupportedMediaType());

        // Verify
        verify(mediaService).uploadFile(any(MultipartFile.class));

    }

    @Test
    @DisplayName("GET /api/media/download → 200 OK with resource")
    void downloadImage_whenValidKey_thenReturnsResource() throws Exception {

        // Given
        String key = "images/pic.png";
        byte[] data = "binary-data".getBytes();
        String contentType = "image/png";

        // When
        when(mediaService.downloadFile(eq(key))).thenReturn(data);
        when(mediaService.getContentType(eq(key))).thenReturn(contentType);

        // Then
        mockMvc.perform(get("/api/media/download")
                        .param("key", key))
                .andExpect(status().isOk())
                .andExpect(header().string(HttpHeaders.CONTENT_TYPE, contentType))
                .andExpect(header().string(HttpHeaders.CONTENT_DISPOSITION,
                        "attachment; filename=\"pic.png\""))
                .andExpect(content().bytes(data));

        // Verify
        verify(mediaService).downloadFile(eq(key));
        verify(mediaService).getContentType(eq(key));

    }

    @Test
    @DisplayName("GET /api/media/download → 500 on FileDownloadException")
    void downloadImage_whenServiceThrows_thenReturns500() throws Exception {

        // Given
        String key = "missing.png";

        // When
        when(mediaService.downloadFile(eq(key)))
                .thenThrow(new FileDownloadException("not found", null));

        // Then
        mockMvc.perform(get("/api/media/download")
                        .param("key", key))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.error").value("File Download Failed"))
                .andExpect(jsonPath("$.message").value("not found"));

        // Verify
        verify(mediaService).downloadFile(eq(key));
        verify(mediaService, never()).getContentType(any());

    }

    @Test
    @DisplayName("PUT /api/media/update → 200 OK with new key")
    void updateImage_whenValid_thenReturnsNewKey() throws Exception {

        // Given
        String oldKey = "docs/old.txt";
        String newKey = "docs/new-uuid-old.txt";

        MockMultipartFile file = new MockMultipartFile(
                "file",
                "old.txt",
                MediaType.TEXT_PLAIN_VALUE,
                "updated".getBytes()
        );

        // When
        when(mediaService.updateFile(eq(oldKey), any(MultipartFile.class)))
                .thenReturn(newKey);

        // Then
        mockMvc.perform(multipart("/api/media/update")
                        .file(file)
                        .param("key", oldKey)
                        .with(req -> { req.setMethod("PUT"); return req; })
                )
                .andExpect(status().isOk())
                .andExpect(content().string(newKey));

        // Verify
        verify(mediaService).updateFile(eq(oldKey), any(MultipartFile.class));

    }

    @Test
    @DisplayName("PUT /api/media/update → 500 on FileDeletionException")
    void updateImage_whenDeleteFails_thenReturns500() throws Exception {

        // Given
        String oldKey = "docs/old.txt";
        MockMultipartFile file = new MockMultipartFile(
                "file", "old.txt", MediaType.TEXT_PLAIN_VALUE, "x".getBytes()
        );

        // When
        when(mediaService.updateFile(eq(oldKey), any(MultipartFile.class)))
                .thenThrow(new FileDeletionException("del failed", null));

        // Then
        mockMvc.perform(multipart("/api/media/update")
                        .file(file)
                        .param("key", oldKey)
                        .with(req -> { req.setMethod("PUT"); return req; })
                )
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.error").value("File Deletion Failed"))
                .andExpect(jsonPath("$.message").value("del failed"));

        // Verify
        verify(mediaService).updateFile(eq(oldKey), any(MultipartFile.class));

    }

    @Test
    @DisplayName("DELETE /api/media/delete → 204 No Content")
    void deleteImage_whenValidKey_thenNoContent() throws Exception {

        // Given
        String key = "videos/old.mp4";

        // When
        doNothing().when(mediaService).deleteFile(eq(key));

        // Then
        mockMvc.perform(delete("/api/media/delete")
                        .param("key", key))
                .andExpect(status().isNoContent());

        // Verify
        verify(mediaService).deleteFile(eq(key));

    }

    @Test
    @DisplayName("DELETE /api/media/delete → 500 on FileDeletionException")
    void deleteImage_whenServiceThrows_thenReturns500() throws Exception {

        // Given
        String key = "videos/old.mp4";

        // When
        doThrow(new FileDeletionException("delete err", null))
                .when(mediaService).deleteFile(eq(key));

        // Then
        mockMvc.perform(delete("/api/media/delete")
                        .param("key", key))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.error").value("File Deletion Failed"))
                .andExpect(jsonPath("$.message").value("delete err"));

        // Verify
        verify(mediaService).deleteFile(eq(key));

    }

}