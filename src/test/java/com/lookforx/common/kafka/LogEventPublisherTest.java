package com.lookforx.common.kafka;

import com.lookforx.common.events.LogEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;

import java.time.LocalDateTime;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LogEventPublisherTest {

    @Mock
    private KafkaTemplate<String, com.lookforx.common.events.BaseEvent> kafkaTemplate;

    @Mock
    private SendResult<String, com.lookforx.common.events.BaseEvent> sendResult;

    private LogEventPublisher logEventPublisher;

    @BeforeEach
    void setUp() {
        logEventPublisher = new LogEventPublisher(kafkaTemplate);
    }

    @Test
    void shouldPublishApplicationLogEvent() {
        // Given
        LogEvent logEvent = createTestLogEvent("INFO");
        CompletableFuture<SendResult<String, com.lookforx.common.events.BaseEvent>> future = 
            CompletableFuture.completedFuture(sendResult);
        
        when(kafkaTemplate.send(eq(LoggingKafkaTopics.APPLICATION_LOGS), eq(logEvent.getEventId()), eq(logEvent)))
            .thenReturn(future);

        // When
        logEventPublisher.publishApplicationLogEvent(logEvent);

        // Then
        verify(kafkaTemplate).send(LoggingKafkaTopics.APPLICATION_LOGS, logEvent.getEventId(), logEvent);
    }

    @Test
    void shouldPublishErrorLogEvent() {
        // Given
        LogEvent logEvent = createTestLogEvent("ERROR");
        CompletableFuture<SendResult<String, com.lookforx.common.events.BaseEvent>> future = 
            CompletableFuture.completedFuture(sendResult);
        
        when(kafkaTemplate.send(eq(LoggingKafkaTopics.ERROR_LOGS), eq(logEvent.getEventId()), eq(logEvent)))
            .thenReturn(future);

        // When
        logEventPublisher.publishErrorLogEvent(logEvent);

        // Then
        verify(kafkaTemplate).send(LoggingKafkaTopics.ERROR_LOGS, logEvent.getEventId(), logEvent);
    }

    @Test
    void shouldPublishLogEventToCorrectTopicBasedOnLevel() {
        // Given - ERROR level should go to error logs
        LogEvent errorEvent = createTestLogEvent("ERROR");
        CompletableFuture<SendResult<String, com.lookforx.common.events.BaseEvent>> future = 
            CompletableFuture.completedFuture(sendResult);
        
        when(kafkaTemplate.send(eq(LoggingKafkaTopics.ERROR_LOGS), eq(errorEvent.getEventId()), eq(errorEvent)))
            .thenReturn(future);

        // When
        logEventPublisher.publishLogEvent(errorEvent);

        // Then
        verify(kafkaTemplate).send(LoggingKafkaTopics.ERROR_LOGS, errorEvent.getEventId(), errorEvent);
    }

    @Test
    void shouldPublishLogEventToApplicationLogsForInfoLevel() {
        // Given - INFO level should go to application logs
        LogEvent infoEvent = createTestLogEvent("INFO");
        CompletableFuture<SendResult<String, com.lookforx.common.events.BaseEvent>> future = 
            CompletableFuture.completedFuture(sendResult);
        
        when(kafkaTemplate.send(eq(LoggingKafkaTopics.APPLICATION_LOGS), eq(infoEvent.getEventId()), eq(infoEvent)))
            .thenReturn(future);

        // When
        logEventPublisher.publishLogEvent(infoEvent);

        // Then
        verify(kafkaTemplate).send(LoggingKafkaTopics.APPLICATION_LOGS, infoEvent.getEventId(), infoEvent);
    }

    private LogEvent createTestLogEvent(String level) {
        return LogEvent.builder()
                .eventId(UUID.randomUUID().toString())
                .logServiceName("test-service")
                .logEnvironment("test")
                .logVersion("1.0.0")
                .level(level)
                .logger("com.test.TestClass")
                .message("Test log message")
                .thread("main")
                .timestamp(LocalDateTime.now())
                .build();
    }
}
