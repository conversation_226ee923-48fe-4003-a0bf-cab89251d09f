package com.lookforx.common.annotations.validators;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.validation.ConstraintValidatorContext;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PasswordConstraintValidatorTest {

    @InjectMocks
    private PasswordConstraintValidator validator;

    @Mock
    private ConstraintValidatorContext context;

    @BeforeEach
    void setUp() {
        validator.initialize(null);
    }

    @Test
    void returnsFalseForNull() {
        assertFalse(validator.isValid(null, context));
    }

    @Test
    void returnsFalseForTooShort() {
        assertFalse(validator.isValid("Ab1!", context));
    }

    @Test
    void returnsFalseWhenNoUppercase() {
        assertFalse(validator.isValid("abcdefg1!", context));
    }

    @Test
    void returnsFalseWhenNoDigit() {
        assertFalse(validator.isValid("Abcdefgh!", context));
    }

    @Test
    void returnsFalseWhenNoSpecialChar() {
        assertFalse(validator.isValid("Abcdefg12", context));
    }

    @Test
    void returnsTrueForValidPassword() {
        assertTrue(validator.isValid("Abcdef1!", context));
        assertTrue(validator.isValid("GoodPass123$", context));
    }
}