package com.lookforx.apigateway.config;

import org.junit.jupiter.api.Test;
import org.springframework.web.reactive.config.CorsRegistration;
import org.springframework.web.reactive.config.CorsRegistry;

import static org.mockito.Mockito.*;
import static org.springframework.web.bind.annotation.RequestMethod.*;


class WebConfigTest {

    @Test
    void testAddCorsMappings() {

        // Given
        WebConfig webConfig = new WebConfig();
        CorsRegistry registry = mock(CorsRegistry.class);
        CorsRegistration registration = mock(CorsRegistration.class);

        // When
        when(registry.addMapping("/**")).thenReturn(registration);
        when(registration.allowedOrigins("*")).thenReturn(registration);
        when(registration.allowedMethods(
                GET.name(), POST.name(), PUT.name(), DELETE.name(), OPTIONS.name()
        )).thenReturn(registration);
        when(registration.allowedHeaders("*")).thenReturn(registration);
        when(registration.allowCredentials(true)).thenReturn(registration);

        // Then
        webConfig.addCorsMappings(registry);

        // Verify
        verify(registry).addMapping("/**");
        verify(registration).allowedOrigins("*");
        verify(registration).allowedMethods(
                GET.name(), POST.name(), PUT.name(), DELETE.name(), OPTIONS.name()
        );
        verify(registration).allowedHeaders("*");
        verify(registration).allowCredentials(true);

    }

}