package com.lookforx.loggingservice.integration;

import com.lookforx.common.events.LogEvent;
import com.lookforx.loggingservice.domain.LogProcessingResult;
import com.lookforx.loggingservice.domain.LogStorageResult;
import com.lookforx.loggingservice.observer.impl.MetricsObserver;
import com.lookforx.loggingservice.service.LoggingFacadeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.testcontainers.containers.MongoDBContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test for the complete logging service with all design patterns.
 * 
 * Tests the interaction between all design patterns:
 * Factory, Strategy, Chain of Responsibility, Template Method, Observer, Facade
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @since 2024-01-01
 */
@SpringBootTest
@ActiveProfiles("test")
@Testcontainers
class LoggingServiceIntegrationTest {
    
    @Container
    static MongoDBContainer mongoDBContainer = new MongoDBContainer("mongo:7.0")
            .withExposedPorts(27017);
    
    @Autowired
    private LoggingFacadeService loggingFacadeService;
    
    @Autowired(required = false)
    private MetricsObserver metricsObserver;
    
    @BeforeEach
    void setUp() {
        if (metricsObserver != null) {
            metricsObserver.resetMetrics();
        }
    }
    
    @Test
    void testCompleteLogProcessingPipeline() throws Exception {
        // Given: Raw log data
        Map<String, Object> rawLogData = createSampleRawLogData();
        
        // When: Process log event through complete pipeline
        CompletableFuture<LogProcessingResult> future = loggingFacadeService.processLogEvent(rawLogData);
        LogProcessingResult result = future.get(5, TimeUnit.SECONDS);
        
        // Then: Verify processing was successful
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertTrue(result.isAccepted());
        assertNotNull(result.getProcessedEvent());
        
        // Verify the log event was created and enriched
        LogEvent processedEvent = result.getProcessedEvent();
        assertNotNull(processedEvent.getEventId());
        assertNotNull(processedEvent.getMessage());
        assertNotNull(processedEvent.getTimestamp());
        assertNotNull(processedEvent.getCustomFields());
        
        // Verify enrichment occurred
        assertTrue(processedEvent.getCustomFields().containsKey("enrichedAt"));
        assertTrue(processedEvent.getCustomFields().containsKey("enrichedBy"));
        assertTrue(processedEvent.getCustomFields().containsKey("logCategory"));
        
        // Verify metrics were updated
        if (metricsObserver != null) {
            assertEquals(1, metricsObserver.getEventsReceived());
            assertEquals(1, metricsObserver.getEventsProcessed());
            assertEquals(0, metricsObserver.getEventsFailed());
            assertTrue(metricsObserver.getSuccessRate() > 0.9);
        }
    }
    
    @Test
    void testFactoryPatternWithDifferentLogTypes() throws Exception {
        // Test application log
        Map<String, Object> appLogData = createApplicationLogData();
        CompletableFuture<LogProcessingResult> appResult = loggingFacadeService.processLogEvent(appLogData);
        LogProcessingResult appProcessingResult = appResult.get(5, TimeUnit.SECONDS);
        
        assertTrue(appProcessingResult.isSuccess());
        assertEquals("application", appProcessingResult.getProcessedEvent().getCustomFields().get("logCategory"));
        
        // Test error log
        Map<String, Object> errorLogData = createErrorLogData();
        CompletableFuture<LogProcessingResult> errorResult = loggingFacadeService.processLogEvent(errorLogData);
        LogProcessingResult errorProcessingResult = errorResult.get(5, TimeUnit.SECONDS);
        
        assertTrue(errorProcessingResult.isSuccess());
        assertEquals("ERROR", errorProcessingResult.getProcessedEvent().getLevel());
    }
    
    @Test
    void testStrategyPatternStorageSelection() throws Exception {
        // Given: Log event
        LogEvent logEvent = createSampleLogEvent();
        
        // When: Store log event (should select best available strategy)
        CompletableFuture<LogStorageResult> future = loggingFacadeService.storeLogEvent(logEvent);
        LogStorageResult result = future.get(5, TimeUnit.SECONDS);
        
        // Then: Verify storage was successful
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(1, result.getStoredCount());
        assertNotNull(result.getStrategyName());
        
        // Verify strategy was selected (either MongoDB or Cache)
        assertTrue(result.getStrategyName().equals("MongoDB") || result.getStrategyName().equals("Cache"));
    }
    
    @Test
    void testObserverPatternMetricsCollection() throws Exception {
        if (metricsObserver == null) {
            return; // Skip if metrics observer not available
        }
        
        // Given: Multiple log events
        int numberOfEvents = 5;
        
        // When: Process multiple events
        for (int i = 0; i < numberOfEvents; i++) {
            Map<String, Object> logData = createSampleRawLogData();
            logData.put("message", "Test message " + i);
            
            CompletableFuture<LogProcessingResult> future = loggingFacadeService.processLogEvent(logData);
            future.get(5, TimeUnit.SECONDS);
        }
        
        // Then: Verify metrics were collected
        assertEquals(numberOfEvents, metricsObserver.getEventsReceived());
        assertEquals(numberOfEvents, metricsObserver.getEventsProcessed());
        assertEquals(0, metricsObserver.getEventsFailed());
        assertEquals(1.0, metricsObserver.getSuccessRate(), 0.01);
        
        // Verify metrics summary
        String summary = metricsObserver.getMetricsSummary();
        assertNotNull(summary);
        assertTrue(summary.contains("Received: " + numberOfEvents));
        assertTrue(summary.contains("Processed: " + numberOfEvents));
    }
    
    @Test
    void testErrorHandlingAndRecovery() throws Exception {
        // Given: Log data that might cause processing issues
        Map<String, Object> problematicLogData = new HashMap<>();
        problematicLogData.put("message", "Test message with very long content ".repeat(1000)); // Very long message
        problematicLogData.put("level", "INFO");
        problematicLogData.put("timestamp", LocalDateTime.now().toString());
        
        // When: Process problematic log
        CompletableFuture<LogProcessingResult> future = loggingFacadeService.processLogEvent(problematicLogData);
        LogProcessingResult result = future.get(5, TimeUnit.SECONDS);
        
        // Then: Should handle gracefully (either process or filter with reason)
        assertNotNull(result);
        assertTrue(result.isSuccess() || result.isFiltered());
        
        if (result.isFiltered()) {
            assertNotNull(result.getFilterReason());
        }
    }
    
    // Helper methods for creating test data
    
    private Map<String, Object> createSampleRawLogData() {
        Map<String, Object> rawData = new HashMap<>();
        rawData.put("message", "Sample log message for testing");
        rawData.put("level", "INFO");
        rawData.put("loggerName", "com.lookforx.test.TestClass");
        rawData.put("timestamp", LocalDateTime.now().toString());
        rawData.put("serviceName", "test-service");
        return rawData;
    }
    
    private Map<String, Object> createApplicationLogData() {
        Map<String, Object> rawData = new HashMap<>();
        rawData.put("message", "Application log message");
        rawData.put("level", "INFO");
        rawData.put("loggerName", "com.lookforx.application.Service");
        rawData.put("timestamp", LocalDateTime.now().toString());
        return rawData;
    }
    
    private Map<String, Object> createErrorLogData() {
        Map<String, Object> rawData = new HashMap<>();
        rawData.put("message", "Error occurred during processing");
        rawData.put("level", "ERROR");
        rawData.put("loggerName", "com.lookforx.error.Handler");
        rawData.put("timestamp", LocalDateTime.now().toString());
        rawData.put("exception", "java.lang.RuntimeException: Test exception");
        return rawData;
    }
    
    private LogEvent createSampleLogEvent() {
        return LogEvent.builder()
                .eventId("test-event-" + System.currentTimeMillis())
                .message("Sample log event for storage testing")
                .level("INFO")
                .logger("com.lookforx.test.StorageTest")
                .timestamp(LocalDateTime.now())
                .serviceName("test-service")
                .build();
    }
}
