spring:
  application:
    name: customer-support-microservice
    data:
      mongodb:
        uri: mongodb+srv://lookforx:<EMAIL>/customersupportdb?retryWrites=true&w=majority&appName=lookforx
        auto-index-creation: true
      redis:
        host: *************
        port: 6379
        database: 3  # exception-service uses db 3 according to memories
        timeout: 2000ms
        lettuce:
          pool:
            max-active: 8
            max-idle: 8
            min-idle: 0
    cache:
      type: redis

  # Logging Configuration (MongoDB-specific)
  logging:
    level:
      root: INFO
      com.lookforx.support: INFO
      org.springframework.data.mongodb: DEBUG
      org.springframework.cache: DEBUG
      org.springframework.web: DEBUG
      org.springframework.kafka: INFO

  cache:
    type: redis
    cache-names:
      - tickets
      - feedback
    redis:
      host: ***********            # Docker Compose servis adı
      port: 6379
      password: ""           # Redis şif<PERSON>, eğer var ise
      timeout: 2000