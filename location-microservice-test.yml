spring:
  application:
    name: location-microservice
  datasource:
    url: **********************************************
    username: postgres
    password: L00kForX_?
    driver-class-name: org.postgresql.Driver
    hikari:
      # 1) Make sure <PERSON><PERSON> retires connections *before* the DB server closes them.
      #    If your DB server’s wait_timeout is e.g. 10m, set this a bit lower:
      max-lifetime: 600000        # 10 minutes in ms

      # 2) How long a connection can sit idle in the pool before eligible for eviction
      idle-timeout: 300000        # 5 minutes

      # 3) How long to wait for a connection from the pool before timing out
      connection-timeout: 30000   # 30 seconds

      # 4) (Optional) Force <PERSON><PERSON> to run a lightweight test query before giving you a connection
      #    This guarantees the connection is alive, avoiding that “closed” edge case
      connection-test-query: SELECT 1

      # 5) (Optional) Keep idle connections alive by pinging periodically
      keepalive-time: 300000      # 5 minutes

  # JPA and Hibernate Configuration
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        # Performance optimizations
        jdbc:
          batch_size: 25
          fetch_size: 50
        order_inserts: true           # Corrected: Removed brackets
        order_updates: true           # Corrected: Removed brackets
        batch_versioned_data: true    # Corrected: Removed brackets
        # Query optimizations
        query:
          plan_cache_max_size: 2048         # Corrected: Removed brackets
          plan_parameter_metadata_max_size: 128 # Corrected: Removed brackets
        # Statistics for monitoring
        generate_statistics: true     # Corrected: Removed brackets
        session:
          events:
            log:
              # Corrected: Removed brackets, adjusted key for standard Hibernate property
              log_queries_slower_than_ms: 1000

    database-platform: org.hibernate.dialect.PostgreSQLDialect
  data:
    redis:
      host: *************
      port: 6379
      # password: "" # It's better to omit if no password, or keep it explicitly empty if required
      timeout: 2000ms     # Already correct, but "2s" is also valid
      database: 9
  cache:
    type: redis
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html