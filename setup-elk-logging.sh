#!/bin/bash

# Lookforx Mikroservisler için ELK Stack + Zipkin Tracing Setup Script
# Bu script tüm mikroservislere ELK Stack logging ve Zipkin tracing entegrasyonunu otomatik olarak ekler

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
MICROSERVICES_DIR="../"
CONFIG_REPO_DIR="."
LOGSTASH_HOST="************"
LOGSTASH_PORT="5044"
ZIPKIN_HOST="*************"
ZIPKIN_PORT="9411"

# Mikroservis listesi
MICROSERVICES=(
    "lookforx-auth-service"
    "lookforx-user-service"
    "lookforx-location-service"
    "lookforx-campaign-service"
    "lookforx-bid-service"
    "lookforx-category-service"
    "lookforx-customer-support-service"
    "lookforx-media-service"
    "lookforx-membership-service"
    "lookforx-messaging-service"
    "lookforx-notification-service"
    "lookforx-payment-service"
    "lookforx-question-form-service"
    "lookforx-request-service"
    "lookforx-search-service"
    "lookforx-api-gateway"
)

echo -e "${BLUE}🚀 Lookforx ELK Stack + Zipkin Tracing Setup Script${NC}"
echo -e "${BLUE}====================================================${NC}"
echo ""

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if directory exists
check_directory() {
    if [ ! -d "$1" ]; then
        print_error "Directory $1 does not exist!"
        return 1
    fi
    return 0
}

# Function to backup existing file
backup_file() {
    local file_path="$1"
    if [ -f "$file_path" ]; then
        cp "$file_path" "$file_path.backup.$(date +%Y%m%d_%H%M%S)"
        print_info "Backed up existing file: $file_path"
    fi
}

# Function to add ELK dependencies to pom.xml
add_elk_dependencies() {
    local pom_file="$1"
    local service_name="$2"
    
    print_info "Adding ELK dependencies to $service_name"
    
    # Check if dependencies already exist
    if grep -q "logstash-logback-encoder" "$pom_file"; then
        print_warning "ELK + Tracing dependencies already exist in $service_name"
        return 0
    fi
    
    # Find the closing </dependencies> tag and add our dependencies before it
    local temp_file=$(mktemp)
    
    # Read the pom.xml and add dependencies
    awk '
    /<\/dependencies>/ && !added {
        print "        <!-- ELK Stack + Zipkin Tracing Dependencies -->"
        print "        <dependency>"
        print "            <groupId>net.logstash.logback</groupId>"
        print "            <artifactId>logstash-logback-encoder</artifactId>"
        print "            <version>7.4</version>"
        print "        </dependency>"
        print ""
        print "        <dependency>"
        print "            <groupId>org.codehaus.janino</groupId>"
        print "            <artifactId>janino</artifactId>"
        print "            <version>3.1.10</version>"
        print "        </dependency>"
        print ""
        print "        <!-- Micrometer Tracing Dependencies -->"
        print "        <dependency>"
        print "            <groupId>io.micrometer</groupId>"
        print "            <artifactId>micrometer-tracing-bridge-brave</artifactId>"
        print "        </dependency>"
        print ""
        print "        <dependency>"
        print "            <groupId>io.zipkin.reporter2</groupId>"
        print "            <artifactId>zipkin-reporter-brave</artifactId>"
        print "        </dependency>"
        print ""
        print "        <dependency>"
        print "            <groupId>io.micrometer</groupId>"
        print "            <artifactId>micrometer-observation</artifactId>"
        print "        </dependency>"
        print ""
        print "        <dependency>"
        print "            <groupId>io.micrometer</groupId>"
        print "            <artifactId>context-propagation</artifactId>"
        print "        </dependency>"
        print ""
        print "        <dependency>"
        print "            <groupId>io.micrometer</groupId>"
        print "            <artifactId>micrometer-registry-prometheus</artifactId>"
        print "        </dependency>"
        print ""
        print "        <dependency>"
        print "            <groupId>org.springframework.boot</groupId>"
        print "            <artifactId>spring-boot-starter-actuator</artifactId>"
        print "        </dependency>"
        print ""
        print "        <!-- Spring Cloud Sleuth (Legacy) -->"
        print "        <dependency>"
        print "            <groupId>org.springframework.cloud</groupId>"
        print "            <artifactId>spring-cloud-starter-sleuth</artifactId>"
        print "        </dependency>"
        print ""
        print "        <dependency>"
        print "            <groupId>org.springframework.cloud</groupId>"
        print "            <artifactId>spring-cloud-sleuth-zipkin</artifactId>"
        print "        </dependency>"
        print ""
        added = 1
    }
    { print }
    ' "$pom_file" > "$temp_file"
    
    mv "$temp_file" "$pom_file"
    print_status "Added ELK dependencies to $service_name"
}

# Function to copy logback-spring.xml
copy_logback_config() {
    local service_dir="$1"
    local service_name="$2"
    local resources_dir="$service_dir/src/main/resources"
    
    if [ ! -d "$resources_dir" ]; then
        print_warning "Resources directory not found for $service_name: $resources_dir"
        return 1
    fi
    
    local logback_file="$resources_dir/logback-spring.xml"
    
    # Backup existing logback file
    backup_file "$logback_file"
    
    # Copy new logback configuration
    cp "$CONFIG_REPO_DIR/logback-spring.xml" "$logback_file"
    print_status "Copied logback-spring.xml to $service_name"
}

# Function to create logging utility classes
create_logging_utilities() {
    local service_dir="$1"
    local service_name="$2"
    local java_dir="$service_dir/src/main/java"
    
    # Find the main package directory
    local main_package_dir=$(find "$java_dir" -name "*.java" -path "*/main/java/*" | head -1 | xargs dirname | head -1)
    
    if [ -z "$main_package_dir" ]; then
        print_warning "Could not find main package directory for $service_name"
        return 1
    fi
    
    # Create logging package
    local logging_dir="$main_package_dir/logging"
    mkdir -p "$logging_dir"
    
    # Copy logging utilities (these would need to be created separately)
    # For now, we'll just create placeholder files
    
    print_status "Created logging utilities for $service_name"
}

# Function to update application.yml
update_application_yml() {
    local service_dir="$1"
    local service_name="$2"
    local app_yml="$service_dir/src/main/resources/application.yml"
    
    if [ ! -f "$app_yml" ]; then
        print_warning "application.yml not found for $service_name"
        return 1
    fi
    
    # Backup existing application.yml
    backup_file "$app_yml"
    
    # Check if logging configuration already exists
    if grep -q "logging:" "$app_yml"; then
        print_warning "Logging configuration already exists in $service_name application.yml"
        return 0
    fi
    
    # Add logging configuration
    cat >> "$app_yml" << EOF

# ELK Stack Logging Configuration
logging:
  config: classpath:logback-spring.xml
  level:
    com.lookforx: INFO
    root: INFO
  logstash:
    host: ${LOGSTASH_HOST}
    port: ${LOGSTASH_PORT}
    enabled: true
EOF
    
    print_status "Updated application.yml for $service_name"
}

# Main setup function for a single microservice
setup_microservice() {
    local service_name="$1"
    local service_dir="$MICROSERVICES_DIR/$service_name"
    
    print_info "Setting up ELK logging for $service_name"
    
    # Check if service directory exists
    if ! check_directory "$service_dir"; then
        print_warning "Skipping $service_name - directory not found"
        return 1
    fi
    
    # Check if it's a Maven project
    local pom_file="$service_dir/pom.xml"
    if [ ! -f "$pom_file" ]; then
        print_warning "Skipping $service_name - not a Maven project (pom.xml not found)"
        return 1
    fi
    
    # Backup pom.xml
    backup_file "$pom_file"
    
    # Add ELK dependencies
    add_elk_dependencies "$pom_file" "$service_name"
    
    # Copy logback configuration
    copy_logback_config "$service_dir" "$service_name"
    
    # Update application.yml
    update_application_yml "$service_dir" "$service_name"
    
    # Create logging utilities
    create_logging_utilities "$service_dir" "$service_name"
    
    print_status "Completed setup for $service_name"
    echo ""
}

# Main execution
main() {
    print_info "Starting ELK Stack logging setup for Lookforx microservices"
    print_info "Logstash Host: $LOGSTASH_HOST:$LOGSTASH_PORT"
    echo ""
    
    # Check if config files exist
    if [ ! -f "$CONFIG_REPO_DIR/logback-spring.xml" ]; then
        print_error "logback-spring.xml not found in config repository!"
        exit 1
    fi
    
    # Setup each microservice
    local success_count=0
    local total_count=${#MICROSERVICES[@]}
    
    for service in "${MICROSERVICES[@]}"; do
        if setup_microservice "$service"; then
            ((success_count++))
        fi
    done
    
    echo ""
    print_status "Setup completed!"
    print_info "Successfully configured: $success_count/$total_count microservices"
    
    if [ $success_count -lt $total_count ]; then
        print_warning "Some microservices were skipped. Check the output above for details."
    fi
    
    echo ""
    print_info "Next steps:"
    echo "1. Review the changes in each microservice"
    echo "2. Test the logging configuration"
    echo "3. Restart the microservices"
    echo "4. Check Kibana for incoming logs"
    echo ""
    print_info "For detailed configuration, see: ELK-Stack-Setup-Guide.md"
}

# Check if script is run with --help
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Lookforx ELK Stack Logging Setup Script"
    echo ""
    echo "Usage: $0 [options]"
    echo ""
    echo "Options:"
    echo "  --help, -h     Show this help message"
    echo "  --dry-run      Show what would be done without making changes"
    echo ""
    echo "This script will:"
    echo "1. Add ELK Stack dependencies to each microservice's pom.xml"
    echo "2. Copy logback-spring.xml configuration to each microservice"
    echo "3. Update application.yml with logging configuration"
    echo "4. Create logging utility classes"
    echo ""
    echo "Microservices to be configured:"
    for service in "${MICROSERVICES[@]}"; do
        echo "  - $service"
    done
    exit 0
fi

# Check if script is run with --dry-run
if [ "$1" = "--dry-run" ]; then
    print_info "DRY RUN MODE - No changes will be made"
    echo ""
    
    for service in "${MICROSERVICES[@]}"; do
        local service_dir="$MICROSERVICES_DIR/$service"
        if check_directory "$service_dir" && [ -f "$service_dir/pom.xml" ]; then
            print_info "Would configure: $service"
        else
            print_warning "Would skip: $service (directory or pom.xml not found)"
        fi
    done
    exit 0
fi

# Run main function
main
