spring:
  application:
    name: request-microservice

  data:
    mongodb:
      uri: mongodb+srv://lookforx:<EMAIL>/requestdb-test?retryWrites=true&w=majority&appName=lookforx
      auto-index-creation: true
    redis:
      host: *************
      port: 6379
      database: 6
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

  cache:
    type: redis
    redis:
      time-to-live: 3600000

  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

# Logging Configuration
logging:
  level:
    root: INFO
    com.lookforx.requestservice: DEBUG
    org.springframework.data.mongodb: DEBUG
    org.springframework.cache: DEBUG
    org.springframework.kafka: INFO