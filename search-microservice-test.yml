spring:
  application:
    name: search-microservice

  data:
    elasticsearch:
      uris: http://*************:9200
    # Jackson configuration for Java 8 time
    redis:
      host: *************
      port: 6379
      password: ""       # <PERSON><PERSON><PERSON><PERSON><PERSON> "" ya da hiç yazma
      timeout: 2000ms     # veya "2s" şeklinde açıkça zaman birimi ile
      database: 1
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  cache:
    type: redis
management:
  tracing:
    sampling:
      probability: 1.0
    zipkin:
      endpoint: http://************:9411/api/v2/spans

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

elasticsearch:
  index:
    name: categories
  url: *************:9200
  settings:
    analysis:
      analyzer:
        autocomplete_analyzer:
          type: custom
          tokenizer: autocomplete_tokenizer
          filter:
            - lowercase
      tokenizer:
        autocomplete_tokenizer:
          type: edge_ngram
          min_gram: 2
          max_gram: 20
          token_chars:
            - letter
