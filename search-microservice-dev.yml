spring:
  application:
    name: search-microservice

  data:
    elasticsearch:
      uris: http://*************:9200
  # Jackson configuration for Java 8 time
    redis:
      host: *************
      port: 6379
      password: ""       # <PERSON><PERSON><PERSON><PERSON><PERSON> "" ya da hiç yazma
      timeout: 2000ms     # veya "2s" şeklinde açıkça zaman birimi ile
      database: 1

  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  cache:
    type: redis

  kafka:
    bootstrap-servers: *************:9092
    producer:
      # Increase timeouts for cloud Kafka
      request-timeout-ms: 600000      # 10 minutes
      delivery-timeout-ms: 1200000    # 20 minutes
      max-block-ms: 600000            # 10 minutes
      retries: 10                     # Increase retry count
      retry-backoff-ms: 5000          # 5 seconds between retries
      # Batch settings for better performance
      batch-size: 32768               # 32KB batch size
      linger-ms: 100                  # Wait 100ms to batch messages
      buffer-memory: 67108864         # 64MB buffer
      # Compression for better network utilization
      compression-type: gzip
      # Acknowledgment settings
      acks: all                       # Wait for all replicas
      # Serialization
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

management:
  tracing:
    sampling:
      probability: 1.0
    zipkin:
      endpoint: http://*************:9411/api/v2/spans
  endpoints:
    web:
      exposure:
        include: health,info,metrics,circuitbreakers,retries,timelimiters
  endpoint:
    health:
      show-details: always
  health:
    circuitbreakers:
      enabled: true

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

elasticsearch:
  index:
    name: categories
  url: *************:9200
  settings:
    analysis:
      analyzer:
        autocomplete_analyzer:
          type: custom
          tokenizer: autocomplete_tokenizer
          filter:
            - lowercase
      tokenizer:
        autocomplete_tokenizer:
          type: edge_ngram
          min_gram: 2
          max_gram: 20
          token_chars:
            - letter
