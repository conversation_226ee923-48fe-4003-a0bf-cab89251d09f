spring:
  application:
    name: customer-support-microservice

  # MongoDB Configuration
  data:
    mongodb:
      uri: mongodb+srv://lookforx:<EMAIL>/customersupportdb?retryWrites=true&w=majority&appName=lookforx
      auto-index-creation: true

    # Redis Configuration for Customer Support Service
    redis:
      host: *************
      port: 6379
      database: 4  # Unique database for customer support service (exception=3, reference-data=2, category=default)
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

  # Cache Configuration
  cache:
    type: redis
    cache-names:
      - tickets
      - feedback
      - notifications
      - templates
      - users
      - preferences

  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
      accept-empty-string-as-null-object: true

  # Kafka Configuration
  kafka:
    bootstrap-servers: *************:9092
    client-id: customer-support-service
    listener:
      auto-startup: true
      ack-mode: manual_immediate
      concurrency: 3
      poll-timeout: 3000
    consumer:
      group-id: customer-support-service
      auto-offset-reset: earliest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      max-poll-records: 500
      fetch-min-size: 1
      fetch-max-wait: 500
      properties:
        spring.json.trusted.packages: "*"
        client.dns.lookup: use_all_dns_ips
        bootstrap.servers: *************:9092
        session.timeout.ms: 30000
        heartbeat.interval.ms: 10000
        request.timeout.ms: 60000
        retry.backoff.ms: 1000
        reconnect.backoff.ms: 1000
        reconnect.backoff.max.ms: 10000
        metadata.max.age.ms: 300000
        connections.max.idle.ms: 540000
        max.poll.interval.ms: 300000
        auto.commit.interval.ms: 5000
    producer:
      client-id: customer-support-service-producer
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 1
      buffer-memory: 33554432
      compression-type: snappy
      properties:
        bootstrap.servers: *************:9092
        request.timeout.ms: 30000
        delivery.timeout.ms: 120000
        retry.backoff.ms: 100
        retry.backoff.max.ms: 1000
        reconnect.backoff.ms: 50
        reconnect.backoff.max.ms: 1000
        max.in.flight.requests.per.connection: 5
        enable.idempotence: true
        metadata.max.age.ms: 300000
        connections.max.idle.ms: 540000

# Logging Configuration (MongoDB-specific)
logging:
  level:
    root: INFO
    com.lookforx.support: INFO
    org.springframework.data.mongodb: DEBUG
    org.springframework.cache: DEBUG
    org.springframework.web: DEBUG
    org.springframework.kafka: INFO

# Management and Monitoring
management:
  tracing:
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: http://*************:9411/api/v2/spans
  endpoints:
    web:
      exposure:
        include: health,info,metrics,caches
  endpoint:
    caches:
      enabled: true

# Swagger/OpenAPI Configuration
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

# Resilience4j Configuration
resilience4j:
  circuitbreaker:
    instances:
      exceptionService:
        registerHealthIndicator: true
        slidingWindowSize: 10
        permittedNumberOfCallsInHalfOpenState: 3
        minimumNumberOfCalls: 5
        waitDurationInOpenState: 10s
        failureRateThreshold: 50
        eventConsumerBufferSize: 10
