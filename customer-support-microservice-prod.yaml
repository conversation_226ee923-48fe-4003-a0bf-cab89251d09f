spring:
  application:
    name: customer-support-microservice
  data:
    elasticsearch:
      uris: http://***********:9200/
  datasource:
    url: *******************************************************
    username: postgres
    password: L00kForX_?
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    database-platform: org.hibernate.dialect.PostgreSQLDialect
  logging:
    level:
      root: INFO
      com.lookforx.support: INFO
  cache:
    cache-names: categories  # Cache ismi (burada categories)
    redis:
      host: ***********            # Docker Compose servis adı
      port: 6379
      password: ""           # Redis şifresi, eğer var ise
      timeout: 2000
  zipkin:
    base-url: http://***********:9411

management:
  tracing:
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: http://***********:9411/api/v2/spans
