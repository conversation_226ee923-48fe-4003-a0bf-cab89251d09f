spring:
  application:
    name: reference-data-microservice
  datasource:
    url: ***************************************************
    username: postgres
    password: L00kForX_?
    driver-class-name: org.postgresql.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.PostgreSQLDialect
  data:
    redis:
      host: *************
      port: 6379
      password: ""       # <PERSON><PERSON><PERSON><PERSON> yoksa "" ya da hiç yazma
      timeout: 2000ms     # veya "2s" şeklinde açıkça zaman birimi ile
      database: 2
  cache:
    type: redis
  # Jackson configuration for Java 8 time
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  logging:
    level:
      org.hibernate.SQL: DEBUG
      org.hibernate.type.descriptor.sql.BasicBinder: TRACE
      springframework.web : DEBUG
      org.springframework.data.redis: DEBUG
      org.springframework.cache: DEBUG
management:
  tracing:
    sampling:
      probability: 1.0
    zipkin:
      endpoint: http://************:9411/api/v2/spans