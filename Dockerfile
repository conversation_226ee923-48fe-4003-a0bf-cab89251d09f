# Simple Dockerfile for notification service
FROM openjdk:23-jdk-slim

# Install curl for health checks
RUN apt-get update && \
    apt-get install -y curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Copy Java application
COPY target/notification-service-*.jar /app/notification-service.jar

# Set working directory
WORKDIR /app

# Expose port
EXPOSE 8093

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8093/actuator/health || exit 1

# Run the application
ENTRYPOINT ["java", "-jar", "notification-service.jar"]
