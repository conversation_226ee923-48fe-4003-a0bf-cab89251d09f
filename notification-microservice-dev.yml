spring:
  application:
    name: notification-microservice
  data:
    mongodb:
      uri: mongodb+srv://lookforx:<EMAIL>/notificationdb
    redis:
      host: *************
      port: 6379
      database: 3  # exception-service uses db 3 according to memories
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  cache:
    type: redis

  # Jackson configuration for Java 8 time
  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  logging:
    level:
      root: INFO
      com.lookforx.notificationservice: INFO

  kafka:
    bootstrap-servers: *************:9092
    listener:
      # Re-enable Kafka listeners after hostname resolution fix
      auto-startup: true
    consumer:
      group-id: notification-service
      auto-offset-reset: earliest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        "[spring.json.trusted.packages]": "*"
        # Force client to use IP instead of hostname resolution
        "[client.dns.lookup]": use_all_dns_ips
        # Override broker hostname resolution
        "[bootstrap.servers]": *************:9092
        # Increase timeouts for remote connection
        "[session.timeout.ms]": 30000
        "[heartbeat.interval.ms]": 10000
        "[request.timeout.ms]": 60000
        # Retry configuration
        "[retry.backoff.ms]": 1000
        "[reconnect.backoff.ms]": 1000
        "[reconnect.backoff.max.ms]": 10000
        # Metadata refresh settings
        "[metadata.max.age.ms]": 300000
        # Connection settings
        "[connections.max.idle.ms]": 540000
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      properties:
        # Producer timeout settings
        "[request.timeout.ms]": 60000
        "[delivery.timeout.ms]": 120000
        "[retry.backoff.ms]": 1000
        # Override broker hostname resolution for producer
        "[bootstrap.servers]": *************:9092

notification:
  email:
    enabled: false
    from: <EMAIL>
    from-name: LookForX
  sms:
    enabled: false
  push:
    enabled: false

management:
  tracing:
    sampling:
      probability: 1.0
  zipkin:
    tracing:
      endpoint: http://*************:9411/api/v2/spans

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
