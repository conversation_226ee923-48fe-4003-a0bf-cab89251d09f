spring:
  application:
    name: auth-microservice
    version: 1.0.0
  data:
    mongodb:
      uri: mongodb+srv://lookforx:<EMAIL>/authdb?retryWrites=true&w=majority&appName=lookforx
      auto-index-creation: true
  # ─── LOGGING CONFIG ─────────────────────────────────────────────────────────
  logging:
    level:
      org.springframework.security: DEBUG
      org.springframework.security.oauth2: DEBUG
      org.springframework.web: DEBUG
      com.lookforx: DEBUG

  security:
    oauth2:
      client:
        registration:
          google:
            client-id: ************-j0h7ldkq77gpe46dhtl2hjjnk97gnir5.apps.googleusercontent.com
            client-secret: 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
            redirect-uri: http://localhost:8080/auth-service/api/v1/oauth2/callback/google
            scope:
              - email
              - profile
            authorization-grant-type: authorization_code
            client-name: Google
        provider:
          google:
            authorization-uri: https://accounts.google.com/o/oauth2/auth
            token-uri: https://oauth2.googleapis.com/token
            user-info-uri: https://www.googleapis.com/oauth2/v3/userinfo
            user-name-attribute: sub

app:
  oauth2:
    # Primary redirect URI for OAuth2 callback
    redirectUri: http://localhost:8080/auth-service/api/v1/oauth2/callback/google
    # Frontend redirect URI after successful authentication
    frontendRedirectUri: http://localhost:3000/auth/success
    # Frontend redirect URI after failed authentication
    frontendErrorUri: http://localhost:3000/auth/error


management:
  tracing:
    sampling:
      probability: 1.0
    zipkin:
      endpoint: http://************:9411/api/v2/spans
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

jwt:
  secret: 404E635266556A586E3272357538782F413F4428472B4B6250645367566B5970
  access-token:
    expiration: ******** # 1 day in milliseconds
  refresh-token:
    expiration: ********* # 7 days in milliseconds