apiVersion: apps/v1
kind: Deployment
metadata:
  name: notification-service
  namespace: lookforx
  labels:
    app: notification-service
    version: v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: notification-service
  template:
    metadata:
      labels:
        app: notification-service
        version: v1
    spec:
      containers:
      - name: notification-service
        image: lookforx/notification-service:latest
        ports:
        - containerPort: 8093
          name: http
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8093
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8093
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: notification-service
  namespace: lookforx
  labels:
    app: notification-service
spec:
  selector:
    app: notification-service
  ports:
  - name: http
    port: 8093
    targetPort: 8093
    protocol: TCP
  type: ClusterIP
