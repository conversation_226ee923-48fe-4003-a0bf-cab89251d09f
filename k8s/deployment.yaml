apiVersion: apps/v1
kind: Deployment
metadata:
  name: config-server
  labels:
    app: config-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: config-server
  template:
    metadata:
      labels:
        app: config-server
    spec:
      containers:
        - name: config-server
          image: europe-central2-docker.pkg.dev/integrated-net-462820-g4/lookforx-repo/config-server:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8888
          env:
            - name: spring.cloud.config.failFast
              value: "false"
            - name: eureka.client.service-url.defaultZone
              value: "http://eureka-server:8761/eureka/"
            - name: eureka.client.register-with-eureka
              value: "true"
            - name: eureka.client.fetch-registry
              value: "true"
            - name: EUREKA_INSTANCE_PREFER_IP_ADDRESS
              value: "false"
            - name: EUREKA_INSTANCE_HOSTNAME
              value: "config-server"
