# api gateway deployment yml (potential modification)
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  labels:
    app: api-gateway
spec:
  replicas: 1
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
        - name: api-gateway
          image: europe-central2-docker.pkg.dev/lookforx-test/lookforx-repo/api-gateway:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8080
          env:
            - name: EUREKA_CLIENT_SERVICEURL_DEFAULTZONE
              value: "http://eureka-server:8761/eureka/"
            - name: EUREKA_CLIENT_REGISTER_WITH_EUREKA
              value: "true"
            - name: EUREKA_CLIENT_FETCH_REGISTRY
              value: "true"
            - name: SPRING_CLOUD_DISCOVERY_ENABLED
              value: "true"
            - name: SPRING_CLOUD_DISCOVERY_SERVICE_ID # This should be the API Gateway's own ID
              value: "api-gateway" # Changed from "config-server"
            - name: SPRING_CLOUD_CONFIG_FAIL_FAST
              value: "true"
            - name: SPRING_PROFILES_ACTIVE
              value: "dev"
            - name: SPRING_CLOUD_CONFIG_URI
              value: "http://config-server:8888"