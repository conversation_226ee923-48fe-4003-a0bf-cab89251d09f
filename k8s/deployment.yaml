apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  labels:
    app: auth-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
        - name: auth-service
          image: europe-central2-docker.pkg.dev/lookforx-test/lookforx-repo/auth-service:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 8081
          env:
            - name: EUREKA_CLIENT_SERVICEURL_DEFAULTZONE
              value: "http://eureka-server:8761/eureka/"
            - name: EUREKA_CLIENT_REGISTER_WITH_EUREKA
              value: "true"
            - name: EUREKA_CLIENT_FETCH_REGISTRY
              value: "true"
            - name: SPRING_CLOUD_DISCOVERY_ENABLED
              value: "true"
            - name: SPRING_CLOUD_DISCOVERY_SERVICE_ID
              value: "api-gateway"
            - name: SPRING_CLOUD_CONFIG_FAIL_FAST
              value: "true"
            - name: SPRING_PROFILES_ACTIVE
              value: "dev"
            - name: SPRING_CLOUD_CONFIG_URI
              value: "http://config-server:8888"
            - name: SPRING_DATASOURCE_URL
              value: "***************************************"
            - name: SPRING_DATASOURCE_USERNAME
              value: "postgres"
            - name: SPRING_DATASOURCE_PASSWORD
              value: "L00kForX_?"

        - name: cloudsql-proxy
          image: gcr.io/cloudsql-docker/gce-proxy:1.33.3
          command:
            - "/cloud_sql_proxy"
            - "-instances=lookforx-test:europe-central2:lookforx-postgresql=tcp:5432"
            - "-credential_file=/secrets/cloudsql/credentials.json"
          volumeMounts:
            - name: cloudsql-instance-credentials
              mountPath: /secrets/cloudsql
              readOnly: true

      volumes:
        - name: cloudsql-instance-credentials
          secret:
            secretName: cloudsql-instance-credentials
