apiVersion: apps/v1
kind: Deployment
metadata:
  name: eureka-server
  labels:
    app: eureka-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: eureka-server
  template:
    metadata:
      labels:
        app: eureka-server
    spec:
      containers:
        - name: eureka-server
          #projectId: integrated-net-462820-g4
          image: europe-central2-docker.pkg.dev/integrated-net-462820-g4/lookforx-repo/eureka-server:latest
          ports:
            - containerPort: 8761
          env:
            - name: EUREKA_CLIENT_REGISTER_WITH_EUREKA
              value: "false"
            - name: EUREKA_CLIENT_FETCH_REGISTRY
              value: "false"
            - name: EUREKA_SERVER_ENABLE_SELF_PRESERVATION
              value: "false"
          resources:
            requests:
              cpu: "100m"
              memory: "256Mi"
            limits:
              cpu: "500m"
              memory: "512Mi"
          readinessProbe:
            httpGet:
              path: /
              port: 8761
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /
              port: 8761
            initialDelaySeconds: 30
            periodSeconds: 20
