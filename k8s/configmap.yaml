apiVersion: v1
kind: ConfigMap
metadata:
  name: notification-service-config
  namespace: lookforx
data:
  application-k8s.yml: |
    spring:
      application:
        name: notification-service
      cloud:
        config:
          enabled: false
      datasource:
        url: ******************************************************
        username: postgres
        password: postgres
        driver-class-name: org.postgresql.Driver
      jpa:
        hibernate:
          ddl-auto: update
        show-sql: false
        properties:
          hibernate:
            dialect: org.hibernate.dialect.PostgreSQLDialect
      data:
        mongodb:
          uri: mongodb://mongo-service:27017/eventdb
      kafka:
        bootstrap-servers: kafka-service:9092
        producer:
          key-serializer: org.apache.kafka.common.serialization.StringSerializer
          value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
        consumer:
          group-id: notification-service
          key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
          value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
          properties:
            spring.json.trusted.packages: "*"
    
    eureka:
      client:
        enabled: false

    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
      endpoint:
        health:
          show-details: always

    logging:
      level:
        com.lookforx: INFO
        org.springframework.kafka: WARN
