# Lookforx Mikroservisler için ELK Stack + Zipkin Tracing Rehberi

## 📋 İçindekiler
1. [<PERSON><PERSON>](#genel-bakış)
2. [ELK Stack Konfigürasyonu](#elk-stack-konfigürasyonu)
3. [Zipkin Tracing Entegrasyonu](#zipkin-tracing-entegrasyonu)
4. [<PERSON><PERSON><PERSON><PERSON><PERSON> Entegrasyonu](#mikroservis-entegrasyonu)
5. [Logging ve Tracing Standartları](#logging-ve-tracing-standartları)
6. [Monitoring ve Alerting](#monitoring-ve-alerting)
7. [Troubleshooting](#troubleshooting)

---

## 🌟 <PERSON><PERSON>ış

Bu rehber, Lookforx mikroservis mimarisinde merkezi logging için ELK Stack (Elasticsearch, Logstash, Kibana) ve distributed tracing için Zipkin entegrasyonunu açıklar.

### ELK Stack + Zipkin Bileşenleri
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Microservice  │    │   Microservice  │    │   Microservice  │
│   (Logback +    │    │   (Logback +    │    │   (Logback +    │
│    Micrometer)  │    │    Micrometer)  │    │    Micrometer)  │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          ├──────────────────────┼──────────────────────┤
          │ (Logs)               │ (Logs)               │ (Logs)
          ▼                      ▼                      ▼
     ┌────────────────────────────────────────────────────────┐
     │                   LOGSTASH                             │
     │               (************:5044)                     │
     └────────────────────────┬───────────────────────────────┘
                              │ (Structured Logs)
                              ▼
     ┌────────────────────────────────────────────────────────┐
     │                ELASTICSEARCH                           │
     │                (Data Storage)                          │
     └────────────────────────┬───────────────────────────────┘
                              │
                              ▼
     ┌────────────────────────────────────────────────────────┐
     │                   KIBANA                               │
     │               (Log Visualization)                      │
     └────────────────────────────────────────────────────────┘

          │ (Traces)             │ (Traces)             │ (Traces)
          ▼                      ▼                      ▼
     ┌────────────────────────────────────────────────────────┐
     │                   ZIPKIN                               │
     │               (*************:9411)                    │
     │              (Distributed Tracing)                    │
     └────────────────────────────────────────────────────────┘
```

### Avantajları
- ✅ **Merkezi Logging**: Tüm mikroservislerden tek noktada log toplama
- ✅ **Distributed Tracing**: Zipkin ile request'lerin mikroservisler arası takibi
- ✅ **Real-time Monitoring**: Anlık log ve trace izleme
- ✅ **Structured Logging**: JSON formatında yapılandırılmış loglar
- ✅ **Advanced Search**: Elasticsearch ile güçlü arama özellikleri
- ✅ **Visualization**: Kibana ile dashboard'lar ve Zipkin UI ile trace görselleştirme
- ✅ **Performance Monitoring**: Micrometer ile detaylı performans metrikleri
- ✅ **Correlation**: Log ve trace'lerin otomatik korelasyonu
- ✅ **Alerting**: Kritik olaylar için otomatik uyarılar
- ✅ **Baggage Propagation**: Context bilgilerinin mikroservisler arası taşınması

---

## 🔍 Zipkin Tracing Entegrasyonu

### Zipkin Server Konfigürasyonu (*************:9411)

Zipkin server'ınız zaten kurulu ve çalışıyor. Mikroservisler otomatik olarak trace'leri bu adrese gönderecek.

### Micrometer Tracing Özellikleri

#### Trace Sampling
```yaml
# Development: %100 sampling
management.tracing.sampling.probability: 1.0

# Test: %50 sampling
management.tracing.sampling.probability: 0.5

# Production: %10 sampling
management.tracing.sampling.probability: 0.1
```

#### Baggage Propagation
```yaml
spring:
  sleuth:
    baggage:
      correlation-enabled: true
      correlation-fields: user-id,session-id,request-id
      remote-fields: user-id,session-id,request-id,correlation-id
```

#### Custom Tags
```java
// Otomatik eklenen tag'ler
- service.name: mikroservis adı
- operation: işlem türü (http.request, db.operation, etc.)
- http.method: HTTP metodu
- http.status_code: HTTP status kodu
- user.id: kullanıcı kimliği
- business.context: iş bağlamı
- error: hata durumu
```

### Trace Correlation with Logs

Her log entry'si otomatik olarak şu trace bilgilerini içerir:
```json
{
  "trace_id": "abc123def456789",
  "span_id": "123456789",
  "parent_span_id": "987654321",
  "baggage": "user-id=user123,session-id=sess456"
}
```

---

## ⚙️ ELK Stack Konfigürasyonu

### Logstash Konfigürasyonu (************)

Mevcut `logstash.conf` dosyanız:
```ruby
input {
  beats {
    port => 5044
  }
}

filter {
  # JSON parsing ve field enrichment
  if [message] {
    json {
      source => "message"
    }
  }
  
  # Service name extraction
  if [service_name] {
    mutate {
      add_field => { "[@metadata][index_prefix]" => "%{service_name}" }
    }
  }
  
  # Timestamp parsing
  if [timestamp] {
    date {
      match => [ "timestamp", "yyyy-MM-dd HH:mm:ss.SSS" ]
    }
  }
}

output {
  elasticsearch {
    hosts => ["http://elasticsearch:9200"]
    index => "lookforx-%{[@metadata][index_prefix]:-microservices}-%{+YYYY.MM.dd}"
  }
}
```

### Elasticsearch Index Template
```json
{
  "index_patterns": ["lookforx-*"],
  "template": {
    "settings": {
      "number_of_shards": 1,
      "number_of_replicas": 1,
      "index.refresh_interval": "5s"
    },
    "mappings": {
      "properties": {
        "@timestamp": { "type": "date" },
        "service_name": { "type": "keyword" },
        "environment": { "type": "keyword" },
        "level": { "type": "keyword" },
        "message": { "type": "text" },
        "logger": { "type": "keyword" },
        "thread": { "type": "keyword" },
        "trace_id": { "type": "keyword" },
        "span_id": { "type": "keyword" },
        "user_id": { "type": "keyword" },
        "request_id": { "type": "keyword" },
        "ip_address": { "type": "ip" },
        "response_time": { "type": "long" },
        "response_status": { "type": "integer" },
        "business_context": { "type": "keyword" }
      }
    }
  }
}
```

---

## 🔧 Mikroservis Entegrasyonu

### 1. Dependencies Ekleme

Her mikroservisin `pom.xml` dosyasına şu dependency'leri ekleyin:

```xml
<!-- ELK Stack Logging Dependencies -->
<dependency>
    <groupId>net.logstash.logback</groupId>
    <artifactId>logstash-logback-encoder</artifactId>
    <version>7.4</version>
</dependency>

<dependency>
    <groupId>org.codehaus.janino</groupId>
    <artifactId>janino</artifactId>
    <version>3.1.10</version>
</dependency>

<!-- Micrometer Tracing Dependencies -->
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-tracing-bridge-brave</artifactId>
</dependency>

<dependency>
    <groupId>io.zipkin.reporter2</groupId>
    <artifactId>zipkin-reporter-brave</artifactId>
</dependency>

<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-observation</artifactId>
</dependency>

<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>context-propagation</artifactId>
</dependency>

<!-- Metrics and Monitoring -->
<dependency>
    <groupId>io.micrometer</groupId>
    <artifactId>micrometer-registry-prometheus</artifactId>
</dependency>

<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>

<!-- Legacy Spring Cloud Sleuth (for Spring Boot 2.x) -->
<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-sleuth</artifactId>
</dependency>

<dependency>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-sleuth-zipkin</artifactId>
</dependency>
```

### 2. Logback Konfigürasyonu

Her mikroservisin `src/main/resources/` dizinine `logback-spring.xml` dosyasını kopyalayın.

### 3. Application Properties

Spring Cloud Config'den otomatik olarak alınacak konfigürasyonlar:

```yaml
# Otomatik olarak Config Server'dan gelecek
logging:
  logstash:
    host: ************
    port: 5044
    enabled: true
```

### 4. Logging Utility Kullanımı

```java
@RestController
@Slf4j
public class UserController {

    @Autowired
    private LoggingUtils loggingUtils;

    @Autowired
    private TracingUtils tracingUtils;

    @GetMapping("/users/{id}")
    public ResponseEntity<User> getUser(@PathVariable Long id, HttpServletRequest request) {
        // Start business operation span
        Span businessSpan = tracingUtils.startBusinessSpan("get-user", "USER_RETRIEVAL");

        try {
            // Add user context to span and baggage
            tracingUtils.addTag(businessSpan, "user.id", id.toString());
            tracingUtils.setBaggage("user-id", id.toString());

            // Request context'i initialize et
            LoggingUtils.initializeRequestContext(request);
            LoggingUtils.setBusinessContext("USER_RETRIEVAL");

            // Database operation with tracing
            Span dbSpan = tracingUtils.startDatabaseSpan("SELECT", "users", "postgresql");
            User user;
            try {
                user = userService.findById(id);
                tracingUtils.addAnnotation(dbSpan, "user.found");
            } catch (Exception e) {
                tracingUtils.addError(dbSpan, e);
                throw e;
            } finally {
                dbSpan.end();
            }

            // Success logging with trace correlation
            LoggingUtils.logBusinessEvent(logger, "USER_RETRIEVED",
                "User successfully retrieved", "userId", id, "traceId",
                tracingUtils.getCurrentTraceId());

            tracingUtils.addAnnotation(businessSpan, "user.retrieval.success");
            return ResponseEntity.ok(user);

        } catch (Exception e) {
            // Error logging and tracing
            tracingUtils.addError(businessSpan, e);
            LoggingUtils.logApplicationError(logger, "GET_USER", e,
                "Failed to retrieve user with id: " + id);
            throw e;

        } finally {
            businessSpan.end();
            LoggingUtils.clearContext();
        }
    }

    @PostMapping("/users")
    public ResponseEntity<User> createUser(@RequestBody CreateUserRequest request) {
        // Use TracingUtils for automatic span management
        return tracingUtils.executeWithTracing("create-user", "USER_CREATION", () -> {
            // Set business context
            tracingUtils.setBaggage("operation", "user-creation");

            // Business logic with automatic error handling
            User user = userService.createUser(request);

            // Log success
            LoggingUtils.logBusinessEvent(logger, "USER_CREATED",
                "New user created", "userId", user.getId());

            return ResponseEntity.status(201).body(user);
        });
    }
}
```

---

## 📊 Logging Standartları

### Log Levels

```yaml
TRACE: Çok detaylı debug bilgileri
DEBUG: Development ve troubleshooting için
INFO:  Normal işlem akışı bilgileri
WARN:  Potansiyel problemler
ERROR: Hata durumları
FATAL: Kritik sistem hataları
```

### Structured Logging Format

```json
{
  "@timestamp": "2024-01-15T10:30:45.123Z",
  "service_name": "user-microservice",
  "environment": "production",
  "level": "INFO",
  "message": "User successfully created",
  "logger": "com.lookforx.user.UserService",
  "thread": "http-nio-8081-exec-1",
  "trace_id": "abc123def456",
  "span_id": "789xyz",
  "user_id": "user123",
  "request_id": "req-456789",
  "ip_address": "*************",
  "response_time": 150,
  "response_status": 201,
  "business_context": "USER_CREATION"
}
```

### Business Context Categories

```java
// API Operations
BUSINESS_CONTEXT = "API_REQUEST"
BUSINESS_CONTEXT = "API_RESPONSE"

// Business Events
BUSINESS_CONTEXT = "USER_CREATION"
BUSINESS_CONTEXT = "ORDER_PROCESSING"
BUSINESS_CONTEXT = "PAYMENT_PROCESSING"

// Security Events
BUSINESS_CONTEXT = "AUTHENTICATION"
BUSINESS_CONTEXT = "AUTHORIZATION"
BUSINESS_CONTEXT = "SECURITY_VIOLATION"

// System Events
BUSINESS_CONTEXT = "DATABASE_OPERATION"
BUSINESS_CONTEXT = "CACHE_OPERATION"
BUSINESS_CONTEXT = "EXTERNAL_SERVICE_CALL"

// Performance Events
BUSINESS_CONTEXT = "PERFORMANCE_METRIC"
BUSINESS_CONTEXT = "SLOW_QUERY"
```

---

## 📈 Monitoring ve Alerting

### Kibana Dashboard'ları

#### 1. Service Overview Dashboard
```
- Request/Response metrics per service
- Error rates and response times
- Active users and sessions
- System health indicators
```

#### 2. Error Monitoring Dashboard
```
- Error count by service
- Error types and frequencies
- Stack trace analysis
- Error trends over time
```

#### 3. Performance Dashboard
```
- Response time percentiles
- Throughput metrics
- Database query performance
- Cache hit/miss ratios
```

#### 4. Security Dashboard
```
- Authentication failures
- Authorization violations
- Suspicious activity patterns
- IP-based threat analysis
```

### Alerting Rules

```yaml
# High Error Rate Alert
- alert: HighErrorRate
  expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
  for: 2m
  labels:
    severity: critical
  annotations:
    summary: "High error rate detected in {{ $labels.service_name }}"

# Slow Response Time Alert
- alert: SlowResponseTime
  expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
  for: 5m
  labels:
    severity: warning
  annotations:
    summary: "Slow response time in {{ $labels.service_name }}"

# Security Alert
- alert: SecurityViolation
  expr: increase(security_violations_total[5m]) > 10
  for: 1m
  labels:
    severity: critical
  annotations:
    summary: "Security violations detected in {{ $labels.service_name }}"
```

---

## 🔍 Troubleshooting

### Common Issues

#### 1. Logstash Connection Issues
```bash
# Check Logstash connectivity
telnet ************ 5044

# Check microservice logs
tail -f logs/microservice-name.log | grep -i logstash
```

#### 2. Missing Logs in Elasticsearch
```bash
# Check Elasticsearch indices
curl -X GET "************:9200/_cat/indices/lookforx-*?v"

# Check Logstash logs
docker logs logstash-container
```

#### 3. Performance Issues
```yaml
# Increase async buffer sizes
logging:
  async:
    queue-size: 2048
    ring-buffer-size: 16384
```

### Debugging Commands

```bash
# Check log file sizes
du -sh logs/*.log

# Monitor real-time logs
tail -f logs/service-name.log | jq '.'

# Search specific patterns
grep -r "ERROR" logs/ | head -20

# Check Logstash pipeline
curl -X GET "************:9600/_node/stats/pipelines"
```

### Health Checks

```bash
# Elasticsearch health
curl -X GET "************:9200/_cluster/health"

# Logstash health
curl -X GET "************:9600/_node/stats"

# Kibana health
curl -X GET "************:5601/api/status"
```

---

## 🎯 Best Practices

### 1. Log Message Guidelines
- Use structured logging with consistent field names
- Include correlation IDs for request tracing
- Mask sensitive information (passwords, tokens)
- Use appropriate log levels
- Include context information

### 2. Performance Optimization
- Use async logging for high-throughput services
- Configure appropriate buffer sizes
- Implement log sampling for very high-volume logs
- Use conditional logging for debug statements

### 3. Security Considerations
- Never log sensitive data (passwords, credit cards, etc.)
- Implement log access controls
- Use secure transport (TLS) for log transmission
- Regular log retention and cleanup

### 4. Operational Guidelines
- Monitor log volume and storage usage
- Set up automated alerts for critical errors
- Implement log rotation and archival
- Regular backup of critical logs

---

## 📚 Useful Queries

### Kibana Search Queries

```javascript
// Find all errors in last hour
level:ERROR AND @timestamp:[now-1h TO now]

// Find slow requests
response_time:>5000 AND @timestamp:[now-1h TO now]

// Find specific user activity
user_id:"user123" AND @timestamp:[now-24h TO now]

// Find authentication failures
business_context:"AUTHENTICATION" AND level:WARN

// Find database errors
logger:*repository* AND level:ERROR

// Find external service failures
business_context:"EXTERNAL_SERVICE_CALL" AND response_status:>400
```

Bu rehber ile Lookforx mikroservislerinde merkezi logging sistemi başarıyla kurulmuş ve yönetilmektedir! 🚀
