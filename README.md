# 🌍 LookForX Location Service

A high-performance microservice for managing geographical location data including countries, states, and cities with Redis caching support.

## 📋 Table of Contents

- [Features](#features)
- [Tech Stack](#tech-stack)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [API Endpoints](#api-endpoints)
- [Caching](#caching)
- [Testing](#testing)
- [Docker](#docker)
- [Contributing](#contributing)

## ✨ Features

- **Complete Location Data**: Countries, states, and cities management
- **High Performance**: Redis caching with cache warmup on startup
- **RESTful API**: Clean and intuitive REST endpoints
- **Data Projections**: Optimized database queries with JPA projections
- **Service Discovery**: Eureka client integration
- **Configuration Management**: Spring Cloud Config integration
- **Health Monitoring**: Actuator endpoints for monitoring
- **Comprehensive Testing**: Unit and integration tests

## 🛠 Tech Stack

- **Java 21**
- **Spring Boot 3.4.5**
- **Spring Data JPA**
- **Spring Cache with Redis**
- **PostgreSQL**
- **Spring Cloud (Eureka, Config)**
- **Maven**
- **Docker**

## 📋 Prerequisites

- Java 21 or higher
- Maven 3.6+
- PostgreSQL 12+
- Redis 6+
- Docker (optional)

## 🚀 Installation

### 1. Clone the repository
```bash
git clone <repository-url>
cd lookforx-location-service
```

### 2. Database Setup
```sql
-- Create database
CREATE DATABASE lookforx_location;

-- Import location data (countries, states, cities)
-- SQL scripts should be provided separately
```

### 3. Redis Setup
```bash
# Using Docker
docker run -d --name redis -p 6379:6379 redis:latest

# Or install Redis locally
# macOS: brew install redis
# Ubuntu: sudo apt-get install redis-server
```

### 4. Build and Run
```bash
# Build the project
mvn clean compile

# Run the application
mvn spring-boot:run
```

The service will start on `http://localhost:8083`

## ⚙️ Configuration

### Application Properties

Key configuration properties in `application.yml`:

```yaml
server:
  port: 8083

spring:
  application:
    name: location-microservice
  
  datasource:
    url: **************************************************
    username: ${DB_USERNAME:your_username}
    password: ${DB_PASSWORD:your_password}
  
  data:
    redis:
      host: localhost
      port: 6379
  
  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1 hour

eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/
```

### Environment Variables

- `DB_USERNAME`: Database username
- `DB_PASSWORD`: Database password
- `REDIS_HOST`: Redis host (default: localhost)
- `REDIS_PORT`: Redis port (default: 6379)

## 🔗 API Endpoints

### Countries

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/locations/countries` | Get all countries |
| GET | `/api/v1/locations/countries/{id}` | Get country by ID |
| GET | `/api/v1/locations/countries/iso2/{iso2}` | Get country by ISO2 code |
| GET | `/api/v1/locations/countries/{id}/states` | Get states by country |

### States

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/locations/states/{id}` | Get state by ID |
| GET | `/api/v1/locations/states/{id}/cities` | Get cities by state |

### Cities

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/locations/cities/{id}` | Get city by ID |

### Example Requests

```bash
# Get all countries
curl http://localhost:8083/api/v1/locations/countries

# Get country by ISO2 code
curl http://localhost:8083/api/v1/locations/countries/iso2/US

# Get states of a country
curl http://localhost:8083/api/v1/locations/countries/233/states

# Get cities of a state
curl http://localhost:8083/api/v1/locations/states/1456/cities
```

## 🚀 Caching

The service implements Redis caching for optimal performance:

### Cache Configuration
- **Cache Provider**: Redis
- **TTL**: 1 hour (configurable)
- **Cache Warmup**: Automatic on startup
- **Cache Keys**: Optimized for different query patterns

### Cached Operations
- Country by ID
- Country by ISO2 code
- State by ID
- City by ID
- Popular countries and states

### Cache Warmup
On startup, the service automatically caches:
- All countries
- Popular countries (US, GB, CA, AU, DE, FR, TR, JP, CN, IN)
- Popular cities from major countries
- Popular states from major countries

## 🧪 Testing

### Run Tests
```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=LocationControllerTest

# Run with coverage
mvn test jacoco:report
```

### Test Categories
- **Unit Tests**: Service layer and utility classes
- **Integration Tests**: Controller and repository layers
- **Cache Tests**: Redis caching functionality

## 🐳 Docker

### Build Docker Image
```bash
docker build -t lookforx-location-service .
```

### Run with Docker Compose
```yaml
version: '3.8'
services:
  location-service:
    image: lookforx-location-service
    ports:
      - "8083:8083"
    environment:
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - redis
  
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: lookforx_location
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
  
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
```

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:8083/actuator/health
```

### Metrics
```bash
curl http://localhost:8083/actuator/metrics
```

### Cache Statistics
```bash
curl http://localhost:8083/actuator/caches
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🔧 Development

### Local Development Setup

1. **Start required services:**
```bash
# Start PostgreSQL
docker run -d --name postgres -p 5432:5432 -e POSTGRES_DB=lookforx_location -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=password postgres:15

# Start Redis
docker run -d --name redis -p 6379:6379 redis:latest

# Start Eureka Server (if not running)
# Start Config Server (if not running)
```

2. **Run the application:**
```bash
mvn spring-boot:run -Dspring.profiles.active=dev
```

### Code Quality

- **Checkstyle**: Code style enforcement
- **SpotBugs**: Static analysis
- **JaCoCo**: Code coverage
- **SonarQube**: Code quality metrics

### Performance Considerations

- **Database Indexing**: Optimized indexes on frequently queried columns
- **Connection Pooling**: HikariCP for efficient database connections
- **Cache Strategy**: Multi-level caching with Redis
- **Query Optimization**: JPA projections for minimal data transfer

## 🚨 Troubleshooting

### Common Issues

1. **Service won't start:**
   - Check if PostgreSQL is running
   - Verify database connection parameters
   - Ensure Redis is accessible

2. **Cache not working:**
   - Verify Redis connection
   - Check cache configuration
   - Monitor Redis logs

3. **Slow performance:**
   - Check database indexes
   - Monitor cache hit rates
   - Review query execution plans

### Logs

```bash
# View application logs
tail -f logs/application.log

# View cache statistics
curl http://localhost:8083/actuator/caches
```

## 📈 Performance Metrics

- **Startup Time**: ~10 seconds (including cache warmup)
- **Response Time**: <50ms (cached), <200ms (database)
- **Throughput**: 1000+ requests/second
- **Cache Hit Rate**: >90% for popular endpoints

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the [Wiki](wiki) for detailed documentation

---

**Made with ❤️ by LookForX Team**
