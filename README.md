# LookforX Category Service

This is the category management microservice for the LookforX platform, built with Spring Boot.

## Features

- Hierarchical category management (main categories and subcategories)
- Multi-language support for category names and descriptions
- Category type classification (PRODUCT, SERVICE, etc.)
- RESTful API for category CRUD operations
- Redis caching for improved performance
- Elasticsearch integration for advanced search
- Real-time category indexing and search
- Category validation and business rules

## Tech Stack

- **Framework**: Spring Boot 3.4.5
- **Language**: Java 21
- **Database**: PostgreSQL 15+
- **Cache**: Redis 7+
- **Search Engine**: Elasticsearch 8+
- **Service Discovery**: Eureka Client
- **Configuration**: Spring Cloud Config
- **Documentation**: Springdoc OpenAPI (Swagger)
- **Build Tool**: Maven 3.8+
- **Testing**: JUnit 5, Mockito, TestContainers
- **Code Coverage**: JaCoCo
- **Containerization**: Docker
- **Deployment**: Kubernetes (GKE)

## Getting Started

### Prerequisites

- JDK 21 or later
- Maven 3.8.x or later
- PostgreSQL 15.x or later
- Redis 7.x or later
- Elasticsearch 8.x or later
- Docker (optional)
- Eureka Server (for service discovery)

### Installation

1. Clone the repository:

```bash
git clone https://github.com/your-username/lookforx-category-service.git
cd lookforx-category-service
```

2. Configure the database, Redis, and Elasticsearch in `application.yml`:

```yaml
spring:
  datasource:
    url: **************************************************
    username: postgres
    password: your_password
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
  elasticsearch:
    uris: http://localhost:9200
```

3. Build the application:

```bash
mvn clean install
```

### Running Locally

Start the application:

```bash
mvn spring-boot:run
```

The service will be available at http://localhost:8082/api

### Running with Docker

1. Build the Docker image:

```bash
docker build -t lookforx/category-service .
```

2. Run the container:

```bash
docker run -p 8082:8082 lookforx/category-service
```

## API Documentation

API documentation is available via Swagger UI at http://localhost:8082/api/swagger-ui.html when the application is running.

### Key Endpoints

- `POST /api/categories` - Create a new category
- `GET /api/categories` - Get all categories (hierarchical or flat)
- `GET /api/categories/{id}` - Get category by ID
- `PUT /api/categories/{id}` - Update a category
- `DELETE /api/categories/{id}` - Delete a category
- `GET /api/categories/root` - Get root categories by type
- `GET /api/categories/by-parent-and-type` - Get categories by parent and type
- `POST /api/categories/clear-cache` - Clear category cache

## Project Structure

```
├── src/
│   ├── main/
│   │   ├── java/com/lookforx/categoryservice/
│   │   │   ├── config/           # Configuration classes
│   │   │   ├── domain/           # Domain models and DTOs
│   │   │   ├── repository/       # Data repositories
│   │   │   ├── service/          # Business logic
│   │   │   ├── web/              # REST controllers and clients
│   │   │   └── CategoryServiceApplication.java  # Main application class
│   │   └── resources/
│   │       ├── application.yml   # Application configuration
│   │       └── bootstrap.yml     # Bootstrap configuration
│   └── test/                     # Unit and integration tests
├── k8s/                          # Kubernetes deployment files
├── .github/workflows/            # CI/CD pipeline
├── pom.xml                       # Maven configuration
└── Dockerfile                    # Docker configuration
```

## Testing

The service includes comprehensive test coverage with the following testing strategies:

### Test Types
- **Unit Tests**: Testing individual components in isolation
- **Integration Tests**: Testing component interactions with TestContainers
- **Repository Tests**: Testing data access layer with embedded databases
- **Service Tests**: Testing business logic with mocked dependencies
- **Controller Tests**: Testing REST endpoints with MockMvc

### Running Tests

```bash
# Run all tests
mvn test

# Run tests with coverage report
mvn clean test jacoco:report

# Run specific test class
mvn test -Dtest=CategoryServiceTest

# Run tests and generate site reports
mvn clean test site
```

### Test Coverage

The project maintains high test coverage standards:
- **Line Coverage**: Minimum 80%
- **Branch Coverage**: Minimum 70%

Coverage reports are generated using JaCoCo and can be found at:
- HTML Report: `target/site/jacoco/index.html`
- XML Report: `target/site/jacoco/jacoco.xml`

### Test Reports

After running tests, various reports are available:
- **Test Results**: `target/surefire-reports/`
- **Coverage Report**: `target/site/jacoco/`
- **Site Reports**: `target/site/` (run `mvn site` to generate)

## Architecture

The service follows a layered architecture pattern:

- **Controller Layer**: REST endpoints and request/response handling
- **Service Layer**: Business logic and transaction management
- **Repository Layer**: Data access and persistence
- **Domain Layer**: Entity models and DTOs
- **Configuration Layer**: Spring configuration and beans

### Key Components

- **Category Entity**: JPA entity representing categories with hierarchical relationships
- **CategoryService**: Business logic for category operations with caching
- **CategoryController**: REST API endpoints for category management
- **SearchClient**: Feign client for Elasticsearch integration
- **CategoryMapper**: MapStruct mapper for entity-DTO conversions

## Caching Strategy

The service implements multi-level caching:

- **Redis Cache**: Distributed caching for category data
- **Hibernate Second Level Cache**: JPA entity caching
- **Method-level Caching**: Spring Cache annotations on service methods

## Search Integration

Categories are automatically indexed in Elasticsearch for advanced search capabilities:

- **Real-time Indexing**: Categories are indexed on create/update operations
- **Multi-language Search**: Support for searching in different languages
- **Hierarchical Search**: Search within category hierarchies

## Deployment

The service is deployed to Google Kubernetes Engine (GKE) using GitHub Actions CI/CD pipeline.

### Kubernetes Deployment

```bash
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/service.yaml
```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## Contact

For any questions or support, please contact the LookforX team.
