# LookForX Request Service

A Spring Boot microservice for managing user requests in the LookForX platform. This service handles the creation, management, and lifecycle of user requests with integration to form submissions, location data, and media files.

## Features

### Core Functionality
- **Request Management**: Create, read, update, delete user requests
- **Status Management**: Track request lifecycle (ACTIVE, EXPIRED, CO<PERSON>LETED, CA<PERSON><PERSON>LED, SUSPENDED)
- **Search & Filtering**: Search requests by location, category, title, and status
- **Pagination**: All list endpoints support pagination and sorting

### Integration Features
- **Form Integration**: Validates form submissions via Form Service
- **Location Validation**: Validates country/city/district via Reference Data Service
- **Category Validation**: Validates categories via Category Service
- **Media Support**: Handles image, document, and video URLs from Media Service

### Technical Features
- **MongoDB Storage**: Document-based storage with indexing
- **Event Publishing**: Kafka events for request lifecycle
- **Caching**: Redis-based caching for performance
- **Scheduled Tasks**: Automatic expiration of old requests
- **API Documentation**: OpenAPI/Swagger documentation

## Architecture

### Domain Model
```
Request
├── id (String)
├── userId (String)
├── categoryId (String)
├── submissionFormId (String)
├── title (String)
├── description (String - Rich HTML)
├── location (LocationInfo)
│   ├── countryId
│   ├── cityId
│   └── districtId
├── priceRange (PriceRange)
│   ├── minPrice
│   ├── maxPrice
│   └── currency
├── media (MediaInfo)
│   ├── imageUrls (max 3)
│   ├── documentUrls (max 3)
│   └── videoUrl (max 1)
├── status (RequestStatus)
├── createdAt
├── expiresAt (max 60 days from creation)
└── updatedAt
```

### External Dependencies
- **Form Service**: Validates form submissions
- **Reference Data Service**: Validates location data
- **Category Service**: Validates categories
- **Media Service**: Provides media URLs

## API Endpoints

### Request Management
- `POST /api/v1/requests` - Create new request
- `GET /api/v1/requests/{id}` - Get request by ID
- `PUT /api/v1/requests/{id}` - Update request
- `DELETE /api/v1/requests/{id}` - Delete request
- `PATCH /api/v1/requests/{id}/status` - Change request status

### Query Endpoints
- `GET /api/v1/requests/user/{userId}` - Get user's requests
- `GET /api/v1/requests/user/{userId}/status/{status}` - Get user's requests by status
- `GET /api/v1/requests/category/{categoryId}` - Get requests by category
- `GET /api/v1/requests/active` - Get all active requests
- `GET /api/v1/requests/search` - Search requests with filters

### Search Parameters
- `countryId` - Filter by country
- `cityId` - Filter by city
- `districtId` - Filter by district
- `categoryId` - Filter by category
- `title` - Search in title
- `page`, `size`, `sortBy`, `sortDir` - Pagination and sorting

## Configuration

### MongoDB Configuration
```yaml
spring:
  data:
    mongodb:
      uri: *****************************************
      auto-index-creation: true
```

### Redis Configuration
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 5
  cache:
    type: redis
```

### Kafka Configuration
```yaml
spring:
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
```

## Events

### Published Events
- **RequestCreatedEvent**: When a new request is created
- **RequestStatusChangedEvent**: When request status changes

### Event Topics
- `request-events`: All request-related events

## Validation Rules

### Request Creation
- Title: 3-200 characters
- Description: 10-5000 characters (Rich HTML)
- Expiry date: Maximum 60 days from creation
- Images: Maximum 3 URLs
- Documents: Maximum 3 URLs
- Video: Maximum 1 URL
- All URLs must be valid HTTP/HTTPS

### External Validations
- Form submission must exist in Form Service
- Category must exist in Category Service
- Location (country/city/district) must exist in Reference Data Service

## Scheduled Tasks

### Request Expiration
- **Schedule**: Daily at 01:00 AM
- **Function**: Automatically expires requests past their expiry date
- **Status Change**: ACTIVE → EXPIRED

## Error Handling

### Custom Exceptions
- `RequestNotFoundException`: Request not found or unauthorized access
- `RequestValidationException`: Validation errors for external dependencies

### Global Exception Handler
Uses `CommonGlobalExceptionHandler` from lookforx-common for consistent error responses.

## Testing

### Unit Tests
- Service layer tests with Mockito
- Controller tests with MockMvc
- Repository tests with @DataMongoTest

### Test Coverage
- Service methods
- Controller endpoints
- Validation scenarios
- Error handling

## Running the Service

### Prerequisites
- Java 21
- Maven 3.8+
- MongoDB
- Redis
- Apache Kafka

### Local Development
```bash
# Build the project
mvn clean compile

# Run tests
mvn test

# Start the service
mvn spring-boot:run
```

### Docker
```bash
# Build Docker image
mvn spring-boot:build-image

# Run with Docker Compose
docker-compose up request-service
```

## Monitoring

### Health Checks
- Spring Boot Actuator endpoints
- MongoDB connection health
- Redis connection health
- Kafka producer health

### Metrics
- Request creation rate
- Request status distribution
- Response times
- Error rates

### Logging
- Structured logging with correlation IDs
- Request/response logging
- Error logging with stack traces
- Performance logging

## Security

### Authentication
- JWT token validation via API Gateway
- User authorization for request operations

### Data Protection
- Input validation and sanitization
- SQL injection prevention (NoSQL)
- XSS protection for HTML content

## Performance

### Caching Strategy
- Redis caching for frequently accessed data
- Cache eviction on data modifications

### Database Optimization
- MongoDB indexes on frequently queried fields
- Compound indexes for complex queries
- TTL indexes for automatic cleanup

### Pagination
- All list endpoints support pagination
- Default page size: 10
- Maximum page size: 100

## Deployment

### Environment Profiles
- `dev`: Development environment
- `test`: Testing environment
- `prod`: Production environment

### Configuration Management
- Spring Cloud Config Server
- Environment-specific configurations
- Secret management

## Contributing

### Code Style
- Follow Spring Boot best practices
- Use Lombok for boilerplate code
- Comprehensive JavaDoc documentation
- Unit test coverage > 80%

### Pull Request Process
1. Create feature branch
2. Implement changes with tests
3. Update documentation
4. Submit pull request
5. Code review and approval
