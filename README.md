# LookForX Customer Support Service

A comprehensive customer support microservice for the LookForX platform, providing ticket management and user feedback capabilities.

## Overview

The Customer Support Service handles:
- **Support Request Tickets**: Issues related to service requests
- **Complaint Tickets**: Complaints against users, requests, bids, or services
- **User Feedback**: Issues and suggestions from users

## Features

### Core Functionality
- ✅ Create and manage support tickets (REQUEST and COMPLAINT types)
- ✅ Submit and manage user feedback (ISSUE and SUGGESTION categories)
- ✅ Ticket status tracking (OPEN, IN_PROGRESS, RESOLVED, REJECTED)
- ✅ Anonymous feedback support
- ✅ Search and filtering capabilities
- ✅ Event-driven architecture with Kafka integration

### Technical Features
- ✅ Spring Boot 3.4.5 with Java 21
- ✅ PostgreSQL database with JPA/Hibernate
- ✅ Redis caching (Hibernate 2nd-level cache)
- ✅ Eureka service discovery
- ✅ Kafka event publishing
- ✅ Comprehensive API documentation with Swagger/OpenAPI
- ✅ Global exception handling
- ✅ Input validation
- ✅ Audit logging
- ✅ Unit and integration testing

## API Endpoints

### Support Request Tickets
```
POST   /api/support/requests                    - Create support request ticket
GET    /api/support/requests/{ticketId}         - Get ticket by ID
GET    /api/support/requests/by-request/{requestId} - Get ticket by request ID
```

### Complaint Tickets
```
POST   /api/support/complaints                  - Create complaint ticket
GET    /api/support/complaints/{ticketId}       - Get complaint by ID
GET    /api/support/complaints/by-bid/{bidId}   - Get complaint by bid ID
```

### User Feedback
```
POST   /api/support/feedback                    - Submit feedback
GET    /api/support/feedback/{feedbackId}       - Get feedback by ID
GET    /api/support/feedback                    - List feedback with filters
GET    /api/support/feedback/category/{category} - Get feedback by category
GET    /api/support/feedback/search             - Search feedback
```

## Domain Model

### Ticket Entity
- `ticketId` (UUID) - Primary key
- `requestId` (String) - Related request ID (for REQUEST tickets)
- `bidId` (String) - Related bid ID (for COMPLAINT tickets)
- `type` (TicketType) - REQUEST or COMPLAINT
- `status` (TicketStatus) - OPEN, IN_PROGRESS, RESOLVED, REJECTED
- `title` (String) - Ticket title
- `description` (String) - Detailed description
- `userId` (UUID) - User who created the ticket
- `assignedTo` (UUID) - Support agent assigned
- `priority` (String) - Priority level
- `targetType` (String) - Type of entity being complained about
- `targetId` (String) - ID of entity being complained about
- `resolutionNotes` (String) - Resolution details
- Audit fields: `createdAt`, `updatedAt`, `resolvedAt`

### Feedback Entity
- `feedbackId` (UUID) - Primary key
- `category` (FeedbackCategory) - ISSUE or SUGGESTION
- `message` (String) - Feedback content
- `userId` (UUID) - User who provided feedback (optional)
- `userEmail` (String) - Email for anonymous feedback
- `isAnonymous` (Boolean) - Whether feedback is anonymous
- `status` (String) - Processing status
- `adminNotes` (String) - Admin review notes
- `reviewedBy` (UUID) - Admin who reviewed
- Audit fields: `submittedAt`, `reviewedAt`

## Configuration

### Database
- PostgreSQL with connection pooling
- JPA/Hibernate with 2nd-level caching
- Automatic schema management

### Caching
- Redis-backed Hibernate 2nd-level cache
- Method-level caching for frequently accessed data
- Cache eviction on data modifications

### Messaging
- Kafka event publishing for domain events
- Topics: `support.ticket.events`, `support.feedback.events`
- Asynchronous event processing

### Service Discovery
- Eureka client registration
- Load balancing and failover support

## Running the Service

### Prerequisites
- Java 21
- Maven 3.8+
- PostgreSQL 13+
- Redis 6+
- Apache Kafka 2.8+

### Local Development
```bash
# Clone the repository
git clone <repository-url>
cd lookforx-customer-support-service

# Build the project
mvn clean compile

# Run tests
mvn test

# Start the service
mvn spring-boot:run
```

### Docker
```bash
# Build Docker image
docker build -t lookforx-customer-support-service .

# Run with Docker Compose
docker-compose up -d
```

### Configuration Profiles
- `dev` - Development environment
- `test` - Testing environment
- `prod` - Production environment

## Testing

### Unit Tests
```bash
mvn test
```

### Integration Tests
```bash
mvn verify -P integration-tests
```

### Test Coverage
- Service layer: 95%+
- Controller layer: 90%+
- Repository layer: 85%+

## Monitoring and Observability

### Health Checks
- `/actuator/health` - Service health status
- `/actuator/info` - Service information

### Metrics
- Custom metrics for ticket creation rates
- Feedback submission tracking
- Response time monitoring

### Logging
- Structured logging with JSON format
- Correlation IDs for request tracing
- Error tracking and alerting

## Security

### Input Validation
- Bean validation annotations
- Custom validators for business rules
- SQL injection prevention

### Data Protection
- Sensitive data encryption
- Anonymous feedback support
- GDPR compliance considerations

## Performance

### Caching Strategy
- Entity-level caching for tickets and feedback
- Query result caching for frequent searches
- Cache warming on application startup

### Database Optimization
- Proper indexing on search columns
- Connection pooling
- Query optimization

## Event Architecture

### Published Events
- `TicketCreatedEvent` - When new tickets are created
- `TicketStatusChangedEvent` - When ticket status changes
- `FeedbackSubmittedEvent` - When feedback is submitted

### Event Consumers
- Notification service integration
- Analytics and reporting
- Audit trail maintenance

## API Documentation

Swagger UI available at: `http://localhost:8080/swagger-ui.html`

OpenAPI specification: `http://localhost:8080/v3/api-docs`

## Contributing

1. Follow the existing code style and patterns
2. Write comprehensive tests for new features
3. Update documentation for API changes
4. Ensure all tests pass before submitting PRs

## Support

For technical support or questions:
- Create an issue in the repository
- Contact the development team
- Check the API documentation

## License

Copyright © 2024 LookForX. All rights reserved.
