# LookForX Notification Service

A comprehensive notification microservice built with Spring Boot that handles multi-language notifications, real-time WebSocket updates, and event-driven architecture using Kafka.

## Features

- **Multi-language Support**: Notifications in 50+ languages with separate translation entities
- **Real-time Notifications**: WebSocket integration for instant bell notifications
- **Event-driven Architecture**: Kafka consumers for processing various event types
- **Bulk Notifications**: Efficient bulk notification processing with thread pools
- **Multiple Notification Types**: Email, SMS, Push, In-app notifications
- **MongoDB Integration**: Event sourcing with MongoDB Atlas
- **PostgreSQL Storage**: Persistent notification storage with Hibernate
- **Eureka Integration**: Service discovery and registration

## Prerequisites

- Java 17 or higher
- Maven 3.6+
- PostgreSQL 17.5
- MongoDB Atlas account
- Apache Kafka
- Eureka Server

## Configuration

### Database Configuration

**PostgreSQL** (Notifications):
- Host: `************:5432`
- Database: `notificationdb`
- Username/Password: `postgres`

**MongoDB Atlas** (Events & Users):
- Event DB: `mongodb+srv://lookforx:<EMAIL>/eventdb`
- Auth DB: `mongodb+srv://lookforx:<EMAIL>/authdb`

### Kafka Configuration

**Remote Kafka Server**: `*************:9092`

⚠️ **IMPORTANT**: Due to Kafka's `advertised.listeners` configuration returning internal hostnames, you must add the following entry to your `/etc/hosts` file:

```bash
# Add this line to /etc/hosts
************* kafka
```

**How to edit /etc/hosts:**

```bash
# Option 1: Using nano editor
sudo nano /etc/hosts

# Option 2: Using echo command
echo "************* kafka" | sudo tee -a /etc/hosts

# Option 3: Using vim editor
sudo vim /etc/hosts
```

After editing, add this line at the end of the file:
```
************* kafka
```

### Event Topics

The service listens to the following Kafka topics:
- `user-events` - User registration, profile updates
- `form-events` - Form submissions and updates
- `request-events` - Service request notifications
- `bid-events` - Bidding system notifications
- `payment-events` - Payment confirmations
- `campaign-events` - Marketing campaign notifications
- `membership-events` - Membership upgrades/changes
- `bulk-notification-commands` - Bulk notification processing

## Installation & Setup

### 1. Clone and Build

```bash
cd lookforx-notification-service
mvn clean install
```

### 2. Configure Hosts File

**CRITICAL STEP**: Add Kafka hostname resolution:

```bash
echo "************* kafka" | sudo tee -a /etc/hosts
```

### 3. Start the Service

```bash
mvn spring-boot:run
```

The service will start on port `8093`.

### 4. Verify Kafka Connectivity

Check the logs for successful Kafka consumer assignments:
```
notification-service: partitions assigned: [user-events-0]
notification-service: partitions assigned: [form-events-0]
...
```

## API Endpoints

### Bell Notifications (WebSocket)

```bash
# Get unread notification count
GET /api/bell/count/{userId}

# Get bell notifications with pagination
GET /api/bell/notifications/{userId}?page=0&size=10&languageCode=EN

# Mark notification as read
PUT /api/bell/notifications/{notificationId}/read

# Mark notification as clicked
PUT /api/bell/notifications/{notificationId}/clicked
```

### Bulk Notifications

```bash
# Send bulk notifications
POST /api/notifications/bulk
Content-Type: application/json

{
  "userIds": [1, 2, 3],
  "titles": {
    "EN": "Welcome!",
    "TR": "Hoş geldiniz!"
  },
  "messages": {
    "EN": "Welcome to our platform",
    "TR": "Platformumuza hoş geldiniz"
  },
  "notificationType": "INFO",
  "priority": "NORMAL"
}
```

### Health Check

```bash
GET /actuator/health
```

## WebSocket Integration

Connect to WebSocket for real-time notifications:

```javascript
const socket = new SockJS('/ws');
const stompClient = Stomp.over(socket);

stompClient.connect({}, function(frame) {
    // Subscribe to user-specific notifications
    stompClient.subscribe('/user/queue/notifications', function(message) {
        const notification = JSON.parse(message.body);
        // Handle notification update
    });
});
```

## Troubleshooting

### Kafka Connection Issues

**Problem**: `UnknownHostException: kafka`

**Solution**: Ensure `/etc/hosts` file contains:
```
************* kafka
```

**Verification**:
```bash
# Test hostname resolution
ping kafka

# Should resolve to *************
```

### Service Won't Start

1. **Check port availability**:
   ```bash
   lsof -i :8093
   ```

2. **Verify database connectivity**:
   ```bash
   telnet ************ 5432
   ```

3. **Check Eureka server**:
   ```bash
   curl http://localhost:8761/eureka/apps
   ```

### MongoDB Connection Issues

Verify MongoDB Atlas connectivity:
```bash
# Test connection (requires mongo client)
mongosh "mongodb+srv://lookforx:<EMAIL>/eventdb"
```

## Development

### Running Tests

```bash
mvn test
```

### Building Docker Image

```bash
docker build -t lookforx-notification-service .
docker run -p 8093:8093 lookforx-notification-service
```

### Environment Variables

```bash
export SPRING_PROFILES_ACTIVE=dev
export EUREKA_CLIENT_SERVICE_URL_DEFAULTZONE=http://localhost:8761/eureka/
```

## Architecture

### Thread Pools

- **Bulk Notifications**: 5-20 threads, queue size 100
- **Individual Notifications**: 3-10 threads, queue size 50  
- **Email Notifications**: 2-8 threads, queue size 200

### Caching Strategy

- **Hibernate Second Level Cache**: Disabled (can be enabled with Redis)
- **Query Optimization**: Indexed queries for user notifications

### Event Processing

1. **Kafka Event Reception**: Events received from various microservices
2. **User Data Fetching**: MongoDB queries to get user information
3. **Notification Creation**: PostgreSQL persistence with translations
4. **Real-time Updates**: WebSocket notifications to connected clients

## Monitoring

### Metrics Endpoints

```bash
# Application metrics
GET /actuator/metrics

# Kafka consumer metrics
GET /actuator/metrics/kafka.consumer

# Database connection pool
GET /actuator/metrics/hikaricp
```

### Log Levels

```yaml
logging:
  level:
    com.lookforx.notificationservice: DEBUG
    org.apache.kafka: INFO
    org.springframework.kafka: DEBUG
```

## Support

For issues related to:
- **Kafka connectivity**: Verify `/etc/hosts` configuration
- **Database issues**: Check connection strings and credentials
- **WebSocket problems**: Verify CORS and security configurations
- **Performance**: Monitor thread pool metrics and database queries
