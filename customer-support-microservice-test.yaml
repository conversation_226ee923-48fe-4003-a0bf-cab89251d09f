spring:
  application:
    name: customer-support-microservice

  datasource:
    url: *****************************************************
    username: postgres
    password: L00kForX_?
    driver-class-name: org.postgresql.Driver
    hikari:
      # retire connections before the server does (e.g. if DB wait_timeout is 10m)
      max-lifetime: 600000        # 10 minutes in ms
      # how long a connection can sit idle before eviction
      idle-timeout: 300000        # 5 minutes
      # how long to wait for a connection from the pool
      connection-timeout: 30000   # 30 seconds
      # run a lightweight test query before handing out a connection
      connection-test-query: SELECT 1
      # ping idle connections periodically to keep them alive
      keepalive-time: 300000      # 5 minutes

  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        jdbc:
          batch_size: 25
          fetch_size: 50
        "[order_inserts]": true
        "[order_updates]": true
        "[batch_versioned_data]": true
        query:
          "[plan_cache_max_size]": 2048
          "[plan_parameter_metadata_max_size]": 128
        "[generate_statistics]": true
        session:
          events:
            log:
              "[LOG_QUERIES_SLOWER_THAN_MS]": 1000
    database-platform: org.hibernate.dialect.PostgreSQLDialect

  jackson:
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

  logging:
    level:
      root: INFO
      com.lookforx.support: INFO
      org.hibernate.SQL: DEBUG
      org.hibernate.type.descriptor.sql.BasicBinder: TRACE
      org.springframework.web: DEBUG
      org.springframework.data.elasticsearch.client.elc: DEBUG
      co.elastic.clients.transport: DEBUG

  data:
    redis:
      host: ***********
      port: 6379
      password: ""          # leave empty if no password
      timeout: 2000ms       # or "2s"
      database: 0

  cache:
    type: redis
    cache-names:
      - tickets
      - feedback

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
