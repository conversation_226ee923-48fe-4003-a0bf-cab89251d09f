spring:
  application:
    name: request-microservice

  data:
    mongodb:
      uri: mongodb+srv://lookforx:<EMAIL>/requestdb?retryWrites=true&w=majority&appName=lookforx
      auto-index-creation: true
    redis:
      host: *************
      port: 6379
      database: 5
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

  cache:
    type: redis
    redis:
      time-to-live: 3600000

  kafka:
    bootstrap-servers: *************:9092
    producer:
      # Increase timeouts for cloud Kafka
      request-timeout-ms: 600000      # 10 minutes
      delivery-timeout-ms: 1200000    # 20 minutes
      max-block-ms: 600000            # 10 minutes
      retries: 10                     # Increase retry count
      retry-backoff-ms: 5000          # 5 seconds between retries
      # Batch settings for better performance
      batch-size: 32768               # 32KB batch size
      linger-ms: 100                  # Wait 100ms to batch messages
      buffer-memory: 67108864         # 64MB buffer
      # Compression for better network utilization
      compression-type: gzip
      # Acknowledgment settings
      acks: all                       # Wait for all replicas
      # Serialization
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer

management:
  tracing:
    sampling:
      probability: 1.0
    zipkin:
      endpoint: http://*************:9411/api/v2/spans

springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html

# Resilience4j Configuration
resilience4j:
  circuitbreaker:
    instances:
      form-service:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        wait-duration-in-open-state: 30s
        failure-rate-threshold: 50
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 2s
      category-service:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        wait-duration-in-open-state: 30s
        failure-rate-threshold: 50
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 2s
      location-service:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        wait-duration-in-open-state: 30s
        failure-rate-threshold: 50
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 2s
  retry:
    instances:
      form-service:
        max-attempts: 3
        wait-duration: 1s
        retry-exceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
      category-service:
        max-attempts: 3
        wait-duration: 1s
        retry-exceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
      location-service:
        max-attempts: 3
        wait-duration: 1s
        retry-exceptions:
          - java.net.ConnectException
          - java.net.SocketTimeoutException
  timelimiter:
    instances:
      form-service:
        timeout-duration: 3s
      category-service:
        timeout-duration: 3s
      location-service:
        timeout-duration: 3s

# Logging Configuration
logging:
  level:
    root: INFO
    com.lookforx.requestservice: INFO
    org.springframework.data.mongodb: DEBUG
    org.springframework.cache: DEBUG
    org.springframework.kafka: INFO
    io.github.resilience4j: DEBUG